#!/bin/bash

# DHR 微服务快速启动脚本
# 用于快速测试Docker镜像是否可以正常运行

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 测试单个服务镜像
test_service_image() {
    local service_name=$1
    local port=$2

    log_info "Testing $service_name image..."

    # 运行容器（不依赖Nacos，仅测试镜像是否可启动）
    docker run -d \
        --name "test-$service_name" \
        -p "$port:$port" \
        -e CONFIG_ENABLE=false \
        -e WAIT_FOR_NACOS=false \
        "dhr/$service_name:latest"

    # 等待启动
    sleep 10

    # 检查容器状态
    if docker ps | grep -q "test-$service_name"; then
        log_success "$service_name container started successfully"

        # 尝试健康检查
        if curl -f "http://localhost:$port/actuator/health" >/dev/null 2>&1; then
            log_success "$service_name health check passed"
        else
            log_warning "$service_name health check failed (may need Nacos)"
        fi
    else
        log_warning "$service_name container failed to start"
        docker logs "test-$service_name" | tail -10
    fi

    # 清理测试容器
    docker rm -f "test-$service_name" >/dev/null 2>&1
}

# 清理之前的测试容器
cleanup() {
    log_info "Cleaning up test containers..."
    docker rm -f test-dhr-gateway-service test-dhr-oauth-service test-dhr-ai-service test-dhr-utility-service >/dev/null 2>&1 || true
}

# 主函数
main() {
    log_info "Testing DHR microservices Docker images..."

    cleanup

    # 测试各个服务
    test_service_image "dhr-gateway-service" "9110"
    test_service_image "dhr-oauth-service" "9006"
    test_service_image "dhr-ai-service" "9044"
    test_service_image "dhr-utility-service" "8080"

    log_success "Image testing completed!"

    echo ""
    log_info "Next steps:"
    echo "1. Start full environment: docker-compose up -d"
    echo "2. Check services: docker-compose ps"
    echo "3. View logs: docker-compose logs -f"
    echo "4. Access Nacos: http://localhost:8848/nacos"
}

# 显示帮助
show_help() {
    cat << EOF
DHR Microservices Image Test Script

This script tests if the built Docker images can start properly.

Usage: $0 [OPTIONS]

Options:
    -h, --help      Show this help message
    -c, --cleanup   Only cleanup test containers

Examples:
    $0              # Test all services
    $0 --cleanup    # Cleanup test containers

EOF
}

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--cleanup)
            cleanup
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主逻辑
main
