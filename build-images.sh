#!/bin/bash

# DHR 微服务 Docker 镜像构建脚本
# 作者: Deloitte DHR Team
# 支持多阶段构建 - 构建过程在容器内部进行

# 镜像仓库配置
REGISTRY="crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com"
NAMESPACE="deloitte-lkk"

# 多架构构建配置
DEFAULT_PLATFORMS="linux/amd64"
SUPPORTED_PLATFORMS="linux/amd64,linux/arm64"
BUILD_PLATFORMS="$DEFAULT_PLATFORMS"
USE_BUILDX=true  # 默认使用传统构建，避免 JVM 崩溃问题

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker Buildx是否可用
check_docker_buildx() {
    if ! docker buildx version >/dev/null 2>&1; then
        log_error "Docker Buildx not found. Please install Docker Desktop or enable buildx plugin."
        log_info "For manual installation: https://docs.docker.com/buildx/working-with-buildx/"
        exit 1
    fi

    log_info "Docker Buildx version: $(docker buildx version)"

    # 检查或创建多架构构建器
    local builder_name="dhr-multi-arch-builder"
    if ! docker buildx inspect "$builder_name" >/dev/null 2>&1; then
        log_info "Creating multi-architecture builder: $builder_name"
        docker buildx create --name "$builder_name" --driver docker-container --use
        docker buildx inspect --bootstrap
    else
        log_info "Using existing builder: $builder_name"
        docker buildx use "$builder_name"
    fi
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker not found. Please install Docker first."
        exit 1
    fi
    log_info "Docker version: $(docker --version)"

    # 如果启用多架构构建，检查buildx
    if [ "$USE_BUILDX" = true ]; then
        check_docker_buildx
    fi
}

# 构建单个服务的Docker镜像
build_service_image() {
    local service_name=$1
    local service_path=$2
    local version=${3:-"3.7.1"}

    log_info "Building Docker image for $service_name..."
    log_info "Target platforms: $BUILD_PLATFORMS"
    log_info "Using multi-stage build (Maven 3.8.6 + JDK 11 build inside container)"
    log_info "Build context: project root directory (for multi-module Maven build)"

    if [ ! -f "$service_path/Dockerfile" ]; then
        log_error "Dockerfile not found in $service_path"
        return 1
    fi

    # 构建镜像标签
    local local_tag="dhr/${service_name}:${version}"
    local local_latest="dhr/${service_name}:latest"
    local remote_versioned_tag="${REGISTRY}/${NAMESPACE}/${service_name}:${version}"
    local remote_latest_tag="${REGISTRY}/${NAMESPACE}/${service_name}:latest"

    if [ "$USE_BUILDX" = true ]; then
        # 使用buildx进行多架构构建
        local build_args=""
        local push_flag=""

        if [ "$PUSH_TO_REGISTRY" = true ]; then
            # 直接推送到远程仓库
            push_flag="--push"
            build_args="--platform $BUILD_PLATFORMS -t $remote_versioned_tag -t $remote_latest_tag"
        else
            # 只构建本地镜像（使用指定平台，但注意--load只支持单平台）
            # 如果指定了多个平台，则只加载第一个平台
            local first_platform=$(echo "$BUILD_PLATFORMS" | cut -d',' -f1)
            build_args="--platform $first_platform -t $local_tag -t $local_latest --load"
        fi

        log_info "Running: docker buildx build $build_args $push_flag . -f $service_path/Dockerfile"
        docker buildx build $build_args $push_flag . -f "$service_path/Dockerfile"

        if [ $? -eq 0 ]; then
            if [ "$PUSH_TO_REGISTRY" = true ]; then
                log_success "Multi-arch Docker image built and pushed successfully: $remote_versioned_tag"
            else
                log_success "Docker image built successfully: $local_tag"
            fi
        else
            log_error "Failed to build Docker image for $service_name"
            return 1
        fi
    else
        # 传统单架构构建 (从项目根目录构建)
        docker build -t "$local_tag" -t "$local_latest" . -f "$service_path/Dockerfile"

        if [ $? -eq 0 ]; then
            log_success "Docker image built successfully: $local_tag"

            # 如果需要推送到仓库，则创建远程标签
            if [ "$PUSH_TO_REGISTRY" = true ]; then
                tag_for_registry "$service_name" "$version"
            fi
        else
            log_error "Failed to build Docker image for $service_name"
            return 1
        fi
    fi
}

# 获取当前平台信息
get_current_platform() {
    local arch=$(uname -m)
    case $arch in
        x86_64)
            echo "linux/amd64"
            ;;
        aarch64|arm64)
            echo "linux/arm64"
            ;;
        *)
            echo "linux/amd64"  # 默认fallback
            ;;
    esac
}

# 为镜像添加远程仓库标签
tag_for_registry() {
    local service_name=$1
    local version=$2

    local remote_versioned_tag="${REGISTRY}/${NAMESPACE}/${service_name}:${version}"
    local remote_latest_tag="${REGISTRY}/${NAMESPACE}/${service_name}:latest"

    log_info "Tagging image for registry: $remote_versioned_tag"

    docker tag "dhr/${service_name}:${version}" "$remote_versioned_tag"
    docker tag "dhr/${service_name}:latest" "$remote_latest_tag"

    if [ $? -eq 0 ]; then
        log_success "Image tagged for registry: $remote_versioned_tag"
    else
        log_error "Failed to tag image for registry"
        return 1
    fi
}

# 推送单个服务镜像到仓库
push_service_image() {
    local service_name=$1
    local version=${2:-"3.7.1"}

    # 如果使用buildx构建，镜像已经直接推送了
    if [ "$USE_BUILDX" = true ]; then
        log_info "Multi-arch image already pushed during build: ${REGISTRY}/${NAMESPACE}/${service_name}:${version}"
        return 0
    fi

    local local_versioned_tag="dhr/${service_name}:${version}"
    local local_latest_tag="dhr/${service_name}:latest"
    local remote_versioned_tag="${REGISTRY}/${NAMESPACE}/${service_name}:${version}"
    local remote_latest_tag="${REGISTRY}/${NAMESPACE}/${service_name}:latest"

    # 检查本地镜像是否存在
    if ! docker image inspect "$local_versioned_tag" >/dev/null 2>&1; then
        log_error "Local image not found: $local_versioned_tag"
        log_info "Please build the image first or run without --push-only"
        return 1
    fi

    # 为本地镜像添加远程标签（如果还没有的话）
    if ! docker image inspect "$remote_versioned_tag" >/dev/null 2>&1; then
        log_info "Tagging image for registry: $remote_versioned_tag"
        docker tag "$local_versioned_tag" "$remote_versioned_tag"
        docker tag "$local_latest_tag" "$remote_latest_tag"
    fi

    log_info "Pushing image to registry: $remote_versioned_tag"

    # 推送版本标签
    docker push "$remote_versioned_tag"
    if [ $? -ne 0 ]; then
        log_error "Failed to push versioned image: $remote_versioned_tag"
        return 1
    fi

    # 推送latest标签
    docker push "$remote_latest_tag"
    if [ $? -ne 0 ]; then
        log_error "Failed to push latest image: $remote_latest_tag"
        return 1
    fi

    log_success "Successfully pushed image: $remote_versioned_tag"
}

# 推送所有服务镜像到仓库
push_all_images() {
    log_info "Pushing all images to registry: ${REGISTRY}/${NAMESPACE}"

    # 检查Docker登录状态
    check_docker_login

    # 推送各个微服务镜像
    push_service_image "dhr-gateway-service"
    # push_service_image "dhr-oauth-service"
    # push_service_image "dhr-ai-service"
    # push_service_image "dhr-utility-service"

    log_success "All images pushed to registry successfully!"

    # 显示推送的镜像
    log_info "Pushed images:"
    echo "  ${REGISTRY}/${NAMESPACE}/dhr-gateway-service:latest"
    echo "  ${REGISTRY}/${NAMESPACE}/dhr-oauth-service:latest"
    echo "  ${REGISTRY}/${NAMESPACE}/dhr-ai-service:latest"
    echo "  ${REGISTRY}/${NAMESPACE}/dhr-utility-service:latest"
}

# 检查Docker登录状态
check_docker_login() {
    log_info "Checking Docker registry login status..."

    # 检查是否已登录到指定仓库
    if ! grep -q "$REGISTRY" ~/.docker/config.json 2>/dev/null; then
        log_warning "Not logged in to registry: $REGISTRY"
        log_info "Please run: docker login $REGISTRY"
        log_info "Then retry with --push option"

        read -p "Do you want to login now? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker login $REGISTRY
            if [ $? -ne 0 ]; then
                log_error "Failed to login to registry"
                exit 1
            fi
        else
            exit 1
        fi
    fi

    log_success "Docker registry login verified"
}

# 检查本地镜像
check_local_images() {
    log_info "Checking local DHR images..."
    if docker images | grep -q "dhr/"; then
        docker images | grep "dhr/"
    else
        log_warning "No local DHR images found"
    fi
}

# 清理函数
cleanup() {
    log_info "Cleaning up old Docker images..."
    # 该命令用于清理本地未被使用的悬空（dangling）Docker镜像，释放磁盘空间
    docker image prune -f
    log_success "Cleanup completed"
}

# 帮助信息
show_help() {
    cat << EOF
DHR Microservices Docker Build Script (Multi-Stage Build)

Usage: $0 [OPTIONS]

Options:
    -h, --help          Show this help message
    -c, --clean         Clean up old Docker images after build
    -p, --push          Push images to registry after build
    --push-only         Only push existing images (skip build)
    --platform PLATFORMS Set target platforms (default: linux/amd64)
    --multi-arch        Build for multiple architectures (linux/amd64,linux/arm64)
    --no-buildx         Disable Docker Buildx (single-arch build only)
    --diagnose          Run diagnostics and show environment info

Registry Configuration:
    REGISTRY: ${REGISTRY}
    NAMESPACE: ${NAMESPACE}

Platform Options:
    --platform linux/amd64              # Build for AMD64 only (default)
    --platform linux/arm64              # Build for ARM64 only
    --platform linux/amd64,linux/arm64  # Build for both architectures
    --multi-arch                         # Shortcut for both architectures

Build Process:
    - Uses multi-stage Docker builds from project root directory
    - Maven build happens inside container (JDK 11 + Maven 3.8.6)
    - Leverages multi-module Maven project structure and parent POM
    - No need for local Maven/Java installation
    - Optimized for AMD64 architecture (ACK compatible)
    - Enhanced Maven dependency caching for faster builds

Examples:
    $0                              # Build for AMD64 (default)
    $0 --multi-arch --push          # Build multi-arch and push to registry
    $0 --platform linux/arm64       # Build for ARM64 only
    $0 --push                       # Build and push to registry
    $0 --no-buildx                  # Use traditional single-arch build
    $0 --diagnose                   # Check environment and requirements

Before pushing images, make sure you are logged in to the registry:
    docker login ${REGISTRY}

EOF
}

# 解析命令行参数
CLEANUP=false
PUSH_TO_REGISTRY=false
PUSH_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--clean)
            CLEANUP=true
            shift
            ;;
        -p|--push)
            PUSH_TO_REGISTRY=true
            shift
            ;;
        --push-only)
            PUSH_ONLY=true
            PUSH_TO_REGISTRY=true
            shift
            ;;
        --platform)
            BUILD_PLATFORMS="$2"
            shift 2
            ;;
        --multi-arch)
            BUILD_PLATFORMS="$SUPPORTED_PLATFORMS"
            shift
            ;;
        --no-buildx)
            USE_BUILDX=false
            shift
            ;;
        --diagnose)
            log_info "Running diagnostics..."
            check_docker
            check_local_images
            check_docker_login
            log_info "Current platform: $(get_current_platform)"
            log_info "Build platforms: $BUILD_PLATFORMS"
            log_info "Using Buildx: $USE_BUILDX"
            log_info "Diagnostics completed"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主构建函数
main() {
    log_info "Starting DHR microservices Docker image build process..."
    log_info "Build platforms: $BUILD_PLATFORMS"
    log_info "Using Buildx: $USE_BUILDX"
    log_info "Using multi-stage builds (Maven build inside container)"

    # 检查依赖
    check_docker

    # 构建各个微服务的Docker镜像
    log_info "Building Docker images for all microservices..."

    # 网关服务
    build_service_image "dhr-gateway-service" "dhr-service/dhr-gateway-service"

    # # 认证服务
    # build_service_image "dhr-oauth-service" "dhr-service/dhr-oauth-service"

    # # AI服务
    # build_service_image "dhr-ai-service" "dhr-service/dhr-ai-service/dhr-ai-provider"

    # # 工具服务
    # build_service_image "dhr-utility-service" "dhr-service/dhr-utility-service/dhr-utility-provider"

    log_success "All Docker images built successfully!"

    # 显示构建的镜像
    if [ "$USE_BUILDX" = false ] || [ "$PUSH_TO_REGISTRY" = false ]; then
        log_info "Built Docker images:"
        docker images | grep "dhr/"
    fi

    # 如果需要推送到仓库且没有使用buildx直接推送
    if [ "$PUSH_TO_REGISTRY" = true ] && [ "$USE_BUILDX" = false ]; then
        push_all_images
    fi
}

# 执行主逻辑
if [ "$PUSH_ONLY" = true ]; then
    log_info "Push-only mode: Only pushing existing images to registry"
    check_docker
    push_all_images
else
    main
fi

# 清理
if [ "$CLEANUP" = true ]; then
    cleanup
fi

log_success "Build process completed!"
