#!/bin/bash

# 构建 DHR Gateway Service Docker 镜像
# 智能平台检测和构建优化

set -e

# 镜像仓库配置
REGISTRY="crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com"
NAMESPACE="deloitte-lkk"

# 检测当前系统架构
CURRENT_ARCH=$(uname -m)
DOCKER_ARCH=$(docker version --format '{{.Server.Arch}}')

# 默认参数
FORCE_PLATFORM=""
IMAGE_TAG="dhr-gateway-service:1.0.0"
BUILD_ARGS=""
PUSH_TO_REGISTRY=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --platform)
            FORCE_PLATFORM="$2"
            shift 2
            ;;
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --amd64)
            FORCE_PLATFORM="linux/amd64"
            shift
            ;;
        --arm64)
            FORCE_PLATFORM="linux/arm64"
            shift
            ;;
        --native)
            FORCE_PLATFORM=""
            shift
            ;;
        --push)
            PUSH_TO_REGISTRY=true
            shift
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --platform PLATFORM  指定目标平台 (如: linux/amd64, linux/arm64)"
            echo "  --tag TAG            指定镜像标签 (默认: dhr-gateway-service:1.0.0)"
            echo "  --amd64              构建 AMD64 架构镜像"
            echo "  --arm64              构建 ARM64 架构镜像"
            echo "  --native             使用本机架构构建 (推荐)"
            echo "  --push               构建完成后推送到镜像仓库"
            echo "  --help, -h           显示此帮助信息"
            echo ""
            echo "镜像仓库:"
            echo "  REGISTRY: $REGISTRY"
            echo "  NAMESPACE: $NAMESPACE"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 智能选择构建平台
if [[ -n "$FORCE_PLATFORM" ]]; then
    BUILD_ARGS="--platform $FORCE_PLATFORM"
    TARGET_ARCH="$FORCE_PLATFORM"
    echo "🎯 强制指定目标架构: $FORCE_PLATFORM"
else
    # 使用本机架构构建（推荐）
    if [[ "$CURRENT_ARCH" == "arm64" ]]; then
        TARGET_ARCH="linux/arm64 (本机架构)"
    elif [[ "$CURRENT_ARCH" == "x86_64" ]]; then
        TARGET_ARCH="linux/amd64 (本机架构)"
    else
        TARGET_ARCH="$DOCKER_ARCH (Docker 默认)"
    fi
    echo "🚀 使用本机架构构建: $TARGET_ARCH"
fi

echo "=================================="
echo "🏗️  DHR Gateway Service 构建信息"
echo "=================================="
echo "📋 镜像标签: $IMAGE_TAG"
echo "🎯 目标架构: $TARGET_ARCH"
echo "💻 当前系统: $CURRENT_ARCH"
echo "🐳 Docker 架构: $DOCKER_ARCH"
if [[ "$PUSH_TO_REGISTRY" == true ]]; then
    echo "📤 推送仓库: ${REGISTRY}/${NAMESPACE}"
fi
echo "=================================="

# 检查Docker登录状态
check_docker_login() {
    if [[ "$PUSH_TO_REGISTRY" == true ]]; then
        echo "🔐 检查镜像仓库登录状态..."
        if ! grep -q "$REGISTRY" ~/.docker/config.json 2>/dev/null; then
            echo "⚠️  未登录到镜像仓库: $REGISTRY"
            echo "请先执行: docker login $REGISTRY"
            exit 1
        fi
        echo "✅ 镜像仓库登录状态正常"
    fi
}

# 推送镜像到仓库
push_image() {
    local image_name=$(echo "$IMAGE_TAG" | cut -d':' -f1)
    local image_version=$(echo "$IMAGE_TAG" | cut -d':' -f2)
    local remote_tag="${REGISTRY}/${NAMESPACE}/${image_name}:${image_version}"
    local remote_latest="${REGISTRY}/${NAMESPACE}/${image_name}:latest"

    echo "📤 推送镜像: $IMAGE_TAG -> $remote_tag"

    # 添加远程标签
    docker tag "$IMAGE_TAG" "$remote_tag"
    docker tag "$IMAGE_TAG" "$remote_latest"

    # 推送版本标签
    if docker push "$remote_tag"; then
        echo "✅ 版本标签推送成功: $remote_tag"
    else
        echo "❌ 版本标签推送失败: $remote_tag"
        return 1
    fi

    # 推送latest标签
    if docker push "$remote_latest"; then
        echo "✅ latest标签推送成功: $remote_latest"
    else
        echo "❌ latest标签推送失败: $remote_latest"
        return 1
    fi
}

# 构建 Docker 镜像
echo "⏳ 开始构建镜像..."
docker build $BUILD_ARGS -t "$IMAGE_TAG" . -f dhr-service/dhr-gateway-service/Dockerfile

echo ""
echo "✅ 构建完成！"
echo "📊 镜像信息:"
docker images | grep "$(echo "$IMAGE_TAG" | cut -d':' -f1)" | head -1

# 如果启用推送，则推送到仓库
if [[ "$PUSH_TO_REGISTRY" == true ]]; then
    echo ""
    echo "📤 开始推送镜像到仓库..."

    # 检查登录状态
    check_docker_login

    # 推送镜像
    if push_image; then
        echo ""
        echo "🎉 镜像推送成功！"
        echo "📤 推送的镜像:"
        echo "  ${REGISTRY}/${NAMESPACE}/$(echo "$IMAGE_TAG" | cut -d':' -f1):$(echo "$IMAGE_TAG" | cut -d':' -f2)"
        echo "  ${REGISTRY}/${NAMESPACE}/$(echo "$IMAGE_TAG" | cut -d':' -f1):latest"
    else
        echo ""
        echo "❌ 镜像推送失败！"
        exit 1
    fi
fi
