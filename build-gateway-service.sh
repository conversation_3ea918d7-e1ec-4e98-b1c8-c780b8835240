#!/bin/bash

# 构建 DHR Gateway Service Docker 镜像
# 使用 AMD64 架构确保兼容性

set -e

echo "开始构建 DHR Gateway Service Docker 镜像..."
echo "目标架构: AMD64"
echo "镜像标签: dhr-gateway-service:4.0.0"

# 构建 Docker 镜像，指定 AMD64 架构
# --platform linux/amd64: 指定目标平台架构为 AMD64，确保跨平台兼容性
# -t dhr-gateway-service:4.0.0: 为镜像指定标签名称和版本号
# .: 构建上下文路径，指向当前目录（项目根目录）
# -f dhr-service/dhr-gateway-service/Dockerfile: 指定 Dockerfile 文件路径
docker build --platform linux/amd64 -t dhr-gateway-service:4.0.0 . -f dhr-service/dhr-gateway-service/Dockerfile

echo "构建完成！"
echo "镜像信息:"
docker images | grep dhr-gateway-service | head -1
