
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ai_chat_dialogue
-- ----------------------------
DROP TABLE IF EXISTS `ai_chat_dialogue`;
CREATE TABLE `ai_chat_dialogue` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `ai_group` varchar(40) NOT NULL COMMENT 'AI分组：数字人-digital_human，文本聊天-text_chat',
  `dialogue_title` varchar(255) NOT NULL COMMENT '对话标题：默认为聊天的第一句话',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识：0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='AI聊天-对话历史记录表';

-- ----------------------------
-- Table structure for ai_chat_msg
-- ----------------------------
DROP TABLE IF EXISTS `ai_chat_msg`;
CREATE TABLE `ai_chat_msg` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `dialogue_id` bigint(20) NOT NULL COMMENT '对话ID',
  `conversation_id` varchar(40) DEFAULT NULL COMMENT '会话ID',
  `ai_type` varchar(40) DEFAULT NULL COMMENT 'AI类型',
  `ai_sub_type` varchar(40) DEFAULT NULL COMMENT 'AI子类型',
  `role` varchar(10) NOT NULL COMMENT '角色',
  `send_time` datetime NOT NULL COMMENT '发送时间',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识：0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='AI聊天-聊天消息表';

-- ----------------------------
-- Table structure for ai_chat_msg_content
-- ----------------------------
DROP TABLE IF EXISTS `ai_chat_msg_content`;
CREATE TABLE `ai_chat_msg_content` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `dialogue_id` bigint(20) NOT NULL COMMENT '对话ID',
  `msg_id` bigint(20) NOT NULL COMMENT '消息ID',
  `content_type` varchar(20) DEFAULT NULL COMMENT '消息类型',
  `content` text DEFAULT NULL COMMENT '消息内容',
  `content_order` int(11) DEFAULT NULL COMMENT '内容排序',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识：0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='AI聊天-聊天消息内容表';

-- ----------------------------
-- Table structure for ai_coach_task_emp
-- ----------------------------
DROP TABLE IF EXISTS `ai_coach_task_emp`;
CREATE TABLE `ai_coach_task_emp` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT '课程资源ID',
  `task_id` bigint(20) NOT NULL COMMENT '培训任务ID',
  `emp_code` varchar(20) NOT NULL COMMENT '员工编号',
  `emp_name` varchar(40) NOT NULL COMMENT '员工名称',
  `emp_avatar_url` varchar(255) DEFAULT NULL COMMENT '员工头像',
  `org_code` varchar(20) DEFAULT NULL COMMENT '员工部门编码',
  `org_name` varchar(20) DEFAULT NULL COMMENT '员工部门名称',
  `position_code` varchar(20) DEFAULT NULL COMMENT '员工岗位编码',
  `position_name` varchar(20) DEFAULT NULL COMMENT '员工岗位名称',
  `status` varchar(10) DEFAULT NULL COMMENT '状态;WKS：未开始，JXZ：进行中，YWC：已完成，YJS：已结束',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `final_score` decimal(10,3) DEFAULT NULL COMMENT '最终得分',
  `score_result` tinyint(1) DEFAULT NULL COMMENT '评分结果;0：不通过，1：通过',
  `elapsed_time` decimal(10,3) DEFAULT NULL COMMENT '消耗时间（minute）',
  `video_file_url` varchar(255) DEFAULT NULL COMMENT '视频文件Url',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='培训任务-员工表;';

-- ----------------------------
-- Table structure for ai_coach_task_info
-- ----------------------------
DROP TABLE IF EXISTS `ai_coach_task_info`;
CREATE TABLE `ai_coach_task_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT '课程资源ID',
  `task_name` varchar(80) NOT NULL COMMENT '培训任务名称',
  `start_date_time` datetime DEFAULT NULL COMMENT '培训开始时间',
  `end_date_time` datetime DEFAULT NULL COMMENT '培训结束时间',
  `task_cover_url` varchar(255) DEFAULT NULL COMMENT '培训封面URL',
  `task_duration` decimal(10,3) DEFAULT NULL COMMENT '培训任务时长（hour）',
  `task_status` varchar(10) DEFAULT NULL COMMENT '培训任务状态;CG：草稿，PXZ：培训中，YWC：已完成，YJS：已结束',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='培训任务-基本信息表;';

-- ----------------------------
-- Table structure for ai_course_category
-- ----------------------------
DROP TABLE IF EXISTS `ai_course_category`;
CREATE TABLE `ai_course_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category_name` varchar(80) NOT NULL COMMENT '类别名称',
  `parent_id` bigint(20) NOT NULL COMMENT '类别父级ID，顶级节点值为0',
  `category_path` varchar(32) DEFAULT NULL COMMENT '类别全路径，用,隔开',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程类别表;';

-- ----------------------------
-- Table structure for ai_course_resource_attach
-- ----------------------------
DROP TABLE IF EXISTS `ai_course_resource_attach`;
CREATE TABLE `ai_course_resource_attach` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT '课程资源ID',
  `file_id` varchar(32) NOT NULL COMMENT '文件ID',
  `file_name` varchar(80) NOT NULL COMMENT '文件名称',
  `file_type` varchar(10) NOT NULL COMMENT '文件类型',
  `file_md5` varchar(40) DEFAULT NULL COMMENT '文件md5值',
  `file_url` varchar(1024) DEFAULT NULL COMMENT '文件URL',
  `file_size` varchar(20) DEFAULT NULL COMMENT '文件大小',
  `datasets_file_id` varchar(40) DEFAULT NULL COMMENT '知识库文件ID',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-学习附件表;';

-- ----------------------------
-- Table structure for ai_course_resource_info
-- ----------------------------
DROP TABLE IF EXISTS `ai_course_resource_info`;
CREATE TABLE `ai_course_resource_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category_id` bigint(20) NOT NULL COMMENT '类别ID',
  `course_name` varchar(80) NOT NULL COMMENT '课程名称',
  `course_type` varchar(10) NOT NULL COMMENT '课程分类;JXKC：教学课程，AIPL：AI陪练',
  `course_cover_url` varchar(255) DEFAULT NULL COMMENT '课程封面图链接',
  `course_duration` decimal(10,3) DEFAULT NULL COMMENT '课程时长（minute）',
  `course_status` varchar(10) NOT NULL COMMENT '课程状态;CG：草稿，TJ：提交',
  `record_id` bigint(20) DEFAULT NULL COMMENT 'AI生成操作的记录ID',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-基本信息表;';

-- ----------------------------
-- Table structure for ai_teach_choice_option
-- ----------------------------
DROP TABLE IF EXISTS `ai_teach_choice_option`;
CREATE TABLE `ai_teach_choice_option` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT '课程资源ID',
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `option_name` varchar(255) NOT NULL COMMENT '选项名称',
  `is_answer` tinyint(1) NOT NULL COMMENT '是否答案',
  `option_sort` int(11) NOT NULL COMMENT '选项序号',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-教学课程-考核题目-选择题选项表;';

-- ----------------------------
-- Table structure for ai_teach_info
-- ----------------------------
DROP TABLE IF EXISTS `ai_teach_info`;
CREATE TABLE `ai_teach_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT '课程资源ID',
  `course_objectives` varchar(500) NOT NULL COMMENT '课程目标',
  `course_design_requirements` varchar(500) DEFAULT NULL COMMENT '课程设计要求',
  `examining_knowledge_points` varchar(1000) DEFAULT NULL COMMENT '考察知识点',
  `single_choice_question_num` int(11) DEFAULT NULL COMMENT '单选题数量',
  `judging_question_num` int(11) DEFAULT NULL COMMENT '判断题数量',
  `essay_question_num` int(11) DEFAULT NULL COMMENT '问答题数量',
  `choice_question_accuracy` decimal(10,3) DEFAULT NULL COMMENT '选择题正确率',
  `text_question_matching_degree` decimal(10,3) DEFAULT NULL COMMENT '问答题匹配度',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-教学课程-基本信息表;';

-- ----------------------------
-- Table structure for ai_teach_my_choice_answer
-- ----------------------------
DROP TABLE IF EXISTS `ai_teach_my_choice_answer`;
CREATE TABLE `ai_teach_my_choice_answer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_emp_id` bigint(20) NOT NULL COMMENT '培训任务员工ID',
  `question_id` bigint(20) NOT NULL COMMENT '考核题目ID',
  `option_ids` varchar(20) NOT NULL COMMENT '选择题选项ID（用,隔开）',
  `is_right` tinyint(1) NOT NULL COMMENT '是否正确',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-教学课程-我的选择问题答案表;';

-- ----------------------------
-- Table structure for ai_teach_my_text_answer
-- ----------------------------
DROP TABLE IF EXISTS `ai_teach_my_text_answer`;
CREATE TABLE `ai_teach_my_text_answer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_emp_id` bigint(20) NOT NULL COMMENT '培训任务员工ID',
  `question_id` bigint(20) NOT NULL COMMENT '考核题目ID',
  `answer_content` varchar(500) DEFAULT NULL COMMENT '文本题答案内容',
  `ai_matching_degree` decimal(10,3) DEFAULT NULL COMMENT '匹配度(来源AI)',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-教学课程-我的文本问题答案表;';

-- ----------------------------
-- Table structure for ai_teach_test_question
-- ----------------------------
DROP TABLE IF EXISTS `ai_teach_test_question`;
CREATE TABLE `ai_teach_test_question` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT '课程资源ID',
  `ai_question_id` varchar(40) DEFAULT NULL COMMENT 'AI题目ID（AI模型生成）',
  `question_name` varchar(255) NOT NULL COMMENT '题目名称',
  `question_type` varchar(10) NOT NULL COMMENT '题目类型;DXT：单选题，PDT：判断题，WDT：问答题',
  `question_order` int(11) DEFAULT NULL COMMENT '题目顺序',
  `question_source` varchar(255) DEFAULT NULL COMMENT '题目来源',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-教学课程-考核题目表;';

-- ----------------------------
-- Table structure for ai_teach_text_sample_answer
-- ----------------------------
DROP TABLE IF EXISTS `ai_teach_text_sample_answer`;
CREATE TABLE `ai_teach_text_sample_answer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT '课程资源ID',
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `sample_answer_content` varchar(500) DEFAULT NULL COMMENT '参考答案',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-教学课程-考核题目-文本题参考答案表;';

-- ----------------------------
-- Table structure for ai_train_communicate_framework
-- ----------------------------
DROP TABLE IF EXISTS `ai_train_communicate_framework`;
CREATE TABLE `ai_train_communicate_framework` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT '课程资源ID',
  `communicate_subject` varchar(255) NOT NULL COMMENT '沟通主题',
  `communicate_objective` varchar(500) DEFAULT NULL COMMENT '沟通目标',
  `grading_criteria` varchar(500) DEFAULT NULL COMMENT '评分标准',
  `weight` decimal(10,3) NOT NULL COMMENT '评分百分比',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-AI陪练-沟通框架表;';

-- ----------------------------
-- Table structure for ai_train_dialogue_question
-- ----------------------------
DROP TABLE IF EXISTS `ai_train_dialogue_question`;
CREATE TABLE `ai_train_dialogue_question` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT '课程资源ID',
  `ai_question_id` varchar(40) DEFAULT NULL COMMENT 'AI题目ID（AI模型生成）',
  `ai_question_voice_file_url` varchar(255) DEFAULT NULL COMMENT 'AI题目语音文件URL',
  `question_name` varchar(255) NOT NULL COMMENT '题目名称',
  `sample_answer_content` varchar(500) DEFAULT NULL COMMENT '题目参考答案',
  `question_order` int(11) DEFAULT NULL COMMENT '题目顺序',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-AI陪练-对话问题表;';

-- ----------------------------
-- Table structure for ai_train_info
-- ----------------------------
DROP TABLE IF EXISTS `ai_train_info`;
CREATE TABLE `ai_train_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT '课程资源ID',
  `dialogue_scene` varchar(500) DEFAULT NULL COMMENT '对话场景',
  `dialogue_objective` varchar(500) DEFAULT NULL COMMENT '对话目标',
  `dialogue_question_num` int(11) DEFAULT NULL COMMENT '对话问题数量',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-AI陪练-基本信息表;';

-- ----------------------------
-- Table structure for ai_train_my_ability_analyse
-- ----------------------------
DROP TABLE IF EXISTS `ai_train_my_ability_analyse`;
CREATE TABLE `ai_train_my_ability_analyse` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_emp_id` bigint(20) NOT NULL COMMENT '培训任务员工ID',
  `ability_code` varchar(40) NOT NULL COMMENT '能力编码',
  `ability_name` varchar(20) NOT NULL COMMENT '能力名称',
  `ability_score` decimal(10,3) NOT NULL COMMENT '能力分数',
  `ability_weight` decimal(10,3) DEFAULT NULL COMMENT '得分权重',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-AI陪练-我的能力分析表;';

-- ----------------------------
-- Table structure for ai_train_my_ability_score
-- ----------------------------
DROP TABLE IF EXISTS `ai_train_my_ability_score`;
CREATE TABLE `ai_train_my_ability_score` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_emp_id` bigint(20) NOT NULL COMMENT '培训任务员工ID',
  `history_id` bigint(20) NOT NULL COMMENT '对话记录ID',
  `ability_analyse_id` bigint(20) NOT NULL COMMENT '能力分析ID',
  `ability_analyse_score` decimal(10,3) DEFAULT NULL COMMENT '能力分析得分',
  `ability_analyse_desc` varchar(500) DEFAULT NULL COMMENT '能力分析描述',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-AI陪练-我的能力分析得分表;';

-- ----------------------------
-- Table structure for ai_train_my_dialogue_history
-- ----------------------------
DROP TABLE IF EXISTS `ai_train_my_dialogue_history`;
CREATE TABLE `ai_train_my_dialogue_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_emp_id` bigint(20) NOT NULL COMMENT 'AI培训任务员工ID',
  `object_id` bigint(20) NOT NULL COMMENT 'AI培训对象ID',
  `message` varchar(500) NOT NULL COMMENT '消息内容',
  `send_time` datetime NOT NULL COMMENT '发送时间',
  `message_belong` varchar(10) NOT NULL COMMENT '消息所属;内容所属：AI-AI培训对象，EMP-AI培训员工',
  `voice_file_url` varchar(255) DEFAULT NULL COMMENT '语音文件url',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-AI陪练-我的对话历史记录表;';

-- ----------------------------
-- Table structure for ai_train_my_scoring_item
-- ----------------------------
DROP TABLE IF EXISTS `ai_train_my_scoring_item`;
CREATE TABLE `ai_train_my_scoring_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_emp_id` bigint(20) NOT NULL COMMENT '培训任务员工ID',
  `scoring_item_code` varchar(20) NOT NULL COMMENT '得分项编码',
  `scoring_item_name` varchar(20) NOT NULL COMMENT '得分项名称',
  `scoring_item_score` decimal(10,3) DEFAULT NULL COMMENT '得分项分数',
  `scoring_item_weight` decimal(10,3) DEFAULT NULL COMMENT '得分项权重',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-AI陪练-我的得分项表;';

-- ----------------------------
-- Table structure for ai_train_my_talk_speed_analyse
-- ----------------------------
DROP TABLE IF EXISTS `ai_train_my_talk_speed_analyse`;
CREATE TABLE `ai_train_my_talk_speed_analyse` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_emp_id` bigint(20) NOT NULL COMMENT '培训任务员工ID',
  `history_id` bigint(20) NOT NULL COMMENT '对话记录ID',
  `speed_code` varchar(32) NOT NULL COMMENT '语速编码',
  `speed_name` varchar(32) NOT NULL COMMENT '语速名称',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-AI陪练-我的语速分析表;';

-- ----------------------------
-- Table structure for ai_train_object
-- ----------------------------
DROP TABLE IF EXISTS `ai_train_object`;
CREATE TABLE `ai_train_object` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT '课程资源ID',
  `object_avatar_url` varchar(255) DEFAULT NULL COMMENT '陪练对象头像URL',
  `object_name` varchar(80) NOT NULL COMMENT '陪练对象名称',
  `object_background_info` varchar(500) DEFAULT NULL COMMENT '陪练对象背景信息',
  `object_gender` varchar(8) DEFAULT NULL COMMENT '陪练对象性别;male：男，female：女，unknown：未知',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-AI陪练-对象表;';

-- ----------------------------
-- Table structure for ai_train_object_tag
-- ----------------------------
DROP TABLE IF EXISTS `ai_train_object_tag`;
CREATE TABLE `ai_train_object_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT '课程资源ID',
  `object_id` bigint(20) NOT NULL COMMENT '陪练对象ID',
  `tag_name` varchar(20) NOT NULL COMMENT '陪练对象标签名称',
  `tag_code` varchar(10) DEFAULT NULL COMMENT '陪练对象标签编码',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='课程资源-AI陪练-对象标签表;';

-- ----------------------------
-- Table structure for ai_interview_category
-- ----------------------------
DROP TABLE IF EXISTS `ai_interview_category`;
CREATE TABLE `ai_interview_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category_name` varchar(80) NOT NULL COMMENT '类别名称',
  `parent_id` bigint(20) NOT NULL COMMENT '类别父级ID，顶级节点值为0',
  `category_path` varchar(32) DEFAULT NULL COMMENT '类别全路径，用,隔开',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ai_interview_category_parent_id_IDX` (`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='AI访谈-类别表;';

-- ----------------------------
-- Table structure for ai_interview_info
-- ----------------------------
DROP TABLE IF EXISTS `ai_interview_info`;
CREATE TABLE `ai_interview_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category_id` bigint(20) NOT NULL COMMENT '类别ID',
  `course_name` varchar(80) NOT NULL COMMENT '访谈名称',
  `course_type` varchar(10) NOT NULL COMMENT '访谈分类;AIFT：AI访谈',
  `course_cover_url` varchar(255) DEFAULT NULL COMMENT '封面图链接',
  `course_duration` decimal(10,3) DEFAULT NULL COMMENT '访谈时长（minute）',
  `course_status` varchar(10) NOT NULL COMMENT '状态;CG：草稿，TJ：提交',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `dialogue_scene` varchar(200) DEFAULT NULL COMMENT '访谈场景',
  `dialogue_objective` varchar(500) DEFAULT NULL COMMENT '访谈目标',
  `dialogue_question_num` int(11) DEFAULT NULL COMMENT '对话问题数量',
  `dialogue_remark` varchar(200) DEFAULT NULL COMMENT '访谈注意事项',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ai_interview_info_course_status_IDX` (`course_status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='AI访谈助手-基本信息表;';

-- ----------------------------
-- Table structure for ai_interview_dialogue_question
-- ----------------------------
DROP TABLE IF EXISTS `ai_interview_dialogue_question`;
CREATE TABLE `ai_interview_dialogue_question` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT 'AI访谈助手ID',
  `ai_question_voice_file_url` varchar(255) DEFAULT NULL COMMENT 'AI题目语音文件URL',
  `interview_question` varchar(255) NOT NULL COMMENT '沟通主题',
  `interview_objective` varchar(500) DEFAULT NULL COMMENT '沟通目标',
  `question_order` int(11) DEFAULT NULL COMMENT '题目顺序',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `ai_interview_dialogue_question_resource_id_IDX` (`resource_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='AI访谈-对话问题表;';

-- ----------------------------
-- Table structure for ai_interview_object
-- ----------------------------
DROP TABLE IF EXISTS `ai_interview_object`;
CREATE TABLE `ai_interview_object` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT 'AI访谈助手ID',
  `object_avatar_type` varchar(2) DEFAULT NULL COMMENT '头像类型，01默认，02自定义',
  `object_avatar_url` varchar(255) DEFAULT NULL COMMENT '访谈对象头像URL',
  `object_name` varchar(20) NOT NULL COMMENT '访谈对象名称',
  `object_background_info` varchar(200) DEFAULT NULL COMMENT '角色设定',
  `object_gender` varchar(8) DEFAULT NULL COMMENT '访谈对象性别;male：男，female：女，unknown：未知',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `ai_interview_object_resource_id_IDX` (`resource_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='AI访谈-对象表;';

-- ----------------------------
-- Table structure for ai_interview_resource_attach
-- ----------------------------
DROP TABLE IF EXISTS `ai_interview_resource_attach`;
CREATE TABLE `ai_interview_resource_attach` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT 'AI访谈助手ID',
  `file_id` varchar(32) NOT NULL COMMENT '文件ID',
  `file_name` varchar(80) NOT NULL COMMENT '文件名称',
  `file_type` varchar(10) NOT NULL COMMENT '文件类型',
  `file_md5` varchar(40) DEFAULT NULL COMMENT '文件md5值',
  `file_url` varchar(1024) DEFAULT NULL COMMENT '文件URL',
  `file_size` varchar(20) DEFAULT NULL COMMENT '文件大小',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ai_interview_resource_attach_resource_id_IDX` (`resource_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='AI访谈-附件表;';

-- ----------------------------
-- Table structure for ai_interview_task_info
-- ----------------------------
DROP TABLE IF EXISTS `ai_interview_task_info`;
CREATE TABLE `ai_interview_task_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT 'AI访谈助手ID',
  `task_name` varchar(80) NOT NULL COMMENT '访谈任务名称',
  `start_date_time` datetime DEFAULT NULL COMMENT '访谈开始时间',
  `end_date_time` datetime DEFAULT NULL COMMENT '访谈结束时间',
  `task_cover_url` varchar(255) DEFAULT NULL COMMENT '访谈封面URL',
  `task_status` varchar(10) DEFAULT NULL COMMENT '访谈任务状态;CG：草稿，JXZ：进行中，YWC：已完成，YJS：已结束',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ai_interview_task_info_resource_id_IDX` (`resource_id`) USING BTREE,
  KEY `ai_interview_task_info_task_status_IDX` (`task_status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='AI访谈任务-基本信息表;';

-- ----------------------------
-- Table structure for ai_interview_task_emp
-- ----------------------------
DROP TABLE IF EXISTS `ai_interview_task_emp`;
CREATE TABLE `ai_interview_task_emp` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT 'AI访谈助手ID',
  `task_id` bigint(20) NOT NULL COMMENT 'AI访谈任务ID',
  `emp_code` varchar(20) NOT NULL COMMENT '员工编号',
  `emp_name` varchar(40) NOT NULL COMMENT '员工名称',
  `emp_avatar_url` varchar(255) DEFAULT NULL COMMENT '员工头像',
  `org_code` varchar(20) DEFAULT NULL COMMENT '员工部门编码',
  `org_name` varchar(20) DEFAULT NULL COMMENT '员工部门名称',
  `position_code` varchar(20) DEFAULT NULL COMMENT '员工岗位编码',
  `position_name` varchar(20) DEFAULT NULL COMMENT '员工岗位名称',
  `status` varchar(10) DEFAULT NULL COMMENT '状态;WKS：未开始，JXZ：进行中，YWC：已完成，YJS：已结束',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `elapsed_time` int(10) DEFAULT NULL COMMENT '消耗时间（second）',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `gt_status` varchar(20) DEFAULT NULL COMMENT '沟通状态',
  `wcd_rate` varchar(10) DEFAULT NULL COMMENT '完成度',
  `hdjj_score` varchar(10) DEFAULT NULL COMMENT '回答积极度',
  `bdlj_score` varchar(10) DEFAULT NULL COMMENT '表达逻辑',
  `analyse01` varchar(200) DEFAULT NULL COMMENT '评价：总体',
  `analyse02` varchar(200) DEFAULT NULL COMMENT '访谈摘要',
  `analyse03` varchar(200) DEFAULT NULL COMMENT '后续关注点',
  `bg_status` varchar(2) DEFAULT NULL COMMENT '报告状态：01未生成，02生成中，03生成成功，04生成失败',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ai_interview_task_emp_resource_id_IDX` (`resource_id`) USING BTREE,
  KEY `ai_interview_task_emp_status_IDX` (`status`) USING BTREE,
  KEY `ai_interview_task_emp_task_id_IDX` (`task_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='AI访谈任务-员工表;';

-- ----------------------------
-- Table structure for ai_interview_my_dialogue_history
-- ----------------------------
DROP TABLE IF EXISTS `ai_interview_my_dialogue_history`;
CREATE TABLE `ai_interview_my_dialogue_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_emp_id` bigint(20) NOT NULL COMMENT 'AI访谈任务员工ID',
  `object_id` bigint(20) NOT NULL COMMENT 'AI访谈对象ID',
  `question_id` bigint(20) DEFAULT NULL COMMENT '所属问题ID',
  `question` varchar(200) DEFAULT NULL COMMENT '问题',
  `message` varchar(500) DEFAULT NULL COMMENT '消息内容',
  `ask_time` datetime DEFAULT NULL COMMENT '提问时间',
  `send_time` datetime DEFAULT NULL COMMENT '发送时间',
  `ai_voice_file_url` varchar(255) DEFAULT NULL COMMENT 'AI-语音文件url',
  `emp_voice_file_url` varchar(255) DEFAULT NULL COMMENT '员工-语音文件url',
  `deleted` tinyint(1) NOT NULL COMMENT '删除标识;0：未删除，1：已删除',
  `create_by` varchar(40) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(40) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` varchar(2) DEFAULT NULL COMMENT '答复状态，01通过，02不通过，03跳过',
  `match_rate` varchar(10) DEFAULT NULL COMMENT '匹配率',
  `analysis` varchar(200) DEFAULT NULL COMMENT '简要分析匹配/不匹配的原因',
  `revision` tinyint(1) DEFAULT 0 COMMENT '乐观锁',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ai_interview_my_dialogue_history_task_emp_id_IDX` (`task_emp_id`) USING BTREE,
  KEY `ai_interview_my_dialogue_history_question_id_IDX` (`question_id`) USING BTREE,
  KEY `ai_interview_my_dialogue_history_status_IDX` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='AI访谈-我的对话历史记录表;';

CREATE TABLE `ai_pic_deal` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `pic1` varchar(500) DEFAULT NULL COMMENT '图片外网地址',
  `pic2` varchar(255) DEFAULT NULL COMMENT '图片内网地址',
  `status` int(1) NOT NULL COMMENT '处理状态，1未处理，2处理成功，3处理失败',
  `num` int(1) DEFAULT NULL COMMENT '处理次数，',
  `create_time` varchar(20) DEFAULT NULL COMMENT '创建时间',
  `deal_time` varchar(20) DEFAULT NULL COMMENT '处理时间',
  `obj_id` bigint(10) DEFAULT NULL COMMENT '数据对象ID',
  `obj_type` varchar(10) DEFAULT NULL COMMENT '对象类型，aiMsg:ai的对话回复',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='图片处理';

SET FOREIGN_KEY_CHECKS = 1;
