
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for param_short_info
-- ----------------------------
DROP TABLE IF EXISTS `param_short_info`;
CREATE TABLE `param_short_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '短网址id',
  `shorter` varchar(10) NOT NULL COMMENT '短网址编码',
  `parameter` varchar(300) NOT NULL COMMENT '原始参数',
  `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用， 1-是, 0-否',
  `create_by` varchar(32) NOT NULL COMMENT '数据创建者id',
  `create_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '数据创建时间',
  `update_by` varchar(32) NOT NULL COMMENT '数据上一次修改人id',
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '数据上一次修改时间',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识符 true --> 已删除 | false --> 未删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `shorter` (`shorter`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='参数短码信息表';

-- ----------------------------
-- Table structure for t_email_send_attachment
-- ----------------------------
DROP TABLE IF EXISTS `t_email_send_attachment`;
CREATE TABLE `t_email_send_attachment` (
  `ID` decimal(19,0) NOT NULL COMMENT '主键',
  `EMAIL_ID` decimal(19,0) NOT NULL COMMENT '邮件日志id',
  `FILE_NAME` varchar(255) NOT NULL COMMENT '附件名称',
  `FILE_SIZE` decimal(19,0) NOT NULL COMMENT '附件大小',
  `STORAGE_ADDRESS` varchar(255) NOT NULL COMMENT '附件地址',
  `SUFFIX` varchar(20) DEFAULT NULL COMMENT '附件前缀',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_email_send_log
-- ----------------------------
DROP TABLE IF EXISTS `t_email_send_log`;
CREATE TABLE `t_email_send_log` (
  `ID` decimal(19,0) NOT NULL COMMENT '主键',
  `CONTENT` longtext DEFAULT NULL COMMENT '邮件内容',
  `COPY_TO` varchar(255) DEFAULT NULL COMMENT '抄送人',
  `RECEIVER` varchar(2000) NOT NULL COMMENT '接收人',
  `SUBJECT` varchar(255) NOT NULL COMMENT '邮件主题',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_file_info
-- ----------------------------
DROP TABLE IF EXISTS `t_file_info`;
CREATE TABLE `t_file_info` (
  `ID` decimal(30,0) NOT NULL COMMENT '主键',
  `ATTACH_NAME` varchar(255) NOT NULL COMMENT '文件名称',
  `ATTACH_TYPE` varchar(255) DEFAULT NULL COMMENT '文件类型',
  `FILE_GROUP_ID` varchar(255) DEFAULT NULL COMMENT '文件组唯一ID',
  `KEY` text DEFAULT NULL COMMENT '文件路径',
  `FILE_SIZE` varchar(255) DEFAULT NULL COMMENT '文件大小',
  `CREATED_DATE` datetime DEFAULT NULL COMMENT '创建时间',
  `CREATE_USER` varchar(16) DEFAULT NULL COMMENT '创建用户',
  `UPDATED_DATE` datetime DEFAULT NULL COMMENT '更新时间',
  `UPDATE_USER` varchar(16) DEFAULT NULL COMMENT '更新用户',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for url_short_info
-- ----------------------------
DROP TABLE IF EXISTS `url_short_info`;
CREATE TABLE `url_short_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '短网址id',
  `shorter` varchar(10) NOT NULL COMMENT '短网址编码',
  `url` varchar(300) NOT NULL COMMENT '原始网址',
  `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用， 1-是, 0-否',
  `create_by` varchar(32) NOT NULL COMMENT '数据创建者id',
  `create_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '数据创建时间',
  `update_by` varchar(32) NOT NULL COMMENT '数据上一次修改人id',
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '数据上一次修改时间',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识符 true --> 已删除 | false --> 未删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `shorter` (`shorter`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='短链接信息表';

SET FOREIGN_KEY_CHECKS = 1;
