dhr-oauth-service 网关服务
===============

当前最新版本： master（发布日期：2022-06-20）


项目介绍：
-----------------------------------
```
认证服务:   Oauth2.0 认证方式，颁发令牌，密码策略，修改密码，登录退出

1.已经和sap账号进行效验，sap有账号的，系统可以通过账号名（如：00000002）和默认密码123456登录，自动生成关联账号，后续可自行修改密码
2.账号安全密码策略,冻结账号，解锁账户
3.对接的外部系统，支持4种令牌发放方式：授权码模式,简化模式,密码模式,客户端模式
```

### 目录模块
```
├─com.deloitte.dhr.security.uaa
│  ├─common : 基础模块
│  ├─config : 核心配置
│  ├─controller : controller层
│  ├─dto : 简单实体
│  ├─exception : 异常处理
│  ├─ext : 扩展token信息
│  ├─feign : 调用其他系统的feign
│  ├─fliter : 密码策略过滤器
│  ├─handler : 兼容帮助模块
│  ├─mapper : mapper映射
│  ├─model : 数据模型
│  ├─service : 服务层
│  ├─util : 工具类


```

#####
备注
----


