package com.deloitte.dhr.security.uaa.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.List;

/**
 * User Dto
 *
 * @author: <PERSON>Yong
 * @version: 1.0
 * @date: 24/04/2022
 */
@Data
public class UserDto  {
    /**id */
    private String id;
    /**用户名 */
    private String username;
    /**密码 */
    private String password;
    /**新密码 */
    private String newPassword;
    /**全名 */
    private String fullname;
    /**电话号码 */
    private String mobile;
    /**邮件 */
    private String email;
    /**菜单权限 */
    private List<Object> menuList;
    /**数据权限 */
    private List<Object> dataAuthList;
    /**授权字符串 */
    private String authorizationString;
    /**员工编码 */
    private String employeeNumber;
    /**oa名称 */
    private String oaName;
    /**界面编码 */
    private String pageCode;
    /**验证码 */
    private String verifyCode;
    /**旧密码*/
    private String oldPassword;
    /**类型：邮件-email  短信-phone*/
    private String type;
    /**值，根据type不同，传不同值，比如为email的时候传邮箱，为phone的时候传电话号码*/
    private String value;
    /**
     * 岗位
     */
    private String position;

    /**
     * 部门
     */
    private String department;

}
