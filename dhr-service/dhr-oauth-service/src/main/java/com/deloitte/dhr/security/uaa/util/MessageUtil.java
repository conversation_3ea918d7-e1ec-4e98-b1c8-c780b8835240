package com.deloitte.dhr.security.uaa.util;

import cn.hutool.extra.spring.SpringUtil;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

import java.util.Locale;

/***
 * @Author: yinyu
 * @Date: 19/09/2022 17:06
 * @description:
 */
@Component
public class MessageUtil {

    public static String getText(String code,String language,Object... args){
        MessageSource messageSource=SpringUtil.getBean(MessageSource.class);
        return messageSource.getMessage(code,args,"",new Locale(language));
    }

    public static String getText(String code,Locale locale,Object... args){
        MessageSource messageSource=SpringUtil.getBean(MessageSource.class);
        return messageSource.getMessage(code,args,"",locale);
    }
}
