package com.deloitte.dhr.security.uaa.common;

/**
 * oauth公共常量
 *
 * @author: davihu
 * @version: 1.0
 * @date: 24/04/2022
 */
public class Constant {
    /**密钥*/
    public static final String OAUTH_SECRET ="123456";

    /**
     * 是否手机端
     */
    public static final String IV_MOBILE = "IV_MOBILE";


    public static final String IV_PC = "IV_PC";

    public static final String REDIS_KEY_USER_CACHE = "USER-CACHE:";

    public static final String LOGIN = "login";
    /**登陆名*/
    public static final String OAUTH_USER = "user_name";

    public static final String BEARER_TOKEN_TYPE = "Bearer";
    /**鉴权标识Authorization*/
    public static final String AUTHORIZE_TOKEN = "Authorization";
}
