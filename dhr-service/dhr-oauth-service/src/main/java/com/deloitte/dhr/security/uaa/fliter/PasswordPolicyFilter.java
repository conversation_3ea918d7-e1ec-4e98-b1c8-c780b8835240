package com.deloitte.dhr.security.uaa.fliter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 密码策略过滤器
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 24/04/2022
 */
@Slf4j
@Component
@WebFilter(urlPatterns = {"/oauth/token"},filterName = "PasswordPolicyFilter")
public class PasswordPolicyFilter extends OncePerRequestFilter {

    public static final String OAUTH_TOKEN_URI = "/oauth/token";

    private static final String CLIENT_CREDENTIALS="client_credentials";

    private static final String GRANT_TYPE="grant_type";

    /**
     * 密码策略过滤器
     *
     * @author: liyong
     * @version: 1.0
     * @date: 24/04/2022
     * @param httpServletRequest
     * @param httpServletResponse
     * @param filterChain
     */
    @Override
    protected void doFilterInternal(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, FilterChain filterChain) throws ServletException, IOException {
        filterChain.doFilter(httpServletRequest, httpServletResponse);
    }
}
