package com.deloitte.dhr.security.uaa.exception;

import lombok.Getter;

/**
 * 业务异常
 *
 * @author: <PERSON><PERSON><PERSON>
 * @version: 1.0
 * @date: 24/04/2022
 */
@Getter
public class BaseException extends RuntimeException {
    /**异常对应的错误类型*/
    private IErrorType IErrorType;

    /**默认是系统异常*/
    public BaseException() {
        this.IErrorType = PlatformIErrorType.SYSTEM_BUSY;
    }


    public BaseException(IErrorType IErrorType) {

        this.IErrorType = IErrorType;
    }


    public BaseException(IErrorType IErrorType, String... args) {
        IErrorType.setArgs(args);
        this.IErrorType = IErrorType;
    }

    public BaseException(String message, IErrorType IErrorType, String... args) {
        super(message);
        IErrorType.setArgs(args);
        this.IErrorType = IErrorType;
    }

    public BaseException(String message, Throwable cause, IErrorType IErrorType, String... args) {
        super(message, cause);
        IErrorType.setArgs(args);
        this.IErrorType = IErrorType;
    }
}
