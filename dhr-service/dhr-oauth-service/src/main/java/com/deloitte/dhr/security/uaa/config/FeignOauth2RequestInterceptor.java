package com.deloitte.dhr.security.uaa.config;

import com.deloitte.dhr.security.uaa.common.Constant;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationDetails;


/**
 * feign调用拦截适配
 *
 * @author: liyong
 * @version: 1.0
 * @date: 24/04/2022
 */
@Configuration
public class FeignOauth2RequestInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate requestTemplate) {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        if (authentication != null && authentication.getDetails() instanceof OAuth2AuthenticationDetails) {
            OAuth2AuthenticationDetails details = (OAuth2AuthenticationDetails) authentication.getDetails();
            requestTemplate.header(Constant.AUTHORIZE_TOKEN, String.format("%s %s", Constant.BEARER_TOKEN_TYPE, details.getTokenValue()));
        }
    }
}