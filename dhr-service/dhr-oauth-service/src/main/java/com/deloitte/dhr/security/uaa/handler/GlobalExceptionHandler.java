package com.deloitte.dhr.security.uaa.handler;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.security.uaa.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 *
 * @author: davihu
 * @version: 1.0
 * @date: 24/04/2022
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 异常返回统一格式
     */
    @ExceptionHandler(ServiceException.class)
    public ResponseVO ServiceException(ServiceException e) {
        log.error(e.getIErrorType().getMsg(), e);
        return ResponseVO.fail(e.getIErrorType().getMsg());
    }
}
