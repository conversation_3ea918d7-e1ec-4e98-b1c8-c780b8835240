package com.deloitte.dhr.security.uaa.util;

import com.deloitte.dhr.security.uaa.common.Constant;

/**
 * 获取账号登陆终端标识
 *
 * <AUTHOR>
 * @version: 1.0
 * @create 28/04/2022
 */

public class DriverUtil {


    public static String getLoginKey(String tag,String userName,String signature){
        return Constant.LOGIN+"-"+tag+userName+"-"+signature;

    }

    public static String getUserCacheKey(String tag,String userName,String signature){
        return Constant.REDIS_KEY_USER_CACHE+"-"+tag+userName+"-"+signature;
    }

}
