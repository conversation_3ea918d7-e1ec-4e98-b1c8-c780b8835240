package com.deloitte.dhr.security.uaa.service;

import com.alibaba.fastjson2.JSON;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.deloitte.dhr.security.uaa.common.Constant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * 自定义Oauth2登陆账号信息
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 24/04/2022
 */
@RefreshScope
@Repository
@Slf4j
public class UserService {

    /**
     * 从jwtString中解析用户
     *
     * @author: liyong
     * @version: 1.0
     * @date: 26/04/2022
     * @param jwtString
     * @return
     */
    public static String getUserNameByAuthorization(String jwtString) {

        String userName = "";
        try {
            Algorithm algorithm = Algorithm.HMAC256(Constant.OAUTH_SECRET);
            JWTVerifier verifier = JWT.require(algorithm).build();
            DecodedJWT jwt = verifier.verify(jwtString);
            Map<String, Claim> claims = jwt.getClaims();
            //取出用户身份信息
            String principal = claims.get("user_name").asString();
            userName = JSON.parseObject(principal).getString("username");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return userName;
    }



    /**
     * 从jwtString中解析用户
     *
     * @author: liyong
     * @version: 1.0
     * @date: 26/04/2022
     * @param jwtString
     * @return
     */
    public static String getSignatureByAuthorization(String jwtString) {

        String Signature = "";
        try {
            Algorithm algorithm = Algorithm.HMAC256(Constant.OAUTH_SECRET);
            JWTVerifier verifier = JWT.require(algorithm).build();
            DecodedJWT jwt = verifier.verify(jwtString);
            Signature = jwt.getSignature();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Signature;
    }

}
