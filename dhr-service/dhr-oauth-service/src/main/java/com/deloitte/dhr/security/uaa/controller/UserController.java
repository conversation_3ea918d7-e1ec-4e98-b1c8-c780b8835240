package com.deloitte.dhr.security.uaa.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.security.uaa.dto.UserDto;
import com.google.common.collect.Maps;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Map;

/**
 * 用户维护
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 24/04/2022
 */
@RestController
@RequestMapping("/user")
public class UserController {
    /**
     * 通过用户id获取token，针对于第三方集成专用
     * @return
     */
    @GetMapping(value = "/get/token")
    public ResponseVO<String> getToken(){
        UserDto userDto = new UserDto();
        //获取随机数工号
        long timestamp = System.currentTimeMillis();
        String randomCode = String.valueOf(timestamp);
        userDto.setFullname("AI用户");
        userDto.setUsername(randomCode);
        userDto.setEmployeeNumber(randomCode);

        Map<String, Object> claims = Maps.newLinkedHashMap();
        claims.put("user_name",JSON.toJSONString(userDto));
        String token = Jwts.builder()
                .setHeaderParam("typ", "JWT")
                .setHeaderParam("alg", "HS256")
                .setSubject("token")
                .setIssuedAt(new Date())
                .setExpiration(DateUtil.offsetDay(new Date(),1))
                .addClaims(claims)
                // HS256算法实际上就是MD5加盐值，此时APP_SECRET就代表盐值
                .signWith(SignatureAlgorithm.HS256, Base64.encode("123456"))
                .compact();
        return ResponseVO.success(token);
    }
}

