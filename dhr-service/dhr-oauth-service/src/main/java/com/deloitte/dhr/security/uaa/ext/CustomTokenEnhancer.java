package com.deloitte.dhr.security.uaa.ext;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.security.uaa.dto.UserDto;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;

import java.util.HashMap;
import java.util.Map;

/**
 * Token加额外的字段信息
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 24/04/2022
 */
public class CustomTokenEnhancer implements TokenEnhancer {
    @Override
    public OAuth2AccessToken enhance(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
        Map<String, Object> additionalInfo = new HashMap<>();

        Object userObject=authentication.getPrincipal();

        UserDto userDto;

        if(userObject instanceof UserDetails){
            UserDetails userDetail = (UserDetails) authentication.getPrincipal();
            String userString = userDetail.getUsername();
            userDto= JSON.parseObject(userString,UserDto.class);
        }else{
            userDto = JSONObject.parseObject(userObject.toString(), UserDto.class);
        }

        additionalInfo.put("staff_no", userDto.getEmployeeNumber());
        additionalInfo.put("oaName", userDto.getOaName());
        additionalInfo.put("user_name", userDto.getUsername());
        additionalInfo.put("pageCode", userDto.getPageCode());

        ((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(additionalInfo);

        return accessToken;
    }
}
