package com.deloitte.dhr.security.uaa.exception;


import lombok.Getter;

/**
 * 异常码
 *
 * @author: <PERSON><PERSON><PERSON>
 * @version: 1.0
 * @date: 24/04/2022
 */
@Getter
public enum PlatformIErrorType implements IErrorType {
    /** 系统异常*/
    SYSTEM_BUSY("1000-000-0001", "platform.basic.error.systemBusy"),
    /**用户不存在*/
    USER_NOT_FOUND("1000-000-0002", "该用户不存在!"),
    /**用户不存在*/
    USER_MORE_FOUND("1000-000-0006", "该邮箱或手机号存在多个用户使用，无法进行该操作"),
    /**发送邮件失败*/
    EMAIL_SEND_FAIL("1000-000-0003", "发送邮件失败!"),
    /**发送短信失败*/
    SMS_SEND_FAIL("1000-000-0004", "发送短信失败!"),
    /**忘记密码修改类型不能为空*/
    FORGOT_PASSWORD_NOT_ALLOW_NULL("1000-000-0005", "忘记密码修改类型不能为空!");
    /** 错误类型码*/
    private String code;
    /**错误类型描述信息 */
    private String msg;


    private String[] args;

    PlatformIErrorType(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String[] getArgs() {
        return this.args;
    }

    @Override
    public void setArgs(String... args) {
        this.args = args;
    }

}
