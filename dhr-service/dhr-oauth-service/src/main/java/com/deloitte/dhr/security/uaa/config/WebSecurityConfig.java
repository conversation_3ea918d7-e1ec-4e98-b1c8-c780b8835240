package com.deloitte.dhr.security.uaa.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 安全配置
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 24/04/2022
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(securedEnabled = true, prePostEnabled = true)
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {


    /**
     * 认证管理器
     *
     * @return
     * @throws Exception
     */
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    /**
     * 密码编码器
     *
     * @return
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 安全拦截机制（最重要）
     *
     * @param http
     * @throws Exception
     */
    @Override
    protected void configure(HttpSecurity http) throws Exception {

        http.csrf().disable()
                // 必须配置，不然OAuth2的http配置不生效----不明觉厉
                .requestMatchers()
                .antMatchers("/auth/login", "/auth/authorize", "/oauth/authorize")
                .and()
                .authorizeRequests()
                // 自定义页面或处理url是，如果不配置全局允许，浏览器会提示服务器将页面转发多次
                .antMatchers("/auth/login", "/auth/authorize")
                .permitAll()
                .antMatchers("/img/**")
                .permitAll()
                .antMatchers("/logout")
                .permitAll();

        http
                .formLogin()
                .loginPage("/auth/login") //自定义的登录页面 **重要**
                .loginProcessingUrl("/login") //原始的处理登录的URL,保持和base-login.html的form表单的action一致 **重要**
                .permitAll() //放开 **重要**
                .and()
                .requestMatchers().antMatchers("/oauth/**", "/login/**", "/logout/**")// **重要**
                .and()
                .authorizeRequests()
                .antMatchers(
                        "/logout/**", "/validToken", "/user/**").permitAll()//一些需要放开的URL
                .anyRequest().authenticated()
                .and().headers().frameOptions().disable()
                .and().csrf().disable();

        http.csrf().disable();
        http.httpBasic();//新增login form支持用户登录及授权,不然会报错User must be authenticated with Spring Security before authorization can be completed.
    }

}
