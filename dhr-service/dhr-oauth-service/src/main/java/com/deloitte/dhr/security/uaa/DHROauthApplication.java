package com.deloitte.dhr.security.uaa;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * Uaa 启动类
 *
 * @author: <PERSON><PERSON><PERSON>
 * @version: 1.0
 * @date: 24/04/2022
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.deloitte.dhr.security.uaa.feign"})
public class DHROauthApplication {
    public static void main(String[] args) {
        SpringApplication.run(DHROauthApplication.class, args);
    }
}
