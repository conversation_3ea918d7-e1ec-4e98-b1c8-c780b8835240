package com.deloitte.dhr.security.uaa.exception;

/**
 * 业务服务异常
 *
 * @author: <PERSON><PERSON><PERSON>
 * @version: 1.0
 * @date: 24/04/2022
 */
public class ServiceException extends BaseException {

    public ServiceException(IErrorType IErrorType) {
        super(IErrorType);
    }

    public ServiceException(IErrorType IErrorType, String... args) {
        super(IErrorType, args);
    }

    public ServiceException(String message, IErrorType IErrorType, String... args) {
        super(message, IErrorType, args);
    }

    public ServiceException(String message, Throwable cause, IErrorType IErrorType, String... args) {
        super(message, cause, IErrorType, args);
    }


}
