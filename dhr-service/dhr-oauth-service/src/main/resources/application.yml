# Nacos配置中心连接配置
nacos:
  server-addr: ${config_nacos_serveraddr:172.16.5.16}
  port: ${config_nacos_port:8848}
  namespace: ${config_nacos_namespace:bcabaf9e-e845-4cea-8f2f-381026ee0c56}
  username: ${config_nacos_username:nacos}
  password: ${config_nacos_password:nacos}

# Spring框架配置
spring:
  profiles:
    active: ${config_profile:dev}
  application:
    name: dhr-oauth-service
  main:
    allow-bean-definition-overriding: true   # 允许Spring容器中Bean名称重复时进行覆盖（多配置或Starter场景下常用）
    allow-circular-references: true          # 允许Bean之间存在循环依赖（部分老项目或特殊依赖场景下需要）

  # MVC配置 - Swagger兼容性配置
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  # Thymeleaf模板引擎配置
  thymeleaf:
    cache: false
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html

  # 国际化配置
  messages:
    basename: i18n.message
    encoding: UTF-8

  # Nacos服务发现和配置管理
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.server-addr}:${nacos.port}
        namespace: ${nacos.namespace}
        group: ${spring.profiles.active}
        username: ${nacos.username}
        password: ${nacos.password}
      config:
        file-extension: yaml
        server-addr: ${nacos.server-addr}:${nacos.port}
        group: ${spring.profiles.active}
        enabled: ${config.enable:true}
        namespace: ${nacos.namespace}
        username: ${nacos.username}
        password: ${nacos.password}

  # 配置导入
  config:
    import:
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
# 服务端口配置
server:
  port: ${config_server_port:9006}
#  servlet:
#    context-path: ${config_context_path:/dhr/dev/oa}

# 加密配置
encrypt:
  key-store:
    location: classpath:mytest.jks
    secret: mypass
    alias: mytest

# 自定义配置
custom:
  remote:
    user-url: http://localhost:8420/bs/datacenter/dc-employee/list/queryNoAuthEmps

# 登出配置
logout:
  url: https://psc.deloitte.com.cn/dhr/demo/web/
  app-url: https://psc.deloitte.com.cn/dhr/demo/app

# 日志配置
logging:
  file:
    name: logs/${spring.application.name}/info/log_info.log
  level:
    root: info  # 输出日志级别

# DHR配置
dhr:
  swagger:
    enabled: true
    docket:
      basic:
        title: 授权中心
        base-package: com.deloitte.dhr.security

