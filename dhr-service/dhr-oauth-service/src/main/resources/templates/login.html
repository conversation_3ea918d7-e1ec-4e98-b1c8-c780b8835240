<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>登录</title>
</head>

<style>
    html,body{
        width: 100%;
        height: 100%;
        padding: 0;
        margin: 0;
        overflow: hidden;
    }
    .login-container {
        width: 100%;
        height: 100vh;
        display: flex;
        box-sizing: border-box;
    }
    .login-container-banner {
        width: 40%;
        background: #86bc25;
        position: relative;
    }
    .login-container-banner img {
        width: 85%;
        display: block;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    .form-container{
        width: 60%;
        position: relative;
    }
    .form-container .logo{
        position: absolute;
        top: 30px;
        left: 40px;
    }
    .form-signin{
        position: absolute;
        top: 50%;
        left: 50%;
        width: 360px;
        transform: translate(-50%, -50%);
    }
    .form-signin-heading{
        height: 42px;
        line-height: 42px;
        font-size: 32px;
        color: #86bc25;
        font-weight: bold;
        margin-bottom: 40px;
        margin-top: 0;
        display: block;
    }
    .from-item{
        margin-bottom: 40px;
        display: flex;
        flex-direction: column;
    }
    .sr-only{
        font-size: 20px;
        color: #333;
        font-weight: 600;
        padding-bottom: 16px;
    }
    .form-control{
        position: relative;
        font-size: 14px;
        color: #333;
        letter-spacing: 0;
        background: #fafafa;
        border: 2px solid #fafafa;
        border-radius: 20px;
        padding:0 20px;
        box-sizing: border-box;
        display: inline-block;
        height: 40px;
        line-height: 40px;
        outline: none;
    }
    .form-control:hover{
        border: 2px solid #86bc25;
    }
    .form-control::placeholder{
        color: #ccc;
    }
    .btn-sub{
        display: inline-block;
        width: 140px;
        height: 38px;
        border-radius: 20px;
        color: #fff;
        font-weight: 400;
        background: #86bc25;
        border: 0;
        cursor: pointer;
        box-sizing: border-box;
        outline: none;
        margin: 0;
        transition: 0.1s;
        font-size: 14px;
        oauthUser-select: none;
    }
</style>
<body>
<div class="login-container">
    <div class="login-container-banner">
        <img class="logo" th:src="@{/img/banner-log.png}"/>
    </div>
    <div class="form-container">
        <img class="logo" th:src="@{/img/logo.svg}"/>
        <form class="form-signin" method="post" th:action="@{/login}">
            <h2 class="form-signin-heading">用户登录</h2>
            <p th:if="${param.logout}" class="bg-warning">你已注销</p>
            <p th:if="${param.error}" class="bg-danger">用户名或密码错误</p>
            <div class="from-item">
                <label for="username" class="sr-only">用户名</label>
                <input type="text" id="username" name="username" class="form-control" placeholder="用户名" required autofocus>
            </div>
            <div class="from-item">
                <label for="password" class="sr-only">密码</label>
                <input type="password" id="password" name="password" class="form-control" placeholder="密码" required>
            </div>
            <button class="btn-sub" type="submit">登录</button>
        </form>
    </div>
</div>
</body>
</html>