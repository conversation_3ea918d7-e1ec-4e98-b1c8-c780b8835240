dhr-ai-service AI员工伙伴
===============

工程介绍：
-----------------------------------

dhr AI陪练 模块服务,功能包括:课程资源、培训任务、我的培训、教学课程、AI陪练、文生图、翻译助手、学习助手、会议纪要、阅读理解、通用助手

dhr-ai-api：
公共对外接口

dhr-ai-provider：
公共服务提供者

## 数据库脚本

```
├─doc: 核心文档
│  ├─data: 工程数据库文件
│  ├─db: 数据库设计
│  ├─table: 初始化脚本
```

## 如何开始

1. 安装maven依赖

```
maven install
```

2. 启动本地服务运行开发环境

```
DHRAiApplication run
```

### 框架

| 框架                                                                                          | 说明               | 版本         | 备注 |
|---------------------------------------------------------------------------------------------|------------------|------------|----|
| [Spring Boot](https://spring.io/projects/spring-boot)                                       | 应用开发框架           | 2.3.5      |    |
| [Spring cloud](https://spring.io/projects/spring-cloud)                                     | 应用开发框架           | Hoxton.SR8 |
| [MySQL](https://www.mysql.com/cn/)                                                          | 数据库服务器           | 5.7 / 8.0+ |    |
| [Druid](https://github.com/alibaba/druid)                                                   | JDBC 连接池、监控组件    | 1.2.8      |    |
| [MyBatis Plus](https://mp.baomidou.com/)                                                    | MyBatis 增强工具包    | 3.4.1      |    |
| [Redis](https://redis.io/)                                                                  | key-value 数据库    | 5.0 / 6.0  |    |
| [Spring MVC](https://github.com/spring-projects/spring-framework/tree/master/spring-webmvc) | MVC 框架           | 5.3.24     |    |
| [Spring Security](https://github.com/spring-projects/spring-security)                       | Spring 安全框架      | 5.3.4      |    |
| [Hibernate Validator](https://github.com/hibernate/hibernate-validator)                     | 参数校验组件           | 6.1.5      |    | |
| [Quartz](https://github.com/quartz-scheduler)                                               | 任务调度组件           | 2.3.2      |    |
| [Knife4j](https://gitee.com/xiaoym/knife4j)                                                 | Swagger 增强 UI 实现 | 2.0.1      |    |
| [Spring Boot Admin](https://github.com/codecentric/spring-boot-admin)                       | Spring Boot 监控平台 | 2.3.0      |    |
| [Lombok](https://projectlombok.org/)                                                        | 消除冗长的 Java 代码    | 1.18.12    |    |
| [JUnit](https://junit.org/junit5/)                                                          | Java 单元测试框架      | 4.13       | -  |

## 配置环境

#### 开发环境

```
配置namespace开发环境
#  3cca2e0a-f8d4-41e5-8dce-64b4a7518415
application-dev.yml
```

#### 测试环境

```
配置namespace测试环境
#  bcabaf9e-e845-4cea-8f2f-381026ee0c56
application-test.yml
```

#### demo环境

```
配置namespace测试环境
application-demo.yml
```

### 目录模块

```
├── dhr-ai-api
│   └── pom.xml
├── dhr-ai-provider
│   ├── pom.xml
│   ├── src.main.java.com.deloitte.dhr.ai
│   │       │                           ├── client                      webflux client
│   │       │                           ├── config                      配置
│   │       │                           ├── constant                    常量
│   │       │                           ├── enums                       枚举
│   │       │                           ├── module
│   │       │                           │        ├── ai                 调用AI相关业务
│   │       │                           │        ├── chat               chat相关业务
│   │       │                           │        ├── coach              陪练相关业务
│   │       │                           │        ├── digitalhuman       数字人相关业务
│   │       │                           │        ├── sr                 语音相关业务
│   │       │                           └── utils                       工具
│   │       └── resources
│   │           ├── i18n                                                国际化
│   │           └── mappers
│   │               ├── chat
│   │               └── coach
│   │                   ├── category
│   │                   ├── task
│   │                   ├── teach
│   │                   └── train
```
----

## 调试地址

http://127.0.0.1:9033/doc.html

## DHR 开发环境演示

https://pscdev.deloitte.com.cn/dhr/dev/web/

## DHR开发人员必读

开发规范相关文档位于docs文件夹

