<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.chat.mapper.AiChatDialogueMapper">

	<sql id="AiChatDialogueColumns">
		a.id AS "id",
		a.ai_group AS "aiGroup",
		a.dialogue_title AS "dialogueTitle",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
    </sql>


	<select id="getList" resultType="com.deloitte.dhr.ai.module.chat.pojo.AiChatDialogueListResponse">
		select
			t1.id AS "id",
			t1.ai_group AS "aiGroup",
			t1.dialogue_title AS "dialogueTitle",
			if(t1.update_time is null, t1.create_time, t1.update_time) as "time"
		from
            ai_chat_dialogue t1
		where
			t1.deleted = 0
		  and t1.ai_group = #{aiGroup}
		  and t1.create_by = #{loginUserCode}
		order by
			`time` desc
	</select>

</mapper>