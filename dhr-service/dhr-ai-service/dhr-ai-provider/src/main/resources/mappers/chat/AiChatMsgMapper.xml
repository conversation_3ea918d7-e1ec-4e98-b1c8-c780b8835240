<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.chat.mapper.AiChatMsgMapper">

	<sql id="AiChatMsgColumns">
		a.id AS "id",
		a.dialogue_id AS "dialogueId",
		a.conversation_id AS "conversationId",
		a.ai_type AS "aiType",
		a.ai_sub_type AS "aiSubType",
		a.role AS "role",
		a.send_time AS "sendTime",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
    </sql>

	<select id="getChatList" resultType="com.deloitte.dhr.ai.module.chat.pojo.AiChatMsgListResponse">
		select
			t1.id AS "id",
			t1.dialogue_id AS "dialogueId",
			t1.conversation_id AS "conversationId",
			t1.ai_type AS "aiType",
			t1.ai_sub_type AS "aiSubType",
			t1.role AS "role",
			t1.send_time AS "sendTime"
		from
			ai_chat_msg t1
		where
			t1.deleted = 0
			and t1.dialogue_id = #{request.dialogueId}
		<if test="request.sendTime != null">
			and t1.send_time <![CDATA[<]]> #{request.sendTime}
		</if>
		<if test="request.aiType !=null and request.aiType != ''">
			and t1.ai_type = #{request.aiType}
		</if>
		<if test="request.aiSubType !=null and request.aiSubType != ''">
			and t1.ai_sub_type = #{request.aiSubType}
		</if>
		order by t1.send_time desc
		limit #{request.size}
	</select>

	<select id="chatHistoryIds" resultType="java.lang.Long">

		select acm.id as msgId
		from ai_chat_msg acm
		where acm.ai_type ="SSC" and acm.`role` = "USER"
		<if test="request.userId !=null and request.userId != ''">
			and acm.create_by = #{request.userId}
		</if>
		<if test="request.startDate !=null and request.startDate != ''">
			and acm.send_time &gt;= concat(#{request.startDate},' 00:00:00')
		</if>
		<if test="request.endDate !=null and request.endDate != ''">
			and acm.send_time &lt;= concat(#{request.endDate},' 23:59:59')
		</if>
		<if test="request.userMsg !=null and request.userMsg != ''">
			and acmc.content like concat('%',#{request.userMsg},'%')
		</if>
		<if test="request.aiMsg !=null and request.aiMsg != ''">
			INTERSECT
			select acm.msg_id as msgId
			from ai_chat_msg_content acmc left join ai_chat_msg acm on acmc.msg_id = acm.id
			where acm.`role` = "ASSISTANT" and acmc.content like concat('%',#{request.aiMsg},'%')
				<if test="request.userId !=null and request.userId != ''">
					and acm.create_by = #{request.userId}
				</if>
				<if test="request.startDate !=null and request.startDate != ''">
					and acm.send_time &gt;= concat(#{request.startDate},' 00:00:00')
				</if>
				<if test="request.endDate !=null and request.endDate != ''">
					and acm.send_time &lt;= concat(#{request.endDate},' 23:59:59')
				</if>
		</if>
		<if test="request.chatType !=null and request.chatType != ''">
			INTERSECT
			select acm.msg_id as msgId
			from ai_chat_msg_content acmc left join ai_chat_msg acm on acmc.msg_id = acm.id
			where acm.`role` = "ASSISTANT" and acmc.content_type ="INTENT" and acmc.content like concat('%',#{request.chatType},'%')
				<if test="request.userId !=null and request.userId != ''">
					and acm.create_by = #{request.userId}
				</if>
				<if test="request.startDate !=null and request.startDate != ''">
					and acm.send_time &gt;= concat(#{request.startDate},' 00:00:00')
				</if>
				<if test="request.endDate !=null and request.endDate != ''">
					and acm.send_time &lt;= concat(#{request.endDate},' 23:59:59')
				</if>
		</if>
		order by msgId DESC
	</select>

</mapper>