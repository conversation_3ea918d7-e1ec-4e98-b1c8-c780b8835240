<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.chat.mapper.AiChatMsgContentMapper">

	<sql id="AiChatMsgContentColumns">
		a.id AS "id",
		a.dialogue_id AS "dialogueId",
		a.msg_id AS "msgId",
		a.content_type AS "contentType",
		a.content AS "content",
		a.content_order AS "contentOrder",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
    </sql>

</mapper>