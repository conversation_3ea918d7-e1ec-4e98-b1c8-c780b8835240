<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.coach.teach.mapper.AiTeachInfoMapper">

	<sql id="AiTeachInfoColumns">
		a.id AS "id",
		a.resource_id AS "resourceId",
		a.course_objectives AS "courseObjectives",
		a.course_design_requirements AS "courseDesignRequirements",
		a.examining_knowledge_points AS "examiningKnowledgePoints",
		a.single_choice_question_num AS "singleChoiceQuestionNum",
		a.judging_question_num AS "judgingQuestionNum",
		a.essay_question_num AS "essayQuestionNum",
		a.choice_question_accuracy AS "choiceQuestionAccuracy",
		a.text_question_matching_degree AS "textQuestionMatchingDegree",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
	</sql>

	<select id="getMatchingDegree" resultType="java.math.BigDecimal">
		select
			a.text_question_matching_degree
		from
			ai_teach_info a
		where
			a.resource_id = #{resourceId} and a.deleted = 0
	</select>

</mapper>