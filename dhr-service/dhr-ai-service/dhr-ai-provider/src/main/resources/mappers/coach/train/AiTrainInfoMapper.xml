<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainInfoMapper">

    <sql id="AiTrainInfoColumns">
        a.id AS "id",
		a.resource_id AS "resourceId",
		a.dialogue_scene AS "dialogueScene",
		a.dialogue_objective AS "dialogueObjective",
		a.dialogue_question_num AS "dialogueQuestionNum",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
    </sql>

</mapper>