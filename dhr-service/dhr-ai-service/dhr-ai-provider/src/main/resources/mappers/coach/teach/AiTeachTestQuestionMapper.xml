<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.coach.teach.mapper.AiTeachTestQuestionMapper">

	<sql id="AiTeachTestQuestionColumns">
		a.id AS "id",
		a.resource_id AS "resourceId",
		a.ai_question_id AS "aiQuestionId",
		a.question_name AS "questionName",
		a.question_type AS "questionType",
		a.question_order AS "questionOrder",
		a.question_source AS "questionSource",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
	</sql>

    <select id="getTextQuestionByIds"
            resultType="com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachTextQuestionResponse">
		select
		 	t1.id as "questionId",
			t1.ai_question_id,
		 	t1.question_name,
		 	t2.sample_answer_content
		from
			ai_teach_test_question t1
		inner join
			ai_teach_text_sample_answer t2 on t1.id = t2.question_id and t1.deleted = 0 and t2.deleted = 0
		where
			<!--该类型来自于常量：AiCoachConstant.TeachQuestionType-->
		    t1.question_type = 'WDT'
			and t1.id in
			<foreach collection="ids" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
	</select>



</mapper>