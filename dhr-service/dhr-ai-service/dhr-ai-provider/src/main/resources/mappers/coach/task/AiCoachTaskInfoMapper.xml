<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.coach.task.mapper.AiCoachTaskInfoMapper">

    <sql id="TaskDetailColumns">
			t1.id,
			t1.resource_id,
			t1.task_name,
			t1.start_date_time,
			t1.end_date_time,
			t1.task_cover_url,
			t1.task_duration,
			t1.task_status,
			t3.category_id,
			t3.course_name,
			t3.course_type,
			t3.course_cover_url,
			count(t2.id)               AS totalEmpNum,
            <!--该状态来自于常量：AiCoachConstant.TaskEmpStatus-->
			sum(t2.`status` = 'YWC')   AS completedEmpNum,
			sum(t2.`status` != 'YWC' ) AS unCompletedEmpNum
    </sql>

	<update id="updateTaskStatus">
		update ai_coach_task_info
        <!--该状态来自于常量：AiCoachConstant.TaskStatus，AiCoachConstant.TaskEmpStatus-->
		set task_status = (select if(sum(status = 'YWC') = count(id), 'YWC', 'PXZ') from ai_coach_task_emp where task_id = #{id} and deleted = 0)
		where id = #{id}
	</update>

	<select id="getTeachTaskDetail"
            resultType="com.deloitte.dhr.ai.module.coach.task.pojo.AiTeachTaskDetailResponse">
		SELECT
		    <include refid="TaskDetailColumns"/>,
			t4.course_objectives,
			t4.examining_knowledge_points
		FROM
		    ai_coach_task_info t1
		INNER JOIN
			ai_coach_task_emp t2 on t1.id = t2.task_id AND t1.deleted = 0 AND t2.deleted = 0 AND t1.id = #{id}
		INNER JOIN
			<!--该状态来自于常量：AiCoachConstant.CourseResourceType-->
			ai_course_resource_info t3 ON t1.resource_id = t3.id AND t3.deleted = 0 AND t3.course_type = 'JXKC'
		INNER JOIN
		    ai_teach_info t4 ON t1.resource_id = t4.resource_id AND t4.deleted = 0
	</select>

	<select id="getTrainTaskDetail"
			resultType="com.deloitte.dhr.ai.module.coach.task.pojo.AiTrainTaskDetailResponse">
		SELECT
			<include refid="TaskDetailColumns"/>,
			t4.dialogue_scene,
			t4.dialogue_objective
		FROM
			ai_coach_task_info t1
		INNER JOIN
			ai_coach_task_emp t2 on t1.id = t2.task_id AND t1.deleted = 0 AND t2.deleted = 0 AND t1.id = #{id}
		INNER JOIN
            <!--该状态来自于常量：AiCoachConstant.CourseResourceType-->
			ai_course_resource_info t3 ON t1.resource_id = t3.id AND t3.deleted = 0 AND t3.course_type = 'AIPL'
		INNER JOIN
			ai_train_info t4 ON t1.resource_id = t4.resource_id AND t4.deleted = 0;
	</select>

	<select id="findListPage" resultType="com.deloitte.dhr.ai.module.coach.task.pojo.AiTaskResponse">
		SELECT
			<include refid="TaskDetailColumns"/>
		FROM
			ai_coach_task_info t1
		LEFT JOIN
			ai_coach_task_emp t2 on t1.id = t2.task_id and t2.deleted = 0
		INNER JOIN
			ai_course_resource_info t3 ON t1.resource_id = t3.id AND t1.deleted = 0 AND t3.deleted = 0
		<where>
			1=1
			<if test="param.taskName != null and param.taskName != ''">
				AND t1.task_name like concat(#{param.taskName}, '%')
			</if>
			<if test="param.taskStatus != null and param.taskStatus != ''">
				AND t1.task_status = #{param.taskStatus}
			</if>
		</where>
		group by t1.id
		order by t1.create_time desc
	</select>

	<select id="myTaskList" resultType="com.deloitte.dhr.ai.module.coach.task.pojo.AiMyTaskListResponse">
		SELECT
			t1.id as taskId,
			t1.resource_id,
			t1.task_name,
			t1.start_date_time,
			t1.end_date_time,
			t1.task_cover_url,
			t1.task_duration,
			t2.id as taskEmpId,
			t2.status,
			t3.category_id,
			t3.course_name,
			t3.course_type,
			t3.course_cover_url
		FROM
			ai_coach_task_info t1
		INNER JOIN
			ai_coach_task_emp t2 on t1.id = t2.task_id and t2.deleted = 0 and t2.emp_code = #{param.loginEmpCode}
		INNER JOIN
			ai_course_resource_info t3 ON t1.resource_id = t3.id AND t1.deleted = 0 AND t3.deleted = 0
		<where>
            <!--该状态来自于常量：AiCoachConstant.TaskStatus-->
			t1.task_status != 'CG'
			<if test="param.taskName != null and param.taskName != ''">
				AND t1.task_name like concat(#{param.taskName},'%')
			</if>
			<if test="param.taskStatus != null and param.taskStatus != ''">
				AND t1.task_status = #{param.taskStatus}
			</if>
		</where>
        <!--该状态来自于常量：AiCoachConstant.TaskEmpStatus-->
		order by (case t2.status when 'WKS' then 1 when 'JXZ' then 2 else 3 end) asc, t1.create_time desc
	</select>


</mapper>