<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.coach.teach.mapper.AiTeachMyChoiceAnswerMapper">

	<sql id="AiTeachMyChoiceAnswerColumns">
		a.id AS "id",
		a.task_emp_id AS "taskEmpId",
		a.question_id AS "questionId",
		a.option_ids AS "optionIds",
		a.is_right AS "isRight",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
	</sql>


</mapper>