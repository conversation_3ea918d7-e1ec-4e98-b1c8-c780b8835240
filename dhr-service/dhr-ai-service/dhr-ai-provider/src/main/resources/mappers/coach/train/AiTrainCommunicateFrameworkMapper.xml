<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainCommunicateFrameworkMapper">

	<sql id="AiTrainCommunicateFrameworkColumns">
		a.id AS "id",
		a.resource_id AS "resourceId",
		a.communicate_subject AS "communicateSubject",
		a.communicate_objective AS "communicateObjective",
		a.grading_criteria AS "gradingCriteria",
		a.weight AS "weight",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
	</sql>

</mapper>