<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainMyAbilityAnalyseMapper">

	<sql id="AiTrainMyAbilityScoreColumns">
		a.id AS "id",
		a.task_emp_id AS "taskEmpId",
		a.history_id AS "historyId",
		a.ability_analyse_id AS "abilityAnalyseId",
		a.ability_analyse_score AS "abilityAnalyseScore",
		a.ability_analyse_desc AS "abilityAnalyseDesc",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
	</sql>

    <select id="getMyDialogueAnalyse"
            resultType="com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyAbilityScoreResponse">
		select
		    t1.ability_analyse_score,
		    t1.ability_analyse_desc,
		    t2.ability_code,
		    t2.ability_name
		from
			ai_train_my_ability_score t1
		inner join
			ai_train_my_ability_analyse t2 on t1.ability_analyse_id = t2.id and t1.deleted = 0 and t2.deleted = 0
		where t1.history_id = #{historyId}
	</select>

</mapper>