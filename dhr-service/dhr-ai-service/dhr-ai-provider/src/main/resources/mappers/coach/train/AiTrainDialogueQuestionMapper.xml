<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainDialogueQuestionMapper">

	<sql id="AiTrainDialogueQuestionColumns">
		a.id AS "id",
		a.resource_id AS "resourceId",
		a.ai_question_id AS "aiQuestionId",
		a.ai_question_voice_file_url AS "aiQuestionVoiceFileUrl",
		a.question_name AS "questionName",
		a.sample_answer_content AS "sampleAnswerContent",
		a.question_order AS "questionOrder",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
	</sql>


	<select id="getAiQuestionList"
			resultType="com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainDialogueQuestionResponse">
		select
		    t1.id,
		    t1.resource_id,
			t1.question_name,
			t1.question_order,
			t1.sample_answer_content,
			t1.ai_question_id,
			t1.ai_question_voice_file_url,
			t2.object_avatar_url
		from
			ai_train_dialogue_question t1
				inner join
			ai_train_object t2 on t1.resource_id = t2.resource_id and t1.deleted = 0 and t2.deleted = 0
				inner join
			ai_coach_task_emp t3 on t1.resource_id = t3.resource_id and t3.deleted = 0
		where
			t3.id = #{taskEmpId}
		order by
			t1.question_order asc
	</select>


</mapper>