<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainMyDialogueHistoryMapper">

	<sql id="AiTrainMyDialogueHistoryColumns">
		a.id AS "id",
		a.task_emp_id AS "taskEmpId",
		a.object_id AS "objectId",
		a.message AS "message",
		a.send_time AS "sendTime",
		a.message_belong AS "messageBelong",
		a.voice_file_url AS "voiceFileUrl",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
	</sql>

    <select id="getMyDialogueHistory"
            resultType="com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyDialogueHistoryResponse">
		select
			t1.id AS "id",
			t1.message AS "message",
			t1.send_time AS "sendTime",
			t1.message_belong AS "messageBelong",
			t1.voice_file_url AS "voiceFileUrl",
			<!--该类型来自于常量：AiCoachConstant.DialogueMessageBelong-->
			if(t1.message_belong = 'EMP', t3.emp_avatar_url, t2.object_avatar_url) AS "avatarUrl"
		from
			ai_train_my_dialogue_history t1
		inner join
			ai_train_object t2 on t1.object_id = t2.id and t1.deleted = 0 and t2.deleted = 0
		inner join
			ai_coach_task_emp t3 on t1.task_emp_id = t3.id and t3.deleted = 0
		where
		    t1.task_emp_id = #{request.taskEmpId}
		<if test="request.sendTime !=null">
			and t1.send_time <![CDATA[<]]> #{request.sendTime}
		</if>
		order by t1.send_time desc, t1.id asc
		<if test="request.size !=null">
			limit #{request.size}
		</if>
	</select>

</mapper>