<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.coach.task.mapper.AiCoachTaskEmpMapper">

	<sql id="AiCoachTaskEmpColumns">
		a.id AS "id",
		a.resource_id AS "resourceId",
		a.task_id AS "taskId",
		a.emp_code AS "empCode",
		a.emp_name AS "empName",
		a.emp_avatar_url AS "empAvatarUrl",
		a.org_code AS "orgCode",
		a.org_name AS "orgName",
		a.position_code AS "positionCode",
		a.position_name AS "positionName",
		a.status AS "status",
		a.complete_time AS "completeTime",
		a.final_score AS "finalScore",
		a.score_result AS "scoreResult",
		a.elapsed_time AS "elapsedTime",
		a.video_file_url AS "videoFileUrl",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
	</sql>

	<select id="findListPage" resultType="com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskEmpResponse">
		SELECT
		<include refid="AiCoachTaskEmpColumns"/>
		from ai_coach_task_emp a
		<where>
			a.task_id = #{param.taskId}
			<if test="param.isCompleted">
				<!--该状态来自于常量：AiCoachConstant.TaskEmpStatus-->
				AND a.status = 'YWC'
			</if>
			<if test="!param.isCompleted">
				<!--该状态来自于常量：AiCoachConstant.TaskEmpStatus-->
				AND a.status != 'YWC'
			</if>
			<if test="param.empCode != null and param.empCode != ''">
				AND a.emp_code like concat(#{param.empCode}, '%')
			</if>
			<if test="param.empName != null and param.empName != ''">
				AND a.emp_name like concat(#{param.empName}, '%')
			</if>
			<if test="param.orgCode != null and param.orgCode != ''">
				AND a.org_code like concat(#{param.orgCode}, '%')
			</if>
			<if test="param.orgName != null and param.orgName != ''">
				AND a.org_name like concat(#{param.orgName}, '%')
			</if>
			<if test="param.positionCode != null and param.positionCode != ''">
				AND a.position_code like concat(#{param.positionCode}, '%')
			</if>
			<if test="param.positionName != null and param.positionName != ''">
				AND a.position_name like concat(#{param.positionName}, '%')
			</if>
			<if test="param.scoreResult != null">
				AND a.score_result like concat(#{param.scoreResult}, '%')
			</if>
			AND a.deleted = false
		</where>
			ORDER BY a.create_time desc
	</select>

	<select id="getMyObjectDetail"
			resultType="com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainObjectResponse">
		select
		    t2.id,
		    t2.resource_id,
			t2.object_avatar_url AS "objectAvatarUrl",
			t2.object_name AS "objectName",
			t2.object_background_info AS "objectBackgroundInfo",
			t2.object_gender AS "objectGender"
		from
			ai_coach_task_emp t1
		inner join
			ai_train_object	t2 on t1.resource_id = t2.resource_id and t1.deleted = 0 and t2.deleted = 0 and t1.id = #{id}
	</select>

    <select id="getMyTaskInfoDetail"
            resultType="com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskInfoResponse">
		select
			t2.id,
			t2.resource_id,
			t2.task_name,
			t2.start_date_time,
			t2.end_date_time,
			t2.task_cover_url,
			t2.task_duration,
			t2.task_status
		from
		    ai_coach_task_emp t1
		inner join
		    ai_coach_task_info t2 on t1.task_id = t2.id and t1.deleted = 0 and t2.deleted = 0 and t1.id = #{taskEmpId}
	</select>

	<select id="getDialogueQuestionNum" resultType="java.lang.Integer">
		select
			count(t2.id)
		from
			ai_coach_task_emp t1
				inner join
			ai_train_dialogue_question t2 on t1.id = #{taskEmpId} and t1.resource_id = t2.resource_id and t1.deleted = 0 and t2.deleted = 0
	</select>

</mapper>