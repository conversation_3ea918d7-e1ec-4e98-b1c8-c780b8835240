<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainObjectMapper">

	<sql id="AiTrainObjectColumns">
		a.id AS "id",
		a.resource_id AS "resourceId",
		a.object_avatar_url AS "objectAvatarUrl",
		a.object_name AS "objectName",
		a.object_background_info AS "objectBackgroundInfo",
		a.object_gender AS "objectGender",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
	</sql>

</mapper>