<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.coach.category.mapper.AiCourseCategoryMapper">

	<sql id="AiCourseCategoryColumns">
		a.id AS "id",
		a.category_name AS "categoryName",
		a.parent_id AS "parentId",
		a.category_path AS "categoryPath",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
	</sql>

    <select id="getCategoryPathById" resultType="java.lang.String">
		select
			t1.category_path
		from
			ai_course_category t1
		where
		    t1.id = #{id} and t1.deleted = 0
	</select>

    <select id="getCurrentAndAllChildId" resultType="java.lang.Long">
		select
		    t1.id
		from
			ai_course_category t1
		where
			t1.category_path like concat((select category_path from ai_course_category where id = #{id} and deleted = 0), '%')
	</select>

</mapper>