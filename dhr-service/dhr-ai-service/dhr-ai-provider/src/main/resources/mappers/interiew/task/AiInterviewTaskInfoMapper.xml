<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.interview.task.mapper.AiInterviewTaskInfoMapper">

	<sql id="TaskDetailColumns">
		t1.id,
		t1.resource_id,
		t1.task_name,
		t1.start_date_time,
		t1.end_date_time,
		t1.task_cover_url,
		t1.task_status,
		t3.category_id,
		t3.course_name,
		t3.course_type,
		t3.course_cover_url,
		t3.dialogue_scene,
		t3.dialogue_objective,
		count(t2.id)               AS totalEmpNum,
		<!--该状态来自于常量：AiInterviewConstant.TaskEmpStatus-->
		sum(t2.`status` = 'WKS')   AS wksEmpNum,
		sum(t2.`status` = 'JXZ' ) AS jxzEmpNum,
		sum(t2.`status` = 'YWC' ) AS ywcEmpNum
	</sql>

	<update id="updateTaskStatus">
		update ai_interview_task_info
		<!--该状态来自于常量：AiInterviewConstant.TaskStatus，AiInterviewConstant.TaskEmpStatus-->
		set task_status = (select if(sum(status = 'YWC') = count(id), 'YWC', 'JXZ') from ai_interview_task_emp where task_id = #{id} and deleted = 0)
		where id = #{id}
	</update>

	<select id="getInterviewTaskDetail"
			resultType="com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskDetailResponse">
		SELECT
		<include refid="TaskDetailColumns"/>

		FROM
		ai_interview_task_info t1
		INNER JOIN
		ai_interview_task_emp t2 on t1.id = t2.task_id AND t1.deleted = 0 AND t2.deleted = 0 AND t1.id = #{id}
		INNER JOIN
		<!--该状态来自于常量：AiInterviewConstant.InterviewResourceType-->
		ai_interview_info t3 ON t1.resource_id = t3.id AND t3.deleted = 0 AND t3.course_type = 'AIFT';
	</select>

	<select id="findListPage" resultType="com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskResponse">
		SELECT
		<include refid="TaskDetailColumns"/>
		FROM
		ai_interview_task_info t1
		LEFT JOIN
		ai_interview_task_emp t2 on t1.id = t2.task_id and t2.deleted = 0
		INNER JOIN
		ai_interview_info t3 ON t1.resource_id = t3.id AND t1.deleted = 0 AND t3.deleted = 0
		<where>
			1=1
			<if test="param.taskName != null and param.taskName != ''">
				AND t1.task_name like concat('%', #{param.taskName}, '%')
			</if>
			<if test="param.taskStatus != null and param.taskStatus != ''">
				AND t1.task_status = #{param.taskStatus}
			</if>
		</where>
		group by t1.id
		order by (case t1.task_status when 'WKS' then 1 when 'JXZ' then 2 when 'YWC' then 3 else 4 end) asc , t1.create_time desc
	</select>

	<select id="myTaskList" resultType="com.deloitte.dhr.ai.module.interview.task.pojo.AiMyInterviewTaskListResponse">
		SELECT
		t1.id as taskId,
		t1.resource_id,
		t1.task_name,
		t1.start_date_time,
		t1.end_date_time,
		t1.task_cover_url,
		t2.id as taskEmpId,
		IF(t1.`task_status` = 'YJS', 'YJS', t2.status) as status,
		t2.bg_status as bgStatus,
		t3.category_id,
		t3.course_name,
		t3.course_type,
		t3.course_cover_url
		FROM
		ai_interview_task_info t1
		INNER JOIN
		ai_interview_task_emp t2 on t1.id = t2.task_id and t2.deleted = 0 and t2.emp_code = #{param.loginEmpCode}
		INNER JOIN
		ai_interview_info t3 ON t1.resource_id = t3.id AND t1.deleted = 0 AND t3.deleted = 0
		<where>
			<!--该状态来自于常量：AiInterviewConstant.TaskStatus-->
			t1.task_status != 'CG'
			<if test="param.taskName != null and param.taskName != ''">
				AND t1.task_name like concat(#{param.taskName},'%')
			</if>
			<if test="param.taskStatus != null and param.taskStatus != ''">
				AND t2.status = #{param.taskStatus} and t1.task_status != 'YJS'
			</if>
		</where>
		<!--该状态来自于常量：AiInterviewConstant.TaskEmpStatus-->
		order by (case t1.task_status when 'JXZ' then 1 when 'YWC' then 2 else 3 end) asc ,(case t2.status when 'WKS' then 1 when 'JXZ' then 2 else 3 end) asc, t1.create_time desc
	</select>


	<select id="myTaskList2" resultType="com.deloitte.dhr.ai.module.interview.task.pojo.AiMyInterviewTaskListResponse">
		SELECT
		t1.id as taskId,
		t1.resource_id,
		t1.task_name,
		t1.start_date_time,
		t1.end_date_time,
		t1.task_cover_url,
		t2.id as taskEmpId,
		IF(t1.`task_status` = 'YJS', 'YJS', t2.status) as status,
		t2.bg_status as bgStatus,
		t3.category_id,
		t3.course_name,
		t3.course_type,
		t3.course_cover_url
		FROM
		ai_interview_task_info t1
		INNER JOIN
		ai_interview_task_emp t2 on t1.id = t2.task_id and t2.deleted = 0 and t2.emp_code = #{param.loginEmpCode}
		INNER JOIN
		ai_interview_info t3 ON t1.resource_id = t3.id AND t1.deleted = 0 AND t3.deleted = 0
		<where>
			<!--该状态来自于常量：AiInterviewConstant.TaskStatus-->
			t1.task_status != 'CG'
			<if test="param.taskName != null and param.taskName != ''">
				AND t1.task_name like concat(#{param.taskName},'%')
			</if>
			<if test="param.taskStatus != null and param.taskStatus != ''">
				AND t1.task_status = #{param.taskStatus}
			</if>
		</where>
		<!--该状态来自于常量：AiInterviewConstant.TaskEmpStatus-->
		order by (case t1.task_status when 'JXZ' then 1 when 'YWC' then 2 else 3 end) asc ,(case t2.status when 'WKS' then 1 when 'JXZ' then 2 else 3 end) asc, t1.create_time desc
	</select>
</mapper>