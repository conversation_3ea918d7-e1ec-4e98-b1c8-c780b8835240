<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.interview.task.mapper.AiInterviewTaskEmpMapper">

	<sql id="AiInterviewTaskEmpColumns">
		a.id AS "id",
		a.resource_id AS "resourceId",
		a.task_id AS "taskId",
		a.emp_code AS "empCode",
		a.emp_name AS "empName",
		a.emp_avatar_url AS "empAvatarUrl",
		a.org_name AS "orgName",
		a.position_name AS "positionName",
		a.status AS "status",
		a.complete_time AS "completeTime",
		a.remark AS "remark",
		a.bg_status as "bgStatus"

	</sql>

	<select id="findListPage" resultType="com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskEmpResponse">
		SELECT
		<include refid="AiInterviewTaskEmpColumns"/>
		from ai_interview_task_emp a
		<where>
			a.task_id = #{param.taskId}
			<if test="param.status != null and param.status != ''">
				AND a.status = #{param.status}
			</if>
			AND a.deleted = false
		</where>
			ORDER BY a.create_time desc
	</select>

	<select id="getMyObjectDetail"
			resultType="com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewObjectResponse">
		select
		    t2.id,
		    t2.resource_id,
			t2.object_avatar_type AS "objectAvatarType",
			t2.object_avatar_url AS "objectAvatarUrl",
			t2.object_name AS "objectName",
			t2.object_background_info AS "objectBackgroundInfo",
			t2.object_gender AS "objectGender"
		from
			ai_interview_task_emp t1
		inner join
			ai_interview_object	t2 on t1.resource_id = t2.resource_id and t1.deleted = 0 and t2.deleted = 0 and t1.id = #{id}
	</select>

    <select id="getMyInterviewTaskInfoDetail"
            resultType="com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskInfoResponse">
		select
			t2.id,
			t2.resource_id,
			t2.task_name,
			t2.start_date_time,
			t2.end_date_time,
			t2.task_cover_url,
			t2.task_duration,
			t2.task_status
		from
		    ai_interview_task_emp t1
		inner join
		    ai_interview_task_info t2 on t1.task_id = t2.id and t1.deleted = 0 and t2.deleted = 0 and t1.id = #{taskEmpId}
	</select>

	<select id="getMyTaskInfoDetail"
			resultType="com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskInfoResponse">
		select
		t2.id,
		t2.resource_id,
		t2.task_name,
		t2.start_date_time,
		t2.end_date_time,
		t2.task_cover_url,
		t2.task_duration,
		t2.task_status
		from
		ai_interview_task_emp t1
		inner join
		ai_interview_task_info t2 on t1.task_id = t2.id and t1.deleted = 0 and t2.deleted = 0 and t1.id = #{taskEmpId}
	</select>

	<select id="getDialogueQuestionNum" resultType="java.lang.Integer">
		select
			count(t2.id)
		from
			ai_interview_task_emp t1
				inner join
			ai_interview_dialogue_question t2 on t1.id = #{taskEmpId} and t1.resource_id = t2.resource_id and t1.deleted = 0 and t2.deleted = 0
	</select>

</mapper>