<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.interview.train.mapper.AiInterviewMyDialogueHistoryMapper">

	<select id="getMyDialogueNum"
			resultType="java.lang.Long">
		select
			count(1) as  "num"
		from
		ai_interview_my_dialogue_history
		where task_emp_id = #{taskEmpId} and question_id = #{questionId}

	</select>
</mapper>