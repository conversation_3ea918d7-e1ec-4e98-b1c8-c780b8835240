<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.interview.resource.mapper.AiInterviewInfoMapper">

    <sql id="AiInterviewResourceInfoColumns">
        a.id AS "id",
		a.category_id AS "categoryId",
		a.course_name AS "courseName",
		a.course_type AS "courseType",
		a.course_cover_url AS "courseCoverUrl",
		a.course_status AS "courseStatus",
        a.dialogue_scene AS "dialogueScene",
        a.dialogue_objective AS "dialogueObjective",
        a.dialogue_question_num AS "dialogueQuestionNum",
		a.deleted AS "deleted",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
    </sql>

    <select id="findListPage" resultType="com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewInfoResponse">
        SELECT
            <include refid="AiInterviewResourceInfoColumns"/>
        from ai_interview_info a
        <where>
            1=1
            <if test="param.categoryPath != null and param.categoryPath != ''">
                AND a.category_id in (select id from ai_interview_category where category_path like concat(#{param.categoryPath}, '%'))
            </if>
            <if test="param.courseName != null and param.courseName != ''">
                AND a.course_name like concat(#{param.courseName},'%')
            </if>
            <if test="param.courseType != null and param.courseType != ''">
                AND a.course_type = #{param.courseType}
            </if>
            <if test="param.courseStatus != null and param.courseStatus != ''">
                AND a.course_status = #{param.courseStatus}
            </if>
            AND a.deleted = false
        </where>
        order by a.create_time desc
    </select>

    <select id="countByCategoryIds" resultType="java.lang.Long">
        select
            count(t1.id)
        from
            ai_interview_info t1
        where
            t1.deleted = 0
            and t1.category_id in
            <foreach item="categoryId" collection="categoryIds" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
    </select>

</mapper>