<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.ai.module.interview.train.mapper.AiInterviewDialogueQuestionMapper">

	<select id="getAiQuestionList"
			resultType="com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewDialogueQuestionResponse">
		select
		    t1.id,
		    t1.resource_id,
			t1.interview_question,
			t1.question_order,
			t1.interview_objective,
			t1.ai_question_voice_file_url,
			t2.object_avatar_url
		from
			ai_interview_dialogue_question t1
				inner join
			ai_interview_object t2 on t1.resource_id = t2.resource_id and t1.deleted = 0 and t2.deleted = 0
				inner join
			ai_interview_task_emp t3 on t1.resource_id = t3.resource_id and t3.deleted = 0
		where
			t3.id = #{taskEmpId}
		order by
			t1.question_order asc
	</select>


</mapper>