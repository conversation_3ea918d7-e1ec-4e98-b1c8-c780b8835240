package com.deloitte.dhr.ai.enums;

import com.deloitte.dhr.common.base.exception.CommRunException;
import lombok.Getter;

/**
 * AI类型枚举
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public enum AiType {

    SSC("ssc", "SSC"),
    DUO("duo", "工作伙伴"),
    COACH("coach", "陪练助手"),
    LEARNING("learning", "学习助手"),
    DIGITAL_HUMAN("digital_human", "数字人"),
    DIGITAL_HUMAN_LINING("digital_human_lining", "数字人-李宁"),
    DATA_ANALYSIS("data_analysis", "数据分析"),
    DATA_ANALYSIS_PREDICTION("data_analysis_prediction", "数据分析-预测"),
    INTERVIEW("interview", "访谈助手"),
    ;

    private final String code;

    private final String name;

    AiType(String code, String name) {
        this.code = code;
        this.name = name;
    }

}
