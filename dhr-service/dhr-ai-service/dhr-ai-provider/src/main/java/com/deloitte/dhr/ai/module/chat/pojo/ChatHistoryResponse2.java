package com.deloitte.dhr.ai.module.chat.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * AI对话导出数据
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("ChatHistoryResponse2")
public class ChatHistoryResponse2 {

    /*
    * 用户提问
    * */
    @ApiModelProperty(value = "chatMsgId", name = "msgId")
    private Long msgId;

    @ApiModelProperty(value = "对话类型", name = "contentType")
    private String contentType;

    @ApiModelProperty(value = "提问内容", name = "content")
    private String content;

    @ApiModelProperty(value = "aiType", name = "aiType")
    private String aiType;

    @ApiModelProperty(value = "aiSubType", name = "aiSubType")
    private String aiSubType;

    @ApiModelProperty(value = "提问时间", name = "time1")
    private Date time1;


    /*
    * ai回答
    * */
    @ApiModelProperty(value = "chatMsgId", name = "msgId2")
    private long msgId2;

    @ApiModelProperty(value = "对话类型", name = "contentType2")
    private String contentType2;

    @ApiModelProperty(value = "回答内容", name = "content2")
    private String content2;

    @ApiModelProperty(value = "回答内容排序", name = "contentOrder2")
    private int contentOrder2;

    @ApiModelProperty(value = "aiType", name = "aiType2")
    private String aiType2;

    @ApiModelProperty(value = "aiSubType", name = "aiSubType2")
    private String aiSubType2;

    @ApiModelProperty(value = "回答时间", name = "time2")
    private Date time2;
}
