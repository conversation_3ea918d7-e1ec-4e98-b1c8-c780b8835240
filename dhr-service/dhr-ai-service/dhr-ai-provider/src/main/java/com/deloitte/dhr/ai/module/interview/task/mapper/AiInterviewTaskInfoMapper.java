package com.deloitte.dhr.ai.module.interview.task.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.interview.task.domain.AiInterviewTaskInfo;
import com.deloitte.dhr.ai.module.interview.task.pojo.*;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI访谈-访谈任务-相关持久化接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface AiInterviewTaskInfoMapper extends SuperMapper<AiInterviewTaskInfo> {

    /**
     * 详情-AI访谈任务
     *
     * @param id AI访谈任务主键ID
     * @return AiInterviewTaskDetailResponse
     */
    AiInterviewTaskDetailResponse getInterviewTaskDetail(@Param("id") Long id);

    /**
     * 分页-任务列表
     *
     * @param page  分页参数
     * @param param 过滤条件
     * @param order 排序条件
     * @return 列表数据
     */
    List<AiInterviewTaskResponse> findListPage(Page<AiInterviewTaskInfo> page, @Param("param") AiInterviewTaskListRequest param, @Param("order") String order);

    /**
     * 分页-我的任务列表
     *
     * @param page  分页参数
     * @param param 过滤条件
     * @param order 排序条件
     * @return 列表数据
     */
    List<AiMyInterviewTaskListResponse> myTaskList(Page<AiInterviewTaskInfo> page, @Param("param") AiInterviewEmpTaskListRequest param, @Param("order") String order);

    List<AiMyInterviewTaskListResponse> myTaskList2(Page<AiInterviewTaskInfo> page, @Param("param") AiInterviewEmpTaskListRequest param, @Param("order") String order);


    /**
     * AI访谈-更新任务状态
     *
     * @param id 访谈任务ID
     */
    void updateTaskStatus(@Param("id") Long id);

}