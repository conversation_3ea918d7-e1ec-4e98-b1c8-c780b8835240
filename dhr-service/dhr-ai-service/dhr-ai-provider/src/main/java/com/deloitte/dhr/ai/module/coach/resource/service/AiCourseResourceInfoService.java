package com.deloitte.dhr.ai.module.coach.resource.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.coach.resource.domain.AiCourseResourceInfo;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceInfoResponse;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceInfoSaveRequest;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceListRequest;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperService;

/**
 * 课程资源-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiCourseResourceInfoService extends SuperService<AiCourseResourceInfo> {

    /**
     * 查询数据详情
     *
     * @param id 课程资源ID
     * @return AiCourseResourceDetailResponse
     */
    AiCourseResourceInfoResponse getDetail(Long id);

    /**
     * 分页查询列表数据
     *
     * @param page    分页参数
     * @param request 过滤条件
     * @param order   排序条件
     * @return ResponsePage<AiCourseResourceListResponse>
     */
    ResponsePage<AiCourseResourceInfoResponse> findListPage(Page<AiCourseResourceInfo> page, AiCourseResourceListRequest request, BaseOrder order);

    /**
     * 保存数据
     *
     * @param request      课程资源保存数据
     * @param courseType   课程分类：XKC：教学课程，AIPL：AI陪练
     * @param courseStatus 课程状态：CG：草稿，TJ：提交
     * @return Long
     */
    Long saveData(AiCourseResourceInfoSaveRequest request, String courseType, String courseStatus);

    /**
     * 删除课程资源
     *
     * @param id 课程资源ID
     * @return Boolean
     */
    Boolean deleteById(Long id);

    /**
     * 校验课程资源
     *
     * @param request 课程资源保存数据
     */
    void verify(AiCourseResourceInfoSaveRequest request);

}
