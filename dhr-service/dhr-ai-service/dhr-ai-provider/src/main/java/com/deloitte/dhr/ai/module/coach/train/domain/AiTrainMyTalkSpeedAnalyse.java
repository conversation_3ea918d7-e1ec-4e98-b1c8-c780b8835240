package com.deloitte.dhr.ai.module.coach.train.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 课程资源-AI陪练-我的语速分析
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_train_my_talk_speed_analyse")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-AI陪练-我的语速分析表")
public class AiTrainMyTalkSpeedAnalyse extends SuperLogicModel<AiTrainMyTalkSpeedAnalyse> {

    /**
     * 培训任务员工ID
     */
    @TableField(value = "task_emp_id")
    private Long taskEmpId;

    /**
     * 对话记录ID
     */
    @TableField(value = "history_id")
    private Long historyId;

    /**
     * 语速编码
     */
    @TableField(value = "speed_code")
    private String speedCode;

    /**
     * 语速名称
     */
    @TableField(value = "speed_name")
    private String speedName;

}