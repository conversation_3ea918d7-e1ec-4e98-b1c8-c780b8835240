package com.deloitte.dhr.ai.module.coach.train.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 课程资源-AI陪练-对象标签表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_train_object_tag")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-AI陪练-对象标签表")
public class AiTrainObjectTag extends SuperLogicModel<AiTrainObjectTag> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 陪练对象ID
     */
    @TableField(value = "object_id")
    private Long objectId;

    /**
     * 陪练对象标签名称
     */
    @TableField(value = "tag_name")
    private String tagName;

    /**
     * 陪练对象标签编码
     */
    @TableField(value = "tag_code")
    private String tagCode;


}