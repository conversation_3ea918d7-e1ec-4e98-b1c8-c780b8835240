package com.deloitte.dhr.ai.module.ai.strategy.ssc;

import com.alibaba.fastjson2.JSON;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiSscIntentAction;
import com.deloitte.dhr.ai.enums.AiSscIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.ActionStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.strategy.ssc.pojo.ProofApplyTqResponse;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * SSC-证明申请-事务查询-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@ActionStrategyType(item = AiSscIntentItem.ZMXX, action = AiSscIntentAction.TQ)
public class ProofApplyTqStrategy implements SscStrategy {
    @Override
    public Flux<List<AiContent>> executeStream(Map<String, Object> param) {

        List<ProofApplyTqResponse> list = new ArrayList<>();
        AiContent content = AiContent.builder()
                .contentType(AiContentType.JSON.getCode())
                .content(JSON.toJSONString(list)).build();
        return Flux.just(List.of(content));
    }

}
