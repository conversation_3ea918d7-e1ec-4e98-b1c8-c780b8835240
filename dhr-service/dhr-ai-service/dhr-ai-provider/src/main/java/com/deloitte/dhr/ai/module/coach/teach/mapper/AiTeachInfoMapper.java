package com.deloitte.dhr.ai.module.coach.teach.mapper;

import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachInfo;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 课程资源-教学课程-基本信息-相关持久化接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTeachInfoMapper extends SuperMapper<AiTeachInfo> {

    /**
     * 查询-匹配度
     *
     * @param resourceId 课程资源ID
     * @return 匹配度
     */
    BigDecimal getMatchingDegree(@Param("resourceId") Long resourceId);

}