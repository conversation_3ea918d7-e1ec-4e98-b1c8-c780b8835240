package com.deloitte.dhr.ai.module.chat.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.enums.AiSscIntentAction;
import com.deloitte.dhr.ai.module.chat.pojo.ChatHistoryRequest;
import com.deloitte.dhr.ai.module.chat.pojo.ChatHistoryResponse;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatMsgListRequest;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatMsgListResponse;
import com.deloitte.dhr.ai.module.chat.service.AiChatMsgService;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 聊天消息记录
 *
 * <AUTHOR>
 * @date 2024-03-27
 */
@RestController
@RequestMapping("/chatMsg")
@Api(tags = "聊天消息记录")
@Validated
public class AiChatMsgController extends SuperController {

    @Autowired
    private AiChatMsgService aiChatMsgService;

    @ApiOperation(value = "列表", notes = "列表")
    @ApiOperationSupport(order = 1)
    @PostMapping("/list")
    public ResponseVO<List<AiChatMsgListResponse>> getChatList(@RequestBody @Validated AiChatMsgListRequest request) {
        return success(aiChatMsgService.getChatList(request));
    }

    @ApiOperation(value = "chatType", notes = "chatType")
    @ApiOperationSupport(order = 4)
    @GetMapping(value = "/chatType")
    public ResponseVO<List<JSONObject>> chatType() {
        List<JSONObject> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", AiSscIntentAction.IR.getCode());
        jsonObject.put("name", "政策问询");
        list.add(jsonObject);
        jsonObject = new JSONObject();
        jsonObject.put("code", AiSscIntentAction.TC.getCode());
        jsonObject.put("name", "业务办理");
        list.add(jsonObject);
        jsonObject = new JSONObject();
        jsonObject.put("code", AiSscIntentAction.TQ.getCode());
        jsonObject.put("name", "数据查询");
        list.add(jsonObject);
        return ResponseVO.success(list);
    }

    @ApiOperation(value = "chatHistory", notes = "submit")
    @ApiOperationSupport(order = 5)
    @PostMapping(value = "/chatHistory/{current}/{size}")
    public ResponseVO<ResponsePage<ChatHistoryResponse>> chatHistory(@PathVariable("current") Long current, @PathVariable("size") Long size,
                                                                     @Validated @RequestBody(required = false) ChatHistoryRequest request) {
        if (request == null) {
            request = new ChatHistoryRequest();
        }
        return ResponseVO.success(aiChatMsgService.chatHistory(new Page(current, size),request));
    }
}
