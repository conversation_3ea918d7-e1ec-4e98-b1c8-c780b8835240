package com.deloitte.dhr.ai.module.coach.teach.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 课程资源-教学课程-考核题目-我的选择题选项-详情
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("AiTeachMyChoiceOptionDetailResponse")
public class AiTeachMyChoiceOptionResponse extends AiTeachChoiceOptionResponse {

    /**
     * 是否是我的答案
     */
    @ApiModelProperty(value = "是否是我的答案", name = "isMyAnswer")
    private Boolean isMyAnswer;

}