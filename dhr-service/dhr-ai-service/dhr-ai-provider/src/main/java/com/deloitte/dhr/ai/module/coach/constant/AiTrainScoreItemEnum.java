package com.deloitte.dhr.ai.module.coach.constant;

import lombok.Getter;

import java.math.BigDecimal;

/**
 * AI训练得分项枚举
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public enum AiTrainScoreItemEnum {

    /**
     * 语速得分项
     */
    speech_speed("speech_speed", "语速分", BigDecimal.valueOf(20)),

    /**
     * 能力得分项
     */
    ability("ability", "能力分", BigDecimal.valueOf(80));

    /**
     * 编码
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 权重
     */
    private final BigDecimal weight;

    AiTrainScoreItemEnum(String code, String name, BigDecimal weight) {
        this.code = code;
        this.name = name;
        this.weight = weight;
    }

}
