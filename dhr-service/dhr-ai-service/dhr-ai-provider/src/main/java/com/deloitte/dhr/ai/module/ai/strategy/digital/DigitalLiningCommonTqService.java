package com.deloitte.dhr.ai.module.ai.strategy.digital;


import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.deloitte.dhr.ai.module.ai.strategy.digital.pojo.DigitalLiningAttendanceExceptionTqResponse;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数字人(李宁)-通用事务查询业务实现类
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class DigitalLiningCommonTqService {

    /**
     * 查询我的考勤异常信息
     *
     * @return List<AttendanceExceptionTqResponse>
     */
    public List<DigitalLiningAttendanceExceptionTqResponse> queryMyAttendanceExceptionInfo() {
        // todo mock假数据模拟
        List<DigitalLiningAttendanceExceptionTqResponse> responseList = new ArrayList<>();
        List<String> randomMonthDates = getRandomMonthDates(new Date(), 3);
        responseList.add(new DigitalLiningAttendanceExceptionTqResponse("10", "漏卡", DateUtil.parse(randomMonthDates.get(0), DatePattern.NORM_DATE_PATTERN), "上班打卡"));
        responseList.add(new DigitalLiningAttendanceExceptionTqResponse("20", "迟到", DateUtil.parse(randomMonthDates.get(1) + " 09:38:28", DatePattern.NORM_DATETIME_PATTERN), ""));
        responseList.add(new DigitalLiningAttendanceExceptionTqResponse("30", "早退", DateUtil.parse(randomMonthDates.get(2) + " 18:29:01", DatePattern.NORM_DATETIME_PATTERN), ""));
        return responseList;
    }

    /**
     * 获取某月随机日期
     *
     * @param date 某个时间
     * @param num  生成个数
     * @return 随机日期列表
     */
    private static List<String> getRandomMonthDates(Date date, int num) {
        // 获取当前月份的天数
        int lengthOfMonth = DateUtil.lengthOfMonth(DateUtil.month(date), DateUtil.isLeapYear(DateUtil.year(date)));
        Set<Date> dates = new HashSet<>();
        while (dates.size() < num) {
            dates.add(RandomUtil.randomDate(DateUtil.beginOfMonth(date), DateField.DAY_OF_MONTH, 0, lengthOfMonth));
        }
        return dates.stream().map(DateUtil::formatDate).collect(Collectors.toList());
    }

    /**
     * 获取当前登录人员工编号
     *
     * @return 当前登录人员工编号
     */
    public String getLoginEmpCode() {
        return LoginUtil.getLoginUser() == null ? "" : LoginUtil.getLoginUser().getUsername();
    }

}
