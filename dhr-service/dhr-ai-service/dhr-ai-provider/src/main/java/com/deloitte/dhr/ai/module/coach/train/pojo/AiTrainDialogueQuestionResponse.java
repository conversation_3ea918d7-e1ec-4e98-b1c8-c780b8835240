package com.deloitte.dhr.ai.module.coach.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 课程资源-AI陪练-对话问题
 *
 * <AUTHOR>
 * @date 2024-01-03
 */
@Data
@ApiModel("AiTrainDialogueQuestionResponse")
public class AiTrainDialogueQuestionResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 课程资源ID
     */
    @ApiModelProperty(value = "课程资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * AI题目ID（AI模型生成）
     */
    @ApiModelProperty(value = "AI题目ID（AI模型生成）", name = "aiQuestionId")
    private String aiQuestionId;

    /**
     * AI题目语音文件URL
     */
    @ApiModelProperty(value = "AI题目语音文件URL", name = "aiQuestionVoiceFileUrl")
    private String aiQuestionVoiceFileUrl;

    /**
     * 题目名称
     */
    @ApiModelProperty(value = "题目名称", name = "questionName")
    private String questionName;

    /**
     * 题目参考答案
     */
    @ApiModelProperty(value = "题目参考答案", name = "sampleAnswerContent")
    private String sampleAnswerContent;

    /**
     * 题目顺序
     */
    @ApiModelProperty(value = "题目顺序", name = "questionOrder")
    private Integer questionOrder;

    /**
     * 培训的AI对象头像
     */
    @ApiModelProperty(value = "培训的AI对象头像", name = "objectAvatarUrl")
    private String objectAvatarUrl;

}