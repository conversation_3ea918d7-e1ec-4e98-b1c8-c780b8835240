package com.deloitte.dhr.ai.module.coach.train.service;

import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainMyAbilityAnalyse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyAbilityAnalyseChartResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyAbilityScoreResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyAbilityScoreSaveRequest;
import com.deloitte.dhr.common.SuperService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课程资源-AI陪练-我的能力分析-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTrainMyAbilityAnalyseService extends SuperService<AiTrainMyAbilityAnalyse> {

    /**
     * 保存-能力分析信息
     *
     * @param taskEmpId       培训任务员工ID
     * @param historyId       本次员工对话历史ID
     * @param saveRequestList 分析信息
     * @return Boolean
     */
    Boolean saveData(Long taskEmpId, Long historyId, List<AiTrainMyAbilityScoreSaveRequest> saveRequestList);

    /**
     * 查询-我的能力分析
     *
     * @param taskEmpId 培训任务员工ID
     * @return List<AiTrainMyAbilityAnalyseResponse>
     */
    List<AiTrainMyAbilityAnalyseChartResponse> getMyAbilityAnalyseChart(Long taskEmpId);

    /**
     * 计算能力得分
     * 能力项得分 = (每项能力项分数 * 每项能力项权重)之和
     *
     * @param taskEmpId 培训任务员工ID
     * @return 能力得分
     */
    BigDecimal calculateScore(Long taskEmpId);

    /**
     * 查询-我的对话分析
     *
     * @param historyId 对话历史记录ID
     * @return List<AiTrainMyAbilityScoreResponse>
     */
    List<AiTrainMyAbilityScoreResponse> getMyDialogueAnalyse(Long historyId);

}
