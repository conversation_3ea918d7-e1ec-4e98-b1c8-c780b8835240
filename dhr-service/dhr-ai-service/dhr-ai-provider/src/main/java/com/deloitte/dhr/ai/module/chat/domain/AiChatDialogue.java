package com.deloitte.dhr.ai.module.chat.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * AI聊天-聊天对话表
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_chat_dialogue")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AI聊天-聊天对话表")
public class AiChatDialogue extends SuperLogicModel<AiChatDialogue> {

    /**
     * AI分组：数字人-digital_human，文本聊天-text_chat
     */
    @TableField(value = "ai_group")
    private String aiGroup;

    /**
     * 对话标题
     */
    @TableField(value = "dialogue_title")
    private String dialogueTitle;

}