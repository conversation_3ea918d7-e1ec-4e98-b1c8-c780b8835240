package com.deloitte.dhr.ai.module.coach.teach.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 课程资源-教学课程-考核题目-选择题选项-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachChoiceOptionSaveRequest")
public class AiTeachChoiceOptionSaveRequest {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 选项名称
     */
    @Length(max = 255, message = "选项名称不能超过255个字符")
    @ApiModelProperty(value = "选项名称", name = "optionName")
    private String optionName;

    /**
     * 是否答案
     */
    @ApiModelProperty(value = "是否答案", name = "isAnswer")
    private Boolean isAnswer;

    /**
     * 选项序号
     */
    @Length(max = 255, message = "选项序号不能超过10个字符")
    @ApiModelProperty(value = "选项序号", name = "optionSort")
    private Integer optionSort;

}