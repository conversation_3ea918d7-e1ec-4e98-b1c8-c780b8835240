package com.deloitte.dhr.ai.module.interview.train.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * AI访谈-访谈任务-对象表
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_interview_object")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AI访谈-对象表")
public class AiInterviewObject extends SuperLogicModel<AiInterviewObject> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 访谈对象头像类型
     */
    @TableField(value = "object_avatar_type")
    private String objectAvatarType;

    /**
     * 访谈对象头像URL
     */
    @TableField(value = "object_avatar_url")
    private String objectAvatarUrl;

    /**
     * 访谈对象名称
     */
    @TableField(value = "object_name")
    private String objectName;

    /**
     * 角色设定
     */
    @TableField(value = "object_background_info")
    private String objectBackgroundInfo;

    /**
     * 访谈对象性别;male：男，female：女，unknown：未知
     */
    @TableField(value = "object_gender")
    private String objectGender;


}