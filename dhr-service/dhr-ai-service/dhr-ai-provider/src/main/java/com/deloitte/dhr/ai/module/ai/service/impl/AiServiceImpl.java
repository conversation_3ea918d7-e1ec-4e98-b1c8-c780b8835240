package com.deloitte.dhr.ai.module.ai.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.deloitte.dhr.ai.client.AiModelWebClient;
import com.deloitte.dhr.ai.enums.*;
import com.deloitte.dhr.ai.module.ai.pojo.*;
import com.deloitte.dhr.ai.module.ai.service.AiService;
import com.deloitte.dhr.ai.module.ai.strategy.transform.ResponseTransformStrategy;
import com.deloitte.dhr.ai.module.ai.strategy.transform.TransformStrategyContext;
import com.deloitte.dhr.ai.module.chat.domain.AiChatMsg;
import com.deloitte.dhr.ai.module.chat.domain.AiPicDeal;
import com.deloitte.dhr.ai.module.chat.mapper.AiPicDealMapper;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatDialogueSaveRequest;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatMsgContentSaveRequest;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatMsgSaveRequest;
import com.deloitte.dhr.ai.module.chat.service.AiChatDialogueService;
import com.deloitte.dhr.ai.module.chat.service.AiChatMsgContentService;
import com.deloitte.dhr.ai.module.chat.service.AiChatMsgService;
import com.deloitte.dhr.ai.module.sr.pojo.AliyunFlashRecognizerRequest;
import com.deloitte.dhr.ai.module.sr.service.AliyunSrService;
import com.deloitte.dhr.ai.utils.CheckUtils;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.pojo.UserDto;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import com.deloitte.dhr.common.base.utils.date.DateStyle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

import java.util.*;
import java.util.stream.Collectors;

/**
 * AI-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Slf4j
@Service
public class AiServiceImpl implements AiService {

    /**
     * 音频文件类型
     */
    public static final Set<String> AUDIO_FILE_TYPES = Set.of("MP3", "WAV");

    @Autowired
    private AiModelWebClient webClient;
    @Autowired
    private AiChatDialogueService dialogueService;
    @Autowired
    private AiChatMsgService msgService;
    @Autowired
    private AiChatMsgContentService msgContentService;
    @Autowired
    private TransformStrategyContext transformStrategyContext;
    @Autowired
    private AliyunSrService aliyunSrService;


    @Autowired
    private AiPicDealMapper aiPicDealMapper;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Flux<AiResponse> chat(AiRequest request) {
        AiSubType subType = AiSubType.getByCode(request.getAiSubType());
        request.setUserId(LoginUtil.getLoginUserCode());
        // 保存对话消息
        Long dialogueId = saveOrUpdateDialogueInfo(request);

        // 保存user消息
        Long userMsgId = saveUserMsgInfo(request, subType);

        // 保存ai消息（无消息内容，先获取到ID，stream请求完后再保存消息内容）
        Long aiMsgId = saveAiMsgInfo(request, subType, userMsgId);

        // 缓存会话id
        StringBuffer conversationIdBuffer = new StringBuffer();

        // 缓存ai消息内容
        Map<String, StringBuffer> contentMap = new LinkedHashMap<>();

        // 获取转换策略
        ResponseTransformStrategy strategy = transformStrategyContext.getStrategy(subType.getAiTransformType());

        // 登录信息上下文传递，防止子线程中获取不到登录信息
        SecurityContext context = SecurityContextHolder.getContext();
        return webClient.post(buildAiModelRequest(request, subType))
                .doOnNext(raw -> {
                    if (conversationIdBuffer.length() == 0) {
                        conversationIdBuffer.append(JSONObject.parseObject(raw).getString("conversation_id"));
                    }
                    if (StrUtil.equals(JSONObject.parseObject(raw).getString("event"), "error")) {
                        throw new CommRunException(JSONObject.parseObject(raw).getString("message"));
                    }
                })
                .transform(rawStream -> request.getStream() ? strategy.transformStream(rawStream, request) : strategy.transformBlock(rawStream, request))
                .filter(CollUtil::isNotEmpty)
                .doOnNext(contents -> contents.forEach(content -> {
                    if (contentMap.get(content.getContentType()) == null) {
                        //判断是否为豆包生成的图片
                        if("IMAGE".equals(content.getContentType()) && content.getExt() != null && !content.getExt().isEmpty()){
                            Map<String, Object> extMap = content.getExt();
                            if (extMap.containsKey("picSource") && "doubao".equals(extMap.get("picSource").toString())){
                                String picUrl = content.getContent();
                                contentMap.put(content.getContentType(), new StringBuffer().append(picUrl));
                                saveAiPicDeal(aiMsgId,picUrl);
                            } else {
                                contentMap.put(content.getContentType(), new StringBuffer().append(content.getContent()));
                            }
                        } else {
                            contentMap.put(content.getContentType(), new StringBuffer().append(content.getContent()));
                        }
                    } else {
                        contentMap.get(content.getContentType()).append(content.getContent());
                    }

                }))
                .map(contents ->
                        AiResponse.builder().id(aiMsgId).dialogueId(dialogueId).conversationId(conversationIdBuffer.toString()).role(AiRoleType.ASSISTANT.getCode()).contents(contents).msgId(userMsgId).build()
                )
                .doOnCancel(() -> {
                    SecurityContextHolder.setContext(context);
                    String errorMsg = "客户端取消连接，请求中断";
                    log.error(errorMsg);
                    handlerError(errorMsg, contentMap);
                    // 取消连接时，更新对话消息
                    updateMsgInfo(conversationIdBuffer.toString(), dialogueId, userMsgId, aiMsgId, contentMap);
                })
                .onErrorResume(e -> {
                    log.error("调用AI ChatFLow 接口失败，错误信息为：{}", e.getMessage());
                    AiContent errorContent = handlerError(e.getMessage(), contentMap);
                    return Flux.just(AiResponse.builder().id(aiMsgId).dialogueId(dialogueId).conversationId(conversationIdBuffer.toString()).contents(List.of(errorContent)).build());
                })
                .doOnTerminate(() -> {
                    SecurityContextHolder.setContext(context);
                    // 异常或者正常完成时，更新对话消息
                    updateMsgInfo(conversationIdBuffer.toString(), dialogueId, userMsgId, aiMsgId, contentMap);
                });

    }

    private void saveAiPicDeal(Long aiMsgId, String picUrl) {
        AiPicDeal aiPicDeal = new AiPicDeal();
        aiPicDeal.setCreateTime(DateUtil.format(DateUtil.date(), DateStyle.YYYY_MM_DD_HH_MM_SS.getValue()));
        aiPicDeal.setPic1(picUrl);
        aiPicDeal.setStatus(1);
        aiPicDeal.setObjId(aiMsgId);
        aiPicDeal.setObjType("aiMsg");
        aiPicDealMapper.insert(aiPicDeal);
    }

    @Override
    public Flux<AiResponse> work(AiRequest request, UserDto userDto) {
        request.setUserId(userDto.getEmployeeNumber());
        AiSubType subType = AiSubType.getByCode(request.getAiSubType());
        // 获取转换策略
        ResponseTransformStrategy strategy = transformStrategyContext.getStrategy(subType.getAiTransformType());
        return webClient.post(buildAiModelRequest(request, subType))
                .transform(rawStream -> request.getStream() ? strategy.transformStream(rawStream, request) : strategy.transformBlock(rawStream, request))
                .map(contents -> AiResponse.builder().role(AiRoleType.ASSISTANT.getCode()).contents(contents).build())
                .onErrorResume(e -> {
                    log.error("调用AI WorkFlow 接口失败，错误信息为：{}", e.getMessage());
                    return Flux.just(AiResponse.builder().contents(List.of(handlerError(e.getMessage(), null))).build());
                });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submit(AiSubmitRequest request) {
        AiChatMsg chatMsg;
        if (request.getMsgId() != null) {
            chatMsg = msgService.get(request.getMsgId());
        } else {
            chatMsg = BeanUtil.copyProperties(request, AiChatMsg.class);
            chatMsg.setRole(AiRoleType.ASSISTANT.getCode());
            chatMsg.setSendTime(DateTime.now());
            msgService.save(chatMsg);
        }
        CheckUtils.checkNull(chatMsg, "该对话消息不存在!");
        List<AiChatMsgContentSaveRequest> list = request.getContents().stream().map(content ->
                AiChatMsgContentSaveRequest.builder()
                        .msgId(chatMsg.getId())
                        .dialogueId(chatMsg.getDialogueId())
                        .content(content.getContent())
                        .contentType(content.getContentType())
                        .contentOrder(10)
                        .build()
        ).collect(Collectors.toList());
        msgContentService.saveBatchData(list);
        return true;
    }

    /**
     * 保存对话信息
     *
     * @param request 请求参数
     * @return 对话ID
     */
    private Long saveOrUpdateDialogueInfo(AiRequest request) {
        AiChatDialogueSaveRequest dialogueRequest = AiChatDialogueSaveRequest.builder()
                .id(request.getDialogueId())
                .dialogueTitle(getUserInput(request))
                .aiGroup(AiGroup.getByAiType(request.getAiType()).getCode())
                .build();
        Long id = dialogueService.saveOrUpdateData(dialogueRequest);
        request.setDialogueId(id);
        return id;
    }

    /**
     * 保存user消息信息
     *
     * @param request 请求参数
     * @param subType AI子类型
     * @return 消息ID
     */
    private Long saveUserMsgInfo(AiRequest request, AiSubType subType) {
        AiChatMsgSaveRequest msgRequest = AiChatMsgSaveRequest.builder()
                .role(AiRoleType.USER.getCode())
                .dialogueId(request.getDialogueId())
                .aiType(subType.getType().getCode())
                .aiSubType(subType.getCode())
                .sendTime(DateTime.now())
                .build();
        // 保存user消息
        Long id = msgService.saveOrUpdateData(msgRequest);

        // 保存user消息内容
        List<AiChatMsgContentSaveRequest> contents = new ArrayList<>();
        request.getContents().forEach(content -> {
            AiChatMsgContentSaveRequest saveRequest = AiChatMsgContentSaveRequest.builder()
                    .msgId(id)
                    .dialogueId(request.getDialogueId())
                    .contentType(content.getContentType())
                    .content(content.getContent())
                    .build();
            contents.add(saveRequest);
        });
        msgContentService.saveBatchData(contents);
        return id;
    }

    /**
     * 保存ai消息信息
     *
     * @param request   请求参数
     * @param subType   AI子类型
     * @param userMsgId
     * @return 消息ID
     */
    private Long saveAiMsgInfo(AiRequest request, AiSubType subType, Long userMsgId) {
        AiChatMsgSaveRequest msgRequest = AiChatMsgSaveRequest.builder()
                .role(AiRoleType.ASSISTANT.getCode())
                .dialogueId(request.getDialogueId())
                .aiType(subType.getType().getCode())
                .aiSubType(subType.getCode())
                .sendTime(DateTime.now())
                .msgId(userMsgId)
                .build();
        return msgService.saveOrUpdateData(msgRequest);
    }

    /**
     * 构建AI模型请求
     *
     * @param request 请求参数
     * @param subType AI子类型
     * @return AiModelRequest
     */
    private AiModelRequest buildAiModelRequest(AiRequest request, AiSubType subType) {
        AiType type = subType.getType();
        Map<String, Object> inputs = new HashMap<>();

        switch (type) {
            case DUO:
            case LEARNING:
            case DIGITAL_HUMAN:
                inputs.put("type", request.getAiSubType());
                break;
            case COACH:
            case INTERVIEW:
                String jsonContent = request.getContents().stream()
                        .filter(content -> StrUtil.equals(content.getContentType(), AiContentType.JSON.getCode()))
                        .findFirst()
                        .orElseThrow(() -> new CommRunException("用户输入为空"))
                        .getContent();
                inputs = JSON.parseObject(jsonContent, new TypeReference<Map<String, Object>>() {});
                break;
            case SSC:
            case DIGITAL_HUMAN_LINING:
                inputs.put("query", getUserInput(request));
                break;
            case DATA_ANALYSIS:
                boolean isJson = false;
                List<AiContent> contents = request.getContents();
                for (AiContent content : contents){
                    if (StrUtil.equals(content.getContentType(), AiContentType.JSON.getCode())) {
                        isJson = true;
                        break;
                    }
                }
                if (isJson) {
                    // 提取contentType为JSON的对象，解析其中的content并放到inputs中
                    String dataAnalysisJsonContent = contents.stream()
                            .filter(content -> StrUtil.equals(content.getContentType(), AiContentType.JSON.getCode()))
                            .findFirst()
                            .orElseThrow(() -> new CommRunException("数据分析请求缺少JSON参数"))
                            .getContent();
                    Map<String, Object> dataAnalysisInputs = JSON.parseObject(dataAnalysisJsonContent, new TypeReference<Map<String, Object>>() {
                    });
                    inputs.putAll(dataAnalysisInputs);
                }

                inputs.put("web_token", LoginUtil.getLoginUser().getAuthorizationString());
                break;
            default:
        }
        StringBuffer audio2TextBuffer = new StringBuffer();
        List<AiModelRequest.AiModelFileRequest> files = new ArrayList<>();
        request.getContents().stream().filter(content -> StrUtil.equals(content.getContentType(), AiContentType.FILE.getCode()))
                .forEach(file -> {
                    AiFileRequest fileRequest = JSON.parseObject(file.getContent(), AiFileRequest.class);
                    // 如果是音频文件，则转换为文字，并追加到用户输入后面
                    if (AUDIO_FILE_TYPES.contains(fileRequest.getFileType().toUpperCase())) {
                        AliyunFlashRecognizerRequest aliyunFlashRecognizerRequest = AliyunFlashRecognizerRequest.builder()
                                .audioAddress(fileRequest.getFileUrl())
                                .format(fileRequest.getFileType())
                                .build();
                        String audio2Text = aliyunSrService.flashRecognizer(aliyunFlashRecognizerRequest);
                        audio2TextBuffer.append(audio2Text);
                    } else {
                        files.add(AiModelRequest.AiModelFileRequest.builder()
                                .transferMethod("remote_url")
                                .type("document")
                                .url(fileRequest.getFileUrl())
                                .build());
                    }
                });
        return AiModelRequest.builder()
                .inputs(inputs)
                .user(request.getUserId())
                .files(files)
                .conversationId(request.getConversationId())
                .query(audio2TextBuffer.length() == 0 ? getUserInput(request) : getUserInput(request) + ": " + audio2TextBuffer)
                .isSearch(request.getIsSearch())
                .responseMode(request.getStream() ? AiResponseMode.STREAMING.getCode() : AiResponseMode.BLOCKING.getCode())
                .type(AiAgentType.getByAgentType(subType))
                .build();
    }

    /**
     * 获取用户输入
     *
     * @param request 请求参数
     * @return 用户输入
     */
    private String getUserInput(AiRequest request) {
        return request.getContents()
                .stream()
                .filter(content -> StrUtil.equals(content.getContentType(), AiContentType.TEXT.getCode()))
                .findAny().orElse(AiContent.builder().content("").build()).getContent();
    }

    /**
     * 处理异常
     *
     * @param errorMsg 错误信息
     * @return AiContent
     */
    private AiContent handlerError(String errorMsg, Map<String, StringBuffer> contentMap) {
        AiContent errorContent = AiContent.builder()
                .content(StrUtil.isBlank(errorMsg) ? "抱歉，我暂时无法理解您的问题，请稍后再试。" : errorMsg)
                .contentType(AiContentType.ERROR.getCode())
                .build();
        if (contentMap == null || contentMap.isEmpty()) {
            return errorContent;
        }
        // 清理缓存的消息内容
        contentMap.clear();
        // 重新塞入错误消息的内容
        contentMap.put(AiContentType.ERROR.getCode(), new StringBuffer().append(errorContent.getContent()));
        return errorContent;
    }

    /**
     * 更新消息
     *
     * @param conversationId dify会话ID
     * @param dialogueId     对话ID
     * @param userMsgId      用户消息ID
     * @param aiMsgId        AI消息ID
     * @param contentMap     AI消息缓存
     */
    private void updateMsgInfo(String conversationId, Long dialogueId, Long userMsgId, Long aiMsgId, Map<String, StringBuffer> contentMap) {

        // 更新user消息
        msgService.saveOrUpdateData(AiChatMsgSaveRequest.builder().id(userMsgId).conversationId(conversationId).build());

        // 更新ai消息
        msgService.saveOrUpdateData(AiChatMsgSaveRequest.builder().id(aiMsgId).conversationId(conversationId).sendTime(DateTime.now()).msgId(userMsgId).build());

        // 保存ai消息内容
        List<AiChatMsgContentSaveRequest> contents = new ArrayList<>();
        contentMap.forEach((contentType, content) ->
                contents.add(AiChatMsgContentSaveRequest.builder().msgId(aiMsgId).dialogueId(dialogueId).contentType(contentType).content(content.toString()).build()));
        msgContentService.saveBatchData(contents);
    }
}
