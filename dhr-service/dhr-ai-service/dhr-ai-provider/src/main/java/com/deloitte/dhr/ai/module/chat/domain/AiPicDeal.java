package com.deloitte.dhr.ai.module.chat.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图片处理
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Data
@TableName("ai_pic_deal")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AI聊天-需要处理的图片列表")
public class AiPicDeal {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 图片外网地址
     */
    @TableField(value = "pic1")
    private String pic1;

    /**
     * 图片内网地址
     */
    @TableField(value = "pic2")
    private String pic2;

    /**
     * 处理状态，1未处理，2处理中，3处理成功，4处理失败
     */
    @TableField(value = "status")
    private int status;

    /**
     * 处理次数
     */
    @TableField(value = "num")
    private int num;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private String createTime;

    /**
     * 处理时间
     */
    @TableField(value = "deal_time")
    private String dealTime;

    /**
     * 数据对象ID
     */
    @TableField(value = "obj_id")
    private Long objId;

    /**
     * 对象类型，aiMsg:ai的对话回复
     */
    @TableField(value = "obj_type")
    private String objType;

}