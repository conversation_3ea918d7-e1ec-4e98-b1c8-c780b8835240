package com.deloitte.dhr.ai.module.coach.train.service;

import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyCompetedRequest;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMySendRequest;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMySendResponse;

/**
 * 课程资源-AI陪练-我的培训-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiMyTrainService {

    /**
     * 发送消息
     *
     * @param request 消息内容
     * @return AiTrainMySendResponse
     */
    AiTrainMySendResponse sendMessage(AiTrainMySendRequest request);

    /**
     * 暂停-AI陪练
     *
     * @param request 保存数据
     * @param status  状态;WKS：未开始，JXZ：进行中，YWC：已完成
     * @return Boolean
     */
    Boolean pauseTraining(AiTrainMyCompetedRequest request, String status);

    /**
     * 结束-AI陪练
     *
     * @param request 提交数据
     * @return Boolean
     */
    Boolean endTraining(AiTrainMyCompetedRequest request);

}
