package com.deloitte.dhr.ai.module.coach.constant;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.math.BigDecimal;

/**
 * AI训练能力分析枚举
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public enum AiTrainAbilityAnalyseEnum {

    /**
     * 表达逻辑
     */
    logical_expression("logical_expression", "表达逻辑", BigDecimal.valueOf(100), BigDecimal.valueOf(0), BigDecimal.valueOf(20)),

    /**
     * 专业知识
     */
    professional_knowledge("professional_knowledge", "专业知识", BigDecimal.valueOf(100), BigDecimal.valueOf(0), BigDecimal.valueOf(80));

    /**
     * 编码
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 最大分数
     */
    private final BigDecimal maxScore;

    /**
     * 最小分数
     */
    private final BigDecimal minScore;

    /**
     * 权重
     */
    private final BigDecimal weight;


    AiTrainAbilityAnalyseEnum(String code, String name, BigDecimal maxScore, BigDecimal minScore, BigDecimal weight) {
        this.code = code;
        this.name = name;
        this.maxScore = maxScore;
        this.minScore = minScore;
        this.weight = weight;
    }

    /**
     * 通过编码获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getNameByCode(String code) {
        for (AiTrainAbilityAnalyseEnum abilityAnalyseEnum : AiTrainAbilityAnalyseEnum.values()) {
            if (StrUtil.equals(abilityAnalyseEnum.code, code)) {
                return abilityAnalyseEnum.name;
            }
        }
        return "";
    }

    /**
     * 通过编码获取名称
     *
     * @param name 名称
     * @return 名称
     */
    public static String getCodeByName(String name) {
        for (AiTrainAbilityAnalyseEnum abilityAnalyseEnum : AiTrainAbilityAnalyseEnum.values()) {
            if (StrUtil.equals(abilityAnalyseEnum.name, name)) {
                return abilityAnalyseEnum.code;
            }
        }
        return "";
    }

    /**
     * 通过编码获取枚举
     *
     * @param code 编码
     * @return 最低分数
     */
    public static AiTrainAbilityAnalyseEnum getByCode(String code) {
        for (AiTrainAbilityAnalyseEnum abilityAnalyseEnum : AiTrainAbilityAnalyseEnum.values()) {
            if (StrUtil.equals(abilityAnalyseEnum.code, code)) {
                return abilityAnalyseEnum;
            }
        }
        return null;
    }

}
