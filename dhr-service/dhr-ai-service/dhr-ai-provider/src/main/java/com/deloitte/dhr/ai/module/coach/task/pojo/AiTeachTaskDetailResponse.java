package com.deloitte.dhr.ai.module.coach.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 培训任务-教学课程-详情
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("AiTeachTaskDetailResponse")
public class AiTeachTaskDetailResponse extends AiTaskResponse {

    /**
     * 类别ID
     */
    @ApiModelProperty(value = "类别ID", name = "categoryId")
    private Long categoryId;

    /**
     * 类别名称
     */
    @ApiModelProperty(value = "类别名称", name = "categoryName")
    private String categoryName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称", name = "courseName")
    private String courseName;

    /**
     * 课程封面图链接
     */
    @ApiModelProperty(value = "课程封面图链接", name = "courseCoverUrl")
    private String courseCoverUrl;

    /**
     * 课程目标
     */
    @ApiModelProperty(value = "课程目标", name = "courseObjectives")
    private String courseObjectives;

    /**
     * 考察知识点
     */
    @ApiModelProperty(value = "考察知识点", name = "examiningKnowledgePoints")
    private String examiningKnowledgePoints;

}