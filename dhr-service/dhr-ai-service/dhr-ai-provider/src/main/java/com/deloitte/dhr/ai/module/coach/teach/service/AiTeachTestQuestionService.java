package com.deloitte.dhr.ai.module.coach.teach.service;

import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachTestQuestion;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachMyTestQuestionResponse;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachTestQuestionResponse;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachTestQuestionSaveRequest;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * 课程资源-教学课程-考核题目-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTeachTestQuestionService extends SuperService<AiTeachTestQuestion> {

    /**
     * 保存考核内容（题目）数据
     *
     * @param saveRequestList 考核内容（题目）保存数据
     * @param resourceId      课程资源ID
     * @return Boolean
     */
    Boolean saveData(List<AiTeachTestQuestionSaveRequest> saveRequestList, Long resourceId);

    /**
     * 删除考核内容（题目）数据
     *
     * @param resourceId 课程资源ID
     */
    void deleteAllByResourceId(Long resourceId);

    /**
     * 查询考核内容（题目）详情数据
     *
     * @param resourceId 课程资源ID
     * @return List<AiTeachTestQuestionDetailResponse>
     */
    List<AiTeachTestQuestionResponse> getByResourceId(Long resourceId);

    /**
     * 查询我的题目数据和答题数据
     *
     * @param resourceId 课程资源ID
     * @param taskEmpId  培训任务ID
     * @param hideAnswer 是否隐藏正确答案
     * @return List<AiTeachMyTestQuestionDetailResponse>
     */
    List<AiTeachMyTestQuestionResponse> getMyQuestionAndAnswer(Long resourceId, Long taskEmpId, Boolean hideAnswer);

    /**
     * 校验考核内容题目
     *
     * @param saveRequestList 考核内容（题目）保存数据
     */
    void verify(List<AiTeachTestQuestionSaveRequest> saveRequestList);

}
