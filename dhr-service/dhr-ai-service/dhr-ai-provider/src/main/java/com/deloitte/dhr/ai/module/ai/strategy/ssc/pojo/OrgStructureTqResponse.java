package com.deloitte.dhr.ai.module.ai.strategy.ssc.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * SSC-组织机构-事务查询
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("OrgStructureTqResponse")
public class OrgStructureTqResponse {

    @ApiModelProperty(value = "组织信息", name = "orgInfo")
    private OrgInfo orgInfo;

    @ApiModelProperty(value = "组织负责人信息", name = "leaderInfos")
    private List<OrgEmpInfo> leaderInfos;

    @ApiModelProperty(value = "组织成员信息", name = "memberInfos")
    private List<OrgEmpInfo> memberInfos;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OrgInfo {

        @ApiModelProperty(value = "组织名称", name = "orgName")
        private String orgName;

        @ApiModelProperty(value = "组织路径", name = "orgPath")
        private String orgPath;

        @ApiModelProperty(value = "组织编码", name = "orgCode")
        private String orgCode;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OrgEmpInfo {

        @ApiModelProperty(value = "员工编号", name = "empCode")
        private String empCode;

        @ApiModelProperty(value = "员工姓名", name = "empName")
        private String empName;

        @ApiModelProperty(value = "员工岗位名称", name = "positionName")
        private String positionName;

    }

}
