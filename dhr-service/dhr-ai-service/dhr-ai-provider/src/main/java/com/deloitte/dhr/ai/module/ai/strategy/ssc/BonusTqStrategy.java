package com.deloitte.dhr.ai.module.ai.strategy.ssc;

import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiSscIntentAction;
import com.deloitte.dhr.ai.enums.AiSscIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.ActionStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;


/**
 * SSC-奖金-事务查询-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@ActionStrategyType(item = AiSscIntentItem.JJXX, action = AiSscIntentAction.TQ)
public class BonusTqStrategy implements SscStrategy {


    @Override
    public Flux<List<AiContent>> executeStream(Map<String, Object> param) {
        // 奖金属于隐私数据，暂时不会返回具体数据
        AiContent content = AiContent.builder()
                .contentType(AiContentType.JSON.getCode())
                .content(null)
                .build();
        return Flux.just(List.of(content));
    }

}
