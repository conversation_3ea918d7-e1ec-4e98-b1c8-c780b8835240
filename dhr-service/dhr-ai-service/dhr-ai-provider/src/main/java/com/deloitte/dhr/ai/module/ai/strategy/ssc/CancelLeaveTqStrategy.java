package com.deloitte.dhr.ai.module.ai.strategy.ssc;

import com.alibaba.fastjson2.JSON;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiSscIntentAction;
import com.deloitte.dhr.ai.enums.AiSscIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.ActionStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.strategy.ssc.pojo.CancelLeaveTqResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * SSC-考勤销假-事务查询-策略
 */
@Service
@ActionStrategyType(item = AiSscIntentItem.KQXJ, action = AiSscIntentAction.TQ)
public class CancelLeaveTqStrategy implements SscStrategy {

    @Autowired
    private CommonTqService commonTqService;

    @Override
    public Flux<List<AiContent>> executeStream(Map<String, Object> param) {
        List<CancelLeaveTqResponse> responses = commonTqService.queryMyCancelLeaveInfo();
        AiContent content = AiContent.builder()
                .contentType(AiContentType.JSON.getCode())
                .content(JSON.toJSONString(responses))
                .build();
        return Flux.just(List.of(content));
    }

}
