package com.deloitte.dhr.ai.module.coach.teach.pojo;

import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 课程资源-教学课程-基本信息-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachInfoSaveRequest")
public class AiTeachInfoSaveRequest {

    /**
     * 课程目标
     */
    @Length(groups = {Save.class, Submit.class}, max = 500, message = "课程目标不能超过500个字符")
    @NotBlank(groups = {Save.class, Submit.class}, message = "课程目标不能为空")
    @ApiModelProperty(value = "课程目标", name = "courseObjectives")
    private String courseObjectives;

    /**
     * 课程设计要求
     */
    @Length(groups = {Save.class, Submit.class}, max = 500, message = "课程设计要求不能超过500个字符")
    @ApiModelProperty(value = "课程设计要求", name = "courseDesignRequirements")
    private String courseDesignRequirements;

    /**
     * 考察知识点
     */
    @Length(groups = {Save.class, Submit.class}, max = 1000, message = "考察知识点不能超过1000个字符")
    @ApiModelProperty(value = "考察知识点", name = "examiningKnowledgePoints")
    private String examiningKnowledgePoints;

    /**
     * 单选题数量
     */
    @NotNull(groups = {Submit.class}, message = "单选题数量不能为空")
    @ApiModelProperty(value = "单选题数量", name = "singleChoiceQuestionNum")
    private Integer singleChoiceQuestionNum;

    /**
     * 判断题数量
     */
    @NotNull(groups = {Submit.class}, message = "判断题数量不能为空")
    @ApiModelProperty(value = "判断题数量", name = "judgingQuestionNum")
    private Integer judgingQuestionNum;

    /**
     * 问答题数量
     */
    @NotNull(groups = {Submit.class}, message = "问答题数量不能为空")
    @ApiModelProperty(value = "问答题数量", name = "essayQuestionNum")
    private Integer essayQuestionNum;

    /**
     * 选择题正确率
     */
    @NotNull(groups = {Submit.class}, message = "选择题正确率不能为空")
    @ApiModelProperty(value = "选择题正确率", name = "choiceQuestionAccuracy")
    private BigDecimal choiceQuestionAccuracy;

    /**
     * 问答题匹配度
     */
    @NotNull(groups = {Submit.class}, message = "问答题匹配度不能为空")
    @ApiModelProperty(value = "问答题匹配度", name = "textQuestionMatchingDegree")
    private BigDecimal textQuestionMatchingDegree;

}