package com.deloitte.dhr.ai.module.coach.teach.mapper;

import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachTestQuestion;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachTextQuestionResponse;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程资源-教学课程-考核题目-相关持久化接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTeachTestQuestionMapper extends SuperMapper<AiTeachTestQuestion> {

    /**
     * 查询-文本题信息
     *
     * @param ids 试题ID
     * @return List<AiTeachTextQuestionResponse>
     */
    List<AiTeachTextQuestionResponse> getTextQuestionByIds(@Param("ids") List<Long> ids);

}