package com.deloitte.dhr.ai.module.ai.strategy.digital.pojo;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.deloitte.dhr.ai.enums.DigitalLiningIntentAction;
import com.deloitte.dhr.ai.enums.DigitalLiningIntentItem;
import com.deloitte.dhr.ai.enums.DigitalLiningIntentType;
import lombok.Builder;
import lombok.Data;

/**
 * 数字人-李宁
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Data
@Builder
public class DigitalLiningIntentResponse {

    /**
     * 类型 {@link DigitalLiningIntentType #name}
     */
    @JSONField(name = "intentType")
    private String intentType;

    /**
     * 类型子项 {@link DigitalLiningIntentItem #name}
     */
    @JSONField(name = "intentItem")
    private String intentItem;

    /**
     * 执行动作 {@link DigitalLiningIntentAction #code}
     */
    @JSONField(name = "intentAction")
    private String intentAction;

    /**
     * 意图识别解析出来的数据
     */
    @JSONField(name = "intentData")
    private JSONObject intentData;

}
