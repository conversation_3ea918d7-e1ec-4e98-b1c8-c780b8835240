package com.deloitte.dhr.ai.module.ai.strategy.transform;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiTransformType;
import com.deloitte.dhr.ai.module.ai.annotation.TransformStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiFileResponse;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceAttachResponse;
import com.deloitte.dhr.ai.module.coach.resource.service.AiCourseResourceAttachService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AI转换-知识库-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@TransformStrategyType(AiTransformType.DATASET)
public class DatasetTransformStrategy implements ResponseTransformStrategy {

    @Autowired
    private AiCourseResourceAttachService aiCourseResourceAttachService;

    @Override
    public Flux<List<AiContent>> transformStream(Flux<String> rawStream, AiRequest aiRequest) {
        return rawStream
                .filter(raw -> {
                    String event = JSONObject.parseObject(raw).getString("event");
                    return StrUtil.equals(event, "message") || StrUtil.equals(event, "message_end");
                })
                .map(raw -> {
                    List<AiContent> contents = new ArrayList<>();
                    JSONObject rawObj = JSONObject.parseObject(raw);

                    // 文本回复信息
                    String answer = rawObj.getString("answer");
                    if (StrUtil.isNotBlank(answer)) {
                        contents.add(AiContent.builder()
                                .contentType(AiContentType.TEXT.getCode())
                                .content(rawObj.getString("answer"))
                                .build());
                    }

                    // 知识库retriever信息
                    JSONObject metadata = rawObj.getJSONObject("metadata");
                    if (metadata != null && metadata.getJSONArray("retriever_resources") != null) {
                        Map<String, List<Map<String, Object>>> retrieverMessageMap = metadata.getJSONArray("retriever_resources").stream()
                                .collect(Collectors.groupingBy(a -> ((JSONObject) a).getString("document_id"), Collectors.mapping(BeanUtil::beanToMap, Collectors.toList())));
                        Map<String, AiCourseResourceAttachResponse> fileRef = aiCourseResourceAttachService.getByDatasetsFileIds(new ArrayList<>(retrieverMessageMap.keySet())).stream()
                                .collect(Collectors.toMap(AiCourseResourceAttachResponse::getDatasetsFileId, a -> a, (k1, k2) -> k1));
                        List<AiFileResponse> retrieverDocuments = new ArrayList<>();
                        retrieverMessageMap.forEach((documentId, list) -> {
                            AiFileResponse retrieverDocument = AiFileResponse.builder()
                                    .fileContent(StrUtil.join("\n", list.stream().map(a -> a.get("content")).collect(Collectors.toList())))
                                    .fileName(String.valueOf(list.get(0).get("document_name")))
                                    .fileType(FileNameUtil.getSuffix(String.valueOf(list.get(0).get("document_name"))))
                                    .build();
                            AiCourseResourceAttachResponse fileInfo = fileRef.get(documentId);
                            retrieverDocuments.add(retrieverDocument);
                            if (fileInfo == null) {
                                return;
                            }
                            BeanUtil.copyProperties(fileInfo, retrieverDocument);
                        });
                        contents.add(AiContent.builder()
                                .contentType(AiContentType.FILE.getCode())
                                .content(JSONObject.toJSONString(retrieverDocuments))
                                .build());
                    }
                    return contents;
                });
    }

    @Override
    public Flux<List<AiContent>> transformBlock(Flux<String> rawStream, AiRequest aiRequest) {
        return transformStream(rawStream, aiRequest);
    }

}
