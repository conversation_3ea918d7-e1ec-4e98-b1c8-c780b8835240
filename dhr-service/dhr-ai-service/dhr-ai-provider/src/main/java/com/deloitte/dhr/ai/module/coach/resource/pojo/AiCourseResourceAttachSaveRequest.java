package com.deloitte.dhr.ai.module.coach.resource.pojo;


import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 课程资源-附件-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiCourseResourceAttachSaveRequest")
public class AiCourseResourceAttachSaveRequest {

    /**
     * 文件ID
     */
    @NotBlank(groups = {Save.class, Submit.class}, message = "文件ID不能为空")
    @Length(groups = {Save.class, Submit.class}, max = 32, message = "文件ID不能超过32个字符")
    @ApiModelProperty(value = "文件ID", name = "fileId")
    private String fileId;

    /**
     * 文件名称
     */
    @NotBlank(groups = {Save.class, Submit.class}, message = "文件名称不能为空")
    @Length(groups = {Save.class, Submit.class}, max = 80, message = "文件名称不能超过80个字符")
    @ApiModelProperty(value = "文件名称", name = "fileName")
    private String fileName;

    /**
     * 文件类型
     */
    @NotBlank(groups = {Save.class, Submit.class}, message = "文件类型不能为空")
    @Length(groups = {Save.class, Submit.class}, max = 10, message = "文件类型不能超过10个字符")
    @ApiModelProperty(value = "文件类型", name = "fileType")
    private String fileType;

    /**
     * 文件md5值
     */
    @Length(groups = {Save.class, Submit.class}, max = 40, message = "文件md5值不能超过40个字符")
    @ApiModelProperty(value = "文件md5值", name = "fileMd5")
    private String fileMd5;

    /**
     * 文件URL
     */
    @Length(groups = {Save.class, Submit.class}, max = 1024, message = "文件地址不能超过1024个字符")
    @ApiModelProperty(value = "文件URL", name = "fileUrl")
    private String fileUrl;

    /**
     * 文件大小
     */
    @Length(groups = {Save.class, Submit.class}, max = 20, message = "文件大小不能超过20个字符")
    @ApiModelProperty(value = "文件大小", name = "fileSize")
    private String fileSize;

}