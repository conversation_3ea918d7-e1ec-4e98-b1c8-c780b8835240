package com.deloitte.dhr.ai.module.ai.strategy.ssc;

import com.alibaba.fastjson2.JSON;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiSscIntentAction;
import com.deloitte.dhr.ai.enums.AiSscIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.ActionStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.strategy.ssc.pojo.AttendanceExceptionTqResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;


/**
 * SSC-考勤异常-事务查询-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@ActionStrategyType(item = AiSscIntentItem.KQYC, action = AiSscIntentAction.TQ)
public class AttendanceExceptionTqStrategy implements SscStrategy {

    @Autowired
    private CommonTqService commonTqService;

    @Override
    public Flux<List<AiContent>> executeStream(Map<String, Object> param) {
        List<AttendanceExceptionTqResponse> responses = commonTqService.queryMyAttendanceExceptionInfo();
        AiContent content = AiContent.builder()
                .contentType(AiContentType.JSON.getCode())
                .content(JSON.toJSONString(responses))
                .build();
        return Flux.just(List.of(content));
    }
}
