package com.deloitte.dhr.ai.module.coach.category.controller;

import com.deloitte.dhr.ai.module.coach.category.pojo.AiCourseCategoryResponse;
import com.deloitte.dhr.ai.module.coach.category.pojo.AiCourseCategorySaveRequest;
import com.deloitte.dhr.ai.module.coach.category.pojo.AiCourseCategoryTreeResponse;
import com.deloitte.dhr.ai.module.coach.category.service.AiCourseCategoryService;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 课程类别
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/AiCourseCategory")
@Api(tags = "课程类别")
@Validated
public class AiCourseCategoryController extends SuperController {

    @Autowired
    private AiCourseCategoryService aiCourseCategoryService;

    @ApiOperation(value = "详情", notes = "详情")
    @ApiOperationSupport(order = 1)
    @GetMapping("/detail/{id}")
    public ResponseVO<AiCourseCategoryResponse> getDetail(@PathVariable("id") Long id) {
        return success(aiCourseCategoryService.getDetail(id));
    }

    @ApiOperation(value = "列表树", notes = "列表树")
    @ApiOperationSupport(order = 2)
    @GetMapping("/tree")
    public ResponseVO<List<AiCourseCategoryTreeResponse>> getTree(@RequestParam(value = "categoryName", required = false) String categoryName) {
        return success(aiCourseCategoryService.getTree(categoryName));
    }

    @ApiOperation(value = "保存", notes = "保存")
    @ApiOperationSupport(order = 3)
    @PostMapping("/save")
    public ResponseVO<Long> save(@RequestBody @Validated AiCourseCategorySaveRequest request) {
        return success(aiCourseCategoryService.saveData(request));
    }

    @ApiOperation(value = "删除", notes = "删除")
    @ApiOperationSupport(order = 4)
    @PostMapping("/del")
    public ResponseVO<Boolean> del(@RequestParam("id") Long id) {
        return success(aiCourseCategoryService.deleteById(id));
    }

}
