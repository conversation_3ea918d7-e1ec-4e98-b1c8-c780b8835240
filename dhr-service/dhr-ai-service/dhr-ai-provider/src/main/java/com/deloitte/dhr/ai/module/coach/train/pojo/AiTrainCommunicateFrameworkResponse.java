package com.deloitte.dhr.ai.module.coach.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 课程资源-AI陪练-沟通框架信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainCommunicateFrameworkResponse")
public class AiTrainCommunicateFrameworkResponse {

    /**
     * 沟通主题
     */
    @ApiModelProperty(value = "沟通主题", name = "communicateSubject")
    private String communicateSubject;

    /**
     * 沟通目标
     */
    @ApiModelProperty(value = "沟通目标", name = "communicateObjective")
    private String communicateObjective;

    /**
     * 评分标准
     */
    @ApiModelProperty(value = "评分标准", name = "gradingCriteria")
    private String gradingCriteria;

    /**
     * 评分百分比
     */
    @ApiModelProperty(value = "评分百分比", name = "weight")
    private BigDecimal weight;

}