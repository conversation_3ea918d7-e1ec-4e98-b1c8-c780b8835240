package com.deloitte.dhr.ai.module.coach.train.mapper;

import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainMyAbilityAnalyse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyAbilityScoreResponse;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程资源-AI陪练-我的能力分析-相关持久化接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTrainMyAbilityAnalyseMapper extends SuperMapper<AiTrainMyAbilityAnalyse> {

    /**
     * 查询-我的对话分析
     *
     * @param historyId 对话历史记录ID
     * @return List<AiTrainMyAbilityScoreResponse>
     */
    List<AiTrainMyAbilityScoreResponse> getMyDialogueAnalyse(@Param("historyId") Long historyId);

}