package com.deloitte.dhr.ai.module.ai.strategy.digital.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数字人(李宁)-人事档案-事务查询
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("DigitalLiningPersonnelFilesTqResponse")
public class DigitalLiningPersonnelFilesTqResponse {

    @ApiModelProperty(value = "头信息", name = "headerInfo")
    private HeaderInfo headerInfo;

    @ApiModelProperty(value = "AI模型输出内容", name = "content")
    private String content;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HeaderInfo {

        @ApiModelProperty(value = "员工编号", name = "empCode")
        private String empCode;

        @ApiModelProperty(value = "员工姓名", name = "empName")
        private String empName;

        @ApiModelProperty(value = "员工组", name = "groupName")
        private String groupName;
    }

}
