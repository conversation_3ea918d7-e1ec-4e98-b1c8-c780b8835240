package com.deloitte.dhr.ai.module.interview.train.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * AI访谈-访谈任务-对话问题表
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_interview_dialogue_question")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AI访谈-对话问题表")
public class AiInterviewDialogueQuestion extends SuperLogicModel<AiInterviewDialogueQuestion> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * AI题目语音文件URL
     */
    @TableField(value = "ai_question_voice_file_url")
    private String aiQuestionVoiceFileUrl;

    /**
     * 沟通主题
     */
    @TableField(value = "interview_question")
    private String interviewQuestion;

    /**
     * 题目内容
     */
    @TableField(value = "interview_objective")
    private String interviewObjective;

    /**
     * 题目顺序
     */
    @TableField(value = "question_order")
    private Integer questionOrder;

}