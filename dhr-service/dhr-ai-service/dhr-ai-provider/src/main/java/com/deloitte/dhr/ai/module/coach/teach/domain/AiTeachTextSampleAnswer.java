package com.deloitte.dhr.ai.module.coach.teach.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 课程资源-教学课程-考核题目-文本题参考答案表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_teach_text_sample_answer")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-教学课程-考核题目-文本题参考答案表")
public class AiTeachTextSampleAnswer extends SuperLogicModel<AiTeachTextSampleAnswer> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 题目ID
     */
    @TableField(value = "question_id")
    private Long questionId;

    /**
     * 参考答案
     */
    @TableField(value = "sample_answer_content")
    private String sampleAnswerContent;

}