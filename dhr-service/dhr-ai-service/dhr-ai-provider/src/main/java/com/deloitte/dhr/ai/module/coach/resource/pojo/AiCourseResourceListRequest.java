package com.deloitte.dhr.ai.module.coach.resource.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 课程资源-列表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiCourseResourceListRequest")
public class AiCourseResourceListRequest {

    /**
     * 类别ID
     */
    @ApiModelProperty(value = "类别ID", name = "categoryId")
    private Long categoryId;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称", name = "courseName")
    private String courseName;

    /**
     * 课程分类;JXKC：教学课程，AIPL：AI陪练
     */
    @ApiModelProperty(value = "课程分类;JXKC：教学课程，AIPL：AI陪练", name = "courseType")
    private String courseType;

    /**
     * 课程状态
     */
    @ApiModelProperty(value = "课程状态", name = "courseStatus")
    private String courseStatus;

    /**
     * 类别全路径
     */
    @JsonIgnore
    @ApiModelProperty(value = "类别全路径", name = "categoryPath")
    private String categoryPath;

}