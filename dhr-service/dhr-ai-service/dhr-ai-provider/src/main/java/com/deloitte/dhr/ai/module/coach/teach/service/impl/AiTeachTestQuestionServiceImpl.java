package com.deloitte.dhr.ai.module.coach.teach.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachChoiceOption;
import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachTestQuestion;
import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachTextSampleAnswer;
import com.deloitte.dhr.ai.module.coach.teach.mapper.AiTeachInfoMapper;
import com.deloitte.dhr.ai.module.coach.teach.mapper.AiTeachTestQuestionMapper;
import com.deloitte.dhr.ai.module.coach.teach.pojo.*;
import com.deloitte.dhr.ai.module.coach.teach.service.*;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 课程资源-教学课程-考核题目-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiTeachTestQuestionServiceImpl extends SuperServiceImpl<AiTeachTestQuestionMapper, AiTeachTestQuestion> implements AiTeachTestQuestionService {

    @Autowired
    private AiTeachTestQuestionMapper aiTeachTestQuestionMapper;
    @Autowired
    private AiTeachChoiceOptionService aiTeachChoiceOptionService;
    @Autowired
    private AiTeachTextSampleAnswerService aiTeachTextSampleAnswerService;
    @Autowired
    private AiTeachMyChoiceAnswerService aiTeachMyChoiceAnswerService;
    @Autowired
    private AiTeachMyTextAnswerService aiTeachMyTextAnswerService;
    @Autowired
    private AiTeachInfoMapper aiTeachInfoMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings("unchecked")
    public Boolean saveData(List<AiTeachTestQuestionSaveRequest> saveRequestList, Long resourceId) {
        // 删除旧的题目相关数据
        this.deleteAllByResourceId(resourceId);

        if (CollUtil.isEmpty(saveRequestList)) {
            return false;
        }
        List<AiTeachChoiceOption> choiceOptionList = new ArrayList<>();
        List<AiTeachTextSampleAnswer> textSampleAnswerList = new ArrayList<>();
        // 保存新的题目数据
        for (int i = 0, saveRequestListSize = saveRequestList.size(); i < saveRequestListSize; i++) {
            AiTeachTestQuestionSaveRequest saveQuestion = saveRequestList.get(i);
            AiTeachTestQuestion question = BeanUtil.copyProperties(saveQuestion, AiTeachTestQuestion.class);
            question.setResourceId(resourceId);
            question.setQuestionOrder(i + 1);
            // 保存题目来源信息
            if (Objects.nonNull(saveQuestion.getQuestionSource())) {
                question.setQuestionSource(JSON.toJSONString(saveQuestion.getQuestionSource()));
            }
            if (!this.save(question) || Objects.isNull(saveQuestion.getQuestionContent())) {
                continue;
            }
            switch (question.getQuestionType()) {
                // 构建单选题、判断题保存对象
                case AiCoachConstant.TeachQuestionType.SINGLE_CHOICE_QUESTION:
                case AiCoachConstant.TeachQuestionType.TRUE_OR_FALSE_QUESTION:
                    List<AiTeachChoiceOption> optionList = BeanUtil.copyToList((List<AiTeachChoiceOptionSaveRequest>) saveQuestion.getQuestionContent(), AiTeachChoiceOption.class);
                    for (int j = 0, optionListSize = optionList.size(); j < optionListSize; j++) {
                        AiTeachChoiceOption option = optionList.get(j);
                        option.setResourceId(resourceId);
                        option.setQuestionId(question.getId());
                        option.setOptionSort(j + 1);
                    }
                    choiceOptionList.addAll(optionList);
                    break;
                // 构建文本题保存对象
                case AiCoachConstant.TeachQuestionType.ESSAY_QUESTION:
                    AiTeachTextSampleAnswer textSampleAnswer = BeanUtil.copyProperties(saveQuestion.getQuestionContent(), AiTeachTextSampleAnswer.class);
                    textSampleAnswer.setResourceId(resourceId);
                    textSampleAnswer.setQuestionId(question.getId());
                    textSampleAnswerList.add(textSampleAnswer);
                    break;
                default:
                    throw new CommRunException("非法的考核类型题目");
            }
        }
        // 保存新的选择题选项数据
        aiTeachChoiceOptionService.saveBatch(choiceOptionList);

        // 保存新的文本题数据
        aiTeachTextSampleAnswerService.saveBatch(textSampleAnswerList);

        return true;
    }

    @Override
    public void deleteAllByResourceId(Long resourceId) {
        // 删除题目数据
        LambdaQueryWrapper<AiTeachTestQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTeachTestQuestion::getResourceId, resourceId);
        aiTeachTestQuestionMapper.delete(queryWrapper);

        // 删除题目选项数据
        aiTeachChoiceOptionService.deleteByResourceId(resourceId);

        // 删除题目文本题参考答案数据
        aiTeachTextSampleAnswerService.deleteByResourceId(resourceId);

    }

    @Override
    public List<AiTeachTestQuestionResponse> getByResourceId(Long resourceId) {
        // 题目数据
        LambdaQueryWrapper<AiTeachTestQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTeachTestQuestion::getResourceId, resourceId);
        queryWrapper.orderByAsc(AiTeachTestQuestion::getQuestionOrder);
        List<AiTeachTestQuestion> questionList = aiTeachTestQuestionMapper.selectList(queryWrapper);

        // 选择题选项数据
        Map<Long, List<AiTeachChoiceOptionResponse>> optionMap = aiTeachChoiceOptionService.getMapByResourceId(resourceId);

        // 文本题参考答案数据
        Map<Long, AiTeachTextSampleAnswerResponse> textMap = aiTeachTextSampleAnswerService.getMapByResourceId(resourceId);

        // 组装数据
        List<AiTeachTestQuestionResponse> responseList = new ArrayList<>();
        questionList.forEach(question -> {
            AiTeachTestQuestionResponse response = BeanUtil.copyProperties(question, AiTeachTestQuestionResponse.class, "questionSource");
            // 把“问题来源”字段转换成Object对象，直接返回，不做处理
            if (StrUtil.isNotBlank(question.getQuestionSource())) {
                response.setQuestionSource(JSONObject.parseObject(question.getQuestionSource(), AiTeachTestQuestionSourceResponse.class));
            }
            // 为了适配前端，把各种题目类型的题目内容都用一个Object对象返回
            switch (question.getQuestionType()) {
                case AiCoachConstant.TeachQuestionType.SINGLE_CHOICE_QUESTION:
                case AiCoachConstant.TeachQuestionType.TRUE_OR_FALSE_QUESTION:
                    response.setChoiceQuestionContent(optionMap.get(question.getId()));
                    response.setQuestionContent(optionMap.get(question.getId()));
                    break;
                case AiCoachConstant.TeachQuestionType.ESSAY_QUESTION:
                    response.setTextQuestionContent(textMap.get(question.getId()));
                    response.setQuestionContent(textMap.get(question.getId()));
                    break;
                default:
                    throw new CommRunException("非法的考核类型题目");
            }
            responseList.add(response);
        });
        return responseList;
    }

    @Override
    public List<AiTeachMyTestQuestionResponse> getMyQuestionAndAnswer(Long resourceId, Long taskEmpId, Boolean hideAnswer) {

        // 查询考核题目信息
        List<AiTeachTestQuestionResponse> questionList = getByResourceId(resourceId);
        List<AiTeachMyTestQuestionResponse> myQuestionList = BeanUtil.copyToList(questionList, AiTeachMyTestQuestionResponse.class);

        // 查询我的选择题选项答案信息
        List<AiTeachMyChoiceAnswerResponse> myChoiceAnswerList = aiTeachMyChoiceAnswerService.getByTaskEmpId(taskEmpId);
        Map<Long, AiTeachMyChoiceAnswerResponse> myChoiceMap = myChoiceAnswerList.stream().collect(Collectors.toMap(AiTeachMyChoiceAnswerResponse::getQuestionId, k -> k, (k1, k2) -> k1));

        // 查询我的文本题答案信息
        List<AiTeachMyTextAnswerResponse> myTextAnswerList = aiTeachMyTextAnswerService.getByTaskEmpId(taskEmpId);
        Map<Long, AiTeachMyTextAnswerResponse> myTextMap = myTextAnswerList.stream().collect(Collectors.toMap(AiTeachMyTextAnswerResponse::getQuestionId, k -> k, (k1, k2) -> k1));

        // 查询所属教学课程的匹配度信息
        BigDecimal matchingDegree = aiTeachInfoMapper.getMatchingDegree(resourceId);

        // 组装数据
        myQuestionList.forEach(question -> {
            // 为了适配前端，把各种题目类型的题目内容都用一个Object对象返回
            switch (question.getQuestionType()) {
                case AiCoachConstant.TeachQuestionType.SINGLE_CHOICE_QUESTION:
                case AiCoachConstant.TeachQuestionType.TRUE_OR_FALSE_QUESTION:
                    List<AiTeachMyChoiceOptionResponse> myChoiceQuestionContent = BeanUtil.copyToList(question.getChoiceQuestionContent(), AiTeachMyChoiceOptionResponse.class);
                    question.setQuestionContent(myChoiceQuestionContent);
                    AiTeachMyChoiceAnswerResponse myChoiceAnswer = myChoiceMap.get(question.getId());
                    if (myChoiceAnswer == null) {
                        myChoiceQuestionContent.forEach(choice -> choice.setIsAnswer(hideAnswer ? null : choice.getIsAnswer()));
                        return;
                    }
                    // 未提交的培训答题任务，不返回该题的正确答案以及我是否答题正确
                    myChoiceQuestionContent.forEach(choice -> {
                        choice.setIsAnswer(hideAnswer ? null : choice.getIsAnswer());
                        choice.setIsMyAnswer(myChoiceAnswer.getOptionIds().contains(choice.getId()));
                    });
                    question.setResult(hideAnswer ? null : myChoiceAnswer.getIsRight());
                    break;
                case AiCoachConstant.TeachQuestionType.ESSAY_QUESTION:
                    AiTeachMyTextSampleAnswerResponse myTextQuestionContent = BeanUtil.copyProperties(question.getTextQuestionContent(), AiTeachMyTextSampleAnswerResponse.class);
                    question.setMyTextQuestionContent(myTextQuestionContent);
                    question.setQuestionContent(myTextQuestionContent);
                    myTextQuestionContent.setSampleAnswerContent(hideAnswer ? null : myTextQuestionContent.getSampleAnswerContent());
                    AiTeachMyTextAnswerResponse myTextAnswer = myTextMap.get(question.getId());
                    if (myTextAnswer == null) {
                        return;
                    }
                    myTextQuestionContent.setMyAnswerContent(myTextAnswer.getAnswerContent());
                    myTextQuestionContent.setAiMatchingDegree(myTextAnswer.getAiMatchingDegree() == null ? BigDecimal.ZERO : myTextAnswer.getAiMatchingDegree().setScale(0, RoundingMode.HALF_UP));
                    question.setResult(hideAnswer ? null : matchingDegree.compareTo(myTextAnswer.getAiMatchingDegree()) <= 0);
                    break;
                default:
                    throw new CommRunException("非法的考核类型题目");
            }
        });
        return myQuestionList;

    }

    @Override
    public void verify(List<AiTeachTestQuestionSaveRequest> saveRequestList) {
        if (CollUtil.isEmpty(saveRequestList)) {
            return;
        }

        // 校验-考核题目名称
        List<String> errorMsgList = new ArrayList<>();
        Map<String, List<AiTeachTestQuestionSaveRequest>> questionMap = saveRequestList.stream().collect(Collectors.groupingBy(AiTeachTestQuestionSaveRequest::getQuestionName));
        questionMap.forEach((questionName, list) -> {
            if (list.size() == 1) {
                return;
            }
            String errorMsg = StrUtil.join("和", list.stream().map(AiTeachTestQuestionSaveRequest::getQuestionOrder).collect(Collectors.toList()));
            errorMsgList.add(errorMsg);
        });
        if (CollUtil.isEmpty(errorMsgList)) {
            return;
        }
        String questionSortStr = StrUtil.join("，", errorMsgList);
        throw new CommRunException("题目序号【" + questionSortStr + "】中含有重复的题目名称");

    }

}

