package com.deloitte.dhr.ai.module.coach.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.coach.category.service.AiCourseCategoryService;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.task.domain.AiCoachTaskEmp;
import com.deloitte.dhr.ai.module.coach.task.domain.AiCoachTaskInfo;
import com.deloitte.dhr.ai.module.coach.task.mapper.AiCoachTaskInfoMapper;
import com.deloitte.dhr.ai.module.coach.task.pojo.*;
import com.deloitte.dhr.ai.module.coach.task.service.AiCoachTaskEmpService;
import com.deloitte.dhr.ai.module.coach.task.service.AiCoachTaskInfoService;
import com.deloitte.dhr.ai.utils.CheckUtils;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.pojo.UserDto;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * 培训任务-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiCoachTaskInfoServiceImpl extends SuperServiceImpl<AiCoachTaskInfoMapper, AiCoachTaskInfo> implements AiCoachTaskInfoService {

    @Autowired
    private AiCoachTaskInfoMapper aiCoachTaskInfoMapper;
    @Autowired
    private AiCoachTaskEmpService aiCoachTaskEmpService;
    @Autowired
    private AiCourseCategoryService aiCourseCategoryService;

    @Override
    public AiTeachTaskDetailResponse getTeachTaskDetail(Long id) {
        AiTeachTaskDetailResponse teachTaskDetail = aiCoachTaskInfoMapper.getTeachTaskDetail(id);
        teachTaskDetail.setCategoryName(aiCourseCategoryService.getCategoryPathNameById(teachTaskDetail.getCategoryId()));
        return teachTaskDetail;
    }

    @Override
    public AiTrainTaskDetailResponse getTrainTaskDetail(Long id) {
        AiTrainTaskDetailResponse trainTaskDetail = aiCoachTaskInfoMapper.getTrainTaskDetail(id);
        trainTaskDetail.setCategoryName(aiCourseCategoryService.getCategoryPathNameById(trainTaskDetail.getCategoryId()));
        return trainTaskDetail;
    }

    @Override
    public ResponsePage<AiTaskResponse> findTaskListPage(Page<AiCoachTaskInfo> page, AiTaskListRequest request, BaseOrder order) {
        List<AiTaskResponse> list = aiCoachTaskInfoMapper.findListPage(page, request, this.adjustOrder(order));
        list.forEach(response -> {
            response.setCompletedEmpNum(response.getCompletedEmpNum() == null ? 0 : response.getCompletedEmpNum());
            response.setUnCompletedEmpNum(response.getUnCompletedEmpNum() == null ? 0 : response.getUnCompletedEmpNum());
        });
        return new ResponsePage<>(page, list);
    }

    @Override
    public ResponsePage<AiMyTaskListResponse> myTaskList(Page<AiCoachTaskInfo> page, AiMyTaskListRequest request, BaseOrder order) {
        UserDto loginUser = LoginUtil.getLoginUser();
        if (loginUser == null || StrUtil.isBlank(loginUser.getUsername())) {
            return new ResponsePage<>(page, Collections.emptyList());
        }
        request.setLoginEmpCode(loginUser.getUsername());
        List<AiMyTaskListResponse> list = aiCoachTaskInfoMapper.myTaskList(page, request, this.adjustOrder(order));
        return new ResponsePage<>(page, list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveData(AiCoachTaskSaveRequest request, String taskStatus) {

        // 校验-培训任务保存信息
        this.verify(request);

        // 保存/更新-培训任务-基本信息
        AiCoachTaskInfo taskInfo = BeanUtil.copyProperties(request.getTaskInfo(), AiCoachTaskInfo.class);
        taskInfo.setTaskStatus(taskStatus);
        this.saveOrUpdate(taskInfo);

        // 保存/更新-培训任务-员工基本信息
        aiCoachTaskEmpService.saveData(request.getTaskEmpInfoList(), taskInfo.getId(), taskInfo.getResourceId());

        return taskInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long publishData(AiCoachTaskSaveRequest request) {
        return this.saveData(request, AiCoachConstant.TaskStatus.ONGOING);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        AiCoachTaskInfo taskInfo = aiCoachTaskInfoMapper.selectById(id);
        CheckUtils.checkNull(taskInfo, "该数据不存在");
        if (!StrUtil.equals(taskInfo.getTaskStatus(), AiCoachConstant.TaskStatus.DRAFT)) {
            throw new CommRunException("已提交数据不能删除");
        }

        // 删除-培训任务-基本信息
        this.removeById(id);

        // 删除-培训任务-员工信息
        aiCoachTaskEmpService.deleteByTaskId(id);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean closeTask(Long id) {

        // 修改任务状态为已结束
        AiCoachTaskInfo taskInfo = aiCoachTaskInfoMapper.selectById(id);
        taskInfo.setTaskStatus(AiCoachConstant.TaskStatus.CLOSE);
        aiCoachTaskInfoMapper.updateById(taskInfo);

        // 修改非已完成员工的状态为已结束
        LambdaQueryWrapper<AiCoachTaskEmp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCoachTaskEmp::getTaskId, id);
        queryWrapper.ne(AiCoachTaskEmp::getStatus, AiCoachConstant.TaskEmpStatus.COMPLETED);
        List<AiCoachTaskEmp> empList = aiCoachTaskEmpService.list(queryWrapper);
        if (CollUtil.isEmpty(empList)) {
            return true;
        }
        empList.forEach(emp -> emp.setStatus(AiCoachConstant.TaskEmpStatus.CLOSE));
        return aiCoachTaskEmpService.saveOrUpdateBatch(empList);
    }

    /**
     * 校验-培训任务
     *
     * @param request 培训任务保存信息
     */
    public void verify(AiCoachTaskSaveRequest request) {

        // 校验-是否允许修改
        this.verifyTaskModify(request.getTaskInfo());

        // 校验-培训任务名称
        this.verifyTaskName(request.getTaskInfo());

        // 校验-培训任务员工
        aiCoachTaskEmpService.verifyTaskEmp(request.getTaskEmpInfoList());

    }

    /**
     * 校验-培训任务是否允许修改
     *
     * @param saveRequest 培训任务基本保存信息
     */
    private void verifyTaskModify(AiCoachTaskInfoSaveRequest saveRequest) {
        // 校验-是否允许修改
        if (saveRequest.getId() == null) {
            return;
        }
        LambdaQueryWrapper<AiCoachTaskInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCoachTaskInfo::getId, saveRequest.getId());
        queryWrapper.ne(AiCoachTaskInfo::getTaskStatus, AiCoachConstant.TaskStatus.DRAFT);
        Integer existSubmit = aiCoachTaskInfoMapper.selectCount(queryWrapper);
        if (existSubmit > 0) {
            throw new CommRunException("无法修改已提交数据");
        }
    }

    /**
     * 校验-培训任务名称是否重复
     *
     * @param saveRequest 培训任务基本保存信息
     */
    private void verifyTaskName(AiCoachTaskInfoSaveRequest saveRequest) {
        // 校验-培训任务名称
        LambdaQueryWrapper<AiCoachTaskInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCoachTaskInfo::getTaskName, saveRequest.getTaskName());
        queryWrapper.ne(saveRequest.getId() != null, AiCoachTaskInfo::getId, saveRequest.getId());
        Integer existTaskName = aiCoachTaskInfoMapper.selectCount(queryWrapper);
        if (existTaskName > 0) {
            throw new CommRunException("培训任务名称已存在");
        }
    }

}

