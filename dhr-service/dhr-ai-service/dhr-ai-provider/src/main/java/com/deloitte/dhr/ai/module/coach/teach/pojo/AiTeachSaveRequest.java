package com.deloitte.dhr.ai.module.coach.teach.pojo;

import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceAttachSaveRequest;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceInfoSaveRequest;
import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 课程资源-教学课程-基本信息-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachSaveRequest")
public class AiTeachSaveRequest {

    /**
     * 课程资源-基本信息
     */
    @Valid
    @ApiModelProperty(value = "课程资源-基本信息", name = "resourceInfo")
    @NotNull(groups = {Save.class, Submit.class}, message = "课程资源基本信息不能为空")
    private AiCourseResourceInfoSaveRequest resourceInfo;

    /**
     * 课程资源-学习附件信息
     */
    @Valid
    @NotEmpty(groups = Submit.class, message = "学习资料不能为空")
    @ApiModelProperty(value = "课程资源-学习附件信息", name = "attachInfoList")
    private List<AiCourseResourceAttachSaveRequest> attachInfoList;

    /**
     * 教学课程-基本信息
     */
    @Valid
    @NotNull(groups = {Save.class, Submit.class}, message = "教学课程基本信息不能为空")
    @ApiModelProperty(value = "教学课程-基本信息", name = "teachInfo")
    private AiTeachInfoSaveRequest teachInfo;

    /**
     * 教学课程-考核内容（题目信息）
     */
    @Valid
    @NotEmpty(groups = Submit.class, message = "考核题目不能为空")
    @ApiModelProperty(value = "教学课程-考核内容（题目信息）", name = "questionInfoList")
    private List<AiTeachTestQuestionSaveRequest> questionInfoList;

}