package com.deloitte.dhr.ai.module.interview.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * AI访谈-访谈任务-洞察报告
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@Builder
@ApiModel("AiInterviewTaskMyReportResponse")
@AllArgsConstructor
@NoArgsConstructor
public class AiInterviewTaskMyReportResponse {

    /**
     * 访谈主题
     */
    @ApiModelProperty(value = "访谈主题", name = "courseName")
    private String courseName;

    /**
     * 聊天记录
     */
    @ApiModelProperty(value = "聊天记录", name = "historyList")
    private List<AiInterviewMyDialogueHistoryResponse> historyList;

    /**
     * 访谈时长
     */
    @ApiModelProperty(value = "访谈时长", name = "interviewTime")
    private String interviewTime;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间", name = "doneTime")
    private String doneTime;


    /**
     * 沟通状态
     */
    @ApiModelProperty(value = "沟通状态", name = "gtSatstus")
    private String gtSatstus;

    /**
     * 完成度
     */
    @ApiModelProperty(value = "完成度", name = "wcdRate")
    private String wcdRate;

    /**
     * 回答积极度
     */
    @ApiModelProperty(value = "回答积极度", name = "hdjjScore")
    private String hdjjScore;

    /**
     * 表达逻辑
     */
    @ApiModelProperty(value = "表达逻辑", name = "bdljScore")
    private String bdljScore;


    /**
     * 总体
     */
    @ApiModelProperty(value = "总体", name = "analyse01")
    private String analyse01;

    /**
     * 访谈摘要
     */
    @ApiModelProperty(value = "访谈摘要", name = "analyse02")
    private String analyse02;

    /**
     * 后续关注点
     */
    @ApiModelProperty(value = "后续关注点", name = "analyse03")
    private String analyse03;
}