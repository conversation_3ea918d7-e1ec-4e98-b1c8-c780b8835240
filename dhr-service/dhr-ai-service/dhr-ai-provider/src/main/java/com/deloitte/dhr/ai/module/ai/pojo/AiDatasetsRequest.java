package com.deloitte.dhr.ai.module.ai.pojo;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * AI知识库
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@Builder
public class AiDatasetsRequest {

    @J<PERSON><PERSON>ield(name = "indexing_technique")
    private String indexingTechnique;

    @JSONField(name = "process_rule")
    private ProcessRule processRule;

    @Data
    @Builder
    public static class ProcessRule {

        private Rules rules;

        private String mode;

    }

    @Data
    @Builder
    public static class Rules {

        @JSONField(name = "pre_processing_rules")
        private List<PreProcessingRule> preProcessingRules;

        private Segmentation segmentation;
    }

    @Data
    @Builder
    public static class PreProcessingRule {

        private String id;

        private boolean enabled;
    }

    @Data
    @Builder
    public static class Segmentation {

        private String separator;

        @JSONField(name = "max_tokens")
        private Integer maxTokens;

    }

}


