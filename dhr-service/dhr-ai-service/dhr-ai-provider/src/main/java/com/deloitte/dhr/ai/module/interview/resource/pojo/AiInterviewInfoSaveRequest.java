package com.deloitte.dhr.ai.module.interview.resource.pojo;


import com.deloitte.dhr.ai.module.interview.validation.Save;
import com.deloitte.dhr.ai.module.interview.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * AI访谈-基本信息-保存
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewInfoSaveRequest")
public class AiInterviewInfoSaveRequest {

    /**
     * 课程资源ID
     */
    @ApiModelProperty(value = "课程资源ID", name = "id")
    private Long id;

    /**
     * 类别ID
     */
    @NotNull(groups = {Save.class, Submit.class}, message = "课程类别不能为空")
    @ApiModelProperty(value = "类别ID", name = "categoryId")
    private Long categoryId;

    /**
     * 课程名称
     */
    @NotBlank(groups = {Save.class, Submit.class}, message = "课程名称不能为空")
    @Length(groups = {Save.class, Submit.class}, max = 50, message = "课程名称不能超过50个字符")
    @ApiModelProperty(value = "课程名称", name = "courseName")
    private String courseName;

    /**
     * 课程封面图链接
     */
    @NotBlank(groups = {Save.class, Submit.class}, message = "课程封面不能为空")
    @Length(groups = {Save.class, Submit.class}, max = 255, message = "课程封面图链接不能超过255个字符")
    @ApiModelProperty(value = "课程封面图链接", name = "courseCoverUrl")
    private String courseCoverUrl;

    /**
     * 对话场景
     */
    @Length(groups = {Save.class, Submit.class}, max = 200, message = "对话场景不能超过200个字符")
    @ApiModelProperty(value = "对话场景", name = "dialogueScene")
    private String dialogueScene;

    /**
     * 访谈目标
     */
    @Length(groups = {Save.class, Submit.class}, max = 500, message = "访谈目标不能超过500个字符")
    @ApiModelProperty(value = "访谈目标", name = "dialogueObjective")
    private String dialogueObjective;

    /**
     * 对话提示问题数量
     */
    @NotNull(groups = {Submit.class}, message = "对话提示问题数量不能为空")
    @Min(groups = {Submit.class}, value = 1, message = "对话提示问题数量至少是1")
    @ApiModelProperty(value = "对话提示问题数量", name = "dialogueQuestionNum")
    private Integer dialogueQuestionNum;

    /**
     * 访谈注意事项
     */
    @Length(groups = {Save.class, Submit.class}, max = 200, message = "访谈注意事项不能超过200个字符")
    @ApiModelProperty(value = "访谈注意事项", name = "dialogueRemark")
    private String dialogueRemark;

    /**
     * 访谈时长（minute）
     */
    @ApiModelProperty(value = "访谈时长（minute）", name = "courseDuration")
    private BigDecimal courseDuration;
}