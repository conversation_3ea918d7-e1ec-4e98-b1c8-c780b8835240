package com.deloitte.dhr.ai;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * AI员工伙伴
 *
 * <AUTHOR>
 * @since 2022-09-02
 */
@EnableFeignClients(basePackages = {"com.deloitte.dhr.utility" })
@EnableTransactionManagement
@EnableScheduling
@SpringBootApplication(scanBasePackages = {"com.deloitte.dhr.common", "com.deloitte.dhr.ai", "com.deloitte.dhr.utility"})
public class DHRAiApplication {
    public static void main(String[] args) {
        SpringApplication.run(DHRAiApplication.class, args);
    }
}