package com.deloitte.dhr.ai.module.chat.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI聊天-聊天消息内容-列表
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("AiChatMsgContentListResponse")
public class AiChatMsgContentListResponse {

    @ApiModelProperty(value = "消息类型", name = "contentType")
    private String contentType;

    @ApiModelProperty(value = "消息内容", name = "content")
    private String content;

    @ApiModelProperty(value = "内容排序", name = "contentOrder")
    private Integer contentOrder;

}