package com.deloitte.dhr.ai.module.interview.task.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AI访谈-访谈任务-我的访谈-列表
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewEmpTaskListRequest")
public class AiInterviewEmpTaskListRequest {

    /**
     * 访谈任务名称
     */
    @ApiModelProperty(value = "访谈任务名称", name = "taskName")
    private String taskName;

    /**
     * AI访谈任务状态;WKS：未开始，JXZ：进行中，YWC：已完成，YJS：已结束
     */
    @ApiModelProperty(value = "AI访谈任务状态;WKS：未开始，JXZ：进行中，YWC：已完成，YJS：已结束", name = "taskStatus")
    private String taskStatus;

    /**
     * 当前登录人员工编号
     */
    @JsonIgnore
    private String loginEmpCode;

}