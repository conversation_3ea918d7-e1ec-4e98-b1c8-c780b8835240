package com.deloitte.dhr.ai.module.interview.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.interview.constant.AiInterviewConstant;
import com.deloitte.dhr.ai.module.interview.resource.domain.AiInterviewInfo;
import com.deloitte.dhr.ai.module.interview.resource.mapper.AiInterviewInfoMapper;
import com.deloitte.dhr.ai.module.interview.task.domain.AiInterviewTaskEmp;
import com.deloitte.dhr.ai.module.interview.task.mapper.AiInterviewTaskEmpMapper;
import com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskEmpListRequest;
import com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskEmpResponse;
import com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskEmpSaveRequest;
import com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskInfoResponse;
import com.deloitte.dhr.ai.module.interview.task.service.AiInterviewTaskEmpService;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewObjectResponse;
import com.deloitte.dhr.ai.utils.CheckUtils;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AI访谈-访谈任务-员工-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class AiInterviewTaskEmpServiceImpl extends SuperServiceImpl<AiInterviewTaskEmpMapper, AiInterviewTaskEmp> implements AiInterviewTaskEmpService {

    @Autowired
    private AiInterviewTaskEmpMapper aiInterviewTaskEmpMapper;

    @Autowired
    private AiInterviewInfoMapper aiInterviewInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveData(List<AiInterviewTaskEmpSaveRequest> requestList, Long taskId, Long resourceId) {
        // 删除旧的访谈员工信息
        this.deleteByTaskId(taskId);

        // 保存新的访谈员工信息
        if (CollUtil.isEmpty(requestList)) {
            return true;
        }
        List<AiInterviewTaskEmp> taskEmpList = BeanUtil.copyToList(requestList, AiInterviewTaskEmp.class);
        taskEmpList.forEach(taskEmp -> {
            taskEmp.setBgStatus(AiInterviewConstant.EmpTaskReportStatus.NO_GENERATED);
            taskEmp.setStatus(AiInterviewConstant.TaskEmpStatus.NOT_START);
            taskEmp.setTaskId(taskId);
            taskEmp.setResourceId(resourceId);
        });
        return this.saveBatch(taskEmpList);
    }

    @Override
    public void deleteByTaskId(Long taskId) {
        LambdaQueryWrapper<AiInterviewTaskEmp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiInterviewTaskEmp::getTaskId, taskId);
        this.remove(queryWrapper);
    }

    @Override
    public AiInterviewTaskEmpResponse getDetail(Long taskEmpId) {
        AiInterviewTaskEmp taskEmp = this.get(taskEmpId);
        CheckUtils.checkNull(taskEmp, "该数据不存在");
        return BeanUtil.copyProperties(taskEmp, AiInterviewTaskEmpResponse.class);
    }

    @Override
    public ResponsePage<AiInterviewTaskEmpResponse> findTaskEmpListPage(Page<AiInterviewTaskEmp> page, AiInterviewTaskEmpListRequest request, BaseOrder order) {
        List<AiInterviewTaskEmpResponse> list = aiInterviewTaskEmpMapper.findListPage(page, request, this.adjustOrder(order));
        return new ResponsePage<>(page, list);
    }

    @Override
    public AiInterviewObjectResponse getMyObjectDetail(Long taskEmpId) {
        AiInterviewObjectResponse myObjectDetail = aiInterviewTaskEmpMapper.getMyObjectDetail(taskEmpId);

        AiInterviewInfo info = aiInterviewInfoMapper.selectById(myObjectDetail.getResourceId());
        myObjectDetail.setCourseName(info.getCourseName());
        return myObjectDetail;
    }

    @Override
    public void verifyTaskEmp(List<AiInterviewTaskEmpSaveRequest> saveRequestList) {

        // 校验-保存的数据是否有重复数据
        Map<String, List<AiInterviewTaskEmpSaveRequest>> empMap = saveRequestList.stream().collect(Collectors.groupingBy(AiInterviewTaskEmpSaveRequest::getEmpCode));
        List<String> errorEmpList = new ArrayList<>();
        empMap.forEach((empCode, list) -> {
            if (list.size() == 1) {
                return;
            }
            errorEmpList.add(empCode);
        });
        if (CollUtil.isNotEmpty(errorEmpList)) {
            throw new CommRunException("不能添加重复的员工数据【" + StrUtil.join("，", errorEmpList) + "】");
        }
    }

    @Override
    public AiInterviewTaskInfoResponse getMyTaskInfoDetail(Long taskEmpId) {
        return aiInterviewTaskEmpMapper.getMyTaskInfoDetail(taskEmpId);
    }
}

