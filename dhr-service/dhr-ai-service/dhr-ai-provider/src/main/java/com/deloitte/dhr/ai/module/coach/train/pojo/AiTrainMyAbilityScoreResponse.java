package com.deloitte.dhr.ai.module.coach.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 课程资源-AI陪练-我的能力分析得分
 *
 * <AUTHOR>
 * @date 2024-01-04
 */
@Data
@Builder
@ApiModel("AiTrainMyAbilityScoreResponse")
@AllArgsConstructor
@NoArgsConstructor
public class AiTrainMyAbilityScoreResponse {

    /**
     * 能力编码
     */
    @ApiModelProperty(value = "能力编码", name = "abilityCode")
    private String abilityCode;

    /**
     * 能力名称
     */
    @ApiModelProperty(value = "能力名称", name = "abilityName")
    private String abilityName;

    /**
     * 能力分析得分
     */
    @ApiModelProperty(value = "能力分析得分", name = "abilityAnalyseScore")
    private BigDecimal abilityAnalyseScore;

    /**
     * 能力分析描述
     */
    @ApiModelProperty(value = "能力分析描述", name = "abilityAnalyseDesc")
    private String abilityAnalyseDesc;

}