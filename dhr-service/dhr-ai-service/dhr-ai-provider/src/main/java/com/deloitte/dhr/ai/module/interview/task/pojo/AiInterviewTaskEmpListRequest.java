package com.deloitte.dhr.ai.module.interview.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * AI访谈-访谈任务-员工-列表-筛选条件
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewTaskEmpListRequest")
public class AiInterviewTaskEmpListRequest {

    /**
     * AI访谈-任务ID
     */
    @NotNull(message = "访谈任务ID不能为空")
    @ApiModelProperty(value = "访谈任务ID", name = "taskId")
    private Long taskId;

    /**
     * 任务状态
     */
    @NotNull(message = "任务状态不能为空")
    @ApiModelProperty(value = "任务状态,WKS:未开始；JXZ:进行中；YWC:已完成", name = "status")
    private String status;


}