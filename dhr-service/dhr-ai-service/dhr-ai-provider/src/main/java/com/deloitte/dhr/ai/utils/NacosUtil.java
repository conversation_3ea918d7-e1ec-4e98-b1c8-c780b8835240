package com.deloitte.dhr.ai.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cloud.nacos.NacosServiceManager;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Nacos 工具类
 *
 * <AUTHOR>
 * @since 2022-09-02
 */
@Component
public class NacosUtil {

    @Value("${spring.cloud.nacos.discovery.group:DEFAULT_GROUP}")
    public String group;

    @PostConstruct
    public void setStaticPort() {
        staticGroup = this.group;
    }

    public static String staticGroup;


    /**
     * 通过服务名获取 Nacos 中服务实例信息
     *
     * @param serviceName 服务名
     * @return
     */
    public static Instance getServiceByName(String serviceName) {
        NacosServiceManager nacosServiceManager = SpringUtil.getBean(NacosServiceManager.class);
        NamingService namingService = nacosServiceManager.getNamingService();
        try {
            return namingService.selectOneHealthyInstance(serviceName, staticGroup);
        } catch (NacosException e) {
            throw new RuntimeException(e);
        }
    }

}
