package com.deloitte.dhr.ai.module.coach.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 培训任务-员工信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiCoachTaskEmpResponse")
public class AiCoachTaskEmpResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 员工编号
     */
    @ApiModelProperty(value = "员工编号", name = "empCode")
    private String empCode;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称", name = "empName")
    private String empName;

    /**
     * 员工头像
     */
    @ApiModelProperty(value = "员工头像", name = "empAvatarUrl")
    private String empAvatarUrl;

    /**
     * 员工部门编码
     */
    @ApiModelProperty(value = "员工部门编码", name = "orgCode")
    private String orgCode;

    /**
     * 员工部门名称
     */
    @ApiModelProperty(value = "员工部门名称", name = "orgName")
    private String orgName;

    /**
     * 员工岗位编码
     */
    @ApiModelProperty(value = "员工岗位编码", name = "positionCode")
    private String positionCode;

    /**
     * 员工岗位名称
     */
    @ApiModelProperty(value = "员工岗位名称", name = "positionName")
    private String positionName;

    /**
     * 状态;WKS：未开始，JXZ：进行中，YWC：已完成
     */
    @ApiModelProperty(value = "状态;WKS：未开始，JXZ：进行中，YWC：已完成，YJS：已结束", name = "status")
    private String status;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间", name = "completeTime")
    private Date completeTime;

    /**
     * 最终得分
     */
    @ApiModelProperty(value = "最终得分", name = "finalScore")
    private BigDecimal finalScore;

    /**
     * 评分结果;0：不通过，1：通过
     */
    @ApiModelProperty(value = "评分结果;0：不通过，1：通过", name = "scoreResult")
    private Boolean scoreResult;

    /**
     * 消耗时间（minute）
     */
    @ApiModelProperty(value = "消耗时间（minute）", name = "elapsedTime")
    private BigDecimal elapsedTime;

    /**
     * 视频文件ID
     */
    @ApiModelProperty(value = "视频文件ID", name = "videoFileId")
    private String videoFileId;

}