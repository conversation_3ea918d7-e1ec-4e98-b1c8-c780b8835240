package com.deloitte.dhr.ai.module.interview.train.mapper;

import com.deloitte.dhr.ai.module.interview.train.domain.AiInterviewMyDialogueHistory;
import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewMyDialogueHistoryListRequest;
import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewMyDialogueHistoryResponse;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI访谈-访谈任务-我的对话历史记录-相关持久化接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface AiInterviewMyDialogueHistoryMapper extends SuperMapper<AiInterviewMyDialogueHistory> {
    /**
     * 查询-答复次数
     *
     * @param taskEmpId 用户ID
     * @param questionId 所属问题ID
     * @return Integer
     */
    Long getMyDialogueNum(@Param("taskEmpId") Long taskEmpId, @Param("questionId") Long questionId);
}