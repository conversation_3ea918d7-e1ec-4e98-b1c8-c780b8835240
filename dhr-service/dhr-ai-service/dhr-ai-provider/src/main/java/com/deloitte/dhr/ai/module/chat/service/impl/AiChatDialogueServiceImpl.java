package com.deloitte.dhr.ai.module.chat.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.deloitte.dhr.ai.enums.AiGroup;
import com.deloitte.dhr.ai.module.chat.domain.AiChatDialogue;
import com.deloitte.dhr.ai.module.chat.mapper.AiChatDialogueMapper;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatDialogueListResponse;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatDialogueSaveRequest;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatDialogueUpdRequest;
import com.deloitte.dhr.ai.module.chat.service.AiChatDialogueService;
import com.deloitte.dhr.ai.module.chat.service.AiChatMsgService;
import com.deloitte.dhr.ai.utils.CheckUtils;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * AI聊天-聊天对话-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class AiChatDialogueServiceImpl extends SuperServiceImpl<AiChatDialogueMapper, AiChatDialogue> implements AiChatDialogueService {

    @Autowired
    private AiChatDialogueMapper aiChatDialogueMapper;
    @Lazy
    @Autowired
    private AiChatMsgService aiChatMsgService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdateData(AiChatDialogueSaveRequest request) {
        AiChatDialogue dialogue = BeanUtil.copyProperties(request, AiChatDialogue.class);
        String dialogueTitle = (dialogue.getDialogueTitle().length() > 200) ? dialogue.getDialogueTitle().substring(0, 200) : dialogue.getDialogueTitle();
        if (request.getId() == null) {
            dialogue.setDialogueTitle(dialogueTitle);
            this.save(dialogue);
        } else {
            dialogue = this.get(request.getId());
            CheckUtils.checkNull(dialogue, "该聊天对话不存在!");
            if (StrUtil.equals(request.getAiGroup(), AiGroup.DIGITAL_HUMAN.getCode())) {
                dialogue.setDialogueTitle(dialogueTitle);
            }
            this.update(dialogue);
        }
        return dialogue.getId();
    }

    @Override
    public List<AiChatDialogueListResponse> getDialogueList(String aiGroup) {
        String loginUserCode = LoginUtil.getLoginUserCode();
        if (StrUtil.isBlank(loginUserCode)) {
            return Collections.emptyList();
        }
        return aiChatDialogueMapper.getList(aiGroup, loginUserCode);
    }


    @Override
    public Long updData(AiChatDialogueUpdRequest request) {

        AiChatDialogue dialogue = this.get(request.getId());
        CheckUtils.checkNull(dialogue, "该对话历史记录不存在!");
        // 更新对话记录标题
        dialogue.setDialogueTitle(request.getDialogueTitle());
        this.update(dialogue);
        return dialogue.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {

        // 删除-对话历史记录数据
        aiChatDialogueMapper.deleteById(id);

        // 删除-聊天历史记录相关数据
        aiChatMsgService.deleteByDialogueId(id);

        return true;
    }

}

