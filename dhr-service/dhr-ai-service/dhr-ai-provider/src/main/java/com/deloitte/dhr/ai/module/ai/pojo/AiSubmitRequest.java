package com.deloitte.dhr.ai.module.ai.pojo;

import com.deloitte.dhr.ai.enums.AiSubType;
import com.deloitte.dhr.ai.enums.AiType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * AI 提交
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("AiSubmitRequest")
public class AiSubmitRequest {

    @ApiModelProperty(value = "消息ID", name = "msgId")
    private Long msgId;

    @ApiModelProperty(value = "对话ID", name = "dialogueId")
    private Long dialogueId;

    @ApiModelProperty(value = "会话ID", name = "conversationId")
    private String conversationId;

    /**
     * {@link AiType}
     */
    @ApiModelProperty(value = "AI类型", name = "aiType")
    private String aiType;

    /**
     * {@link AiSubType}
     */
    @ApiModelProperty(value = "AI子类型", name = "aiSubType")
    private String aiSubType;


    @NotEmpty(message = "消息内容不能为空")
    @ApiModelProperty(value = "消息内容", name = "contents")
    private List<AiContent> contents;

}
