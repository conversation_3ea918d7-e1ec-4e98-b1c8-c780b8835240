package com.deloitte.dhr.ai.module.coach.teach.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 课程资源-教学课程-考核题目来源-信息-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachTestQuestionSourceSaveRequest")
public class AiTeachTestQuestionSourceSaveRequest {

    /**
     * 来源文件ID
     */
    @ApiModelProperty(value = "来源文件ID", name = "fileId")
    private String fileId;

    /**
     * 来源页码
     */
    @ApiModelProperty(value = "来源页码", name = "pageIdList")
    private List<Integer> pageIdList;

}