package com.deloitte.dhr.ai.enums;


import lombok.Getter;


/**
 * AI返回模式枚举
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public enum AiResponseMode {

    /**
     * 流式模式（推荐：基于 SSE（Server-Sent Events）实现类似打字机输出方式的流式返回
     */
    STREAMING("streaming", "流式模式"),

    /**
     * 阻塞模式，等待执行完毕后返回结果。（请求若流程较长可能会被中断）。 由于 Cloudflare 限制，请求会在 100 秒超时无返回后中断。
     */
    BLOCKING("blocking", "阻塞模式");

    private final String code;

    private final String name;

    AiResponseMode(String code, String name) {
        this.code = code;
        this.name = name;
    }

}
