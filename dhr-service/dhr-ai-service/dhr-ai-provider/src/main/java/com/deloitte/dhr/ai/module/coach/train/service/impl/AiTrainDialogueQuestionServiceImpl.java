package com.deloitte.dhr.ai.module.coach.train.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.config.AiModelConfig;
import com.deloitte.dhr.ai.enums.AiSubType;
import com.deloitte.dhr.ai.enums.AiType;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.module.ai.service.AiService;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachTextSampleAnswer;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachTestQuestionSaveRequest;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainDialogueQuestion;
import com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainDialogueQuestionMapper;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainDialogueQuestionResponse;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainDialogueQuestionService;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 课程资源-AI陪练-对话问题-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-01-03
 */
@Slf4j
@Service
public class AiTrainDialogueQuestionServiceImpl extends SuperServiceImpl<AiTrainDialogueQuestionMapper, AiTrainDialogueQuestion> implements AiTrainDialogueQuestionService {


    @Autowired
    private AiTrainDialogueQuestionMapper aiTrainDialogueQuestionMapper;
    @Autowired
    private AiModelConfig aiModelConfig;
    @Autowired
    private AiService aiService;


    @Override
    public List<AiTrainDialogueQuestionResponse> getByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiTrainDialogueQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrainDialogueQuestion::getResourceId, resourceId);
        queryWrapper.orderByAsc(AiTrainDialogueQuestion::getQuestionOrder);
        List<AiTrainDialogueQuestion> list = aiTrainDialogueQuestionMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(list, AiTrainDialogueQuestionResponse.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AiTrainDialogueQuestion> saveData(List<AiTeachTestQuestionSaveRequest> requestList, Long resourceId) {

        // 删除旧的AI陪练-对话问题数据
        this.deleteByResourceId(resourceId);

        // 保存新的AI陪练-对话问题数据
        if (CollUtil.isEmpty(requestList)) {
            return Collections.emptyList();
        }
        List<AiTrainDialogueQuestion> questionList = new ArrayList<>();
        requestList.forEach(saveQuestion -> {
            AiTrainDialogueQuestion question = BeanUtil.copyProperties(saveQuestion, AiTrainDialogueQuestion.class);
            questionList.add(question);
            question.setResourceId(resourceId);
            switch (saveQuestion.getQuestionType()) {
                case AiCoachConstant.TeachQuestionType.SINGLE_CHOICE_QUESTION:
                case AiCoachConstant.TeachQuestionType.TRUE_OR_FALSE_QUESTION:
                    break;
                // 构建文本题保存对象
                case AiCoachConstant.TeachQuestionType.ESSAY_QUESTION:
                    AiTeachTextSampleAnswer textSampleAnswer = BeanUtil.copyProperties(saveQuestion.getQuestionContent(), AiTeachTextSampleAnswer.class);
                    question.setSampleAnswerContent(textSampleAnswer.getSampleAnswerContent());
                    break;
                default:
                    throw new CommRunException("非法的考核类型题目");
            }
        });
        this.saveBatch(questionList);
        return questionList;
    }

    @Override
    public void deleteByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiTrainDialogueQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrainDialogueQuestion::getResourceId, resourceId);
        aiTrainDialogueQuestionMapper.delete(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAiQuestionVoice(List<AiTrainDialogueQuestion> questionList) {

        // 构建AI请求入参
        AiContent aiContent = AiContent.builder().contentType(AiContentType.JSON.getCode()).build();
        AiRequest aiRequest = AiRequest.builder()
                .aiType(AiType.COACH.getCode())
                .aiSubType(AiSubType.TRAIN_TEXT_TO_SPEECH.getCode())
                .userId(LoginUtil.getLoginUserCode())
                .stream(false)
                .contents(List.of(aiContent))
                .build();


        SecurityContext context = SecurityContextHolder.getContext();
        Flux.fromIterable(questionList)
                .delayElements(Duration.ofSeconds(1))
                .flatMap(question -> {
                    SecurityContextHolder.setContext(context);
                    aiContent.setContent(JSON.toJSONString(Map.of("content", question.getQuestionName())));
                    return aiService.work(aiRequest, LoginUtil.getLoginUser())
                            .filter(raw -> CollUtil.isNotEmpty(raw.getContents()))
                            .flatMap(raw -> {
                                // todo 临时妥协方案，后续再优化调整
                                JSONObject fileObject = JSONObject.parseObject(raw.getContents().get(0).getContent());
                                String url = aiModelConfig.getBaseUrl().replace("/v1", "").concat(fileObject.getString("url"));
                                question.setAiQuestionVoiceFileUrl(url);
                                return Flux.just(question);
                            })
                            .onErrorResume(e -> {
                                log.error("问题生成语音出错：{}，原因：{}", question.getQuestionName(), e.getMessage());
                                return Mono.empty();
                            });
                })
                .subscribe(entity -> {
                    SecurityContextHolder.setContext(context);
                    update(entity);
                }, error -> log.error("生成问题语音出错: {}", error.getMessage()));
    }

}

