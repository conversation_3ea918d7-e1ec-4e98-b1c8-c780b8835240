package com.deloitte.dhr.ai.module.ai.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AI文件-请求
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiFileRequest")
public class AiFileRequest {

    @ApiModelProperty(value = "文件ID", name = "fileId")
    private String fileId;

    @ApiModelProperty(value = "文件名称", name = "fileName")
    private String fileName;

    @ApiModelProperty(value = "文件类型", name = "fileType")
    private String fileType;

    @ApiModelProperty(value = "文件URL", name = "fileUrl")
    private String fileUrl;

    @ApiModelProperty(value = "文件大小", name = "fileSize")
    private String fileSize;

    @ApiModelProperty(value = "文件页码", name = "page")
    private Integer page;

}