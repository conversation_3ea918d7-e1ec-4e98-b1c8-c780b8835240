package com.deloitte.dhr.ai.module.coach.train.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.module.coach.constant.AiTrainAbilityAnalyseEnum;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainMyAbilityAnalyse;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainMyAbilityScore;
import com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainMyAbilityAnalyseMapper;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyAbilityAnalyseChartResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyAbilityScoreResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyAbilityScoreSaveRequest;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainMyAbilityAnalyseService;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainMyAbilityScoreService;
import com.deloitte.dhr.common.SuperServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.deloitte.dhr.ai.module.coach.constant.AiTrainAbilityAnalyseEnum.*;

/**
 * 课程资源-AI陪练-我的能力分析-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiTrainMyAbilityAnalyseServiceImpl extends SuperServiceImpl<AiTrainMyAbilityAnalyseMapper, AiTrainMyAbilityAnalyse> implements AiTrainMyAbilityAnalyseService {

    @Autowired
    private AiTrainMyAbilityScoreService aiTrainMyAbilityScoreService;
    @Autowired
    private AiTrainMyAbilityAnalyseMapper aiTrainMyAbilityAnalyseMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveData(Long taskEmpId, Long historyId, List<AiTrainMyAbilityScoreSaveRequest> saveRequestList) {
        LambdaQueryWrapper<AiTrainMyAbilityAnalyse> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrainMyAbilityAnalyse::getTaskEmpId, taskEmpId);
        List<AiTrainMyAbilityAnalyse> list = this.list(queryWrapper);

        // 首次保存-能力分析-总得分信息
        if (CollUtil.isEmpty(list)) {
            list.add(new AiTrainMyAbilityAnalyse(taskEmpId, logical_expression.getCode(), logical_expression.getName(), BigDecimal.ZERO, logical_expression.getWeight()));
            list.add(new AiTrainMyAbilityAnalyse(taskEmpId, professional_knowledge.getCode(), professional_knowledge.getName(), BigDecimal.ZERO, professional_knowledge.getWeight()));
            this.saveBatch(list);
        }
        Map<String, AiTrainMyAbilityAnalyse> map = list.stream().collect(Collectors.toMap(AiTrainMyAbilityAnalyse::getAbilityName, v -> v, (k1, k2) -> k1));

        // 保存-能力分析-每次对话分析信息
        if (CollUtil.isEmpty(saveRequestList)) {
            return true;
        }
        List<AiTrainMyAbilityScore> scoreList = new ArrayList<>();
        saveRequestList.forEach(request -> {
            AiTrainMyAbilityAnalyse abilityAnalyse = map.get(request.getAbilityName());
            if (abilityAnalyse == null) {
                return;
            }
            scoreList.add(new AiTrainMyAbilityScore(taskEmpId, historyId, abilityAnalyse.getId(), request.getAbilityAnalyseScore(), request.getAbilityAnalyseDesc()));

        });
        return aiTrainMyAbilityScoreService.saveBatch(scoreList);
    }


    @Override
    public List<AiTrainMyAbilityAnalyseChartResponse> getMyAbilityAnalyseChart(Long taskEmpId) {
        return BeanUtil.copyToList(this.getByTaskEmpId(taskEmpId), AiTrainMyAbilityAnalyseChartResponse.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal calculateScore(Long taskEmpId) {

        // 查询-能力分析信息
        List<AiTrainMyAbilityAnalyse> list = this.getByTaskEmpId(taskEmpId);
        Map<Long, AiTrainMyAbilityAnalyse> abilityMap = list.stream().collect(Collectors.toMap(AiTrainMyAbilityAnalyse::getId, v -> v, (k1, k2) -> k1));

        // 查询-能力分析每次对话得分信息
        LambdaQueryWrapper<AiTrainMyAbilityScore> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrainMyAbilityScore::getTaskEmpId, taskEmpId);
        List<AiTrainMyAbilityScore> scoreList = aiTrainMyAbilityScoreService.list(queryWrapper);
        Map<Long, List<BigDecimal>> scoreMap = scoreList.stream().collect(Collectors.groupingBy(AiTrainMyAbilityScore::getAbilityAnalyseId, Collectors.mapping(AiTrainMyAbilityScore::getAbilityAnalyseScore, Collectors.toList())));

        // 能力项得分 = 每项能力分数之和
        abilityMap.forEach((id, analyse) -> {
            List<BigDecimal> scores = scoreMap.get(id);
            if (CollUtil.isEmpty(scores)) {
                return;
            }
            // 计算得分数
            BigDecimal totalScore = scores.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            // 获取能力项的最低和最高分数
            AiTrainAbilityAnalyseEnum analyseEnum = getByCode(analyse.getAbilityCode());
            BigDecimal minScore = analyseEnum == null ? BigDecimal.ZERO : analyseEnum.getMinScore();
            BigDecimal maxScore = analyseEnum == null ? BigDecimal.valueOf(100) : analyseEnum.getMaxScore();
            if (totalScore.compareTo(minScore) < 0) {
                analyse.setAbilityScore(minScore);
            } else if (totalScore.compareTo(minScore) >= 0 && totalScore.compareTo(maxScore) <= 0) {
                analyse.setAbilityScore(totalScore);
            } else {
                analyse.setAbilityScore(maxScore);
            }
        });
        // 更新-能力得分项分数
        this.saveOrUpdateBatch(list);
        // 能力项总得分 = (每项能力项总分数 * 每项能力项权重)之和
        return list.stream().map(a -> a.getAbilityScore().multiply(a.getAbilityWeight()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP)).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public List<AiTrainMyAbilityScoreResponse> getMyDialogueAnalyse(Long historyId) {
        return aiTrainMyAbilityAnalyseMapper.getMyDialogueAnalyse(historyId);
    }

    /**
     * 通过培训任务员工ID获取信息
     *
     * @param taskEmpId 培训任务员工ID
     * @return List<AiTrainMyAbilityAnalyse>
     */
    private List<AiTrainMyAbilityAnalyse> getByTaskEmpId(Long taskEmpId) {
        LambdaQueryWrapper<AiTrainMyAbilityAnalyse> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrainMyAbilityAnalyse::getTaskEmpId, taskEmpId);
        return this.list(queryWrapper);
    }

}

