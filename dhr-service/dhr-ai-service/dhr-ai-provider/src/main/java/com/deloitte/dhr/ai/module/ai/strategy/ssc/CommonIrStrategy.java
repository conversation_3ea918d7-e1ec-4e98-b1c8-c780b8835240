package com.deloitte.dhr.ai.module.ai.strategy.ssc;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.ai.client.AiModelWebClient;
import com.deloitte.dhr.ai.enums.*;
import com.deloitte.dhr.ai.module.ai.annotation.ActionStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiFileResponse;
import com.deloitte.dhr.ai.module.ai.pojo.AiModelRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SSC-信息检索-AI通用策略实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@ActionStrategyType(item = AiSscIntentItem.TY, action = AiSscIntentAction.IR)
public class CommonIrStrategy implements SscStrategy {

    @Autowired
    private AiModelWebClient aiModelWebClient;

    @Override
    public Flux<List<AiContent>> executeStream(Map<String, Object> param) {
        AiModelRequest modelRequest = AiModelRequest.builder()
                .user(String.valueOf(param.get("userId")))
                .query(String.valueOf(param.get("userInput")))
                .inputs(Map.of("query", param.get("userInput"), "isSearch", param.get("isSearch"), "context", param.get("context") == null ? "" : param.get("context")))
                .responseMode(AiResponseMode.STREAMING.getCode())
                .type(AiAgentType.SSC_RAG)
                .build();
        return aiModelWebClient.post(modelRequest)
                .filter(raw -> StrUtil.equals(JSONObject.parseObject(raw).getString("event"), "text_chunk") ||
                        StrUtil.equals(JSONObject.parseObject(raw).getString("event"), "workflow_finished"))
                .map(raw -> {
                    List<AiContent> contents = new ArrayList<>();
                    JSONObject rawObj = JSONObject.parseObject(raw);
                    if (StrUtil.equals(rawObj.getString("event"), "text_chunk")) {
                        AiContent content = AiContent.builder()
                                .contentType(AiContentType.TEXT.getCode())
                                .content(rawObj.getJSONObject("data").getString("text"))
                                .build();
                        contents.add(content);
                    }
                    // 知识库召回文件信息
                    if (StrUtil.equals(rawObj.getString("event"), "workflow_finished")) {
                        JSONArray files = rawObj.getJSONObject("data").getJSONObject("outputs").getJSONArray("files");
                        if (CollUtil.isNotEmpty(files)) {
                            Map<String, AiFileResponse> fileMap = new HashMap<>();
                            files.forEach(file -> {
                                JSONObject fileObj = (JSONObject) file;
                                String fileId = fileObj.getString("document_id");
                                AiFileResponse fileResponse = fileMap.get(fileId);
                                if (fileResponse == null) {
                                    fileResponse = AiFileResponse.builder()
                                            .fileId(fileId)
                                            .fileName(fileObj.getString("document_name"))
                                            .fileType(FileNameUtil.getSuffix(fileObj.getString("document_name")))
                                            .fileSize("")
                                            .fileUrl("")
                                            .fileContent(fileObj.getString("content"))
                                            .page(0)
                                            .build();
                                    fileMap.put(fileId, fileResponse);
                                    return;
                                }
                                fileResponse.setFileContent(fileResponse.getFileContent().concat("\n").concat(fileObj.getString("content")));
                            });
                            if (!fileMap.isEmpty()) {
                                AiContent content = AiContent.builder()
                                        .contentType(AiContentType.FILE.getCode())
                                        .content(JSON.toJSONString(fileMap.values()))
                                        .build();
                                contents.add(content);
                            }
                        }
                    }
                    return contents;
                });
    }


}
