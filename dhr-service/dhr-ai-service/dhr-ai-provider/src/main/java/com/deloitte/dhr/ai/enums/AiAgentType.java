package com.deloitte.dhr.ai.enums;


import lombok.Getter;

/**
 * AI模型类型枚举
 *
 * <AUTHOR>
 * {@code @date} 2023_12_05
 */
@Getter
public enum AiAgentType {


    //============================================SSC============================================
    SSC_INTENT("ssc_intent", "意图识别"),
    SSC_NORMAL("ssc_normal", "通用"),
    SSC_RAG("ssc_rag", "知识库"),

    //============================================DUO============================================
    DUO("duo", "AI_DUO"),

    //============================================COACH============================================
    TEACH_QUESTION_GENERATE("teach_question_generate", "教学课程-题目生成"),
    TEACH_QUESTION_EVALUATE("teach_question_evaluate", "教学课程-题目评分"),
    TEACH_QUESTION_EVALUATE_BATCH("teach_question_evaluate_batch", "教学课程-题目评分-批量"),
    TRAIN_QUESTION_GENERATE("train_question_generate", "AI陪练-题目生成"),
    TRAIN_FRAMEWORK_GENERATE("train_framework_generate", "AI陪练-沟通框架"),
    TRAIN_TAG_EXTRACTOR("train_tag_extractor", "AI陪练-标签抽取"),
    TRAIN_CHAT_ANALYZE("train_chat_analyze", "AI陪练-聊天分析"),
    TRAIN_TEXT_TO_SPEECH("train_text_to_speech", "AI陪练-文字转语音"),

    //==========================================INTERVIEW============================================
    INTERVIEW_FRAMEWORK_GENERATE("interview_framework_generate", "访谈助手-沟通框架生成"),
    INTERVIEW_ANSWER_ANALYZE("interview_answer_analyze", "访谈助手-回答分析"),
    INTERVIEW_REPORT_ANALYZE("interview_report_analyze", "访谈助手-报告分析"),

    //============================================LEARNING============================================
    LEARNING("learning", "AI HR助手"),

    //============================================数字人（李宁）============================================
    DIGITAL_HUMAN_LINING_INTENT("digital_human_lining_intent", "意图识别"),
    DIGITAL_HUMAN_LINING_NORMAL("digital_human_lining_normal", "通用"),
    DIGITAL_HUMAN_LINING_RAG("digital_human_lining_rag", "知识库"),

    //============================================ANALYSIS============================================
    DATA_ANALYSIS("data_analysis", "数据分析"),
    DATA_PREDICTION("data_prediction", "数据预测");

    private final String code;

    private final String name;

    AiAgentType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据AI类型获取AI模型类型
     *
     * @param subType 类型
     * @return AI模型类型
     */
    public static AiAgentType getByAgentType(AiSubType subType) {
        AiAgentType modelType;
        switch (subType) {
            case SSC:
                modelType = SSC_INTENT;
                break;
            case DIGITAL_HUMAN_LINING:
                modelType = DIGITAL_HUMAN_LINING_INTENT;
                break;
            case GENERAL:
            case XMIND:
            case MEETING_MINUTES:
            case GENERATE_REPORT:
            case TEXT_GENERATED_IMAGE:
            case TRANSLATE:
            case DOCUMENT_QA:
            case DOCUMENT_SUMMARY:
            case LEARNING:
            case DIGITAL_HUMAN:
                modelType = DUO;
                break;
            case TEACH_QUESTION_GENERATE:
                modelType = TEACH_QUESTION_GENERATE;
                break;
            case TEACH_QUESTION_EVALUATE:
                modelType = TEACH_QUESTION_EVALUATE;
                break;
            case TEACH_QUESTION_EVALUATE_BATCH:
                modelType = TEACH_QUESTION_EVALUATE_BATCH;
                break;
            case TRAIN_QUESTION_GENERATE:
                modelType = TRAIN_QUESTION_GENERATE;
                break;
            case TRAIN_FRAMEWORK_GENERATE:
                modelType = TRAIN_FRAMEWORK_GENERATE;
                break;
            case TRAIN_TAG_EXTRACTOR:
                modelType = TRAIN_TAG_EXTRACTOR;
                break;
            case TRAIN_CHAT_ANALYZE:
                modelType = TRAIN_CHAT_ANALYZE;
                break;
            case TRAIN_TEXT_TO_SPEECH:
                modelType = TRAIN_TEXT_TO_SPEECH;
                break;
            case INTERVIEW_FRAMEWORK_GENERATE:
                modelType = INTERVIEW_FRAMEWORK_GENERATE;
                break;
            case INTERVIEW_ANSWER_ANALYZE:
                modelType = INTERVIEW_ANSWER_ANALYZE;
                break;
            case INTERVIEW_REPORT_ANALYZE:
                modelType = INTERVIEW_REPORT_ANALYZE;
                break;
            case DATA_ANALYSIS:
                modelType = DATA_ANALYSIS;
                break;
            case DATA_PREDICTION:
                modelType = DATA_PREDICTION;
                break;
            default:
                modelType = DUO;
        }
        return modelType;
    }

}