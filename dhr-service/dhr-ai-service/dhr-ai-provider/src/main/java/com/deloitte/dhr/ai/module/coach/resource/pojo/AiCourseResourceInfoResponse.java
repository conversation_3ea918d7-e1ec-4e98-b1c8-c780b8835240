package com.deloitte.dhr.ai.module.coach.resource.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 课程资源-基本信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiCourseResourceInfoResponse")
public class AiCourseResourceInfoResponse {

    /**
     * 课程资源ID
     */
    @ApiModelProperty(value = "课程资源ID", name = "id")
    private Long id;

    /**
     * 类别ID
     */
    @ApiModelProperty(value = "类别ID", name = "categoryId")
    private Long categoryId;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称", name = "courseName")
    private String courseName;

    /**
     * 课程分类;JXKC：教学课程，AIPL：AI陪练
     */
    @ApiModelProperty(value = "课程分类;JXKC：教学课程，AIPL：AI陪练", name = "courseType")
    private String courseType;

    /**
     * 课程封面图链接
     */
    @ApiModelProperty(value = "课程封面图链接", name = "courseCoverUrl")
    private String courseCoverUrl;

    /**
     * 课程时长（minute）
     */
    @ApiModelProperty(value = "课程时长（minute）", name = "courseDuration")
    private BigDecimal courseDuration;

    /**
     * 课程状态
     */
    @ApiModelProperty(value = "课程状态", name = "courseStatus")
    private String courseStatus;

    /**
     * AI生成操作的记录ID
     */
    @ApiModelProperty(value = "AI生成操作的记录ID", name = "recordId")
    private Long recordId;

}