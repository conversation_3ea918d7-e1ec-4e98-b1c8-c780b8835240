package com.deloitte.dhr.ai.module.interview.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.interview.task.domain.AiInterviewTaskEmp;
import com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskEmpListRequest;
import com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskEmpResponse;
import com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskEmpSaveRequest;
import com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskInfoResponse;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewObjectResponse;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * AI访谈-访谈任务-员工-相关业务接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface AiInterviewTaskEmpService extends SuperService<AiInterviewTaskEmp> {

    /**
     * 保存/更新-访谈员工信息
     *
     * @param requestList 访谈员工信息-保存/更新信息
     * @param taskId      访谈任务主键ID
     * @param resourceId  课程资源ID
     * @return Boolean
     */
    Boolean saveData(List<AiInterviewTaskEmpSaveRequest> requestList, Long taskId, Long resourceId);

    /**
     * 删除-访谈员工信息
     *
     * @param taskId 访谈任务主键ID
     */
    void deleteByTaskId(Long taskId);

    /**
     * 查询-访谈员工详情
     *
     * @param taskEmpId AI访谈任务-员工ID
     * @return AiInterviewTaskEmpDetailResponse
     */
    AiInterviewTaskEmpResponse getDetail(Long taskEmpId);

    /**
     * 分页-任务员工列表
     *
     * @param page    分页参数
     * @param request 过滤条件
     * @param order   排序条件
     * @return ResponsePage<AiInterviewTaskEmpDetailResponse>
     */
    ResponsePage<AiInterviewTaskEmpResponse> findTaskEmpListPage(Page<AiInterviewTaskEmp> page, AiInterviewTaskEmpListRequest request, BaseOrder order);

    /**
     * 查询-我的访谈对象详情
     *
     * @param taskEmpId AI访谈任务-员工ID
     * @return AiInterviewObjectDetailResponse
     */
    AiInterviewObjectResponse getMyObjectDetail(Long taskEmpId);

    /**
     * 校验-AI访谈任务员工
     *
     * @param saveRequestList 保存的访谈员工信息
     */
    void verifyTaskEmp(List<AiInterviewTaskEmpSaveRequest> saveRequestList);

    /**
     * 查询-我的访谈任务详情
     *
     * @param taskEmpId AI访谈任务-员工ID
     * @return AiInterviewTaskInfoResponse
     */
    AiInterviewTaskInfoResponse getMyTaskInfoDetail(Long taskEmpId);
}
