package com.deloitte.dhr.ai.module.coach.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 培训任务-AI陪练-详情
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("AiTrainTaskDetailResponse")
public class AiTrainTaskDetailResponse extends AiTaskResponse {

    /**
     * 类别ID
     */
    @ApiModelProperty(value = "类别ID", name = "categoryId")
    private Long categoryId;

    /**
     * 类别名称
     */
    @ApiModelProperty(value = "类别名称", name = "categoryName")
    private String categoryName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称", name = "courseName")
    private String courseName;

    /**
     * 课程封面图链接
     */
    @ApiModelProperty(value = "课程封面图链接", name = "courseCoverUrl")
    private String courseCoverUrl;

    /**
     * 对话场景
     */
    @ApiModelProperty(value = "对话场景", name = "dialogueScene")
    private String dialogueScene;

    /**
     * 对话目标
     */
    @ApiModelProperty(value = "对话目标", name = "dialogueObjective")
    private String dialogueObjective;

}