package com.deloitte.dhr.ai.module.ai.strategy.digital;

import com.deloitte.dhr.ai.enums.DigitalLiningIntentAction;
import com.deloitte.dhr.ai.enums.DigitalLiningIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.DigitalLiningActionStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * 数字人(李宁)-考勤异常-事务提交-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
@DigitalLiningActionStrategyType(item = DigitalLiningIntentItem.KQYC, action = DigitalLiningIntentAction.TC)
public class DigitalLiningAttendanceExceptionTcStrategy implements DigitalLiningStrategy {

    @Override
    public Flux<List<AiContent>> executeStream(Map<String, Object> param) {
        return Flux.empty();
    }

}
