package com.deloitte.dhr.ai.module.coach.train.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskEmpResponse;
import com.deloitte.dhr.ai.module.coach.task.service.AiCoachTaskEmpService;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainMyScoringItem;
import com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainMyScoringItemMapper;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyOverviewResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyScoringItemResponse;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainMyScoringItemService;
import com.deloitte.dhr.common.SuperServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 课程资源-AI陪练-我的得分项-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiTrainMyScoringItemServiceImpl extends SuperServiceImpl<AiTrainMyScoringItemMapper, AiTrainMyScoringItem> implements AiTrainMyScoringItemService {

    @Autowired
    private AiCoachTaskEmpService aiCoachTaskEmpService;

    @Override
    public AiTrainMyOverviewResponse getMyOverview(Long taskEmpId) {

        // 查询总分和消耗时常信息
        AiCoachTaskEmpResponse empResponse = aiCoachTaskEmpService.getDetail(taskEmpId);
        AiTrainMyOverviewResponse response = BeanUtil.copyProperties(empResponse, AiTrainMyOverviewResponse.class);
        response.setFinalScore(response.getFinalScore() == null ? BigDecimal.ZERO : response.getFinalScore().setScale(1, RoundingMode.HALF_UP));
        response.setElapsedTime(response.getElapsedTime() == null ? BigDecimal.ZERO : response.getElapsedTime().setScale(1, RoundingMode.HALF_UP));

        // 查询-得分项信息
        LambdaQueryWrapper<AiTrainMyScoringItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrainMyScoringItem::getTaskEmpId, taskEmpId);
        List<AiTrainMyScoringItem> list = this.list(queryWrapper);
        List<AiTrainMyScoringItemResponse> scoringItemList = BeanUtil.copyToList(list, AiTrainMyScoringItemResponse.class);
        response.setScoringItemInfoList(scoringItemList);
        scoringItemList.forEach(item -> item.setScoringItemScore(item.getScoringItemScore() == null ? null : item.getScoringItemScore().setScale(1, RoundingMode.HALF_UP)));
        return response;
    }

}

