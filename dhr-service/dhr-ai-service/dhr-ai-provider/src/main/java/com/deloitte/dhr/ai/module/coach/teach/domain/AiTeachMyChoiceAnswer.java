package com.deloitte.dhr.ai.module.coach.teach.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 课程资源-教学课程-我的选择问题答案表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_teach_my_choice_answer")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-教学课程-我的选择问题答案表")
public class AiTeachMyChoiceAnswer extends SuperLogicModel<AiTeachMyChoiceAnswer> {

    /**
     * 培训任务员工ID
     */
    @TableField(value = "task_emp_id")
    private Long taskEmpId;

    /**
     * 考核题目ID
     */
    @TableField(value = "question_id")
    private Long questionId;

    /**
     * 选择题选项ID（用,隔开）
     */
    @TableField(value = "option_ids")
    private String optionIds;

    /**
     * 是否正确
     */
    @TableField(value = "is_right")
    private Boolean isRight;


}