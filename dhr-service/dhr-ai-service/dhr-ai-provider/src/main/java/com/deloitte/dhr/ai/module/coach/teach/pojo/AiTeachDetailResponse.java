package com.deloitte.dhr.ai.module.coach.teach.pojo;

import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceAttachResponse;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceInfoResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 课程资源-教学课程-详情
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachDetailResponse")
public class AiTeachDetailResponse {

    /**
     * 课程资源-基本信息
     */
    @ApiModelProperty(value = "课程资源-基本信息", name = "resourceInfo")
    private AiCourseResourceInfoResponse resourceInfo;

    /**
     * 课程资源-学习附件信息
     */
    @ApiModelProperty(value = "课程资源-学习附件信息", name = "attachInfoList")
    private List<AiCourseResourceAttachResponse> attachInfoList;

    /**
     * 教学课程-基本信息
     */
    @ApiModelProperty(value = "教学课程-基本信息", name = "teachInfo")
    private AiTeachInfoResponse teachInfo;

    /**
     * 教学课程-考核内容（题目信息）
     */
    @ApiModelProperty(value = "教学课程-考核内容（题目信息）", name = "questionInfoList")
    private List<AiTeachTestQuestionResponse> questionInfoList;

}