package com.deloitte.dhr.ai.module.coach.train.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 课程资源-AI陪练-我的对话历史记录表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_train_my_dialogue_history")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-AI陪练-我的对话历史记录表")
public class AiTrainMyDialogueHistory extends SuperLogicModel<AiTrainMyDialogueHistory> {

    /**
     * AI培训任务员工ID
     */
    @TableField(value = "task_emp_id")
    private Long taskEmpId;

    /**
     * AI培训对象ID
     */
    @TableField(value = "object_id")
    private Long objectId;

    /**
     * 消息内容
     */
    @TableField(value = "message")
    private String message;

    /**
     * 发送时间
     */
    @TableField(value = "send_time")
    private Date sendTime;

    /**
     * 内容所属：AI-AI培训对象，EMP-AI培训员工
     */
    @TableField(value = "message_belong")
    private String messageBelong;

    /**
     * 语音文件Url
     */
    @TableField(value = "voice_file_url")
    private String voiceFileUrl;


}