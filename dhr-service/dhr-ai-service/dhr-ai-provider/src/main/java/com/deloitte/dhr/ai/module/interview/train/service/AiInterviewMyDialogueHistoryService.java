package com.deloitte.dhr.ai.module.interview.train.service;

import cn.hutool.json.JSONObject;
import com.deloitte.dhr.ai.module.interview.train.domain.AiInterviewMyDialogueHistory;
import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewDialogueQuestionResponse;
import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewMyDialogueHistoryListRequest;
import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewMyDialogueHistoryResponse;
import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewMyQuestionListResponse;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * AI访谈-访谈任务-我的对话历史记录-相关业务接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface AiInterviewMyDialogueHistoryService extends SuperService<AiInterviewMyDialogueHistory> {

    /**
     * 查询-我的对话历史记录
     *
     * @param request 查询条件
     * @return List<AiInterviewMyDialogueHistoryResponse>
     */
    List<AiInterviewMyDialogueHistoryResponse> getMyDialogueHistory(AiInterviewMyDialogueHistoryListRequest request);

    /**
     * 获取AI下个问题
     *
     * @return AiInterviewMyDialogueHistory
     */
    AiInterviewMyDialogueHistory getNextAiQuestion(List<AiInterviewDialogueQuestionResponse> aiQuestionList, List<JSONObject> analyzeList, AiInterviewMyDialogueHistory dialogueHistorysaveRequest, List<AiInterviewMyDialogueHistory> dialogueHistoryList);

    List<AiInterviewMyQuestionListResponse> getMyQuestionList(Long taskEmpId);
}
