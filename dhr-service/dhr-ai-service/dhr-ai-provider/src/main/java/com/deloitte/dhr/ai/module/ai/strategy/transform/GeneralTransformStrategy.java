package com.deloitte.dhr.ai.module.ai.strategy.transform;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiRoleType;
import com.deloitte.dhr.ai.enums.AiTransformType;
import com.deloitte.dhr.ai.module.ai.annotation.TransformStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;


/**
 * AI转换-常规（纯文本）-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@TransformStrategyType(AiTransformType.TEXT)
public class GeneralTransformStrategy implements ResponseTransformStrategy {

    @Override
    public Flux<List<AiContent>> transformStream(Flux<String> rawStream, AiRequest aiRequest) {
        return rawStream
                .filter(raw -> StrUtil.equals(JSONObject.parseObject(raw).getString("event"), "message"))
                .map(raw -> {
                    JSONObject rawObj = JSONObject.parseObject(raw);
                    AiContent content = AiContent.builder()
                            .contentType(AiContentType.TEXT.getCode())
                            .content(rawObj.getString("answer"))
                            .build();
                    return List.of(content);
                });
    }

    @Override
    public Flux<List<AiContent>> transformBlock(Flux<String> rawStream, AiRequest aiRequest) {
        return transformStream(rawStream, aiRequest);
    }

}

