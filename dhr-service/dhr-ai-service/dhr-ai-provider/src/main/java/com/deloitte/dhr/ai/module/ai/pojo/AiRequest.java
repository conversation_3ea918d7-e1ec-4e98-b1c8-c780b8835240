package com.deloitte.dhr.ai.module.ai.pojo;

import com.deloitte.dhr.ai.enums.AiSubType;
import com.deloitte.dhr.ai.enums.AiType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * AI
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("AiRequest")
public class AiRequest {

    @ApiModelProperty(value = "会话ID", name = "conversationId")
    private String conversationId;

    @ApiModelProperty(value = "对话ID", name = "dialogueId")
    private Long dialogueId;

    @ApiModelProperty(value = "消息内容", name = "contents")
    private List<AiContent> contents;

    @ApiModelProperty(value = "是否流响应", name = "stream")
    private Boolean stream = true;

    @ApiModelProperty(value = "用户ID", name = "userId")
    private String userId;

    /**
     * {@link AiType}
     */
    @NotBlank(message = "AI类型不能为空")
    @ApiModelProperty(value = "AI类型", name = "aiType")
    private String aiType;

    /**
     * {@link AiSubType}
     */
    @NotBlank(message = "AI子类型不能为空")
    @ApiModelProperty(value = "AI子类型", name = "aiSubType")
    private String aiSubType;

    @ApiModelProperty(value = "是否联网", name = "isSearch")
    private Boolean isSearch = true;
}
