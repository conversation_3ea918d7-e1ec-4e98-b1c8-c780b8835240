package com.deloitte.dhr.ai.module.chat.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.enums.AiSscIntentAction;
import com.deloitte.dhr.ai.module.chat.domain.AiChatMsg;
import com.deloitte.dhr.ai.module.chat.domain.AiChatMsgContent;
import com.deloitte.dhr.ai.module.chat.mapper.AiChatMsgContentMapper;
import com.deloitte.dhr.ai.module.chat.mapper.AiChatMsgMapper;
import com.deloitte.dhr.ai.module.chat.pojo.*;
import com.deloitte.dhr.ai.module.chat.service.AiChatMsgContentService;
import com.deloitte.dhr.ai.module.chat.service.AiChatMsgService;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.pojo.UserDto;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * AI聊天-聊天消息-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class AiChatMsgServiceImpl extends SuperServiceImpl<AiChatMsgMapper, AiChatMsg> implements AiChatMsgService {

    @Autowired
    private AiChatMsgMapper aiChatMsgMapper;
    @Autowired
    private AiChatMsgContentService chatMsgContentService;
    @Autowired
    private AiChatMsgContentMapper aiChatMsgContentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdateData(AiChatMsgSaveRequest request) {

        AiChatMsg chatMsg = BeanUtil.copyProperties(request, AiChatMsg.class);
        this.saveOrUpdate(chatMsg);

        return chatMsg.getId();
    }

    @Override
    public void deleteByDialogueId(Long dialogueId) {

        // 删除-聊天消息
        aiChatMsgMapper.delete(new LambdaQueryWrapper<AiChatMsg>().eq(AiChatMsg::getDialogueId, dialogueId));

        // 删除-聊天消息内容
        chatMsgContentService.remove(new LambdaQueryWrapper<AiChatMsgContent>().eq(AiChatMsgContent::getDialogueId, dialogueId));

    }

    @Override
    public List<AiChatMsgListResponse> getChatList(AiChatMsgListRequest request) {
        // 查询-聊天消息
        List<AiChatMsgListResponse> responseList = aiChatMsgMapper.getChatList(request);
        if (CollUtil.isEmpty(responseList)) {
            return Collections.emptyList();
        }
        List<Long> msgIds = responseList.stream().map(AiChatMsgListResponse::getId).distinct().collect(Collectors.toList());

        // 查询-聊天消息内容
        List<AiChatMsgContent> msgContents = chatMsgContentService.list(new LambdaQueryWrapper<AiChatMsgContent>().in(AiChatMsgContent::getMsgId, msgIds));
        Map<Long, List<AiChatMsgContentListResponse>> contentHistoryMap =
                msgContents.stream().collect(Collectors.groupingBy(AiChatMsgContent::getMsgId,
                        Collectors.mapping(a -> BeanUtil.copyProperties(a, AiChatMsgContentListResponse.class), Collectors.toList())));

        // 组装数据
        responseList.forEach(response -> {
            List<AiChatMsgContentListResponse> contents = contentHistoryMap.get(response.getId());
            if (CollUtil.isEmpty(contents)) {
                response.setContents(Collections.emptyList());
                return;
            }
            // 聊天内容
            List<AiChatMsgContentListResponse> list = contents.stream().sorted(Comparator.comparing(AiChatMsgContentListResponse::getContentOrder)).collect(Collectors.toList());
            response.setContents(list);
        });
        return responseList.stream().sorted(Comparator.comparing(AiChatMsgListResponse::getSendTime)).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponsePage<ChatHistoryResponse> chatHistory(Page page, ChatHistoryRequest request) {
        UserDto loginUser = LoginUtil.getLoginUser();
        if (loginUser == null){
            throw new CommRunException("用户未登录");
        }

        if(CharSequenceUtil.isNotEmpty(request.getChatType())){
            if(AiSscIntentAction.IR.getCode().equals(request.getChatType())){
                request.setChatType(AiSscIntentAction.IR.getName());
            } else if(AiSscIntentAction.TC.getCode().equals(request.getChatType())){
                request.setChatType(AiSscIntentAction.TC.getName());
            } else if(AiSscIntentAction.TQ.getCode().equals(request.getChatType())){
                request.setChatType(AiSscIntentAction.TQ.getName());
            }
        }
        List<Long> ids = aiChatMsgMapper.chatHistoryIds(page, request);
        if(ids.isEmpty()){
            return new ResponsePage<>(page, new ArrayList<>());
        }
        List<AiChatMsgContent> userMsgContentList = aiChatMsgContentMapper.selectList(new LambdaQueryWrapper<AiChatMsgContent>()
                .in(AiChatMsgContent::getMsgId, ids).orderByDesc(AiChatMsgContent::getMsgId));

        if (userMsgContentList.isEmpty()) {
            return new ResponsePage<>(page, new ArrayList<>());
        }
        List<AiChatMsg> aiMsgList = aiChatMsgMapper.selectList(new LambdaQueryWrapper<AiChatMsg>().in(AiChatMsg::getMsgId, ids));
        List<Long> aiIds = aiMsgList.stream().map(AiChatMsg::getId).collect(Collectors.toList());
        Map<Long,List<AiChatMsg>> aiIdMap = aiMsgList.stream().collect(Collectors.groupingBy(AiChatMsg::getMsgId));

        List<AiChatMsgContent> aiMsgContentList = aiChatMsgContentMapper.selectList(new LambdaQueryWrapper<AiChatMsgContent>()
                .in(AiChatMsgContent::getMsgId, aiIds).orderByDesc(AiChatMsgContent::getMsgId).orderByAsc(AiChatMsgContent::getContentOrder));

        //数据分组
        Map<Long, List<AiChatMsgContent>> userMsgContentMap = userMsgContentList.stream().collect(Collectors.groupingBy(AiChatMsgContent::getMsgId));
        Map<Long, List<AiChatMsgContent>> aiMsgContentMap = aiMsgContentList.stream().collect(Collectors.groupingBy(AiChatMsgContent::getMsgId));

        List<ChatHistoryResponse> list = ids.stream().map(a -> {
            ChatHistoryResponse response = new ChatHistoryResponse();
            List<AiChatMsgContent> userMsgConList = userMsgContentMap.get(a);
            if (CollUtil.isEmpty(userMsgConList)) {
                return response;
            }
            response.setUserTime(userMsgConList.get(0).getCreateTime());
            response.setUserMsgList(userMsgConList.stream().map(this::dealUserMsgContent).collect(Collectors.toList()));
            List<AiChatMsg> aiMsgList2 = aiIdMap.get( a);
            if (CollUtil.isEmpty(aiMsgList2)) {
                return response;
            }
            List<AiChatMsgContent> aiMsgConList = aiMsgContentMap.get(aiMsgList2.get(0).getId());
            if (CollUtil.isEmpty(aiMsgConList)) {
                return response;
            }
            response.setAiTime(aiMsgConList.get(aiMsgConList.size()-1).getCreateTime());
            response.setAiMsgList(aiMsgConList.stream().map(this::dealAiMsgContent).collect(Collectors.toList()));
            response.setTimenum((int) (response.getAiTime().getTime() - response.getUserTime().getTime()) / 1000);
            return response;
        }).collect(Collectors.toList());
        return new ResponsePage<>(page, list);
    }
    private ChatHistoryResponse.AiChatContent dealAiMsgContent(AiChatMsgContent item) {
        ChatHistoryResponse.AiChatContent cc = new ChatHistoryResponse.AiChatContent();
        cc.setAiMsg(item.getContent());
        cc.setType(item.getContentType());
        return cc;
    }

    private ChatHistoryResponse.UserChatContent dealUserMsgContent(AiChatMsgContent item) {
        ChatHistoryResponse.UserChatContent cc = new ChatHistoryResponse.UserChatContent();
        cc.setUserMsg(item.getContent());
        cc.setType(item.getContentType());
        return cc;
    }
}

