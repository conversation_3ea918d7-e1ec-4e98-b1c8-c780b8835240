package com.deloitte.dhr.ai.module.coach.train.service;

import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachTestQuestionSaveRequest;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainDialogueQuestion;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainDialogueQuestionResponse;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * 课程资源-AI陪练-对话问题-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-01-03
 */
public interface AiTrainDialogueQuestionService extends SuperService<AiTrainDialogueQuestion> {

    /**
     * 查询-AI陪练-对话问题信息
     *
     * @param resourceId 课程资源ID
     * @return List<AiTrainDialogueQuestionResponse>
     */
    List<AiTrainDialogueQuestionResponse> getByResourceId(Long resourceId);

    /**
     * 保存/更新-AI陪练-对话问题信息
     *
     * @param requestList AI陪练-对话问题-保存/更新信息
     * @param resourceId  课程资源ID
     * @return List<AiTrainDialogueQuestion>
     */
    List<AiTrainDialogueQuestion> saveData(List<AiTeachTestQuestionSaveRequest> requestList, Long resourceId);

    /**
     * 删除-AI陪练-对话问题信息
     *
     * @param resourceId 课程资源ID
     */
    void deleteByResourceId(Long resourceId);

    /**
     * 保存AI生成问题语音信息
     *
     * @param questionList AI问题信息
     */
    void saveAiQuestionVoice(List<AiTrainDialogueQuestion> questionList);

}
