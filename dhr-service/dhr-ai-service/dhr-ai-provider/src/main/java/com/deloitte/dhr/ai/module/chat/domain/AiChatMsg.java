package com.deloitte.dhr.ai.module.chat.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * AI聊天-聊天消息表
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_chat_msg")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AI聊天-聊天消息表")
public class AiChatMsg extends SuperLogicModel<AiChatMsg> {

    /**
     * 对话ID
     */
    @TableField(value = "dialogue_id")
    private Long dialogueId;

    /**
     * 会话ID
     */
    @TableField(value = "conversation_id")
    private String conversationId;

    /**
     * AI类型
     */
    @TableField(value = "ai_type")
    private String aiType;

    /**
     * AI子类型
     */
    @TableField(value = "ai_sub_type")
    private String aiSubType;

    /**
     * 角色
     */
    @TableField(value = "role")
    private String role;

    /**
     * 发送时间
     */
    @TableField(value = "send_time")
    private Date sendTime;

    /**
     * 提问消息ID
     */
    @TableField(value = "msg_id")
    private Long msgId;
}