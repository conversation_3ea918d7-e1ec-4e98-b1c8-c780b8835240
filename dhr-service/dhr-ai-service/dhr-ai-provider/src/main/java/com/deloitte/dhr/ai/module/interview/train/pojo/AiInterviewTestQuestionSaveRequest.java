package com.deloitte.dhr.ai.module.interview.train.pojo;

import com.deloitte.dhr.ai.module.interview.validation.Save;
import com.deloitte.dhr.ai.module.interview.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * AI访谈-访谈任务-考核题目-保存
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewTestQuestionSaveRequest")
public class AiInterviewTestQuestionSaveRequest {

    /**
     * 沟通主题
     */
    @Length(groups = {Save.class, Submit.class}, max = 255, message = "沟通主题不能超过255个字符")
    @NotBlank(groups = {Save.class, Submit.class}, message = "沟通主题不能为空")
    @ApiModelProperty(value = "沟通主题", name = "interviewQuestion")
    private String interviewQuestion;

    /**
     * 题目顺序
     */
    @ApiModelProperty(value = "题目顺序", name = "questionOrder")
    private Integer questionOrder;

    /**
     * 沟通目标
     */
    @NotNull(groups = {Submit.class}, message = "沟通目标不能为空")
    @ApiModelProperty(value = "沟通目标", name = "interviewObjective")
    private String interviewObjective;
}