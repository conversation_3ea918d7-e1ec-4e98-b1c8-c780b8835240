package com.deloitte.dhr.ai.module.interview.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
/**
 * AI访谈-访谈任务-我的对话历史记录
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("AiInterviewMyDialogueHistoryResponse")
public class AiInterviewMyDialogueHistoryResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容", name = "message")
    private String message;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间", name = "sendTime")
    private Date sendTime;

    /**
     * 内容所属：AI访谈对象，EMP-AI访谈员工
     */
    @ApiModelProperty(value = "内容所属：AI访谈对象，EMP-AI访谈员工", name = "messageBelong")
    private String messageBelong;

    /**
     * 语音文件Url
     */
    @ApiModelProperty(value = "语音文件Url", name = "voiceFileUrl")
    private String voiceFileUrl;

    /**
     * 所属问题ID
     */
    @ApiModelProperty(value = "所属问题ID", name = "questionId")
    private Long questionId;

    /**
     * 答复状态,EmpTaskQuestionStatus
     */
    @ApiModelProperty(value = "答复状态，01通过，02不通过，03跳过，99结束", name = "status")
    private String status;

    /**
     * 答复次数
     */
    @ApiModelProperty(value = "答复次数", name = "dfNum")
    private Long dfNum;
}