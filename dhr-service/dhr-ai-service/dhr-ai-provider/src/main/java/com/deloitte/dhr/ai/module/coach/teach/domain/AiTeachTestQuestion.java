package com.deloitte.dhr.ai.module.coach.teach.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 课程资源-教学课程-考核题目表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_teach_test_question")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-教学课程-考核题目表")
public class AiTeachTestQuestion extends SuperLogicModel<AiTeachTestQuestion> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * AI题目ID
     */
    @TableField(value = "ai_question_id")
    private String aiQuestionId;

    /**
     * 题目名称
     */
    @TableField(value = "question_name")
    private String questionName;

    /**
     * 题目类型;DXT：单选题，PDT：判断题，WDT：问答题
     */
    @TableField(value = "question_type")
    private String questionType;

    /**
     * 题目顺序
     */
    @TableField(value = "question_order")
    private Integer questionOrder;

    /**
     * 题目来源（保存AI大模型返回的json数据）
     */
    @TableField(value = "question_source")
    private String questionSource;

}