package com.deloitte.dhr.ai.enums;

import com.deloitte.dhr.common.base.exception.CommRunException;
import lombok.Getter;

/**
 * AI类型子项枚举
 *
 * <AUTHOR>
 * {@code @date} 2024-12-05
 */
@Getter
public enum AiSubType {

    //============================================================================== SSC =========================================================================
    SSC("ssc", "共享中心", AiType.SSC, AiTransformType.SSC),

    //============================================DUO=============================== DUO =========================================================================
    GENERAL("general", "普通回答", AiType.DUO, AiTransformType.TEXT),
    XMIND("xmind", "思维导图", AiType.DUO, AiTransformType.XMIND),
    MEETING_MINUTES("meeting_minutes", "整理纪要", AiType.DUO, AiTransformType.TEXT),
    GENERATE_REPORT("generate_report", "生成报告", AiType.DUO, AiTransformType.TEXT),
    TEXT_GENERATED_IMAGE("text_generated_image", "生成图片", AiType.DUO, AiTransformType.IMAGE),
    TRANSLATE("translate", "精准翻译", AiType.DUO, AiTransformType.TEXT),
    DOCUMENT_QA("document_qa", "文档问答", AiType.DUO, AiTransformType.TEXT),
    DOCUMENT_SUMMARY("document_summary", "总结文档", AiType.DUO, AiTransformType.TEXT),

    //================================= COACH =================================
    TEACH_QUESTION_GENERATE("teach_question_generate", "教学课程-题目生成", AiType.COACH, AiTransformType.COACH),
    TEACH_QUESTION_EVALUATE("teach_question_evaluate", "教学课程-题目评分", AiType.COACH, AiTransformType.COACH),
    TEACH_QUESTION_EVALUATE_BATCH("teach_question_evaluate_batch", "教学课程-题目评分-批量", AiType.COACH, AiTransformType.COACH),
    TRAIN_QUESTION_GENERATE("train_question_generate", "AI陪练-题目生成", AiType.COACH, AiTransformType.COACH),
    TRAIN_FRAMEWORK_GENERATE("train_framework_generate", "AI陪练-沟通框架", AiType.COACH, AiTransformType.COACH),
    TRAIN_TAG_EXTRACTOR("train_tag_extractor", "AI陪练-标签抽取", AiType.COACH, AiTransformType.COACH),
    TRAIN_CHAT_ANALYZE("train_chat_analyze", "AI陪练-聊天分析", AiType.COACH, AiTransformType.COACH),
    TRAIN_TEXT_TO_SPEECH("train_text_to_speech", "AI陪练-文字转语音", AiType.COACH, AiTransformType.COACH),

    //================================= INTERVIEW =================================
    INTERVIEW_FRAMEWORK_GENERATE("interview_framework_generate", "访谈助手-沟通框架生成", AiType.INTERVIEW, AiTransformType.INTERVIEW),
    INTERVIEW_ANSWER_ANALYZE("interview_answer_analyze", "访谈助手-回答分析", AiType.INTERVIEW, AiTransformType.INTERVIEW),
    INTERVIEW_REPORT_ANALYZE("interview_report_analyze", "访谈助手-生成报告", AiType.INTERVIEW, AiTransformType.INTERVIEW),


    //================================= ANALYSIS =================================
    DATA_ANALYSIS("data_analysis", "数据分析", AiType.DATA_ANALYSIS, AiTransformType.DATA_ANALYSIS),
    DATA_PREDICTION("data_prediction", "数据预测", AiType.DATA_ANALYSIS, AiTransformType.DATA_ANALYSIS_PREDICTION),

    //================================= LEARNING =================================
    LEARNING("learning", "学习助手", AiType.LEARNING, AiTransformType.DATASET),

    //================================= DIGITAL HUMAN =================================
    DIGITAL_HUMAN("digital_human", "数字人", AiType.DIGITAL_HUMAN, AiTransformType.TEXT),
    DIGITAL_HUMAN_LINING("digital_human_lining", "数字人-李宁", AiType.DIGITAL_HUMAN_LINING, AiTransformType.DigitalLining);

    private final String code;

    private final String name;

    private final AiType type;

    private final AiTransformType aiTransformType;


    AiSubType(String code, String name, AiType type, AiTransformType aiTransformType) {
        this.code = code;
        this.name = name;
        this.type = type;
        this.aiTransformType = aiTransformType;

    }

    /**
     * 根据子类型获取枚举
     *
     * @param code code
     * @return AiSubType
     */
    public static AiSubType getByCode(String code) {
        for (AiSubType aiSubType : AiSubType.values()) {
            if (aiSubType.getCode().equals(code)) {
                return aiSubType;
            }
        }
        throw new CommRunException("该AI子类型未定义！");
    }

}
