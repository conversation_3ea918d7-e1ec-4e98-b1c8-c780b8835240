package com.deloitte.dhr.ai.module.coach.teach.pojo;

import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 课程资源-教学课程-考核题目-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachTestQuestionSaveRequest")
public class AiTeachTestQuestionSaveRequest {

    /**
     * AI题目ID
     */
    @Length(groups = {Save.class, Submit.class}, max = 40, message = "AI题目ID不能超过32个字符")
    @ApiModelProperty(value = "AI题目ID", name = "aiQuestionId")
    private String aiQuestionId;

    /**
     * 题目名称
     */
    @Length(groups = {Save.class, Submit.class}, max = 255, message = "题目名称不能超过255个字符")
    @NotBlank(groups = {Save.class, Submit.class}, message = "题目名称不能为空")
    @ApiModelProperty(value = "题目名称", name = "questionName")
    private String questionName;

    /**
     * 题目类型;DXT：单选题，PDT：判断题，WDT：问答题
     */
    @Length(groups = {Save.class, Submit.class}, max = 10, message = "题目类型不能超过10个字符")
    @NotBlank(groups = {Save.class, Submit.class}, message = "题目类型不能为空")
    @ApiModelProperty(value = "题目类型;DXT：单选题，PDT：判断题，WDT：问答题", name = "questionType")
    private String questionType;

    /**
     * 题目顺序
     */
    @ApiModelProperty(value = "题目顺序", name = "questionOrder")
    private Integer questionOrder;

    /**
     * 题目内容（用对象接收，兼容各种题目类型）
     */
    @NotNull(groups = {Submit.class}, message = "题目内容不能为空")
    @ApiModelProperty(value = "题目内容", name = "questionContent")
    private Object questionContent;

    /**
     * 题目来源
     */
    @ApiModelProperty(value = "题目来源", name = "questionSource")
    private AiTeachTestQuestionSourceSaveRequest questionSource;

}