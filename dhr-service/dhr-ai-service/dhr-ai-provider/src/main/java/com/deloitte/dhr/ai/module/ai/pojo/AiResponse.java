package com.deloitte.dhr.ai.module.ai.pojo;

import com.deloitte.dhr.ai.enums.AiRoleType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * AI
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AiResponse")
public class AiResponse {

    @ApiModelProperty(value = "聊天消息ID", name = "id")
    private Long id;

    @ApiModelProperty(value = "对话ID", name = "dialogueId")
    private Long dialogueId;

    @ApiModelProperty(value = "会话ID", name = "conversationId")
    private String conversationId;

    /**
     * {@link AiRoleType}
     */
    @ApiModelProperty(value = "角色", name = "role")
    private String role;

    @ApiModelProperty(value = "消息内容", name = "contents")
    private List<AiContent> contents;

    @ApiModelProperty(value = "对话ID", name = "msgId")
    private Long msgId;
}
