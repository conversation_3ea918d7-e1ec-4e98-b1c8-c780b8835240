package com.deloitte.dhr.ai.module.chat.controller;

import com.deloitte.dhr.ai.module.chat.pojo.AiChatDialogueListResponse;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatDialogueUpdRequest;
import com.deloitte.dhr.ai.module.chat.service.AiChatDialogueService;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperController;
import com.deloitte.dhr.common.validation.Update;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 聊天对话记录
 *
 * <AUTHOR>
 * @date 2024-03-27
 */
@RestController
@RequestMapping("/dialogue")
@Api(tags = "聊天对话记录")
@Validated
public class AiChatDialogueController extends SuperController {

    @Autowired
    private AiChatDialogueService aiChatDialogueService;

    @ApiOperation(value = "列表", notes = "列表")
    @ApiOperationSupport(order = 1)
    @GetMapping("/list/{aiGroup}")
    public ResponseVO<List<AiChatDialogueListResponse>> getDialogueList(@PathVariable("aiGroup") String aiGroup) {
        return success(aiChatDialogueService.getDialogueList(aiGroup));
    }

    @ApiOperation(value = "修改", notes = "修改")
    @ApiOperationSupport(order = 2)
    @PostMapping("/upd")
    public ResponseVO<Long> upd(@RequestBody @Validated(Update.class) AiChatDialogueUpdRequest request) {
        return success(aiChatDialogueService.updData(request));
    }

    @ApiOperation(value = "删除", notes = "删除")
    @ApiOperationSupport(order = 3)
    @DeleteMapping("/del")
    public ResponseVO<Boolean> del(@RequestParam("id") @NotNull(message = "id不能为空") Long id) {
        return success(aiChatDialogueService.deleteById(id));
    }

}
