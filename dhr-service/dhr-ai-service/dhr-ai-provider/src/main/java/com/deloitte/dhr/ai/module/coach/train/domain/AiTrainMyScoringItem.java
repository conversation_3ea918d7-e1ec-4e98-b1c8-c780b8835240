package com.deloitte.dhr.ai.module.coach.train.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.math.BigDecimal;

/**
 * 课程资源-AI陪练-我的得分项
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@TableName("ai_train_my_scoring_item")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-AI陪练-我的得分项表")
public class AiTrainMyScoringItem extends SuperLogicModel<AiTrainMyScoringItem> {

    /**
     * 培训任务员工ID
     */
    @TableField(value = "task_emp_id")
    private Long taskEmpId;

    /**
     * 得分项编码
     */
    @TableField(value = "scoring_item_code")
    private String scoringItemCode;

    /**
     * 得分项名称
     */
    @TableField(value = "scoring_item_name")
    private String scoringItemName;

    /**
     * 得分项分数
     */
    @TableField(value = "scoring_item_score")
    private BigDecimal scoringItemScore;

    /**
     * 得分项权重
     */
    @TableField(value = "scoring_item_weight")
    private BigDecimal scoringItemWeight;


}