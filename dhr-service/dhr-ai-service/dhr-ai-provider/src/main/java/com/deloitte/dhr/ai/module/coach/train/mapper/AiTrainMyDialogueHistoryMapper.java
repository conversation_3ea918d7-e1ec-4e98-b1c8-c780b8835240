package com.deloitte.dhr.ai.module.coach.train.mapper;

import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainMyDialogueHistory;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyDialogueHistoryListRequest;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyDialogueHistoryResponse;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程资源-AI陪练-我的对话历史记录-相关持久化接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTrainMyDialogueHistoryMapper extends SuperMapper<AiTrainMyDialogueHistory> {

    /**
     * 查询-我的对话历史记录
     *
     * @param request 查询条件
     * @return List<AiTrainMyDialogueHistoryResponse>
     */
    List<AiTrainMyDialogueHistoryResponse> getMyDialogueHistory(@Param("request") AiTrainMyDialogueHistoryListRequest request);

}