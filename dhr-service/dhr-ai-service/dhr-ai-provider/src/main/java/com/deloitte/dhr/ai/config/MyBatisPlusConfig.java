package com.deloitte.dhr.ai.config;


import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.deloitte.dhr.common.interceptor.DataAuthInnerInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * MyBatisPlus 配置
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 11/05/2022
 */
@EnableTransactionManagement
@Configuration
@MapperScan(basePackages = {"com.deloitte.dhr.ai.module.**.**.mapper" })
public class MyBatisPlusConfig {

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new DhrMybatisPlusInterceptor();
        //注释掉,数据拦截权限不会生效
        interceptor.addInnerInterceptor(new DataAuthInnerInterceptor());
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MARIADB));
        return interceptor;
    }

}
