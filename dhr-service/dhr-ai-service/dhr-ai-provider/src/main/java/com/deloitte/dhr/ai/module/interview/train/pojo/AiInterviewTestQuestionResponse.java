package com.deloitte.dhr.ai.module.interview.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * AI访谈资源-教学AI访谈-考核题目信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel("AiInterviewTestQuestionResponse")
public class AiInterviewTestQuestionResponse {

    /**
     * 题目ID
     */
    @ApiModelProperty(value = "题目ID", name = "id")
    private Long id;

    /**
     * AI访谈资源ID
     */
    @ApiModelProperty(value = "AI访谈资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * 沟通主题
     */
    @ApiModelProperty(value = "沟通主题", name = "interviewQuestion")
    private String interviewQuestion;

    /**
     * 题目类型;DXT：单选题，PDT：判断题，WDT：问答题
     */
    @ApiModelProperty(value = "题目类型;DXT：单选题，PDT：判断题，WDT：问答题", name = "questionType")
    private String questionType;

    /**
     * 题目顺序
     */
    @ApiModelProperty(value = "题目顺序", name = "questionOrder")
    private Integer questionOrder;

    /**
     * 沟通目标
     */
    @ApiModelProperty(value = "沟通目标", name = "interviewObjective")
    private String interviewObjective;

}