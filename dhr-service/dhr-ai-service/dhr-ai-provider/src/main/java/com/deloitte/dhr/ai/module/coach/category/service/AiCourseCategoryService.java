package com.deloitte.dhr.ai.module.coach.category.service;

import com.deloitte.dhr.ai.module.coach.category.domain.AiCourseCategory;
import com.deloitte.dhr.ai.module.coach.category.pojo.AiCourseCategoryResponse;
import com.deloitte.dhr.ai.module.coach.category.pojo.AiCourseCategorySaveRequest;
import com.deloitte.dhr.ai.module.coach.category.pojo.AiCourseCategoryTreeResponse;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * 课程类别-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiCourseCategoryService extends SuperService<AiCourseCategory> {

    /**
     * 查询-课程类别详情
     *
     * @param id 课程类别ID
     * @return AiCourseCategoryDetailResponse
     */
    AiCourseCategoryResponse getDetail(Long id);

    /**
     * 查询-课程类别树
     *
     * @param categoryName 课程类别名称
     * @return List<Tree < Long>>
     */
    List<AiCourseCategoryTreeResponse> getTree(String categoryName);

    /**
     * 保存/更新-课程类别信息
     *
     * @param request 课程类别-保存/更新信息
     * @return Long
     */
    Long saveData(AiCourseCategorySaveRequest request);

    /**
     * 删除数据
     *
     * @param id 课程类别ID
     * @return Boolean
     */
    Boolean deleteById(Long id);

    /**
     * 查询-类别全路径名称
     *
     * @param id 类别ID
     * @return 类别全路径名称
     */
    String getCategoryPathNameById(Long id);

}
