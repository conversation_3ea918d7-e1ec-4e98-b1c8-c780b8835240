package com.deloitte.dhr.ai.module.coach.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 课程资源-AI陪练-基本信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainInfoResponse")
public class AiTrainInfoResponse {

    /**
     * 对话场景
     */
    @ApiModelProperty(value = "对话场景", name = "dialogueScene")
    private String dialogueScene;

    /**
     * 对话目标
     */
    @ApiModelProperty(value = "对话目标", name = "dialogueObjective")
    private String dialogueObjective;

    /**
     * 对话提示问题数量
     */
    @ApiModelProperty(value = "对话提示问题数量", name = "dialogueQuestionNum")
    private Integer dialogueQuestionNum;

}