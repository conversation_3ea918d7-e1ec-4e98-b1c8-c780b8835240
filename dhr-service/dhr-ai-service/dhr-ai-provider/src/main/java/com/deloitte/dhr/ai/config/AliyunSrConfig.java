package com.deloitte.dhr.ai.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云语音服务配置
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "aliyun.sr")
public class AliyunSrConfig {

    /**
     * 应用appKey
     */
    private String appKey;

    /**
     * 应用appKey
     */
    private String accessKeyId;

    /**
     * 应用appKey
     */
    private String accessKeySecret;

    /**
     * 语音识别URL
     */
    private AliyunSrUrlConfig url;

    @Data
    public static class AliyunSrUrlConfig {

        /**
         * 一句话识别 https调用URL
         */
        private String asrUrl;

        /**
         * 录音文件识别极速版 https调用链接
         */
        private String flashRecognizerUrl;

    }
}
