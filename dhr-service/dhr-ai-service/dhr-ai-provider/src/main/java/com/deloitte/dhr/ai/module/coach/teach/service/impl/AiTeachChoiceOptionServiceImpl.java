package com.deloitte.dhr.ai.module.coach.teach.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachChoiceOption;
import com.deloitte.dhr.ai.module.coach.teach.mapper.AiTeachChoiceOptionMapper;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachChoiceOptionResponse;
import com.deloitte.dhr.ai.module.coach.teach.service.AiTeachChoiceOptionService;
import com.deloitte.dhr.common.SuperServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 课程资源-教学课程-考核题目-选择题选项-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiTeachChoiceOptionServiceImpl extends SuperServiceImpl<AiTeachChoiceOptionMapper, AiTeachChoiceOption> implements AiTeachChoiceOptionService {

    @Autowired
    private AiTeachChoiceOptionMapper aiTeachChoiceOptionMapper;

    @Override
    public Map<Long, List<AiTeachChoiceOptionResponse>> getMapByResourceId(Long resourceId) {
        List<AiTeachChoiceOption> choiceOptionList = this.getByResourceId(resourceId);
        return choiceOptionList.stream().collect(Collectors.groupingBy(AiTeachChoiceOption::getQuestionId, Collectors.mapping(choiceOption -> BeanUtil.copyProperties(choiceOption, AiTeachChoiceOptionResponse.class), Collectors.toList())));
    }

    @Override
    public Map<Long, Set<Long>> getMapRightOptionIdsByResourceId(Long resourceId) {
        List<AiTeachChoiceOption> choiceOptionList = this.getByResourceId(resourceId);
        return choiceOptionList.stream().filter(AiTeachChoiceOption::getIsAnswer).collect(Collectors.groupingBy(AiTeachChoiceOption::getQuestionId, Collectors.mapping(AiTeachChoiceOption::getId, Collectors.toSet())));
    }

    @Override
    public void deleteByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiTeachChoiceOption> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTeachChoiceOption::getResourceId, resourceId);
        aiTeachChoiceOptionMapper.delete(queryWrapper);
    }

    /**
     * 获取选择题选项列表
     *
     * @param resourceId 课程资源ID
     * @return List<AiTeachChoiceOption>
     */
    private List<AiTeachChoiceOption> getByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiTeachChoiceOption> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTeachChoiceOption::getResourceId, resourceId);
        queryWrapper.orderByAsc(AiTeachChoiceOption::getOptionSort);
        return aiTeachChoiceOptionMapper.selectList(queryWrapper);
    }

}

