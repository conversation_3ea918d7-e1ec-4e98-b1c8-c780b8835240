package com.deloitte.dhr.ai.enums;

import lombok.Getter;

/**
 * AI 输出结果转换方式枚举
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public enum AiTransformType {

    TEXT("text", "文本转换"),
    DATASET("dataset", "知识库转换"),
    IMAGE("image", "图片转换"),
    JSON("json", "JSON转换"),
    XMIND("xmind", "思维导图"),
    SSC("ssc", "SSC转换"),
    COACH("coach", "COACH转换"),
    DATA_ANALYSIS("data_analysis", "数据分析转换"),
    DATA_ANALYSIS_PREDICTION("data_prediction", "数据预测转换"),
    INTERVIEW("interview", "访谈转换"),
    DigitalLining("DigitalLining", "数字人-李宁")
    ;
    private final String code;

    private final String desc;


    AiTransformType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
