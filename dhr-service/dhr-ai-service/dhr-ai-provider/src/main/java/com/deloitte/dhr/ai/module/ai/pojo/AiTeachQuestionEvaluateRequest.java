package com.deloitte.dhr.ai.module.ai.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI生成教学题目
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AiTeachQuestionEvaluateRequest")
public class AiTeachQuestionEvaluateRequest {

    @ApiModelProperty(value = "题目ID", name = "questionId")
    private Long questionId;

    @ApiModelProperty(value = "题目名称", name = "question")
    private String question;

    @ApiModelProperty(value = "题目参考答案", name = "sampleAnswer")
    private String sampleAnswer;

    @ApiModelProperty(value = "用户答案", name = "userAnswer")
    private String userAnswer;

}
