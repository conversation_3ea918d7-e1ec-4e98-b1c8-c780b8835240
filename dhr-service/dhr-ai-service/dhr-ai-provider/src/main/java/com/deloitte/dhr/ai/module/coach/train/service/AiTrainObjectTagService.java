package com.deloitte.dhr.ai.module.coach.train.service;

import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainObjectTag;
import com.deloitte.dhr.common.SuperService;

/**
 * 课程资源-AI陪练-对象标签-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTrainObjectTagService extends SuperService<AiTrainObjectTag> {

    /**
     * 保存/更新-AI陪练对象标签信息
     *
     * @param objectBackgroundInfo AI陪练对象背景信息（用于AI模型解析出标签）
     * @param resourceId           课程资源ID
     * @param objectId             AI陪练对象ID
     */
    void saveData(String objectBackgroundInfo, Long resourceId, Long objectId);

    /**
     * 删除-AI陪练对象标签信息
     *
     * @param resourceId 课程资源ID
     */
    void deleteByResourceId(Long resourceId);

}
