package com.deloitte.dhr.ai.module.interview.category.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AI访谈-类别
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewCategoryResponse")
public class AiInterviewCategoryResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 类别名称
     */
    @ApiModelProperty(value = "类别名称", name = "categoryName")
    private String categoryName;

    /**
     * 类别父级ID
     */
    @ApiModelProperty(value = "类别父级ID", name = "parentId")
    private Long parentId;

    /**
     * 类别全路径
     */
    @ApiModelProperty(value = "类别全路径", name = "categoryPath")
    private String categoryPath;

}