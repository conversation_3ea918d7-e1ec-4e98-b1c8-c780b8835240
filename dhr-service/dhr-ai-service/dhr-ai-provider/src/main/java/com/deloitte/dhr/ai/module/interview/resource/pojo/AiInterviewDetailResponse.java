package com.deloitte.dhr.ai.module.interview.resource.pojo;

import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewTestQuestionResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * AI访谈-访谈任务-详情
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewDetailResponse")
public class AiInterviewDetailResponse {

    /**
     * AI访谈-基本信息
     */
    @ApiModelProperty(value = "AI访谈-基本信息", name = "resourceInfo")
    private AiInterviewInfoResponse resourceInfo;

    /**
     * AI访谈-学习附件信息
     */
    @ApiModelProperty(value = "AI访谈-学习附件信息", name = "attachInfoList")
    private List<AiInterviewResourceAttachResponse> attachInfoList;

    /**
     * AI访谈-访谈对象信息
     */
    @ApiModelProperty(value = "AI访谈-访谈对象信息", name = "objectInfo")
    private AiInterviewObjectResponse objectInfo;

    /**
     * AI访谈-对话提示信息
     */
    @ApiModelProperty(value = "AI访谈-对话提示信息", name = "questionInfoList")
    private List<AiInterviewTestQuestionResponse> questionInfoList;

}