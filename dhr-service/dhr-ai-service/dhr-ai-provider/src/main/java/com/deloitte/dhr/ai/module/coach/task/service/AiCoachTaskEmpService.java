package com.deloitte.dhr.ai.module.coach.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.coach.task.domain.AiCoachTaskEmp;
import com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskEmpListRequest;
import com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskEmpResponse;
import com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskEmpSaveRequest;
import com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskInfoResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainObjectResponse;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * 培训任务-员工-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiCoachTaskEmpService extends SuperService<AiCoachTaskEmp> {

    /**
     * 保存/更新-培训员工信息
     *
     * @param requestList 培训员工信息-保存/更新信息
     * @param taskId      培训任务主键ID
     * @param resourceId  课程资源ID
     * @return Boolean
     */
    Boolean saveData(List<AiCoachTaskEmpSaveRequest> requestList, Long taskId, Long resourceId);

    /**
     * 删除-培训员工信息
     *
     * @param taskId 培训任务主键ID
     */
    void deleteByTaskId(Long taskId);

    /**
     * 查询-陪练员工详情
     *
     * @param taskEmpId 培训任务员工ID
     * @return AiCoachTaskEmpDetailResponse
     */
    AiCoachTaskEmpResponse getDetail(Long taskEmpId);

    /**
     * 分页-任务员工列表
     *
     * @param page    分页参数
     * @param request 过滤条件
     * @param order   排序条件
     * @return ResponsePage<AiCoachTaskEmpDetailResponse>
     */
    ResponsePage<AiCoachTaskEmpResponse> findTaskEmpListPage(Page<AiCoachTaskEmp> page, AiCoachTaskEmpListRequest request, BaseOrder order);

    /**
     * 查询-我的陪练对象详情
     *
     * @param taskEmpId 培训任务员工ID
     * @return AiCoachObjectDetailResponse
     */
    AiTrainObjectResponse getMyObjectDetail(Long taskEmpId);

    /**
     * 校验-培训员工
     *
     * @param saveRequestList 保存的培训员工信息
     */
    void verifyTaskEmp(List<AiCoachTaskEmpSaveRequest> saveRequestList);

    /**
     * 查询-我的陪练任务详情
     *
     * @param taskEmpId 培训任务员工ID
     * @return AiCoachTaskInfoResponse
     */
    AiCoachTaskInfoResponse getMyTaskInfoDetail(Long taskEmpId);

}
