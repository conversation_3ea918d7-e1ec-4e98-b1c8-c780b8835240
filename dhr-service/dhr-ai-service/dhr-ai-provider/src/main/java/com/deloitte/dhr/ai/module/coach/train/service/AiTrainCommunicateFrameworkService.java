package com.deloitte.dhr.ai.module.coach.train.service;

import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainCommunicateFramework;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainCommunicateFrameworkResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainCommunicateFrameworkSaveRequest;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * 课程资源-AI陪练-沟通框架-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTrainCommunicateFrameworkService extends SuperService<AiTrainCommunicateFramework> {

    /**
     * 查询-AI陪练沟通框架信息
     *
     * @param resourceId 课程资源ID
     * @return List<AiTrainCommunicateFrameworkDetailResponse>
     */
    List<AiTrainCommunicateFrameworkResponse> getByResourceId(Long resourceId);

    /**
     * 保存/更新-AI陪练-沟通框架信息
     *
     * @param saveRequestList AI陪练-沟通框架-保存/更新信息
     * @param resourceId      课程资源ID
     * @return Boolean
     */
    Boolean saveData(List<AiTrainCommunicateFrameworkSaveRequest> saveRequestList, Long resourceId);

    /**
     * 删除-AI陪练-沟通框架信息
     *
     * @param resourceId 课程资源ID
     */
    void deleteByResourceId(Long resourceId);

    /**
     * 校验沟通框架
     *
     * @param saveRequestList AI陪练-沟通框架-保存/更新信息
     */
    void verify(List<AiTrainCommunicateFrameworkSaveRequest> saveRequestList);

}
