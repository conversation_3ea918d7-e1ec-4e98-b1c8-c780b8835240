package com.deloitte.dhr.ai.module.ai.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI陪练-对话分析
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AiTrainChatAnalyzeRequest")
public class AiTrainChatAnalyzeRequest {

    @ApiModelProperty(value = "题目名称", name = "question")
    private String question;

    @ApiModelProperty(value = "题目参考答案", name = "sampleAnswer")
    private String sampleAnswer;

    @ApiModelProperty(value = "用户答案", name = "userAnswer")
    private String userAnswer;

    @ApiModelProperty(value = "分析维度", name = "dimensions")
    private String dimensions;

}
