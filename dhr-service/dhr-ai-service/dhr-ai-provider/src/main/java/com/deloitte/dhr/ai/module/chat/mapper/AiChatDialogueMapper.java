package com.deloitte.dhr.ai.module.chat.mapper;

import com.deloitte.dhr.ai.module.chat.domain.AiChatDialogue;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatDialogueListResponse;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI聊天-聊天对话-相关持久化接口
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface AiChatDialogueMapper extends SuperMapper<AiChatDialogue> {

    /**
     * 查询-聊天对话列表
     *
     * @param aiGroup       AI分组
     * @param loginUserCode 当前登陆人编号
     * @return List<AiChatDialogueListResponse>
     */
    List<AiChatDialogueListResponse> getList(@Param("aiGroup") String aiGroup, @Param("loginUserCode") String loginUserCode);

}