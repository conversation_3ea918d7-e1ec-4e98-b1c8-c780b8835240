package com.deloitte.dhr.ai.module.interview.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AI访谈-访谈任务-对话问题
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewDialogueQuestionResponse")
public class AiInterviewDialogueQuestionResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 课程资源ID
     */
    @ApiModelProperty(value = "课程资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * AI题目语音文件URL
     */
    @ApiModelProperty(value = "AI题目语音文件URL", name = "aiQuestionVoiceFileUrl")
    private String aiQuestionVoiceFileUrl;

    /**
     * 沟通主题
     */
    @ApiModelProperty(value = "沟通主题", name = "interviewQuestion")
    private String interviewQuestion;

    /**
     * 沟通目标
     */
    @ApiModelProperty(value = "沟通目标", name = "interviewObjective")
    private String interviewObjective;
    /**
     * 题目顺序
     */
    @ApiModelProperty(value = "题目顺序", name = "questionOrder")
    private Integer questionOrder;

    /**
     * 访谈的AI对象头像
     */
    @ApiModelProperty(value = "访谈的AI对象头像", name = "objectAvatarUrl")
    private String objectAvatarUrl;

    /**
     * 答复次数
     */
    @ApiModelProperty(value = "答复次数", name = "dfNum")
    private Long dfNum;
}