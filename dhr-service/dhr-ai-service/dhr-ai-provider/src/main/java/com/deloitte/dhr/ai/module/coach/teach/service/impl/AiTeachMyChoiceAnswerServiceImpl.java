package com.deloitte.dhr.ai.module.coach.teach.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachMyChoiceAnswer;
import com.deloitte.dhr.ai.module.coach.teach.mapper.AiTeachMyChoiceAnswerMapper;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachMyChoiceAnswerResponse;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachMyCoachQuestionSaveRequest;
import com.deloitte.dhr.ai.module.coach.teach.service.AiTeachChoiceOptionService;
import com.deloitte.dhr.ai.module.coach.teach.service.AiTeachMyChoiceAnswerService;
import com.deloitte.dhr.common.SuperServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 课程资源-教学课程-我的选择问题答案-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiTeachMyChoiceAnswerServiceImpl extends SuperServiceImpl<AiTeachMyChoiceAnswerMapper, AiTeachMyChoiceAnswer> implements AiTeachMyChoiceAnswerService {

    @Autowired
    private AiTeachMyChoiceAnswerMapper aiTeachMyChoiceAnswerMapper;
    @Autowired
    private AiTeachChoiceOptionService aiTeachChoiceOptionService;

    @Override
    public void deleteByTaskEmpId(Long taskEmpId) {
        LambdaQueryWrapper<AiTeachMyChoiceAnswer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTeachMyChoiceAnswer::getTaskEmpId, taskEmpId);
        this.remove(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<Long, Boolean> saveData(List<AiTeachMyCoachQuestionSaveRequest> requestList, Long resourceId, Long taskId, Long taskEmpId) {

        // 删除旧的题目答案信息
        this.deleteByTaskEmpId(taskEmpId);

        // 保存新的的题目答案信息
        requestList = requestList.stream().filter(question -> !StrUtil.equals(question.getQuestionType(), AiCoachConstant.TeachQuestionType.ESSAY_QUESTION)).collect(Collectors.toList());
        if (CollUtil.isEmpty(requestList)) {
            return Collections.emptyMap();
        }
        Map<Long, Boolean> result = new HashMap<>(AiCoachConstant.LIST_INIT_SIZE);
        List<AiTeachMyChoiceAnswer> saveList = new ArrayList<>();
        // 查询每道题的正确选项ID
        Map<Long, Set<Long>> rightOptionMap = aiTeachChoiceOptionService.getMapRightOptionIdsByResourceId(resourceId);
        for (AiTeachMyCoachQuestionSaveRequest question : requestList) {
            Set<Long> rightOptionIds = rightOptionMap.get(question.getQuestionId());
            if (CollUtil.isEmpty(rightOptionIds)) {
                continue;
            }
            // 判断是否答题正确
            Boolean isRight = isAnswerRight(rightOptionIds, question.getQuestionAnswer());
            result.put(question.getQuestionId(), isRight);
            AiTeachMyChoiceAnswer answer = new AiTeachMyChoiceAnswer(taskEmpId, question.getQuestionId(), question.getQuestionAnswer(), isRight);
            saveList.add(answer);
        }
        this.saveBatch(saveList);
        return result;
    }

    @Override
    public List<AiTeachMyChoiceAnswerResponse> getByTaskEmpId(Long taskEmpId) {
        LambdaQueryWrapper<AiTeachMyChoiceAnswer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTeachMyChoiceAnswer::getTaskEmpId, taskEmpId);
        List<AiTeachMyChoiceAnswer> list = aiTeachMyChoiceAnswerMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<AiTeachMyChoiceAnswerResponse> responseList = new ArrayList<>();
        list.forEach(answer -> {
            AiTeachMyChoiceAnswerResponse response = BeanUtil.copyProperties(answer, AiTeachMyChoiceAnswerResponse.class);
            response.setOptionIds(StrUtil.split(answer.getOptionIds(), ",").stream().map(Long::valueOf).collect(Collectors.toList()));
            responseList.add(response);
        });
        return responseList;
    }

    /**
     * 判断选择题是否答题正确
     *
     * @param rightOptionIds       正确的选项ID
     * @param myAnswerOptionIdsStr 我的选项ID，多个用','隔开
     * @return Boolean
     */
    private Boolean isAnswerRight(Set<Long> rightOptionIds, String myAnswerOptionIdsStr) {
        Set<Long> myAnswerOptionIds = StrUtil.split(myAnswerOptionIdsStr, ",").stream().map(Long::valueOf).collect(Collectors.toSet());
        return rightOptionIds.containsAll(myAnswerOptionIds);
    }

}

