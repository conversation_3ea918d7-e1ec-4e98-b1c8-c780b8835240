package com.deloitte.dhr.ai.module.coach.teach.service;

import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachMyCoachDetailResponse;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachMyCoachSaveRequest;

/**
 * 课程资源-教学课程-我的培训-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiMyTeachService {

    /**
     * 查询-教学课程-我的培训详情信息
     *
     * @param taskEmpId 培训任务员工ID
     * @return AiTeachMyCoachDetailResponse
     */
    AiTeachMyCoachDetailResponse getDetail(Long taskEmpId);

    /**
     * 保存/更新-题目答案信息
     *
     * @param request 题目答案-保存/更新信息
     * @param status  状态;WKS：未开始，JXZ：进行中，YWC：已完成
     * @return Boolean
     */
    Boolean saveAnswer(AiTeachMyCoachSaveRequest request, String status);

    /**
     * 提交-题目答案信息
     *
     * @param request 题目答案-提交信息
     * @return Boolean
     */
    Boolean submitAnswer(AiTeachMyCoachSaveRequest request);

}
