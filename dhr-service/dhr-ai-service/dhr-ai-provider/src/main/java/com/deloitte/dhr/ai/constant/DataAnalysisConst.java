package com.deloitte.dhr.ai.constant;

import lombok.Getter;

import javax.sql.DataSource;
import java.util.regex.Pattern;


/**
 * 数据分析常量
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public class DataAnalysisConst {

    /**
     * SQL安全校验相关的正则表达式模式，预编译以提高性能
     */
    public static final Pattern SELECT_PATTERN = Pattern.compile("^\\s*SELECT\\s+", Pattern.CASE_INSENSITIVE);

    public static final Pattern DANGEROUS_PATTERN = Pattern.compile(
            "(?i)(INSERT|UPDATE|DELETE|DROP|ALTER|CREATE|TRUNCATE|REPLACE|EXEC|EXECUTE|" +
                    "DECLARE|INTO\\s+OUTFILE|INTO\\s+DUMPFILE|LOAD\\s+DATA|" +
                    "LOAD\\s+FILE|SHUTDOWN|SLEEP\\(|BENCHMARK\\(|EXTRACTVALUE|" +
                    "UPDATEXML|FLOOR\\s*\\(|PROCEDURE\\s+ANALYSE|HANDLER|SCRIPT|XP_CMDSHELL|" +
                    "SYS\\.USER_OBJECTS|SYS\\.USER_TABLES|WAITFOR\\s+DELAY)");
    public static final Pattern COMMENT_PATTERN = Pattern.compile("--.*?$|/\\*.*?\\*/", Pattern.DOTALL);

    public static final Pattern MULTI_STATEMENTS_PATTERN = Pattern.compile(";\\s*\\w+", Pattern.CASE_INSENSITIVE);

    /**
     * 数据源实例
     */
    public DataSource dataSource;

    /**
     * 缓存过期时间（秒）
     */
    public static final long CACHE_EXPIRY_SECONDS = 60; // 1分钟

    /**
     * Redis缓存前缀
     */
    public static final String CACHE_PREFIX = "sql_query:";

}
