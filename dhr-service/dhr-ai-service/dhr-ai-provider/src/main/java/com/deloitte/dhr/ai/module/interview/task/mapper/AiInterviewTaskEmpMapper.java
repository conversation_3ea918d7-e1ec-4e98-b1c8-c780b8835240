package com.deloitte.dhr.ai.module.interview.task.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.interview.task.domain.AiInterviewTaskEmp;
import com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskEmpListRequest;
import com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskEmpResponse;
import com.deloitte.dhr.ai.module.interview.task.pojo.AiInterviewTaskInfoResponse;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewObjectResponse;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI访谈-访谈任务-员工-相关持久化接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface AiInterviewTaskEmpMapper extends SuperMapper<AiInterviewTaskEmp> {

    /**
     * 分页-任务员工列表
     *
     * @param page  分页参数
     * @param param 过滤条件
     * @param order 排序条件
     * @return 列表数据
     */
    List<AiInterviewTaskEmpResponse> findListPage(Page<AiInterviewTaskEmp> page, @Param("param") AiInterviewTaskEmpListRequest param, @Param("order") String order);

    /**
     * 查询我的访谈对象详情
     *
     * @param id AI访谈任务-员工ID
     * @return AiInterviewObjectResponse
     */
    AiInterviewObjectResponse getMyObjectDetail(@Param("id") Long id);

    /**
     * AI访谈-查询-我的访谈任务详情
     *
     * @param taskEmpId 访谈任务-员工ID
     * @return AiInterviewTaskInfoResponse
     */
    AiInterviewTaskInfoResponse getMyTaskInfoDetail(@Param("taskEmpId") Long taskEmpId);

    /**
     * AI访谈任务-获取员工对话问题数量
     *
     * @param taskEmpId AI访谈任务-员工ID
     * @return 对话问题数量
     */
    Integer getDialogueQuestionNum(@Param("taskEmpId") Long taskEmpId);

}