package com.deloitte.dhr.ai.module.chat.service;

import com.deloitte.dhr.ai.module.chat.domain.AiChatDialogue;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatDialogueListResponse;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatDialogueSaveRequest;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatDialogueUpdRequest;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * AI聊天-聊天对话-相关业务接口
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface AiChatDialogueService extends SuperService<AiChatDialogue> {

    /**
     * 保存-数据
     *
     * @param request 保存数据
     * @return 主键ID
     */
    Long saveOrUpdateData(AiChatDialogueSaveRequest request);

    /**
     * 查询-聊天对话列表
     *
     * @param aiGroup AI分组：duo-AI员工伙伴，coach-AI学习助手
     * @return List<AiChatDialogueListResponse>
     */
    List<AiChatDialogueListResponse> getDialogueList(String aiGroup);

    /**
     * 编辑-聊天对话
     *
     * @param request 编辑参数
     * @return Boolean
     */
    Long updData(AiChatDialogueUpdRequest request);

    /**
     * 删除-对话历史记录
     *
     * @param id 主键ID
     * @return Boolean
     */
    Boolean deleteById(Long id);

}
