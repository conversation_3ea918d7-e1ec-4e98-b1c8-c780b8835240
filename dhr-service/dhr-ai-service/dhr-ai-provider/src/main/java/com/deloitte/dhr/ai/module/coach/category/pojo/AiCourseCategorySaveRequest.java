package com.deloitte.dhr.ai.module.coach.category.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 课程类别-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiCourseCategorySaveRequest")
public class AiCourseCategorySaveRequest {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 类别名称
     */
    @NotBlank(message = "类别名称不能为空")
    @Length(max = 80, message = "类别名称不能超过80个字符")
    @ApiModelProperty(value = "类别名称", name = "categoryName")
    private String categoryName;

    /**
     * 父级ID，顶级节点默认为0
     */
    @ApiModelProperty(value = "父级ID，顶级节点默认为0", name = "parentId")
    private Long parentId = 0L;

}