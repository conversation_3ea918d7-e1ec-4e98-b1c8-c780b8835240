package com.deloitte.dhr.ai.module.coach.train.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.module.coach.constant.AiTrainSpeedAnalyseEnum;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainMyTalkSpeedAnalyse;
import com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainMyTalkSpeedAnalyseMapper;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyTalkSpeedAnalyseChartResponse;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainMyTalkSpeedAnalyseService;
import com.deloitte.dhr.common.SuperServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 课程资源-AI陪练-我的语速分析-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiTrainMyTalkSpeedAnalyseServiceImpl extends SuperServiceImpl<AiTrainMyTalkSpeedAnalyseMapper, AiTrainMyTalkSpeedAnalyse> implements AiTrainMyTalkSpeedAnalyseService {

    @Autowired
    private AiTrainMyTalkSpeedAnalyseMapper aiTrainMyTalkSpeedAnalyseMapper;

    @Override
    public List<AiTrainMyTalkSpeedAnalyseChartResponse> getMyTalkSpeedAnalyseChart(Long taskEmpId) {
        LambdaQueryWrapper<AiTrainMyTalkSpeedAnalyse> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrainMyTalkSpeedAnalyse::getTaskEmpId, taskEmpId);
        List<AiTrainMyTalkSpeedAnalyse> list = aiTrainMyTalkSpeedAnalyseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<AiTrainMyTalkSpeedAnalyseChartResponse> responseList = new ArrayList<>();
        list.stream().collect(Collectors.groupingBy(AiTrainMyTalkSpeedAnalyse::getSpeedCode)).forEach((speedCode, speedAnalyseList) -> {
            AiTrainMyTalkSpeedAnalyseChartResponse response = BeanUtil.copyProperties(speedAnalyseList.get(0), AiTrainMyTalkSpeedAnalyseChartResponse.class);
            BigDecimal speedWeight = BigDecimal.valueOf(speedAnalyseList.size() * 100L).divide(BigDecimal.valueOf(list.size()), 2, RoundingMode.HALF_UP);
            response.setSpeedWeight(speedWeight);
            responseList.add(response);
        });

        // 补全数据
        List<String> existSpeedCodeList = responseList.stream().map(AiTrainMyTalkSpeedAnalyseChartResponse::getSpeedCode).distinct().collect(Collectors.toList());
        List<AiTrainSpeedAnalyseEnum> notExistSpeedEnumList = Arrays.stream(AiTrainSpeedAnalyseEnum.values()).filter(speedAnalyseEnum -> !existSpeedCodeList.contains(speedAnalyseEnum.getCode())).collect(Collectors.toList());
        notExistSpeedEnumList.forEach(speedEnum -> responseList.add(new AiTrainMyTalkSpeedAnalyseChartResponse(speedEnum.getCode(), speedEnum.getName(), BigDecimal.ZERO, null)));

        // 排序
        responseList.forEach(response -> {
            if (StrUtil.equals(response.getSpeedCode(), AiTrainSpeedAnalyseEnum.slow.getCode())) {
                response.setSort(1);
            } else if (StrUtil.equals(response.getSpeedCode(), AiTrainSpeedAnalyseEnum.normal.getCode())) {
                response.setSort(2);
            } else {
                response.setSort(3);
            }
        });
        return responseList.stream().sorted(Comparator.comparing(AiTrainMyTalkSpeedAnalyseChartResponse::getSort)).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveData(Long taskEmpId, Long historyId, String speechRate) {
        if (StrUtil.isBlank(speechRate)) {
            return true;
        }
        AiTrainMyTalkSpeedAnalyse speedAnalyse = new AiTrainMyTalkSpeedAnalyse();
        speedAnalyse.setTaskEmpId(taskEmpId);
        speedAnalyse.setHistoryId(historyId);
        speedAnalyse.setSpeedCode(AiTrainSpeedAnalyseEnum.getCodeByName(speechRate));
        speedAnalyse.setSpeedName(speechRate);
        return this.saveOrUpdate(speedAnalyse);
    }

    @Override
    public BigDecimal calculateScore(Long taskEmpId) {
        List<AiTrainMyTalkSpeedAnalyseChartResponse> myTalkSpeedAnalyseList = this.getMyTalkSpeedAnalyseChart(taskEmpId);
        if (CollUtil.isEmpty(myTalkSpeedAnalyseList)) {
            return null;
        }
        return myTalkSpeedAnalyseList.stream().filter(a -> StrUtil.equals(a.getSpeedCode(), AiTrainSpeedAnalyseEnum.normal.getCode()))
                .map(AiTrainMyTalkSpeedAnalyseChartResponse::getSpeedWeight).findAny().orElse(BigDecimal.ZERO);
    }

}

