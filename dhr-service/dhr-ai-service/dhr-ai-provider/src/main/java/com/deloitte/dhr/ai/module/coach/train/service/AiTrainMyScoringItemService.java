package com.deloitte.dhr.ai.module.coach.train.service;

import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainMyScoringItem;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyOverviewResponse;
import com.deloitte.dhr.common.SuperService;

/**
 * 课程资源-AI陪练-我的得分项-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTrainMyScoringItemService extends SuperService<AiTrainMyScoringItem> {

    /**
     * 查询-我的总体概况
     *
     * @param taskEmpId 陪练任务员工ID
     * @return AiTrainMyOverviewResponse
     */
    AiTrainMyOverviewResponse getMyOverview(Long taskEmpId);

}
