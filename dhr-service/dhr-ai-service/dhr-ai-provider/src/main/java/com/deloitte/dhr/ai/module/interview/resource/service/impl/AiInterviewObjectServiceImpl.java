package com.deloitte.dhr.ai.module.interview.resource.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.module.interview.train.domain.AiInterviewObject;
import com.deloitte.dhr.ai.module.interview.resource.mapper.AiInterviewObjectMapper;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewObjectResponse;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewObjectSaveRequest;
import com.deloitte.dhr.ai.module.interview.resource.service.AiInterviewObjectService;
import com.deloitte.dhr.common.SuperServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * AI访谈-访谈任务-对象-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class AiInterviewObjectServiceImpl extends SuperServiceImpl<AiInterviewObjectMapper, AiInterviewObject> implements AiInterviewObjectService {

    @Autowired
    private AiInterviewObjectMapper aiInterviewObjectMapper;

    @Override
    public AiInterviewObjectResponse getByResourceId(Long resourceId) {

        // 查询AI陪练对象信息
        LambdaQueryWrapper<AiInterviewObject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiInterviewObject::getResourceId, resourceId);
        AiInterviewObject aiInterviewObject = aiInterviewObjectMapper.selectOne(queryWrapper);
        AiInterviewObjectResponse response = BeanUtil.copyProperties(aiInterviewObject, AiInterviewObjectResponse.class);

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveData(AiInterviewObjectSaveRequest request, Long resourceId) {
        // 保存/更新-访谈对象信息
        LambdaQueryWrapper<AiInterviewObject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiInterviewObject::getResourceId, resourceId);
        AiInterviewObject aiInterviewObject = aiInterviewObjectMapper.selectOne(queryWrapper);
        if (aiInterviewObject == null) {
            aiInterviewObject = BeanUtil.copyProperties(request, AiInterviewObject.class);
        } else {
            BeanUtil.copyProperties(request, aiInterviewObject);
        }
        aiInterviewObject.setResourceId(resourceId);
        this.saveOrUpdate(aiInterviewObject);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByResourceId(Long resourceId) {
        // 删除 AI陪练-对象基本数据
        LambdaQueryWrapper<AiInterviewObject> objectQueryWrapper = new LambdaQueryWrapper<>();
        objectQueryWrapper.eq(AiInterviewObject::getResourceId, resourceId);
        aiInterviewObjectMapper.delete(objectQueryWrapper);

    }
}

