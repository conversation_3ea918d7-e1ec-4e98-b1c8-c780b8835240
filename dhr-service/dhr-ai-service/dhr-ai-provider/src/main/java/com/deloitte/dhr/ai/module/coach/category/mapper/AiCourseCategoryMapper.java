package com.deloitte.dhr.ai.module.coach.category.mapper;

import com.deloitte.dhr.ai.module.coach.category.domain.AiCourseCategory;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程类别-相关持久化接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiCourseCategoryMapper extends SuperMapper<AiCourseCategory> {

    /**
     * 查询-类型全路径
     *
     * @param id 类型ID
     * @return 类型全路径
     */
    String getCategoryPathById(@Param("id") Long id);

    /**
     * 获取当前节点以及所有下级节点ID
     *
     * @param id 类型ID
     * @return List<Long>
     */
    List<Long> getCurrentAndAllChildId(@Param("id") Long id);

}