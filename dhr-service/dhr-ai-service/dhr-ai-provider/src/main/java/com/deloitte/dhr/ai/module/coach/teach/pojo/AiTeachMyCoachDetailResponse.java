package com.deloitte.dhr.ai.module.coach.teach.pojo;

import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceAttachResponse;
import com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskInfoResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 课程资源-教学课程-我的培训-详情
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachMyCoachDetailResponse")
public class AiTeachMyCoachDetailResponse {

    /**
     * 培训任务-基本信息
     */
    @ApiModelProperty(value = "培训任务-基本信息", name = "taskInfo")
    private AiCoachTaskInfoResponse taskInfo;

    /**
     * 课程资源-学习清单信息
     */
    @ApiModelProperty(value = "课程资源-学习清单信息", name = "attachInfoList")
    private List<AiCourseResourceAttachResponse> attachInfoList;

    /**
     * 教学课程-我的考核题目信息
     */
    @ApiModelProperty(value = "教学课程-我的考核题目信息", name = "myQuestionInfoList")
    private List<AiTeachMyTestQuestionResponse> myQuestionInfoList;

    /**
     * 培训任务-我的培训任务结果
     */
    @ApiModelProperty(value = "培训任务-我的培训任务结果", name = "resultInfo")
    private AiTeachMyCoachResultResponse resultInfo;

}