package com.deloitte.dhr.ai.module.ai.strategy.ssc;

import cn.hutool.core.util.StrUtil;
import com.deloitte.dhr.ai.enums.AiSscIntentAction;
import com.deloitte.dhr.ai.enums.AiSscIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.ActionStrategyType;
import com.deloitte.dhr.ai.module.ai.strategy.ssc.pojo.SscIntentResponse;
import com.deloitte.dhr.common.base.exception.CommRunException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * SSC-策略Context
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@Slf4j
public class SscStrategyContext {

    /**
     * 策略类集合
     */
    private Map<AiSscIntentItem, Map<AiSscIntentAction, SscStrategy>> strategyMap;

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    private void init() {
        strategyMap = applicationContext.getBeansOfType(SscStrategy.class).values()
                .stream()
                .collect(Collectors.groupingBy(a -> a.getClass().getAnnotation(ActionStrategyType.class).item(),
                        Collectors.toMap(a -> a.getClass().getAnnotation(ActionStrategyType.class).action(), a -> a)));
    }

    /**
     * 获取ssc策略
     *
     * @param item   策略类型
     * @param action 策略行为
     * @return 类型对应的策略
     * @throws CommRunException 异常
     */
    public SscStrategy getStrategy(AiSscIntentItem item, AiSscIntentAction action) throws CommRunException {
        // 参数校验
        if (item == null || action == null) {
            throw new CommRunException("类型或行为参数为空，无法获取ssc策略");
        }
        return strategyMap.get(item).get(action);
    }

    /**
     * 获取ssc策略
     *
     * @param intent ssc意图识别结果
     */
    public SscStrategy getOrDefaultStrategy(SscIntentResponse intent) {
        log.info("ssc意图识别结果：intentType：{}，intentItem：{}，intentAction：{}", intent.getIntentType(), intent.getIntentItem(), intent.getIntentAction());
        AiSscIntentItem itemEnum = AiSscIntentItem.getByName(intent.getIntentItem());
        AiSscIntentAction actionEnum = AiSscIntentAction.getByName(intent.getIntentAction());
        if (!StrUtil.equals(intent.getIntentItem(), itemEnum.getName()) || !StrUtil.equals(intent.getIntentAction(), actionEnum.getName())) {
            log.info("ssc意图识别枚举未定义，执行兜底枚举：intentType：{}，intentItem：{}，intentAction：{}", itemEnum.getType().getName(), itemEnum.getName(), actionEnum.getName());
            intent.setIntentType(itemEnum.getType().getName());
            intent.setIntentItem(itemEnum.getName());
            intent.setIntentAction(actionEnum.getName());
        }
        // 判断兜底策略
        if (strategyMap.get(itemEnum) == null || strategyMap.get(itemEnum).get(actionEnum) == null) {
            switch (actionEnum) {
                case TQ:
                case TC:
                    itemEnum = AiSscIntentItem.TY;
                    actionEnum = AiSscIntentAction.IR;
                    break;
                case IR:
                    itemEnum = AiSscIntentItem.TY;
                    break;
                default:
            }
            log.info("ssc策略未定义，执行兜底策略：intentType：{}，intentItem：{}，intentAction：{}", itemEnum.getType().getName(), itemEnum.getName(), actionEnum.getName());
            // 更新ssc意图识别结果
            intent.setIntentType(itemEnum.getType().getName());
            intent.setIntentItem(itemEnum.getName());
            intent.setIntentAction(actionEnum.getName());
        }
        return strategyMap.get(itemEnum).get(actionEnum);
    }

}