package com.deloitte.dhr.ai.module.interview.resource.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AI访谈-列表
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewListRequest")
public class AiInterviewListRequest {

    /**
     * 类别ID
     */
    @ApiModelProperty(value = "类别ID", name = "categoryId")
    private Long categoryId;

    /**
     * AI访谈名称
     */
    @ApiModelProperty(value = "AI访谈名称", name = "courseName")
    private String courseName;

    /**
     * AI访谈分类;AI访谈：AIFT
     */
    @ApiModelProperty(value = "AI访谈分类;AI访谈：AIFT", name = "courseType")
    private String courseType;

    /**
     * AI访谈状态
     */
    @ApiModelProperty(value = "AI访谈状态", name = "courseStatus")
    private String courseStatus;

    /**
     * 类别全路径
     */
    @JsonIgnore
    @ApiModelProperty(value = "类别全路径", name = "categoryPath")
    private String categoryPath;

}