package com.deloitte.dhr.ai.module.interview.resource.service;

import com.deloitte.dhr.ai.module.interview.resource.domain.AiInterviewResourceAttach;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewResourceAttachResponse;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewResourceAttachSaveRequest;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * AI访谈-附件信息-相关业务接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface AiInterviewResourceAttachService extends SuperService<AiInterviewResourceAttach> {

    /**
     * 查询数据详情
     *
     * @param resourceId 课程资源ID
     * @return List<AiInterviewResourceAttachDetailResponse>
     */
    List<AiInterviewResourceAttachResponse> getByResourceId(Long resourceId);

    /**
     * 通过课程资源ID删除数据
     *
     * @param resourceId 课程资源ID
     */
    void deleteByResourceId(Long resourceId);

    /**
     * 保存/更新-课程资源-学习附件信息
     *
     * @param requestList 课程资源-学习附件-保存/更新信息
     * @param resourceId  课程资源ID
     * @return Boolean
     */
    Boolean saveData(List<AiInterviewResourceAttachSaveRequest> requestList, Long resourceId);

}
