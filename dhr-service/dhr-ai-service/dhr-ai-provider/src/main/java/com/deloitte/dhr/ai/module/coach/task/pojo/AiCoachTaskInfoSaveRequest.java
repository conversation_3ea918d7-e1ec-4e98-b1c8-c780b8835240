package com.deloitte.dhr.ai.module.coach.task.pojo;

import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 培训任务-基本信息-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiCoachTaskInfoSaveRequest")
public class AiCoachTaskInfoSaveRequest {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 课程资源ID
     */
    @NotNull(groups = {Save.class, Submit.class}, message = "课程资源ID不能为空")
    @ApiModelProperty(value = "课程资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * 培训任务名称
     */
    @Length(groups = Submit.class, max = 80, message = "培训任务名称不能超过80个字符")
    @NotBlank(groups = {Save.class, Submit.class}, message = "培训任务名称不能为空")
    @ApiModelProperty(value = "培训任务名称", name = "taskName")
    private String taskName;

    /**
     * 培训开始时间
     */
    @ApiModelProperty(value = "培训开始时间", name = "startDateTime")
    private Date startDateTime;

    /**
     * 培训结束时间
     */
    @ApiModelProperty(value = "培训结束时间", name = "endDateTime")
    private Date endDateTime;

    /**
     * 培训封面URL
     */
    @Length(groups = Submit.class, max = 255, message = "培训封面URL不能超过255个字符")
    @ApiModelProperty(value = "培训封面URL", name = "taskCoverUrl")
    private String taskCoverUrl;

    /**
     * 培训任务时长（hour）
     */
    @ApiModelProperty(value = "培训任务时长（hour）", name = "taskDuration")
    private BigDecimal taskDuration;

}