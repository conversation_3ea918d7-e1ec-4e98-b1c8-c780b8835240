package com.deloitte.dhr.ai.module.ai.strategy.transform;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiTransformType;
import com.deloitte.dhr.ai.module.ai.annotation.TransformStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.module.ai.strategy.digital.DigitalLiningStrategyContext;
import com.deloitte.dhr.ai.module.ai.strategy.digital.pojo.DigitalLiningIntentResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数字人-李宁
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Component
@TransformStrategyType(AiTransformType.DigitalLining)
public class DigitalLiningIntentTransformStrategy implements ResponseTransformStrategy {

    @Autowired
    private DigitalLiningStrategyContext liningStrategyContext;

    @Override
    public Flux<List<AiContent>> transformStream(Flux<String> rawStream, AiRequest aiRequest) {
        SecurityContext context = SecurityContextHolder.getContext();
        return rawStream
                .filter(raw -> StrUtil.equals(JSONObject.parseObject(raw).getString("event"), "workflow_finished"))
                .flatMap(raw -> {
                    SecurityContextHolder.setContext(context);
                    // 根据意图识别结果，执行不同的逻辑
                    JSONObject rawObj = JSONObject.parseObject(raw);
                    DigitalLiningIntentResponse intentRecognition = rawObj.getJSONObject("data").getJSONObject("outputs").getObject("result", DigitalLiningIntentResponse.class);
                    Map<String, Object> param = buildStrategyRequest(intentRecognition, aiRequest);
                    Flux<List<AiContent>> strategyFlux = liningStrategyContext.getOrDefaultStrategy(intentRecognition).executeStream(param);
                    param.remove("userId");
                    param.remove("userInput");
                    param.remove("context");
                    Flux<List<AiContent>> intentFlux = Flux.just(List.of(AiContent.builder()
                            .contentType(AiContentType.INTENT.getCode())
                            .content(JSON.toJSONString(intentRecognition))
                            .build()));
                    return intentFlux.concatWith(strategyFlux);
                });
    }

    @Override
    public Flux<List<AiContent>> transformBlock(Flux<String> rawStream, AiRequest aiRequest) {
        // todo 暂未实现
        return Flux.empty();
    }

    /**
     * 构建策略请求
     *
     * @param intentResponse 数字人-李宁:意图识别结果
     * @param aiRequest      ai请求
     * @return 策略请求
     */
    private Map<String, Object> buildStrategyRequest(DigitalLiningIntentResponse intentResponse, AiRequest aiRequest) {
        String userInput = aiRequest.getContents()
                .stream()
                .filter(content -> StrUtil.equals(content.getContentType(), AiContentType.TEXT.getCode()))
                .findAny().orElseThrow(() -> new RuntimeException("用户输入为空")).getContent();

        Map<String, Object> map = MapUtil.isEmpty(intentResponse.getIntentData()) ? new HashMap<>() : intentResponse.getIntentData();
        map.put("userId", aiRequest.getUserId());
        map.put("userInput", userInput);
        return map;
    }

}

