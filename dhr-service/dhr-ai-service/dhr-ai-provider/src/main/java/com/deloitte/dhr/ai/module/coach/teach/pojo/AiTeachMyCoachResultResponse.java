package com.deloitte.dhr.ai.module.coach.teach.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 课程资源-教学课程-我的培训-结果
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachMyCoachResultResponse")
public class AiTeachMyCoachResultResponse {

    /**
     * 选择题正确数量
     */
    @ApiModelProperty(value = "选择题正确数量", name = "choiceQuestionRightNum")
    private Integer choiceQuestionRightNum;

    /**
     * 选择题错误数量
     */
    @ApiModelProperty(value = "选择题错误数量", name = "choiceQuestionErrorNum")
    private Integer choiceQuestionErrorNum;

    /**
     * 计算文本题正确率
     */
    @ApiModelProperty(value = "计算文本题正确率", name = "textQuestionMatchingDegree")
    private BigDecimal textQuestionMatchingDegree;


}