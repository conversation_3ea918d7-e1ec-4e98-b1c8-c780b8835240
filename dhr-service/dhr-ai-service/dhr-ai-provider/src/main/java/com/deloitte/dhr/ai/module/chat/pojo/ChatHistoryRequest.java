package com.deloitte.dhr.ai.module.chat.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * AI对话导出条件
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("ChatHistoryRequest")
public class ChatHistoryRequest {

    @ApiModelProperty(value = "开始日期", name = "startDate")
    private String startDate;

    @ApiModelProperty(value = "结束日期", name = "endDate")
    private String endDate;

    @ApiModelProperty(value = "提问关键词", name = "userMsg")
    private String userMsg;

    @ApiModelProperty(value = "回答关键词", name = "aiMsg")
    private String aiMsg;

    @ApiModelProperty(value = "用户ID", name = "userId")
    private String userId;

    @ApiModelProperty(value = "对话类型", name = "chatType")
    private String chatType;
}
