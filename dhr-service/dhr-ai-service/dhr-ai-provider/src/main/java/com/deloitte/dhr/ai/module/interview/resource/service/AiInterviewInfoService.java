package com.deloitte.dhr.ai.module.interview.resource.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.interview.resource.domain.AiInterviewInfo;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewInfoResponse;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewInfoSaveRequest;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewListRequest;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewDetailResponse;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewSaveRequest;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperService;

/**
 * 课程资源-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
/**
 * AI访谈-相关业务接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface AiInterviewInfoService extends SuperService<AiInterviewInfo> {

    /**
     * 查询数据详情
     *
     * @param id 课程资源ID
     * @return AiInterviewDetailResponse
     */
    AiInterviewInfoResponse getDetail(Long id);

    /**
     * 分页查询列表数据
     *
     * @param page    分页参数
     * @param request 过滤条件
     * @param order   排序条件
     * @return ResponsePage<AiInterviewListResponse>
     */
    ResponsePage<AiInterviewInfoResponse> findListPage(Page<AiInterviewInfo> page, AiInterviewListRequest request, BaseOrder order);

    /**
     * 保存数据
     *
     * @param request      课程资源保存数据
     * @param courseType   课程分类：XKC：教学课程，AIPL：AI访谈
     * @param courseStatus 课程状态：CG：草稿，TJ：提交
     * @return Long
     */
    Long saveData(AiInterviewInfoSaveRequest request, String courseType, String courseStatus);

    /**
     * 删除课程资源
     *
     * @param id 课程资源ID
     * @return Boolean
     */
    Boolean deleteById(Long id);

    /**
     * 查询-AI访谈详情信息
     *
     * @param resourceId 课程资源ID
     * @return AiInterviewDetailResponse
     */
    AiInterviewDetailResponse getInterviewDetail(Long resourceId);

    /**
     * 保存/更新-AI访谈信息
     *
     * @param request      AI访谈-保存/更新信息
     * @param courseStatus 课程资源状态
     * @return 课程资源ID
     */
    Long saveAll(AiInterviewSaveRequest request, String courseStatus);

    /**
     * 提交-AI访谈信息
     *
     * @param request AI访谈-提交信息
     * @return 课程资源ID
     */
    Long submitAll(AiInterviewSaveRequest request);

    /**
     * 复制-AI访谈详情信息
     *
     * @param resourceId 课程资源ID
     * @return 课程资源ID
     */
    Long copyInterview(Long resourceId);
}
