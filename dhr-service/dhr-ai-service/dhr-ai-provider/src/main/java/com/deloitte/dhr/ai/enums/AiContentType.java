package com.deloitte.dhr.ai.enums;

import lombok.Getter;

/**
 * AI内容类型枚举
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public enum AiContentType {

    /**
     * 以下为常规类型
     */
    JSON("JSON", "JSO<PERSON>"),
    TEXT("TEXT", "文本"),
    FILE("FILE", "文件"),
    IMAGE("IMAGE", "图片"),
    XMIND("XMIND", "思维导图"),
    ERROR("ERROR", "错误"),
    UNDEFINED("UNDEFINED", "未定义"),

    /**
     * 以下为特殊类型，根据具体的业务来制定的
     */
    INTENT("INTENT", "意图识别"),
    SUBMIT("SUBMIT", "表单提交"),

    // 以下为特殊类型，数据分析相关类型
    QUESTION_UNDERSTAND("QUESTION_UNDERSTAND", "问题理解"),
    ANALYSIS_RESULT("ANALYSIS_RESULT", "分析结果"),
    GENERATE_CHART("GENERATE_CHART", "生成图表"),
    VANNA_GENERATE_SQL("VANNA_GENERATE_SQL", "提取VANNA的SQL"),
    VANNA_EXECUTE_RESULT("VANNA_EXECUTE_RESULT", "提取VANNA的SQL执行结果"),
    IRRELEVANT_QUESTION("IRRELEVANT_QUESTION", "无关问题"),

    // 以下为特殊类型，数据预测相关类型
    PREDICTION_QUESTION_UNDERSTAND("PREDICTION_QUESTION_UNDERSTAND", "预测问题理解"),
    PREDICTION_EXECUTE_RESULT("PREDICTION_EXECUTE_RESULT", "预测结果"),
    PREDICTION_RESULT_ANALYSIS("PREDICTION_RESULT_ANALYSIS", "预测结果分析"),
    ;

    private final String code;

    private final String name;

    AiContentType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据名称获取枚举
     *
     * @param name 名称
     * @return AiContentType
     */
    public static AiContentType getByName(String name) {
        for (AiContentType aiContentType : AiContentType.values()) {
            if (aiContentType.getName().equals(name)) {
                return aiContentType;
            }
        }
        return UNDEFINED;
    }

}



