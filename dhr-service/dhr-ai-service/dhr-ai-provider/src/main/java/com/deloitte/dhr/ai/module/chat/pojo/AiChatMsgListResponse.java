package com.deloitte.dhr.ai.module.chat.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 聊天消息-列表
 *
 * <AUTHOR>
 * @date 2024-03-27
 */
@Data
@ApiModel("AiChatMsgListResponse")
public class AiChatMsgListResponse {

    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    @ApiModelProperty(value = "对话ID", name = "dialogueId")
    private Long dialogueId;

    @ApiModelProperty(value = "AI类型", name = "aiType")
    private String aiType;

    @ApiModelProperty(value = "AI子类型", name = "aiSubType")
    private String aiSubType;

    @ApiModelProperty(value = "角色：assistant-AI机器人，user-用户", name = "role")
    private String role;

    @ApiModelProperty(value = "发送时间", name = "sendTime")
    private Date sendTime;

    @ApiModelProperty(value = "消息内容", name = "contents")
    private List<AiChatMsgContentListResponse> contents;

}