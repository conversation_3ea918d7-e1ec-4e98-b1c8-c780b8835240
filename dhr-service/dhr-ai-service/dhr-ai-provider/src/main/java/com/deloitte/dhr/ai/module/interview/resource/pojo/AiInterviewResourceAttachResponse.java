package com.deloitte.dhr.ai.module.interview.resource.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AI访谈-附件信息
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewResourceAttachResponse")
public class AiInterviewResourceAttachResponse {

    /**
     * 课程资源ID
     */
    @ApiModelProperty(value = "课程资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * 文件ID
     */
    @ApiModelProperty(value = "文件ID", name = "fileId")
    private String fileId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", name = "fileName")
    private String fileName;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", name = "fileType")
    private String fileType;

    /**
     * 文件md5值
     */
    @ApiModelProperty(value = "文件md5值", name = "fileMd5")
    private String fileMd5;

    /**
     * 文件预览URL
     */
    @ApiModelProperty(value = "文件URL", name = "fileUrl")
    private String fileUrl;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小", name = "fileSize")
    private String fileSize;


}