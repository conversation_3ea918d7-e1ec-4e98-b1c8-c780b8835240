package com.deloitte.dhr.ai.module.coach.teach.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 课程资源-教学课程-考核题目-文本题参考答案信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AiTeachTextSampleAnswerResponse")
public class AiTeachTextSampleAnswerResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 参考答案
     */
    @ApiModelProperty(value = "参考答案", name = "sampleAnswerContent")
    private String sampleAnswerContent;

}