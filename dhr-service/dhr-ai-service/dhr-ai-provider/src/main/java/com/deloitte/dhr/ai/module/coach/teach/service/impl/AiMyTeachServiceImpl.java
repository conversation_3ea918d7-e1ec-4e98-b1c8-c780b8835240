package com.deloitte.dhr.ai.module.coach.teach.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.resource.service.AiCourseResourceAttachService;
import com.deloitte.dhr.ai.module.coach.task.domain.AiCoachTaskEmp;
import com.deloitte.dhr.ai.module.coach.task.mapper.AiCoachTaskInfoMapper;
import com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskInfoResponse;
import com.deloitte.dhr.ai.module.coach.task.service.AiCoachTaskEmpService;
import com.deloitte.dhr.ai.module.coach.teach.pojo.*;
import com.deloitte.dhr.ai.module.coach.teach.service.*;
import com.deloitte.dhr.ai.utils.CheckUtils;
import com.deloitte.dhr.common.base.exception.CommRunException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 课程资源-教学课程-我的培训-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiMyTeachServiceImpl implements AiMyTeachService {

    @Autowired
    private AiCoachTaskInfoMapper aiCoachTaskInfoMapper;
    @Autowired
    private AiCourseResourceAttachService aiCourseResourceAttachService;
    @Autowired
    private AiCoachTaskEmpService aiCoachTaskEmpService;
    @Autowired
    private AiTeachTestQuestionService aiTeachTestQuestionService;
    @Autowired
    private AiTeachMyChoiceAnswerService aiTeachMyChoiceAnswerService;
    @Autowired
    private AiTeachMyTextAnswerService aiTeachMyTextAnswerService;
    @Autowired
    private AiTeachInfoService aiTeachInfoService;


    @Override
    public AiTeachMyCoachDetailResponse getDetail(Long taskEmpId) {
        AiTeachMyCoachDetailResponse response = new AiTeachMyCoachDetailResponse();

        // 查询-培训任务-员工基本信息
        AiCoachTaskEmp taskEmp = aiCoachTaskEmpService.get(taskEmpId);
        CheckUtils.checkNull(taskEmp, "该数据不存在");

        // 查询-培训任务-基本信息
        response.setTaskInfo(BeanUtil.copyProperties(aiCoachTaskInfoMapper.selectById(taskEmp.getTaskId()), AiCoachTaskInfoResponse.class));

        // 查询-课程资源-学习清单信息
        response.setAttachInfoList(aiCourseResourceAttachService.getByResourceId(taskEmp.getResourceId()));

        // 查询-教学课程-考核题目信息
        List<AiTeachMyTestQuestionResponse> myQuestionInfoList;
        // 查询已完成的培训任务需要展示相关答案以及最终结果
        if (StrUtil.equals(taskEmp.getStatus(), AiCoachConstant.TaskEmpStatus.COMPLETED)) {
            // 展示题目正确答案/参考答案、选择题是否答题正确/文本题AI匹配度
            myQuestionInfoList = aiTeachTestQuestionService.getMyQuestionAndAnswer(taskEmp.getResourceId(), taskEmp.getId(), false);
            // 展示我的答题最终结果
            AiTeachMyCoachResultResponse resultResponse = this.countResult(myQuestionInfoList);
            response.setResultInfo(resultResponse);
        } else {
            // 不展示题目正确答案以及我是否答题正确
            myQuestionInfoList = aiTeachTestQuestionService.getMyQuestionAndAnswer(taskEmp.getResourceId(), taskEmp.getId(), true);
        }
        response.setMyQuestionInfoList(myQuestionInfoList);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveAnswer(AiTeachMyCoachSaveRequest request, String status) {

        // 校验
        AiCoachTaskEmp taskEmp = this.verify(request, status);

        // 保存/更新-我的选择题答案信息
        Map<Long, Boolean> myChoiceAnswerResultMap = aiTeachMyChoiceAnswerService.saveData(request.getAnswerInfoList(), taskEmp.getResourceId(), taskEmp.getTaskId(), taskEmp.getId());

        // 保存/更新-我的文本题答案信息
        Map<Long, BigDecimal> myTextAiMatchingDegreeMap = aiTeachMyTextAnswerService.saveData(request.getAnswerInfoList(), taskEmp.getResourceId(), taskEmp.getTaskId(), taskEmp.getId());

        // 更新-培训任务员工相关信息
        taskEmp.setStatus(status);
        if (!StrUtil.equals(status, AiCoachConstant.TaskEmpStatus.COMPLETED)) {
            return aiCoachTaskEmpService.update(taskEmp);
        }
        // 如果是提交答题，则需要计算最终结果
        taskEmp.setCompleteTime(DateTime.now());
        taskEmp.setElapsedTime(request.getElapsedTime());

        // 计算员工得分结果
        calculateResult(myChoiceAnswerResultMap, myTextAiMatchingDegreeMap, taskEmp.getResourceId(), taskEmp);
        aiCoachTaskEmpService.update(taskEmp);

        // 更新-培训任务状态
        aiCoachTaskInfoMapper.updateTaskStatus(taskEmp.getTaskId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitAnswer(AiTeachMyCoachSaveRequest request) {
        return this.saveAnswer(request, AiCoachConstant.TaskEmpStatus.COMPLETED);
    }

    /**
     * 校验-我的答题信息
     *
     * @param request 答题信息
     * @param status  状态
     * @return AiCoachTaskEmp 员工信息
     */
    private AiCoachTaskEmp verify(AiTeachMyCoachSaveRequest request, String status) {

        // 校验-员工数据是否存在
        AiCoachTaskEmp taskEmp = aiCoachTaskEmpService.get(request.getTaskEmpId());
        CheckUtils.checkNull(taskEmp, "该数据不存在");

        // 校验-已提交数据不能修改
        if (StrUtil.equals(taskEmp.getStatus(), AiCoachConstant.TaskEmpStatus.COMPLETED)) {
            throw new CommRunException("培训已提交，请勿继续作答");
        }

        // 校验-已结束数据不能修改
        if (StrUtil.equals(taskEmp.getStatus(), AiCoachConstant.TaskEmpStatus.CLOSE)) {
            throw new CommRunException("培训已结束，请勿继续作答");
        }

        // 校验-未答题信息
        if (StrUtil.equals(status, AiCoachConstant.TaskEmpStatus.COMPLETED)) {
            List<Integer> questionOrderList = new ArrayList<>();
            for (int i = 0; i < request.getAnswerInfoList().size(); i++) {
                String questionAnswer = request.getAnswerInfoList().get(i).getQuestionAnswer();
                if (StrUtil.isNotBlank(questionAnswer)) {
                    continue;
                }
                questionOrderList.add(i + 1);
            }
            if (CollUtil.isNotEmpty(questionOrderList)) {
                throw new CommRunException("序号为【" + StrUtil.join("，", questionOrderList) + "】的题目还未答题");
            }
        }
        return taskEmp;
    }

    /**
     * 统计我的答题结果
     *
     * @param myQuestionInfoList 我的答题信息
     * @return AiTeachMyCoachResultDetailResponse 我的答题结果
     */
    private AiTeachMyCoachResultResponse countResult(List<AiTeachMyTestQuestionResponse> myQuestionInfoList) {

        AiTeachMyCoachResultResponse resultResponse = new AiTeachMyCoachResultResponse();
        int choiceQuestionRightNum = 0, choiceQuestionErrorNum = 0, textQuestionNum = 0;
        BigDecimal textQuestionMatchingDegreeTotal = BigDecimal.ZERO;
        for (AiTeachMyTestQuestionResponse myQuestion : myQuestionInfoList) {
            if (myQuestion.getResult() == null) {
                continue;
            }
            switch (myQuestion.getQuestionType()) {
                case AiCoachConstant.TeachQuestionType.SINGLE_CHOICE_QUESTION:
                case AiCoachConstant.TeachQuestionType.TRUE_OR_FALSE_QUESTION:
                    if (myQuestion.getResult()) {
                        choiceQuestionRightNum++;
                    } else {
                        choiceQuestionErrorNum++;
                    }
                    break;
                case AiCoachConstant.TeachQuestionType.ESSAY_QUESTION:
                    textQuestionMatchingDegreeTotal = textQuestionMatchingDegreeTotal.add(myQuestion.getMyTextQuestionContent().getAiMatchingDegree());
                    textQuestionNum++;
                    break;
                default:
                    throw new CommRunException("非法的考核类型题目");
            }
        }
        resultResponse.setChoiceQuestionRightNum(choiceQuestionRightNum);
        resultResponse.setChoiceQuestionErrorNum(choiceQuestionErrorNum);
        BigDecimal textQuestionMatchingDegree = textQuestionNum == 0 ? BigDecimal.ZERO : textQuestionMatchingDegreeTotal.divide(BigDecimal.valueOf(textQuestionNum), 2, RoundingMode.HALF_UP);
        resultResponse.setTextQuestionMatchingDegree(textQuestionMatchingDegree);
        return resultResponse;
    }

    /**
     * 计算教学课程最终结果
     *
     * @param choiceMap  选择题结果
     * @param textMap    文本题结果
     * @param resourceId 教学课程ID
     * @param taskEmp    培训任务员工
     */
    private void calculateResult(Map<Long, Boolean> choiceMap, Map<Long, BigDecimal> textMap, Long resourceId, AiCoachTaskEmp taskEmp) {

        // 查询教学课程-基本信息
        AiTeachInfoResponse teachInfo = aiTeachInfoService.getByResourceId(resourceId);

        // 计算选择题正确率，如果没有选择题类型，则设置为100%，代表这个条件为true，不纳入判断
        BigDecimal choiceQuestionAccuracy = choiceMap.isEmpty() ? BigDecimal.valueOf(100) : BigDecimal.valueOf(choiceMap.values().stream().filter(a -> a).count()).divide(BigDecimal.valueOf(choiceMap.size()), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));

        // 计算文本题正确率，如果没有文本题类型，则设置为100%，代表这个条件为true，不纳入判断
        BigDecimal textQuestionMatchingDegree = textMap.isEmpty() ? BigDecimal.valueOf(100) : textMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(textMap.size()), 2, RoundingMode.HALF_UP);

        // 与教学课程配置的“选择题正确率”和“问答题匹配度”比较
        Boolean result = choiceQuestionAccuracy.compareTo(teachInfo.getChoiceQuestionAccuracy()) >= 0 && textQuestionMatchingDegree.compareTo(teachInfo.getTextQuestionMatchingDegree()) >= 0;
        taskEmp.setScoreResult(result);

        // todo 暂时以平均分为它的总分
        BigDecimal choiceQuestionScore = choiceMap.isEmpty() ? BigDecimal.ZERO : BigDecimal.valueOf(choiceMap.values().stream().filter(a -> a).count()).divide(BigDecimal.valueOf(choiceMap.size()), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        BigDecimal textQuestionScore = textMap.isEmpty() ? BigDecimal.ZERO : textMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(textMap.size()), 2, RoundingMode.HALF_UP);
        BigDecimal finalScore = choiceQuestionScore.add(textQuestionScore).divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
        taskEmp.setFinalScore(finalScore);
    }

}

