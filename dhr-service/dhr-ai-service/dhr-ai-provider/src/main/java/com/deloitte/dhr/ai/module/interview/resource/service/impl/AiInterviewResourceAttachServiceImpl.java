package com.deloitte.dhr.ai.module.interview.resource.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.deloitte.dhr.ai.config.AiModelConfig;
import com.deloitte.dhr.ai.module.ai.pojo.AiDatasetsRequest;
import com.deloitte.dhr.ai.module.interview.resource.domain.AiInterviewResourceAttach;
import com.deloitte.dhr.ai.module.interview.resource.mapper.AiInterviewResourceAttachMapper;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewResourceAttachResponse;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewResourceAttachSaveRequest;
import com.deloitte.dhr.ai.module.interview.resource.service.AiInterviewResourceAttachService;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.utility.api.CommonFileDfsInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * AI访谈-附件信息-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
@Slf4j
public class AiInterviewResourceAttachServiceImpl extends SuperServiceImpl<AiInterviewResourceAttachMapper, AiInterviewResourceAttach> implements AiInterviewResourceAttachService {

    @Autowired
    private AiInterviewResourceAttachMapper aiInterviewResourceAttachMapper;
    @Autowired
    private CommonFileDfsInterface commonFileDfsInterface;


    @Override
    public List<AiInterviewResourceAttachResponse> getByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiInterviewResourceAttach> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiInterviewResourceAttach::getResourceId, resourceId);
        List<AiInterviewResourceAttach> attachList = aiInterviewResourceAttachMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(attachList, AiInterviewResourceAttachResponse.class);
    }

    @Override
    public void deleteByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiInterviewResourceAttach> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiInterviewResourceAttach::getResourceId, resourceId);
        List<AiInterviewResourceAttach> attachList = aiInterviewResourceAttachMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(attachList)) {
            return;
        }
        List<Long> ids = attachList.stream().map(AiInterviewResourceAttach::getId).collect(Collectors.toList());
        aiInterviewResourceAttachMapper.deleteBatchIds(ids);

        // 远程调用文件服务删除数据接口
        List<String> fileIds = attachList.stream().map(AiInterviewResourceAttach::getFileId).collect(Collectors.toList());
        fileIds.forEach(fileId -> commonFileDfsInterface.deleteFileById(fileId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveData(List<AiInterviewResourceAttachSaveRequest> requestList, Long resourceId) {
        // 全量保存 课程资源-学习资料文件信息
        LambdaQueryWrapper<AiInterviewResourceAttach> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiInterviewResourceAttach::getResourceId, resourceId);
        // 删除该课程资源下所有学习资料文件
        this.remove(queryWrapper);
        if (CollUtil.isEmpty(requestList)) {
            return false;
        }
        List<AiInterviewResourceAttach> attachList = BeanUtil.copyToList(requestList, AiInterviewResourceAttach.class);
        attachList.forEach(attach -> attach.setResourceId(resourceId));
        // 重新保存新的课程资源学习资料文件
        return this.saveBatch(attachList);
    }

}
