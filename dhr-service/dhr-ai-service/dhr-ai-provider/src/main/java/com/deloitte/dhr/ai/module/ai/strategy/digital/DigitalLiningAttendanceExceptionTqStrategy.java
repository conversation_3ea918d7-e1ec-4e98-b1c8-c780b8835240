package com.deloitte.dhr.ai.module.ai.strategy.digital;

import com.alibaba.fastjson2.JSON;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.DigitalLiningIntentAction;
import com.deloitte.dhr.ai.enums.DigitalLiningIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.DigitalLiningActionStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.strategy.digital.pojo.DigitalLiningAttendanceExceptionTqResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;


/**
 * 数字人(李宁)-考勤异常-事务查询-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@DigitalLiningActionStrategyType(item = DigitalLiningIntentItem.KQYC, action = DigitalLiningIntentAction.TQ)
public class DigitalLiningAttendanceExceptionTqStrategy implements DigitalLiningStrategy {

    @Autowired
    private DigitalLiningCommonTqService commonTqService;

    @Override
    public Flux<List<AiContent>> executeStream(Map<String, Object> param) {
        List<DigitalLiningAttendanceExceptionTqResponse> responses = commonTqService.queryMyAttendanceExceptionInfo();
        AiContent content = AiContent.builder()
                .contentType(AiContentType.JSON.getCode())
                .content(JSON.toJSONString(responses))
                .build();
        return Flux.just(List.of(content));
    }

}
