package com.deloitte.dhr.ai.module.chat.pojo;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * AI对话导出数据
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("ChatHistoryResponse")
public class ChatHistoryResponse {

    @ApiModelProperty(value = "提问时间", name = "userTime")
    private Date userTime;

    @ApiModelProperty(value = "回答时间", name = "aiTime")
    private Date aiTime;

    @ApiModelProperty(value = "提问内容", name = "userMsg")
    private List<UserChatContent> userMsgList;

    @ApiModelProperty(value = "回答内容", name = "aiMsgList")
    private List<AiChatContent> aiMsgList;

    @ApiModelProperty(value = "耗时（秒）", name = "timenum")
    private int timenum;

    @Data
    public static class UserChatContent {

        @ApiModelProperty(value = "回答内容", name = "aiMsg")
        private String userMsg;

        @ApiModelProperty(value = "回答类型", name = "type")
        private String type;
    }


    @Data
    public static class AiChatContent {

        @ApiModelProperty(value = "回答内容", name = "aiMsg")
        private String aiMsg;

        @ApiModelProperty(value = "回答类型", name = "type")
        private String type;
    }
}
