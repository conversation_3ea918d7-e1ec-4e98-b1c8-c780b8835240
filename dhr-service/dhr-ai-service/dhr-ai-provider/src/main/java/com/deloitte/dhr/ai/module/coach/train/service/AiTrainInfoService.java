package com.deloitte.dhr.ai.module.coach.train.service;

import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainInfo;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainDetailResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainInfoResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainInfoSaveRequest;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainSaveRequest;
import com.deloitte.dhr.common.SuperService;

/**
 * 课程资源-AI陪练-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTrainInfoService extends SuperService<AiTrainInfo> {

    /**
     * 保存/更新-AI陪练信息
     *
     * @param request      AI陪练-保存/更新信息
     * @param courseStatus 课程资源状态
     * @return 课程资源ID
     */
    Long saveAll(AiTrainSaveRequest request, String courseStatus);

    /**
     * 提交-AI陪练信息
     *
     * @param request AI陪练-提交信息
     * @return 课程资源ID
     */
    Long submitAll(AiTrainSaveRequest request);

    /**
     * 保存/更新-AI陪练-基础信息
     *
     * @param request    AI陪练-基础信息-保存/更新信息
     * @param resourceId 课程资源ID
     * @return Boolean
     */
    Boolean saveData(AiTrainInfoSaveRequest request, Long resourceId);

    /**
     * 删除-AI陪练信息
     *
     * @param resourceId 课程资源ID
     */
    void deleteByResourceId(Long resourceId);

    /**
     * 查询-AI陪练详情信息
     *
     * @param resourceId 课程资源ID
     * @return AiTrainDetailResponse
     */
    AiTrainDetailResponse getTrainDetail(Long resourceId);

    /**
     * 查询-AI陪练基本信息
     *
     * @param resourceId 课程资源ID
     * @return AiTrainInfoDetailResponse
     */
    AiTrainInfoResponse getByResourceId(Long resourceId);


}
