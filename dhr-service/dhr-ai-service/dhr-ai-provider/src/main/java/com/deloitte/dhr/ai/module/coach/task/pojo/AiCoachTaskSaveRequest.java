package com.deloitte.dhr.ai.module.coach.task.pojo;

import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 培训任务-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiCoachTaskSaveRequest")
public class AiCoachTaskSaveRequest {

    /**
     * 培训任务-基本信息
     */
    @Valid
    @NotNull(groups = {Save.class, Submit.class}, message = "培训任务基本信息不能为空")
    @ApiModelProperty(value = "培训任务-基本信息", name = "taskInfo")
    private AiCoachTaskInfoSaveRequest taskInfo;

    /**
     * 培训任务-员工基本信息
     */
    @Valid
    @NotEmpty(groups = Submit.class, message = "培训任务员工信息不能为空")
    @ApiModelProperty(value = "培训任务-员工基本信息", name = "taskEmpInfoList")
    private List<AiCoachTaskEmpSaveRequest> taskEmpInfoList;

}