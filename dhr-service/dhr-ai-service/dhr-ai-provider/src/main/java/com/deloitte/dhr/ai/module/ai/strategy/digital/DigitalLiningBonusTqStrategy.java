package com.deloitte.dhr.ai.module.ai.strategy.digital;

import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.DigitalLiningIntentAction;
import com.deloitte.dhr.ai.enums.DigitalLiningIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.DigitalLiningActionStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;


/**
 * 数字人(李宁)-奖金-事务查询-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@DigitalLiningActionStrategyType(item = DigitalLiningIntentItem.JJXX, action = DigitalLiningIntentAction.TQ)
public class DigitalLiningBonusTqStrategy implements DigitalLiningStrategy {


    @Override
    public Flux<List<AiContent>> executeStream(Map<String, Object> param) {
        // 奖金属于隐私数据，暂时不会返回具体数据
        AiContent content = AiContent.builder()
                .contentType(AiContentType.JSON.getCode())
                .content(null)
                .build();
        return Flux.just(List.of(content));
    }

}
