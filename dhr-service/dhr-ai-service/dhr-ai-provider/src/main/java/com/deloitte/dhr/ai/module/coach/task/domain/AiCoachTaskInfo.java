package com.deloitte.dhr.ai.module.coach.task.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 培训任务-基本信息表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_coach_task_info")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("培训任务表")
public class AiCoachTaskInfo extends SuperLogicModel<AiCoachTaskInfo> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 培训任务名称
     */
    @TableField(value = "task_name")
    private String taskName;

    /**
     * 培训开始时间
     */
    @TableField(value = "start_date_time")
    private Date startDateTime;

    /**
     * 培训结束时间
     */
    @TableField(value = "end_date_time")
    private Date endDateTime;

    /**
     * 培训封面URL
     */
    @TableField(value = "task_cover_url")
    private String taskCoverUrl;

    /**
     * 培训任务时长（hour）
     */
    @TableField(value = "task_duration")
    private BigDecimal taskDuration;

    /**
     * 培训任务状态;CG：草稿，PXZ：培训中，YWC：已完成，YJS：已结束
     */
    @TableField(value = "task_status")
    private String taskStatus;

}