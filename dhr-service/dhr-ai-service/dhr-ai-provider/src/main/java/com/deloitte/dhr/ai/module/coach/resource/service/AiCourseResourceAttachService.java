package com.deloitte.dhr.ai.module.coach.resource.service;

import com.deloitte.dhr.ai.module.coach.resource.domain.AiCourseResourceAttach;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceAttachResponse;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceAttachSaveRequest;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * 课程资源-附件-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiCourseResourceAttachService extends SuperService<AiCourseResourceAttach> {

    /**
     * 查询数据详情
     *
     * @param resourceId 课程资源ID
     * @return List<AiCourseResourceAttachDetailResponse>
     */
    List<AiCourseResourceAttachResponse> getByResourceId(Long resourceId);

    /**
     * 通过课程资源ID删除数据
     *
     * @param resourceId 课程资源ID
     */
    void deleteByResourceId(Long resourceId);

    /**
     * 保存/更新-课程资源-学习附件信息
     *
     * @param requestList 课程资源-学习附件-保存/更新信息
     * @param resourceId  课程资源ID
     * @return Boolean
     */
    Boolean saveData(List<AiCourseResourceAttachSaveRequest> requestList, Long resourceId);

    /**
     * 上传文件到知识库-异步
     *
     * @param requestList 文件信息
     */
    void uploadFile2DatasetsAsync(List<AiCourseResourceAttachSaveRequest> requestList);

    /**
     * 通过知识库文件ID查询文件信息
     *
     * @param datasetsFileIds 知识库文件ID
     * @return List<AiCourseResourceAttachResponse>
     */
    List<AiCourseResourceAttachResponse> getByDatasetsFileIds(List<String> datasetsFileIds);

}
