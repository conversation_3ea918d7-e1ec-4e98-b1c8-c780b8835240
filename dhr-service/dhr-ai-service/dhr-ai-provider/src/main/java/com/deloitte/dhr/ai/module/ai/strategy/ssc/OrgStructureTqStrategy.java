package com.deloitte.dhr.ai.module.ai.strategy.ssc;


import com.alibaba.fastjson2.JSON;
import com.deloitte.dhr.ai.constant.SscConst;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiSscIntentAction;
import com.deloitte.dhr.ai.enums.AiSscIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.ActionStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.strategy.ssc.pojo.OrgStructureTqResponse;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * SSC-组织机构-事务查询-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@ActionStrategyType(item = AiSscIntentItem.ZZJG, action = AiSscIntentAction.TQ)
public class OrgStructureTqStrategy implements SscStrategy {

    @Override
    public Flux<List<AiContent>> executeStream(Map<String, Object> param) {
        Object queryTypeObj = param.get("selfOrOther");
        if (Objects.isNull(queryTypeObj)) {
            return Flux.empty();
        }
        List<OrgStructureTqResponse> list;
        switch (queryTypeObj.toString()) {
            case SscConst.OTHER:
                list = new ArrayList<>();
                break;
            case SscConst.MYSELF:
            default:
                list = new ArrayList<>();
        }
        AiContent content = AiContent.builder()
                .contentType(AiContentType.JSON.getCode())
                .content(JSON.toJSONString(list))
                .build();
        return Flux.just(List.of(content));
    }

}
