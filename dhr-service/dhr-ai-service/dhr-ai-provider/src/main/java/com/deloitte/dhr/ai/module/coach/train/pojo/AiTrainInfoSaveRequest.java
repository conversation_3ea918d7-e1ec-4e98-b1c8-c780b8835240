package com.deloitte.dhr.ai.module.coach.train.pojo;

import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 课程资源-AI陪练-基本信息-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainInfoSaveRequest")
public class AiTrainInfoSaveRequest {

    /**
     * 对话场景
     */
    @Length(groups = {Save.class, Submit.class}, max = 500, message = "对话场景不能超过500个字符")
    @ApiModelProperty(value = "对话场景", name = "dialogueScene")
    private String dialogueScene;

    /**
     * 对话目标
     */
    @Length(groups = {Save.class, Submit.class}, max = 500, message = "对话场景不能超过500个字符")
    @ApiModelProperty(value = "对话目标", name = "dialogueObjective")
    private String dialogueObjective;

    /**
     * 对话提示问题数量
     */
    @NotNull(groups = {Submit.class}, message = "对话提示问题数量不能为空")
    @Min(groups = {Submit.class}, value = 1, message = "对话提示问题数量至少是1")
    @ApiModelProperty(value = "对话提示问题数量", name = "dialogueQuestionNum")
    private Integer dialogueQuestionNum;

}