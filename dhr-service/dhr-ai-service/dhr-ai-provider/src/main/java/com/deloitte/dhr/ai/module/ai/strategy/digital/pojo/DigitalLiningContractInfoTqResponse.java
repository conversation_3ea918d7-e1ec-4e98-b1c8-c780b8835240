package com.deloitte.dhr.ai.module.ai.strategy.digital.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 数字人(李宁)-合同信息-事务查询
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("DigitalLiningContractInfoTqResponse")
public class DigitalLiningContractInfoTqResponse {

    @ApiModelProperty(value = "合同类型", name = "contractType")
    private String contractType;

    @ApiModelProperty(value = "合同名称", name = "contractName")
    private String contractName;

    @ApiModelProperty(value = "开始时间", name = "startDate")
    private Date startDate;

    @ApiModelProperty(value = "结束时间", name = "endDate")
    private Date endDate;

    @ApiModelProperty(value = "合同状态", name = "contractStatus")
    private String contractStatus;

    @ApiModelProperty(value = "合同文件ID", name = "fileId")
    private String fileId;

    @ApiModelProperty(value = "合同文件名称", name = "fileName")
    private String fileName;

    @ApiModelProperty(value = "合同是否可以下载", name = "isDownLoad")
    private Boolean isDownLoad;

}
