package com.deloitte.dhr.ai.module.coach.task.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.coach.task.domain.AiCoachTaskInfo;
import com.deloitte.dhr.ai.module.coach.task.pojo.*;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 培训任务-相关持久化接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiCoachTaskInfoMapper extends SuperMapper<AiCoachTaskInfo> {

    /**
     * 详情-教学课程任务
     *
     * @param id 培训任务主键ID
     * @return AiTeachTaskDetailResponse
     */
    AiTeachTaskDetailResponse getTeachTaskDetail(@Param("id") Long id);

    /**
     * 详情-AI陪练任务
     *
     * @param id 培训任务主键ID
     * @return AiCoachTaskDetailResponse
     */
    AiTrainTaskDetailResponse getTrainTaskDetail(@Param("id") Long id);

    /**
     * 分页-任务列表
     *
     * @param page  分页参数
     * @param param 过滤条件
     * @param order 排序条件
     * @return 列表数据
     */
    List<AiTaskResponse> findListPage(Page<AiCoachTaskInfo> page, @Param("param") AiTaskListRequest param, @Param("order") String order);

    /**
     * 分页-我的任务列表
     *
     * @param page  分页参数
     * @param param 过滤条件
     * @param order 排序条件
     * @return 列表数据
     */
    List<AiMyTaskListResponse> myTaskList(Page<AiCoachTaskInfo> page, @Param("param") AiMyTaskListRequest param, @Param("order") String order);

    /**
     * 更新培训任务状态
     *
     * @param id 培训任务ID
     */
    void updateTaskStatus(@Param("id") Long id);

}