package com.deloitte.dhr.ai.module.coach.train.pojo;

import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 课程资源-AI陪练-对象-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainObjectSaveRequest")
public class AiTrainObjectSaveRequest {

    /**
     * 陪练对象头像URL
     */
    @NotBlank(groups = {Save.class, Submit.class}, message = "陪练对象头像不能为空")
    @Length(groups = {Save.class, Submit.class}, max = 255, message = "陪练对象头像URL不能超过255个字符")
    @ApiModelProperty(value = "陪练对象头像URL", name = "objectAvatarUrl")
    private String objectAvatarUrl;

    /**
     * 陪练对象名称
     */
    @NotBlank(groups = {Save.class, Submit.class}, message = "陪练对象名称不能为空")
    @Length(groups = {Save.class, Submit.class}, max = 80, message = "陪练对象名称不能超过80个字符")
    @ApiModelProperty(value = "陪练对象名称", name = "objectName")
    private String objectName;

    /**
     * 陪练对象背景信息
     */
    @Length(groups = {Save.class, Submit.class}, max = 500, message = "陪练对象背景信息不能超过500个字符")
    @ApiModelProperty(value = "陪练对象背景信息", name = "objectBackgroundInfo")
    private String objectBackgroundInfo;

    /**
     * 陪练对象性别;male：男，female：女，unknown：未知
     */
    @Length(groups = {Save.class, Submit.class}, max = 8, message = "陪练对象性别不能为空不能超过8个字符")
    @ApiModelProperty(value = "陪练对象性别;male：男，female：女，unknown：未知", name = "objectGender")
    private String objectGender;

}