package com.deloitte.dhr.ai.module.coach.resource.pojo;


import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 课程资源-基本信息-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiCourseResourceInfoSaveRequest")
public class AiCourseResourceInfoSaveRequest {

    /**
     * 课程资源ID
     */
    @ApiModelProperty(value = "课程资源ID", name = "id")
    private Long id;

    /**
     * 类别ID
     */
    @NotNull(groups = {Save.class, Submit.class}, message = "课程类别不能为空")
    @ApiModelProperty(value = "类别ID", name = "categoryId")
    private Long categoryId;

    /**
     * 课程名称
     */
    @NotBlank(groups = {Save.class, Submit.class}, message = "课程名称不能为空")
    @Length(groups = {Save.class, Submit.class}, max = 80, message = "课程名称不能超过80个字符")
    @ApiModelProperty(value = "课程名称", name = "courseName")
    private String courseName;

    /**
     * 课程封面图链接
     */
    @NotBlank(groups = {Save.class, Submit.class}, message = "课程封面不能为空")
    @Length(groups = {Save.class, Submit.class}, max = 255, message = "课程封面图链接不能超过255个字符")
    @ApiModelProperty(value = "课程封面图链接", name = "courseCoverUrl")
    private String courseCoverUrl;

    /**
     * 课程时长（minute）
     */
    @ApiModelProperty(value = "课程时长（minute）", name = "courseDuration")
    private BigDecimal courseDuration;

    /**
     * AI生成操作的记录ID
     */
    @ApiModelProperty(value = "AI生成操作的记录ID", name = "recordId")
    private Long recordId;

}