package com.deloitte.dhr.ai.module.coach.teach.service;

import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachInfo;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachDetailResponse;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachInfoResponse;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachInfoSaveRequest;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachSaveRequest;
import com.deloitte.dhr.common.SuperService;

/**
 * 课程资源-教学课程-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTeachInfoService extends SuperService<AiTeachInfo> {

    /**
     * 保存/更新-教学课程信息
     *
     * @param request      教学课程-保存/更新信息
     * @param courseStatus 课程状态：CG：草稿，TJ：提交
     * @return 课程资源ID
     */
    Long saveAll(AiTeachSaveRequest request, String courseStatus);

    /**
     * 提交-教学课程信息
     *
     * @param request 教学课程-提交信息
     * @return 课程资源ID
     */
    Long submitAll(AiTeachSaveRequest request);

    /**
     * 保存/更新-教学课程-基本信息
     *
     * @param teachInfo  教学课程-基本信息-保存/更新信息
     * @param resourceId 课程资源ID
     * @return Boolean
     */
    Boolean saveData(AiTeachInfoSaveRequest teachInfo, Long resourceId);

    /**
     * 查询教学课程详情
     *
     * @param resourceId 课程资源ID
     * @return AiTeachDetailResponse
     */
    AiTeachDetailResponse getTeachDetail(Long resourceId);

    /**
     * 查询教学课程基本信息
     *
     * @param resourceId 课程资源ID
     * @return AiTeachInfoDetailResponse
     */
    AiTeachInfoResponse getByResourceId(Long resourceId);

    /**
     * 删除教学课程信息
     *
     * @param resourceId 课程资源ID
     */
    void deleteByResourceId(Long resourceId);

}
