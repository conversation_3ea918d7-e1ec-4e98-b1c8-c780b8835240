package com.deloitte.dhr.ai.module.coach.train.service;

import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainMyDialogueHistory;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainDialogueQuestionResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyDialogueHistoryListRequest;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyDialogueHistoryResponse;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * 课程资源-AI陪练-我的对话历史记录-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTrainMyDialogueHistoryService extends SuperService<AiTrainMyDialogueHistory> {

    /**
     * 查询-我的对话历史记录
     *
     * @param request 查询条件
     * @return List<AiTrainMyDialogueHistoryResponse>
     */
    List<AiTrainMyDialogueHistoryResponse> getMyDialogueHistory(AiTrainMyDialogueHistoryListRequest request);

    /**
     * 获取AI下个问题
     *
     * @param currentQuestion     当前问题
     * @param aiQuestionList      问题库
     * @param dialogueHistoryList 对话历史信息
     * @return AiTrainDialogueQuestionResponse
     */
    AiTrainDialogueQuestionResponse getNextAiQuestion(String currentQuestion, List<AiTrainDialogueQuestionResponse> aiQuestionList, List<AiTrainMyDialogueHistoryResponse> dialogueHistoryList);

}
