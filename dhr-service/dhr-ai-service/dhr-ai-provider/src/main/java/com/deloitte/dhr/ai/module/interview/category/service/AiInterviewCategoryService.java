package com.deloitte.dhr.ai.module.interview.category.service;

import com.deloitte.dhr.ai.module.interview.category.domain.AiInterviewCategory;
import com.deloitte.dhr.ai.module.interview.category.pojo.AiInterviewCategoryResponse;
import com.deloitte.dhr.ai.module.interview.category.pojo.AiInterviewCategorySaveRequest;
import com.deloitte.dhr.ai.module.interview.category.pojo.AiInterviewCategoryTreeResponse;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * AI访谈-类别-相关业务接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface AiInterviewCategoryService extends SuperService<AiInterviewCategory> {

    /**
     * 查询-课程类别详情
     *
     * @param id 课程类别ID
     * @return AiInterviewCategoryDetailResponse
     */
    AiInterviewCategoryResponse getDetail(Long id);

    /**
     * 查询-课程类别树
     *
     * @param categoryName 课程类别名称
     * @return List<Tree < Long>>
     */
    List<AiInterviewCategoryTreeResponse> getTree(String categoryName);

    /**
     * 保存/更新-课程类别信息
     *
     * @param request 课程类别-保存/更新信息
     * @return Long
     */
    Long saveData(AiInterviewCategorySaveRequest request);

    /**
     * 删除数据
     *
     * @param id 课程类别ID
     * @return Boolean
     */
    Boolean deleteById(Long id);

    /**
     * 查询-类别全路径名称
     *
     * @param id 类别ID
     * @return 类别全路径名称
     */
    String getCategoryPathNameById(Long id);

}
