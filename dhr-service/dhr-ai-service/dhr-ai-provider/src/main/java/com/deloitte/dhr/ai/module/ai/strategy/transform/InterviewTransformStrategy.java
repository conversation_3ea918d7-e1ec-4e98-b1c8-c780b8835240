package com.deloitte.dhr.ai.module.ai.strategy.transform;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiTransformType;
import com.deloitte.dhr.ai.module.ai.annotation.TransformStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.utils.StrParseJsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * AI转换-访谈-策略
 *
 * <AUTHOR>
 * {@code @date} 2026-06-16
 */
@Component
@TransformStrategyType(AiTransformType.INTERVIEW)
@Slf4j
public class InterviewTransformStrategy implements ResponseTransformStrategy {

    private static final String EVENT_MESSAGE = "text_chunk";

    @Override
    public Flux<List<AiContent>> transformStream(Flux<String> rawStream, AiRequest aiRequest) {
        StringBuilder builder = new StringBuilder();
        return rawStream
                .filter(this::filterEvent)
                .map(this::parseContent)
                .flatMap(raw -> {
                    builder.append(raw);
                    List<String> jsonStrings = StrParseJsonUtil.strParseJson(builder, false);
                    if (!jsonStrings.isEmpty()) {
                        try {

                            AiContent content = AiContent.builder()
                                    .contentType(AiContentType.TEXT.getCode())
                                    .content(jsonStrings.get(0))
                                    .build();

                            return Flux.just(List.of(content));
                        } catch (Exception e) {
                            return Flux.empty();
                        }
                    }
                    return Flux.empty();
                });
    }


    @Override
    public Flux<List<AiContent>> transformBlock(Flux<String> rawStream, AiRequest aiRequest) {
        return rawStream
                .map(raw -> {
                    StringBuilder builder = new StringBuilder();
                    // 处理访谈分析结果
                    try {
                        JSONObject rawObj = JSONObject.parseObject(raw);
                        if (rawObj.containsKey("data") && rawObj.getJSONObject("data").containsKey("outputs")) {
                            JSONObject outputs = rawObj.getJSONObject("data").getJSONObject("outputs");
                            if (outputs.containsKey("text")) {
                                String text = outputs.getString("text");
                                if (StrUtil.isNotBlank(text)) {
                                    builder = new StringBuilder(text);
                                }
                            }
                        }
                    } catch (Exception e) {
                        // 解析失败时保持原始数据
                        log.error("解析访谈分析结果失败, raw data: {}, error: {}", raw, e.getMessage(), e);
                    }


                    List<String> jsonStrings = StrParseJsonUtil.strParseJson(builder, false);
                    if (!jsonStrings.isEmpty()) {
                        AiContent content = AiContent.builder()
                                .contentType(AiContentType.TEXT.getCode())
                                .content(jsonStrings.get(0))
                                .build();
                        return List.of(content);
                    }
                    return List.of();
                });
    }

    /**
     * 过滤事件
     *
     * @param raw 原始数据
     * @return 是否通过过滤
     */
    private boolean filterEvent(String raw) {
        try {
            JSONObject rawObj = JSONObject.parseObject(raw);
            String event = rawObj.getString("event");
            return StrUtil.equals(event, EVENT_MESSAGE);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 解析内容
     *
     * @param raw 原始数据
     * @return AiContent列表
     */
    private String parseContent(String raw) {
        try {
            JSONObject rawObj = JSONObject.parseObject(raw);
            String answer = rawObj.getJSONObject("data").getString("text");

            // 如果内容为空，直接返回空列表
            if (StrUtil.isBlank(answer)) {
                return "";
            }

            return answer;
        } catch (Exception e) {
            return "";
        }
    }
}