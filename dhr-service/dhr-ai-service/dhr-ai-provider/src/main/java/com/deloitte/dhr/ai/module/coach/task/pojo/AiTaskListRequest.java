package com.deloitte.dhr.ai.module.coach.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 培训任务-列表-筛选条件
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTaskListRequest")
public class AiTaskListRequest {

    /**
     * 培训任务名称
     */
    @ApiModelProperty(value = "培训任务名称", name = "taskName")
    private String taskName;

    /**
     * 培训任务状态;CG：草稿，PXZ：培训中，YWC：已完成
     */
    @ApiModelProperty(value = "培训任务状态;CG：草稿，PXZ：培训中，YWC：已完成", name = "taskStatus")
    private String taskStatus;

}