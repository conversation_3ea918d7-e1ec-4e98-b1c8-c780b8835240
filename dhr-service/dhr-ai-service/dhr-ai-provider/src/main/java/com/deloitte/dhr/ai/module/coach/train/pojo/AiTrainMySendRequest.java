package com.deloitte.dhr.ai.module.coach.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 课程资源-AI陪练-我的消息发送
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainMySendRequest")
public class AiTrainMySendRequest {

    /**
     * 培训任务员工ID
     */
    @NotNull(message = "培训任务员工ID不能为空")
    @ApiModelProperty(value = "培训任务员工ID", name = "taskEmpId")
    private Long taskEmpId;

    /**
     * AI消息
     */
    @ApiModelProperty(value = "AI消息", name = "aiMessage")
    private String aiMessage;

    /**
     * AI消息语音文件链接
     */
    @ApiModelProperty(value = "AI消息语音文件链接", name = "aiMessageVoiceFileUrl")
    private String aiMessageVoiceFileUrl;

    /**
     * 员工消息
     */
    @ApiModelProperty(value = "员工消息", name = "empMessage")
    private String empMessage;

    /**
     * 员工消息语音文件链接
     */
    @ApiModelProperty(value = "员工消息语音文件链接", name = "empMessageVoiceFileUrl")
    private String empMessageVoiceFileUrl;

    /**
     * 员工消息语音时长（秒）
     */
    @ApiModelProperty(value = "员工消息语音时长（秒）", name = "empMessageVoiceDurationSeconds")
    private Double empMessageVoiceDurationSeconds;

}