package com.deloitte.dhr.ai.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.deloitte.dhr.common.base.pojo.UserDto;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 新增和修改时设置通用属性值
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Component
public class AiMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        UserDto loginUser = LoginUtil.getLoginUser();
        String empNum = loginUser == null ? "SYSTEM" : loginUser.getUsername();
        this.setFieldValByName("createBy", empNum, metaObject);
        this.setFieldValByName("createTime", new Date(), metaObject);
        this.setFieldValByName("updateBy", empNum, metaObject);
        this.setFieldValByName("updateTime", new Date(), metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        UserDto loginUser = LoginUtil.getLoginUser();
        String empNum = loginUser == null ? "SYSTEM" : loginUser.getUsername();
        this.setFieldValByName("updateBy", empNum, metaObject);
        this.setFieldValByName("updateTime", new Date(), metaObject);
    }

}
