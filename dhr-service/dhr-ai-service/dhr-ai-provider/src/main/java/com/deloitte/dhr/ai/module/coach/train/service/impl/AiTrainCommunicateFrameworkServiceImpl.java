package com.deloitte.dhr.ai.module.coach.train.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainCommunicateFramework;
import com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainCommunicateFrameworkMapper;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainCommunicateFrameworkResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainCommunicateFrameworkSaveRequest;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainCommunicateFrameworkService;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课程资源-AI陪练-沟通框架-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiTrainCommunicateFrameworkServiceImpl extends SuperServiceImpl<AiTrainCommunicateFrameworkMapper, AiTrainCommunicateFramework> implements AiTrainCommunicateFrameworkService {

    @Autowired
    private AiTrainCommunicateFrameworkMapper aiTrainCommunicateFrameworkMapper;

    @Override
    public List<AiTrainCommunicateFrameworkResponse> getByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiTrainCommunicateFramework> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(AiTrainCommunicateFramework::getCreateTime);
        queryWrapper.eq(AiTrainCommunicateFramework::getResourceId, resourceId);
        List<AiTrainCommunicateFramework> frameworkList = aiTrainCommunicateFrameworkMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(frameworkList, AiTrainCommunicateFrameworkResponse.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveData(List<AiTrainCommunicateFrameworkSaveRequest> saveRequestList, Long resourceId) {
        // 删除旧的AI陪练-沟通框架数据
        this.deleteByResourceId(resourceId);

        // 保存新的AI陪练-沟通框架数据
        if (CollUtil.isEmpty(saveRequestList)) {
            return false;
        }
        List<AiTrainCommunicateFramework> frameworkList = BeanUtil.copyToList(saveRequestList, AiTrainCommunicateFramework.class);
        frameworkList.forEach(framework -> framework.setResourceId(resourceId));
        return this.saveBatch(frameworkList);
    }

    @Override
    public void deleteByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiTrainCommunicateFramework> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrainCommunicateFramework::getResourceId, resourceId);
        aiTrainCommunicateFrameworkMapper.delete(queryWrapper);
    }

    @Override
    public void verify(List<AiTrainCommunicateFrameworkSaveRequest> saveRequestList) {

        // 校验-沟通框架权重信息
        if (CollUtil.isEmpty(saveRequestList)) {
            return;
        }
        BigDecimal weightTotal = saveRequestList.stream().map(AiTrainCommunicateFrameworkSaveRequest::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (AiCoachConstant.MAX_WEIGHT.compareTo(weightTotal) != 0) {
            throw new CommRunException("沟通框架信息权重总和应为100%");
        }

    }

}

