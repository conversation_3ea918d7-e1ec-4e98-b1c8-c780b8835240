package com.deloitte.dhr.ai.module.coach.resource.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceInfoResponse;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceListRequest;
import com.deloitte.dhr.ai.module.coach.resource.service.AiCourseResourceInfoService;
import com.deloitte.dhr.common.Request;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 课程资源
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/AiCourseResource")
@Api(tags = "课程资源")
@Validated
public class AiCourseResourceController extends SuperController {

    @Autowired
    private AiCourseResourceInfoService aiCourseResourceInfoService;

    @ApiOperation(value = "列表", notes = "列表")
    @ApiOperationSupport(order = 1)
    @PostMapping("/list/{current}/{size}")
    public ResponseVO<ResponsePage<AiCourseResourceInfoResponse>> list(@PathVariable("current") Long current, @PathVariable("size") Long size, @RequestBody Request<AiCourseResourceListRequest> request) {
        return success(aiCourseResourceInfoService.findListPage(new Page<>(current, size), request.get(), request.getOrder()));
    }

    @ApiOperation(value = "删除", notes = "删除")
    @ApiOperationSupport(order = 2)
    @PostMapping("/del")
    public ResponseVO<Boolean> del(@RequestParam("id") Long id) {
        return success(aiCourseResourceInfoService.deleteById(id));
    }

}
