package com.deloitte.dhr.ai.enums;

import lombok.Getter;

/**
 * 阿里云-语音识别类型
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public enum AliyunSrTypeEnum {

    /**
     * 一句话识别
     */
    ASR("asr", "一句话识别"),

    /**
     * 录音文件识别极速版
     */
    FLASH_RECOGNIZER("flashRecognizer", "录音文件识别极速版");

    /**
     * 类型
     */
    private final String type;

    /**
     * 描述
     */
    private final String desc;

    AliyunSrTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

}
