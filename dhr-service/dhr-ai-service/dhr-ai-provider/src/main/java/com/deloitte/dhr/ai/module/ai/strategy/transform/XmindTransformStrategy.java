package com.deloitte.dhr.ai.module.ai.strategy.transform;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiRoleType;
import com.deloitte.dhr.ai.enums.AiTransformType;
import com.deloitte.dhr.ai.module.ai.annotation.TransformStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.utils.StrParseJsonUtil;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * AI转换-思维导图-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@TransformStrategyType(AiTransformType.XMIND)
public class XmindTransformStrategy implements ResponseTransformStrategy {

    @Override
    public Flux<List<AiContent>> transformStream(Flux<String> rawStream, AiRequest aiRequest) {
        StringBuilder builder = new StringBuilder();
        return rawStream
                .filter(raw -> StrUtil.equals(JSONObject.parseObject(raw).getString("event"), "message"))
                .flatMap(raw -> {
                    String answer = JSONObject.parseObject(raw).getString("answer");
                    builder.append(answer);
                    List<String> list = StrParseJsonUtil.strParseJson(builder, false);
                    return Flux.fromIterable(list);
                })
                .map(raw -> {
                    AiContent content = AiContent.builder()
                            .contentType(AiContentType.XMIND.getCode())
                            .content(raw)
                            .build();
                    return List.of(content);
                });
    }

    @Override
    public Flux<List<AiContent>> transformBlock(Flux<String> rawStream, AiRequest aiRequest) {
        return transformStream(rawStream, aiRequest);
    }

}

