package com.deloitte.dhr.ai.module.interview.train.controller;

import com.deloitte.dhr.ai.module.interview.constant.AiInterviewConstant;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewObjectResponse;
import com.deloitte.dhr.ai.module.interview.task.service.AiInterviewTaskEmpService;
import com.deloitte.dhr.ai.module.interview.train.pojo.*;
import com.deloitte.dhr.ai.module.interview.train.service.*;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI访谈-访谈任务-我的访谈
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/AiInterview/my")
@Api(tags = "AI访谈-访谈任务-我的访谈")
@Validated
public class AiMyInterviewController extends SuperController {

    @Autowired
    private AiMyInterviewService aiMyInterviewService;
    @Autowired
    private AiInterviewMyDialogueHistoryService aiInterviewMyDialogueHistoryService;
    @Autowired
    private AiInterviewTaskEmpService aiInterviewTaskEmpService;

    @ApiOperation(value = "我的问题列表", notes = "我的问题列表")
    @ApiOperationSupport(order = 1)
    @GetMapping("/getMyQuestionList/{taskEmpId}")
    public ResponseVO<List<AiInterviewMyQuestionListResponse>> getMyQuestionList(@PathVariable("taskEmpId") Long taskEmpId) {
        return success(aiInterviewMyDialogueHistoryService.getMyQuestionList(taskEmpId));
    }

    @ApiOperation(value = "陪练对象", notes = "陪练对象")
    @ApiOperationSupport(order = 1)
    @GetMapping("/object/detail/{taskEmpId}")
    public ResponseVO<AiInterviewObjectResponse> getMyObjectDetail(@PathVariable("taskEmpId") Long taskEmpId) {
        return success(aiInterviewTaskEmpService.getMyObjectDetail(taskEmpId));
    }

    @ApiOperation(value = "对话历史", notes = "对话历史")
    @ApiOperationSupport(order = 2)
    @PostMapping("/dialogue/history")
    public ResponseVO<List<AiInterviewMyDialogueHistoryResponse>> getMyDialogueHistory(@RequestBody @Validated AiInterviewMyDialogueHistoryListRequest request) {
        return success(aiInterviewMyDialogueHistoryService.getMyDialogueHistory(request));
    }

    @ApiOperation(value = "发送消息", notes = "总体概况")
    @ApiOperationSupport(order = 6)
    @PostMapping("/send")
    public ResponseVO<AiInterviewMySendResponse> sendMessage(@RequestBody @Validated AiInterviewMySendRequest request) {
        return success(aiMyInterviewService.sendMessage(request));
    }

    @ApiOperation(value = "跳过", notes = "跳过")
    @ApiOperationSupport(order = 7)
    @PostMapping("/skip/{id}")
    public ResponseVO<AiInterviewMySendResponse> skip(@PathVariable("id") Long id) {
        return success(aiMyInterviewService.skip(id));
    }

    @ApiOperation(value = "洞察报告", notes = "洞察报告")
    @ApiOperationSupport(order = 10)
    @GetMapping("/report/{taskEmpId}")
    public ResponseVO<AiInterviewTaskMyReportResponse> getMyReport(@PathVariable("taskEmpId") Long taskEmpId) {
        return success(aiMyInterviewService.getMyReport(taskEmpId));
    }

    @ApiOperation(value = "重新生成洞察报告", notes = "重新生成洞察报告")
    @ApiOperationSupport(order = 10)
    @GetMapping("/createReport/{taskEmpId}")
    public ResponseVO<Boolean> createReport(@PathVariable("taskEmpId") Long taskEmpId) {
        aiMyInterviewService.createReport(taskEmpId);
        return success(true);
    }
}
