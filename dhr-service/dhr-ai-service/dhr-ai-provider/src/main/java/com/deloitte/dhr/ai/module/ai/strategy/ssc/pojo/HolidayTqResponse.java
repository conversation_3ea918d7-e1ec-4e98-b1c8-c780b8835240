package com.deloitte.dhr.ai.module.ai.strategy.ssc.pojo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * SSC-休假-事务查询
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HolidayTqResponse {

    @ApiModelProperty(value = "假期类型", name = "holidayType")
    private String holidayType;

    @ApiModelProperty(value = "假期名称", name = "holidayName")
    private String holidayName;

    @ApiModelProperty(value = "剩余假期数", name = "remainingNum")
    private Double remainingNum;

    @ApiModelProperty(value = "已使用假期数", name = "usedNum")
    private Double usedNum;

    @ApiModelProperty(value = "假期过期时间", name = "expiryTime")
    private Date expiryTime;

}
