package com.deloitte.dhr.ai.module.ai.strategy.transform;

import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import reactor.core.publisher.Flux;

import java.util.List;


/**
 * AI响应转换-策略-抽象层
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface ResponseTransformStrategy {

    /**
     * 处理流式数据
     *
     * @param rawStream 原始数据
     * @param aiRequest AI请求
     * @return Flux<List < AIContent>>
     */
    Flux<List<AiContent>> transformStream(Flux<String> rawStream, AiRequest aiRequest);

    /**
     * 处理阻塞数据
     *
     * @param rawStream 原始数据
     * @param aiRequest AI请求
     * @return Mono<List < AIContent>>
     */
    Flux<List<AiContent>> transformBlock(Flux<String> rawStream, AiRequest aiRequest);

}
