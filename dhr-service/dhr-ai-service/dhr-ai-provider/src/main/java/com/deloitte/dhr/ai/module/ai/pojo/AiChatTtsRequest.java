package com.deloitte.dhr.ai.module.ai.pojo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * AI语音合成
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@Builder
public class AiChatTtsRequest {

    @ApiModelProperty(value = "语音合成文本", name = "text")
    @NotBlank(message = "需要语言合成的文本不能为空")
    private String text;

    @ApiModelProperty(value = "语速：[0.8,2]，默认为 1，通常保留一位小数即可", name = "speedRatio")
    private Double speedRatio;

}
