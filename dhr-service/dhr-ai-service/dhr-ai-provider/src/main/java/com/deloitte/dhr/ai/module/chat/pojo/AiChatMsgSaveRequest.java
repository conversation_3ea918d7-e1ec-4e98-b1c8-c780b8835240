package com.deloitte.dhr.ai.module.chat.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * AI聊天-聊天消息-保存
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("AiChatMsgSaveRequest")
public class AiChatMsgSaveRequest {

    @ApiModelProperty(value = "ID", name = "id")
    private Long id;

    @ApiModelProperty(value = "对话ID", name = "dialogueId")
    private Long dialogueId;

    @ApiModelProperty(value = "会话ID", name = "conversationId")
    private String conversationId;

    @ApiModelProperty(value = "AI类型", name = "aiType")
    private String aiType;

    @ApiModelProperty(value = "AI子类型", name = "aiSubType")
    private String aiSubType;

    @ApiModelProperty(value = "角色", name = "role")
    private String role;

    @ApiModelProperty(value = "发送时间", name = "sendTime")
    private Date sendTime;

    //问题ID
    private Long msgId;
}