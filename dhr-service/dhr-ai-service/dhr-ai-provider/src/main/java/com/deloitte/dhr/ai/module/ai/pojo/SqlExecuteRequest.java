package com.deloitte.dhr.ai.module.ai.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * SQL执行请求
 *
 * <AUTHOR>
 * @date 2024-03-03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SQL执行请求")
public class SqlExecuteRequest {

    @NotBlank(message = "SQL语句不能为空")
    @Size(max = 100000, message = "SQL语句长度不能超过100000个字符")
    @ApiModelProperty(value = "SQL语句", required = true, example = "SELECT * FROM users LIMIT 10")
    private String sql;
}
