package com.deloitte.dhr.ai.module.coach.teach.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 课程资源-教学课程-考核题目-选择题选项表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_teach_choice_option")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-教学课程-考核题目-选择题选项表")
public class AiTeachChoiceOption extends SuperLogicModel<AiTeachChoiceOption> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 题目ID
     */
    @TableField(value = "question_id")
    private Long questionId;

    /**
     * 选项名称
     */
    @TableField(value = "option_name")
    private String optionName;

    /**
     * 是否答案
     */
    @TableField(value = "is_answer")
    private Boolean isAnswer = false;

    /**
     * 选项序号
     */
    @TableField(value = "option_sort")
    private Integer optionSort;

}