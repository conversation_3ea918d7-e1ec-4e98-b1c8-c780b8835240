package com.deloitte.dhr.ai.module.coach.teach.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 课程资源-教学课程-考核题目-我的文本题答案信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("AiTeachMyTextSampleAnswerResponse")
public class AiTeachMyTextSampleAnswerResponse extends AiTeachTextSampleAnswerResponse {

    /**
     * 我的答案
     */
    @ApiModelProperty(value = "我的答案", name = "myAnswerContent")
    private String myAnswerContent;

    /**
     * AI匹配度
     */
    @ApiModelProperty(value = "AI匹配度", name = "aiMatchingDegree")
    private BigDecimal aiMatchingDegree;

}