package com.deloitte.dhr.ai.module.coach.teach.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 课程资源-教学课程-基本信息-详情
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachInfoResponse")
public class AiTeachInfoResponse {

    /**
     * 课程资源ID
     */
    @ApiModelProperty(value = "课程资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * 课程目标
     */
    @ApiModelProperty(value = "课程目标", name = "courseObjectives")
    private String courseObjectives;

    /**
     * 课程设计要求
     */
    @ApiModelProperty(value = "课程设计要求", name = "courseDesignRequirements")
    private String courseDesignRequirements;

    /**
     * 考察知识点
     */
    @ApiModelProperty(value = "考察知识点", name = "examiningKnowledgePoints")
    private String examiningKnowledgePoints;

    /**
     * 单选题数量
     */
    @ApiModelProperty(value = "单选题数量", name = "singleChoiceQuestionNum")
    private Integer singleChoiceQuestionNum;

    /**
     * 判断题数量
     */
    @ApiModelProperty(value = "判断题数量", name = "judgingQuestionNum")
    private Integer judgingQuestionNum;

    /**
     * 问答题数量
     */
    @ApiModelProperty(value = "问答题数量", name = "essayQuestionNum")
    private Integer essayQuestionNum;

    /**
     * 选择题正确率
     */
    @ApiModelProperty(value = "选择题正确率", name = "choiceQuestionAccuracy")
    private BigDecimal choiceQuestionAccuracy;

    /**
     * 问答题匹配度
     */
    @ApiModelProperty(value = "问答题匹配度", name = "textQuestionMatchingDegree")
    private BigDecimal textQuestionMatchingDegree;

}