package com.deloitte.dhr.ai.module.coach.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 课程资源-AI陪练-我的消息发送
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainMySendResponse")
public class AiTrainMySendResponse {

    /**
     * 下一条AI消息
     */
    @ApiModelProperty(value = "下一条AI消息", name = "nextAiMessageInfo")
    private AiTrainMyDialogueHistoryResponse nextAiMessageInfo;

    /**
     * 语速：较快、较慢、正常
     */
    @ApiModelProperty(value = "语速：较快、较慢、正常", name = "speedName")
    private String speedName;

    /**
     * 能力分析信息
     */
    @ApiModelProperty(value = "能力分析信息", name = "abilityAnalyseInfoList")
    private List<AiTrainMyAbilityScoreResponse> abilityAnalyseInfoList;

    /**
     * 对话是否结束
     */
    @ApiModelProperty(value = "对话是否结束", name = "isDialogueEnd")
    private Boolean isDialogueEnd;

    /**
     * 本次员工对话消息ID
     */
    @ApiModelProperty(value = "本次员工对话消息ID", name = "currentEmpMessageId")
    private Long currentEmpMessageId;

}