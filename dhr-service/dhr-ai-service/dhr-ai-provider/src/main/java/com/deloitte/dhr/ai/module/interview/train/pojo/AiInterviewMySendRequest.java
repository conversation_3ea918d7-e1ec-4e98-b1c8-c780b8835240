package com.deloitte.dhr.ai.module.interview.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * AI访谈-访谈任务-我的消息发送
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewMySendRequest")
public class AiInterviewMySendRequest {

    /**
     * AI访谈任务问题数据ID
     */
    @NotNull(message = "AI访谈任务问题数据ID不能为空")
    @ApiModelProperty(value = "AI访谈任务问题数据ID", name = "id")
    private Long id;

    /**
     * 员工消息
     */
    @NotNull(message = "员工消息不能为空")
    @ApiModelProperty(value = "员工消息", name = "empMessage")
    private String empMessage;

    /**
     * 员工消息语音文件链接
     */
    @ApiModelProperty(value = "员工消息语音文件链接", name = "empMessageVoiceFileUrl")
    private String empMessageVoiceFileUrl;

    /**
     * 员工消息语音时长（秒）
     */
    @ApiModelProperty(value = "员工消息语音时长（秒）", name = "empMessageVoiceDurationSeconds")
    private Double empMessageVoiceDurationSeconds;

}