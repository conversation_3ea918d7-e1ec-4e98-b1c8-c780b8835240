package com.deloitte.dhr.ai.module.coach.constant;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * AI训练语速分析枚举
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public enum AiTrainSpeedAnalyseEnum {

    /**
     * 语速较慢
     */
    slow("slow", "较慢"),

    /**
     * 语速正常
     */
    normal("normal", "正常"),

    /**
     * 语速较快
     */
    fast("fast", "较快");

    /**
     * 编码
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    AiTrainSpeedAnalyseEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 通过name获取code
     *
     * @param name 名称
     * @return 编码
     */
    public static String getCodeByName(String name) {
        for (AiTrainSpeedAnalyseEnum analyseEnum : AiTrainSpeedAnalyseEnum.values()) {
            if (StrUtil.equals(analyseEnum.name, name)) {
                return analyseEnum.code;
            }
        }
        return "";
    }

}
