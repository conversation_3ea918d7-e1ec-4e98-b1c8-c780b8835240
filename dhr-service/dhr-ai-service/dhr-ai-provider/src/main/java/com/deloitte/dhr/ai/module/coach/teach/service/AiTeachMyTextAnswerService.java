package com.deloitte.dhr.ai.module.coach.teach.service;

import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachMyTextAnswer;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachMyCoachQuestionSaveRequest;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachMyTextAnswerResponse;
import com.deloitte.dhr.common.SuperService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 课程资源-教学课程-我的文本问题答案-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTeachMyTextAnswerService extends SuperService<AiTeachMyTextAnswer> {

    /**
     * 删除-文本题答案信息
     *
     * @param taskEmpId 培训任务员工ID
     */
    void deleteByTaskEmpId(Long taskEmpId);

    /**
     * 保存/更新-我的文本题答案信息
     *
     * @param requestList 我的选择题答案-保存/更新信息
     * @param resourceId  课程资源ID
     * @param taskId      培训任务Id
     * @param taskEmpId   培训任务员工ID
     * @return Map<Long, BigDecimal> key值：题目ID，value值：答案匹配度
     */
    Map<Long, BigDecimal> saveData(List<AiTeachMyCoachQuestionSaveRequest> requestList, Long resourceId, Long taskId, Long taskEmpId);

    /**
     * 查询-我的文本题答案信息
     *
     * @param taskEmpId 培训任务员工ID
     * @return List<AiTeachMyTextAnswerDetailResponse>
     */
    List<AiTeachMyTextAnswerResponse> getByTaskEmpId(Long taskEmpId);

}
