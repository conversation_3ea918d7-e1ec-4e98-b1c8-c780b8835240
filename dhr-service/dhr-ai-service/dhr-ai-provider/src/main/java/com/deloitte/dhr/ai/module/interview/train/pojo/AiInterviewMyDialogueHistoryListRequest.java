package com.deloitte.dhr.ai.module.interview.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * AI访谈-访谈任务-我的对话历史记录-查询条件
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewMyDialogueHistoryListRequest")
@AllArgsConstructor
@NoArgsConstructor
public class AiInterviewMyDialogueHistoryListRequest {

    /**
     * AI访谈任务员工ID
     */
    @NotNull(message = "访谈任务员工ID不能为空")
    @ApiModelProperty(value = "AI访谈任务员工ID", name = "taskEmpId")
    private Long taskEmpId;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间", name = "sendTime")
    private Date sendTime;

    /**
     * 条数，默认20条
     */
    @ApiModelProperty(value = "条数", name = "size")
    private Integer size = 20;

}