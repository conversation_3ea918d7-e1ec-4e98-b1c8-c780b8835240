package com.deloitte.dhr.ai.module.coach.train.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 课程资源-AI陪练-基本信息表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_train_info")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-AI陪练-基本信息表")
public class AiTrainInfo extends SuperLogicModel<AiTrainInfo> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 对话场景
     */
    @TableField(value = "dialogue_scene")
    private String dialogueScene;

    /**
     * 对话目标
     */
    @TableField(value = "dialogue_objective")
    private String dialogueObjective;

    /**
     * 对话提示问题数量
     */
    @TableField(value = "dialogue_question_num")
    private Integer dialogueQuestionNum;

}