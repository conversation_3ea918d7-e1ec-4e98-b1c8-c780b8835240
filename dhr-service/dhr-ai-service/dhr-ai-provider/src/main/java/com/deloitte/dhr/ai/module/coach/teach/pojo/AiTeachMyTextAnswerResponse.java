package com.deloitte.dhr.ai.module.coach.teach.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 课程资源-教学课程-我的文本问题答案信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachMyTextAnswerResponse")
public class AiTeachMyTextAnswerResponse {

    /**
     * 考核题目ID
     */
    @ApiModelProperty(value = "考核题目ID", name = "question_id")
    private Long questionId;

    /**
     * 文本题答案内容
     */
    @ApiModelProperty(value = "文本题答案内容", name = "answer_content")
    private String answerContent;

    /**
     * 匹配度(来源AI)
     */
    @ApiModelProperty(value = "匹配度(来源AI)", name = "ai_matching_degree")
    private BigDecimal aiMatchingDegree;

}