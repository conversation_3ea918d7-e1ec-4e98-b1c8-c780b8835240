package com.deloitte.dhr.ai.module.interview.resource.pojo;

import com.deloitte.dhr.ai.module.interview.validation.Save;
import com.deloitte.dhr.ai.module.interview.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 课程资源-AI访谈-对象-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */

/**
 * AI访谈-访谈任务-我的得分项
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewObjectSaveRequest")
public class AiInterviewObjectSaveRequest {

    /**
     * 访谈对象头像类型
     */
    @NotBlank(groups = {Save.class, Submit.class}, message = "角色头像类型不能为空")
    @Length(groups = {Save.class, Submit.class}, max = 255, message = "角色头像类型不能超过255个字符")
    @ApiModelProperty(value = "角色头像类型; 01默认，02自定义", name = "objectAvatarType")
    private String objectAvatarType;

    @ApiModelProperty(value = "访谈对象头像URL", name = "objectAvatarUrl")
    private String objectAvatarUrl;

    /**
     * 角色名称
     */
    @NotBlank(groups = {Save.class, Submit.class}, message = "角色名称不能为空")
    @Length(groups = {Save.class, Submit.class}, max = 20, message = "角色名称不能超过20个字符")
    @ApiModelProperty(value = "角色名称", name = "objectName")
    private String objectName;

    /**
     * 角色设定
     */
    @Length(groups = {Save.class, Submit.class}, max = 200, message = "角色设定信息不能超过200个字符")
    @ApiModelProperty(value = "角色设定", name = "objectBackgroundInfo")
    private String objectBackgroundInfo;

    /**
     * 访谈对象性别;male：男，female：女，unknown：未知
     */
    @Length(groups = {Save.class, Submit.class}, max = 8, message = "角色性别不能为空不能超过8个字符")
    @ApiModelProperty(value = "角色性别;male：男，female：女，unknown：未知", name = "objectGender")
    private String objectGender;

}