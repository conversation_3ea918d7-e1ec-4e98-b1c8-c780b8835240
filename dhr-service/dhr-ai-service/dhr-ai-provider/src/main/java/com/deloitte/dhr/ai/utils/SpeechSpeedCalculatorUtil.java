package com.deloitte.dhr.ai.utils;

/**
 * 语速计算工具类
 *
 * <AUTHOR>
 */
public class SpeechSpeedCalculatorUtil {

    /**
     * 计算语音速度
     *
     * @param text              文本内容
     * @param durationInSeconds 语音时长（秒）
     * @return 语音速度（较慢、正常、较快）
     */
    public static String calculateSpeechRate(String text, Double durationInSeconds) {
        if (text == null || durationInSeconds == null) {
            return "";
        }
        int wordCount = text.length();
        double durationInMinutes = durationInSeconds / 60.0;
        double wordsPerMinute = wordCount / durationInMinutes;
        if (wordsPerMinute < 180) {
            return "较慢";
        } else if (wordsPerMinute >= 180 && wordsPerMinute <= 240) {
            return "正常";
        } else {
            return "较快";
        }
    }

}
