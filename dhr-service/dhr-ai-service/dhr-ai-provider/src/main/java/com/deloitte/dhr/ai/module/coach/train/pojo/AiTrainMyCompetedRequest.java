package com.deloitte.dhr.ai.module.coach.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 课程资源-AI陪练-我的陪练-完成
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainMyCompetedRequest")
public class AiTrainMyCompetedRequest {

    /**
     * 培训任务员工ID
     */
    @NotNull(message = "培训任务员工ID不能为空")
    @ApiModelProperty(value = "培训任务员工ID", name = "taskEmpId")
    private Long taskEmpId;

    /**
     * 消耗时间（minute）
     */
    @ApiModelProperty(value = "消耗时间（minute）", name = "elapsedTime")
    private BigDecimal elapsedTime;

}