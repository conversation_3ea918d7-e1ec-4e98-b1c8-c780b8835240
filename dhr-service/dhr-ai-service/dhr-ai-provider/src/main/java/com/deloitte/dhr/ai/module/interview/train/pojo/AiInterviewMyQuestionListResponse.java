package com.deloitte.dhr.ai.module.interview.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * AI访谈-访谈任务-我的对话历史记录
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("AiInterviewMyQuestionListResponse")
public class AiInterviewMyQuestionListResponse {

    /**
     * 问题名称
     */
    @ApiModelProperty(value = "问题名称", name = "question")
    private String question;

    /**
     * 所属问题ID
     */
    @ApiModelProperty(value = "所属问题ID", name = "questionId")
    private Long questionId;

    /**
     * 答复状态,EmpTaskQuestionStatus
     */
    @ApiModelProperty(value = "答复状态，01通过，02不通过，03跳过", name = "status")
    private String status;


}