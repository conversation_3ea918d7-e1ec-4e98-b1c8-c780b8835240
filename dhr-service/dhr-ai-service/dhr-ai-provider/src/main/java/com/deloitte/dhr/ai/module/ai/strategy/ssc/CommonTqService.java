package com.deloitte.dhr.ai.module.ai.strategy.ssc;


import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.deloitte.dhr.ai.module.ai.strategy.ssc.pojo.AttendanceExceptionTqResponse;
import com.deloitte.dhr.ai.module.ai.strategy.ssc.pojo.CancelLeaveTqResponse;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * SSC-通用事务查询业务实现类
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class CommonTqService {

    /**
     * 查询我的考勤异常信息
     *
     * @return List<AttendanceExceptionTqResponse>
     */
    public List<AttendanceExceptionTqResponse> queryMyAttendanceExceptionInfo() {
        // todo mock假数据模拟
        List<AttendanceExceptionTqResponse> responseList = new ArrayList<>();
        List<String> randomMonthDates = getRandomMonthDates(new Date(), 3);
        responseList.add(new AttendanceExceptionTqResponse("10", "漏卡", DateUtil.parse(randomMonthDates.get(0), DatePattern.NORM_DATE_PATTERN), "上班打卡"));
        responseList.add(new AttendanceExceptionTqResponse("20", "迟到", DateUtil.parse(randomMonthDates.get(1) + " 09:38:28", DatePattern.NORM_DATETIME_PATTERN), ""));
        responseList.add(new AttendanceExceptionTqResponse("30", "早退", DateUtil.parse(randomMonthDates.get(2) + " 18:29:01", DatePattern.NORM_DATETIME_PATTERN), ""));
        return responseList;
    }

    /**
     * 获取当前登录人员工编号
     *
     * @return 当前登录人员工编号
     */
    public String getLoginEmpCode() {
        return LoginUtil.getLoginUser() == null ? "" : LoginUtil.getLoginUser().getUsername();
    }

    /**
     * 查询我的考勤销假信息
     *
     * @return List<CancelLeaveTqResponse>
     */
    public List<CancelLeaveTqResponse> queryMyCancelLeaveInfo() {
        // 创建Mock考勤销假数据，使用最近一个月的时间
        List<CancelLeaveTqResponse> dataList = new ArrayList<>();

        // 获取当前日期
        Date now = DateUtil.date();
        // 获取最近一个月的开始日期
        Date lastMonthStart = DateUtil.beginOfMonth(DateUtil.offsetMonth(now, -1));
        // 获取当前月份的开始日期
        Date currentMonthStart = DateUtil.beginOfMonth(now);

        // 第一条数据：年休假，审批中
        CancelLeaveTqResponse item1 = new CancelLeaveTqResponse();
        // 生成最近一个月内的日期范围
        long startTime1 = lastMonthStart.getTime();
        long endTime1 = currentMonthStart.getTime();
        Date startDate1 = new Date(startTime1 + RandomUtil.randomLong(0, endTime1 - startTime1));
        Date endDate1 = DateUtil.offsetDay(startDate1, RandomUtil.randomInt(1, 5));
        item1.setDate(DateUtil.format(startDate1, DatePattern.NORM_DATE_PATTERN) + " ~ " +
                DateUtil.format(endDate1, DatePattern.NORM_DATE_PATTERN));
        item1.setDuration(RandomUtil.randomInt(8, 40));
        item1.setType("年休假");
        item1.setApprovalStatus("审批中");
        item1.setOperation("直接撤销");
        item1.setHandoverPerson("夏仁栩");
        item1.setHandoverPhone("13800138001");
        item1.setLeaveReason("个人年假安排，需要休息调整");
        dataList.add(item1);

        // 第二条数据：事假，已通过
        CancelLeaveTqResponse item2 = new CancelLeaveTqResponse();
        long startTime2 = lastMonthStart.getTime();
        long endTime2 = currentMonthStart.getTime();
        Date singleDate = new Date(startTime2 + RandomUtil.randomLong(0, endTime2 - startTime2));
        item2.setDate(DateUtil.format(singleDate, DatePattern.NORM_DATE_PATTERN));
        item2.setDuration(RandomUtil.randomInt(4, 16));
        item2.setType("事假");
        item2.setApprovalStatus("已通过");
        item2.setOperation("销假申请");
        item2.setHandoverPerson("陈燕妮");
        item2.setHandoverPhone("13900139002");
        item2.setLeaveReason("家中有急事需要处理");
        dataList.add(item2);

        // 第三条数据：调休假，已通过
        CancelLeaveTqResponse item3 = new CancelLeaveTqResponse();
        long startTime3 = lastMonthStart.getTime();
        long endTime3 = currentMonthStart.getTime();
        Date startDate3 = new Date(startTime3 + RandomUtil.randomLong(0, endTime3 - startTime3));
        Date endDate3 = DateUtil.offsetDay(startDate3, RandomUtil.randomInt(2, 8));
        item3.setDate(DateUtil.format(startDate3, DatePattern.NORM_DATE_PATTERN) + " ~ " +
                DateUtil.format(endDate3, DatePattern.NORM_DATE_PATTERN));
        item3.setDuration(RandomUtil.randomInt(16, 64));
        item3.setType("调休假");
        item3.setApprovalStatus("已通过");
        item3.setOperation("销假申请");
        item3.setHandoverPerson("王曦");
        item3.setHandoverPhone("13700137003");
        item3.setLeaveReason("加班调休，需要休息");
        dataList.add(item3);

        return dataList;
    }

    /**
     * 获取某月随机日期
     *
     * @param date 某个时间
     * @param num  生成个数
     * @return 随机日期列表
     */
    private static List<String> getRandomMonthDates(Date date, int num) {
        // 获取当前月份的天数
        int lengthOfMonth = DateUtil.lengthOfMonth(DateUtil.month(date), DateUtil.isLeapYear(DateUtil.year(date)));
        Set<Date> dates = new HashSet<>();
        while (dates.size() < num) {
            dates.add(RandomUtil.randomDate(DateUtil.beginOfMonth(date), DateField.DAY_OF_MONTH, 0, lengthOfMonth));
        }
        return dates.stream().map(DateUtil::formatDate).collect(Collectors.toList());
    }

}
