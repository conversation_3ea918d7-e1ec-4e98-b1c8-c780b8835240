package com.deloitte.dhr.ai.module.interview.task.pojo;

import com.deloitte.dhr.ai.module.interview.validation.Save;
import com.deloitte.dhr.ai.module.interview.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * AI访谈-访谈任务-保存
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewTaskSaveRequest")
public class AiInterviewTaskSaveRequest {

    /**
     * AI访谈任务-基本信息
     */
    @Valid
    @NotNull(groups = {Save.class, Submit.class}, message = "访谈任务基本信息不能为空")
    @ApiModelProperty(value = "AI访谈任务-基本信息", name = "taskInfo")
    private AiInterviewTaskInfoSaveRequest taskInfo;

    /**
     * AI访谈任务-员工基本信息
     */
    @Valid
    @NotEmpty(groups = Submit.class, message = "访谈任务员工信息不能为空")
    @ApiModelProperty(value = "AI访谈任务-员工基本信息", name = "taskEmpInfoList")
    private List<AiInterviewTaskEmpSaveRequest> taskEmpInfoList;

}