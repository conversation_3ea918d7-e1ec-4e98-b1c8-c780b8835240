package com.deloitte.dhr.ai.module.chat.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.chat.pojo.ChatHistoryRequest;
import com.deloitte.dhr.ai.module.chat.pojo.ChatHistoryResponse;
import com.deloitte.dhr.ai.module.chat.domain.AiChatMsg;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatMsgListRequest;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatMsgListResponse;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatMsgSaveRequest;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * AI聊天-聊天消息-相关业务接口
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface AiChatMsgService extends SuperService<AiChatMsg> {

    /**
     * 保存-数据
     *
     * @param request 聊天消息
     * @return 主键
     */
    Long saveOrUpdateData(AiChatMsgSaveRequest request);

    /**
     * 删除-聊天消息
     *
     * @param dialogueId 聊天对话ID
     */
    void deleteByDialogueId(Long dialogueId);

    /**
     * 查询-聊天消息列表
     *
     * @param request 查询条件
     * @return List<AiPalChatHistoryListResponse>
     */
    List<AiChatMsgListResponse> getChatList(AiChatMsgListRequest request);

    ResponsePage<ChatHistoryResponse> chatHistory(Page page, ChatHistoryRequest request);
}
