package com.deloitte.dhr.ai.enums;


import lombok.Getter;

import static com.deloitte.dhr.ai.enums.AiSscIntentType.*;


/**
 * SSC意图识别类型项目枚举
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public enum AiSscIntentItem {

    HTXX("HTXX", "合同信息", RS),
    ZMXX("ZMXX", "证明信息", RS),
    RSDA("RSDA", "人事档案", RS),

    ZZJG("ZZJG", "组织机构", ZZ),

    GZXX("GZXX", "工资信息", XC),
    JJXX("JJXX", "奖金信息", XC),

    XJXX("XJXX", "休假信息", KQ),
    GCCC("GCCC", "公出出差", KQ),
    KQXJ("KQXJ", "考勤销假", KQ),
    KQYC("KQYC", "考勤异常", KQ),

    JXMB("JXMB", "绩效目标", JX),
    JXJG("JXJG", "绩效结果", JX),

    TY("TY", "通用", AiSscIntentType.TY);

    private final String code;

    private final String name;

    private final AiSscIntentType type;

    AiSscIntentItem(String code, String name, AiSscIntentType type) {
        this.code = code;
        this.name = name;
        this.type = type;
    }


    /**
     * 通过name获取枚举
     *
     * @param name 名称
     * @return IntentRecognitionTypeItem
     */
    public static AiSscIntentItem getByName(String name) {
        AiSscIntentItem result = null;
        for (AiSscIntentItem item : values()) {
            if (item.getName().equals(name)) {
                result = item;
            }
        }
        if (result == null) {
            return TY;
        }
        return result;

    }

}
