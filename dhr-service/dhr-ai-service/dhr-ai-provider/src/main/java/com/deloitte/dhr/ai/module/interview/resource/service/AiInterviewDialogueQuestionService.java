package com.deloitte.dhr.ai.module.interview.resource.service;

import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewTestQuestionSaveRequest;
import com.deloitte.dhr.ai.module.interview.train.domain.AiInterviewDialogueQuestion;
import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewDialogueQuestionResponse;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * AI访谈-访谈任务-对话问题-相关业务接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface AiInterviewDialogueQuestionService extends SuperService<AiInterviewDialogueQuestion> {

    /**
     * 查询-AI陪练-对话问题信息
     *
     * @param resourceId 课程资源ID
     * @return List<AiInterviewDialogueQuestionResponse>
     */
    List<AiInterviewDialogueQuestionResponse> getByResourceId(Long resourceId);

    /**
     * 保存/更新-AI陪练-对话问题信息
     *
     * @param requestList AI陪练-对话问题-保存/更新信息
     * @param resourceId  课程资源ID
     * @return List<AiInterviewDialogueQuestion>
     */
    List<AiInterviewDialogueQuestion> saveData(List<AiInterviewTestQuestionSaveRequest> requestList, Long resourceId);

    /**
     * 删除-AI陪练-对话问题信息
     *
     * @param resourceId 课程资源ID
     */
    void deleteByResourceId(Long resourceId);

    /**
     * 保存AI生成问题语音信息
     *
     * @param questionList AI问题信息
     */
    void saveAiQuestionVoice(List<AiInterviewDialogueQuestion> questionList);

}
