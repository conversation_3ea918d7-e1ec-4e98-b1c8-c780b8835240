package com.deloitte.dhr.ai.module.ai.strategy.ssc.pojo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SSC-考勤销假-事务查询
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelLeaveTqResponse {

    @ApiModelProperty(value = "日期", name = "date")
    private String date;

    @ApiModelProperty(value = "时长", name = "duration")
    private Integer duration;

    @ApiModelProperty(value = "类型", name = "type")
    private String type;

    @ApiModelProperty(value = "审批状态", name = "approvalStatus")
    private String approvalStatus;

    @ApiModelProperty(value = "操作", name = "operation")
    private String operation;

    @ApiModelProperty(value = "交接人", name = "handoverPerson")
    private String handoverPerson;

    @ApiModelProperty(value = "交接人电话", name = "handoverPhone")
    private String handoverPhone;

    @ApiModelProperty(value = "请假原因", name = "leaveReason")
    private String leaveReason;

}