package com.deloitte.dhr.ai.module.chat.service;

import com.deloitte.dhr.ai.module.chat.domain.AiChatMsgContent;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatMsgContentSaveRequest;
import com.deloitte.dhr.common.SuperService;

import java.util.List;

/**
 * AI聊天-聊天消息内容-相关业务接口
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface AiChatMsgContentService extends SuperService<AiChatMsgContent> {

    /**
     * 批量保存消息内容
     *
     * @param contents 保存数据
     */
    void saveBatchData(List<AiChatMsgContentSaveRequest> contents);

}
