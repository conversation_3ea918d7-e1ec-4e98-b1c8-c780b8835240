package com.deloitte.dhr.ai.module.sr.pojo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 阿里云-一句话识别-请求
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
public class AliyunAsrRequest {

    /**
     * 应用appKey
     */
    @ApiModelProperty(value = "应用appKey")
    private String appkey;

    /**
     * 单声道（mono）16bit采样位数音频，包括无压缩的PCM、WAV、OPUS、AMR、SPEEX、MP3、AAC格式
     */
    @ApiModelProperty(value = "单声道（mono）16bit采样位数音频：PCM、WAV、OPUS、AMR、SPEEX、MP3、AAC")
    private String format;

    /**
     * 音频采样率：16000 Hz、8000 Hz。默认值：16000 Hz
     */
    @ApiModelProperty(value = "音频采样率：16000 Hz、8000 Hz。默认值：16000 Hz")
    private Integer sampleRate;

    /**
     * 添加热词表ID。默认：不添加
     */
    @ApiModelProperty(value = "添加热词表ID")
    private String vocabularyId;

    /**
     * 添加自学习模型ID。默认：不添加
     */
    @ApiModelProperty(value = "添加自学习模型ID")
    private String customizationId;

    /**
     * 是否在后处理中添加标点，默认值：False
     */
    @ApiModelProperty(value = "是否在后处理中添加标点")
    private Boolean enablePunctuationPrediction;

    /**
     * ITN（逆文本inverse text normalization）中文数字转换阿拉伯数字。设置为True时，中文数字将转为阿拉伯数字输出，默认值：False
     */
    @ApiModelProperty(value = "是否中文数字转换阿拉伯数字")
    private Boolean enableInverseTextNormalization;

    /**
     * 是否启动语音检测。开启后能够识别出一段音频中有效语音的开始和结束，剔除噪音数据。默认值：False
     */
    @ApiModelProperty(value = "是否启动语音检测")
    private Boolean enableVoiceDetection;

    /**
     * 过滤语气词，即声音顺滑，默认值：False
     */
    @ApiModelProperty(value = "过滤语气词")
    private Boolean disfluency;

    /**
     * 可通过公网访问的音频文件下载链接
     */
    @ApiModelProperty(value = "可通过公网访问的音频文件下载链接")
    private String audioAddress;

}
