package com.deloitte.dhr.ai.module.interview.train.service;

import com.deloitte.dhr.ai.module.interview.task.domain.AiInterviewTaskEmp;
import com.deloitte.dhr.ai.module.interview.task.domain.AiInterviewTaskInfo;
import com.deloitte.dhr.ai.module.interview.train.pojo.*;

import java.util.List;

/**
 * AI访谈-访谈任务-我的访谈-相关业务接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface AiMyInterviewService {

    /**
     * 发送消息
     *
     * @param request 消息内容
     * @return AiInterviewMySendResponse
     */
    AiInterviewMySendResponse sendMessage(AiInterviewMySendRequest request);

    /**
     * 发送消息
     *
     * @param id 消息ID
     * @return AiInterviewMySendResponse
     */
    AiInterviewMySendResponse skip(Long id);

    AiInterviewTaskMyReportResponse getMyReport(Long taskEmpId);

    void createReport(Long taskEmpId);
}
