package com.deloitte.dhr.ai.module.coach.train.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 课程资源-AI陪练-沟通框架表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_train_communicate_framework")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-AI陪练-沟通框架表")
public class AiTrainCommunicateFramework extends SuperLogicModel<AiTrainCommunicateFramework> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 沟通主题
     */
    @TableField(value = "communicate_subject")
    private String communicateSubject;

    /**
     * 沟通目标
     */
    @TableField(value = "communicate_objective")
    private String communicateObjective;

    /**
     * 评分标准
     */
    @TableField(value = "grading_criteria")
    private String gradingCriteria;

    /**
     * 评分百分比
     */
    @TableField(value = "weight")
    private BigDecimal weight;

}