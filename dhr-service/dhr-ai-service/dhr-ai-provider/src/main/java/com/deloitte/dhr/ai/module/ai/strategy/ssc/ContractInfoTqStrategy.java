package com.deloitte.dhr.ai.module.ai.strategy.ssc;

import com.deloitte.dhr.ai.enums.AiSscIntentAction;
import com.deloitte.dhr.ai.enums.AiSscIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.ActionStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * SSC-合同信息-事务查询-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@ActionStrategyType(item = AiSscIntentItem.HTXX, action = AiSscIntentAction.TQ)
public class ContractInfoTqStrategy implements SscStrategy {

    @Override
    public Flux<List<AiContent>> executeStream(Map<String, Object> param) {

        return Flux.just(new ArrayList<>());
    }

}
