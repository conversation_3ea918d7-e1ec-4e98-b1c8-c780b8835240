package com.deloitte.dhr.ai.enums;


import lombok.Getter;

/**
 * SSC意图识别行为枚举
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public enum AiSscIntentAction {

    TQ("transaction_query", "事务查询"),//数据查询
    TC("transaction_commit", "事务提交"),//业务办理
    IR("information_retrieval", "信息检索");//政策问询

    private final String code;
    private final String name;

    AiSscIntentAction(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 通过name获取枚举
     *
     * @param name 名称
     * @return AiSscIntentAction
     */
    public static AiSscIntentAction getByName(String name) {
        AiSscIntentAction result = null;
        for (AiSscIntentAction action : AiSscIntentAction.values()) {
            if (action.getName().equals(name)) {
                result = action;
            }
        }
        if (result == null) {
            return IR;
        }
        return result;
    }

}
