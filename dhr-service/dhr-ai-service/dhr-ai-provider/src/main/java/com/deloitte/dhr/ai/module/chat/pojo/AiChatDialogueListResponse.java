package com.deloitte.dhr.ai.module.chat.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 聊天对话-列表
 *
 * <AUTHOR>
 * @date 2024-03-27
 */
@Data
@ApiModel("AiChatDialogueListResponse")
public class AiChatDialogueListResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * AI分组：数字人-digital_human，文本聊天-text_chat
     */
    @ApiModelProperty(value = "AI分组：数字人-digital_human，文本聊天-text_chat", name = "aiGroup")
    private String aiGroup;

    /**
     * 对话标题
     */
    @ApiModelProperty(value = "对话标题", name = "dialogueTitle")
    private String dialogueTitle;

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间", name = "time")
    private Date time;

}