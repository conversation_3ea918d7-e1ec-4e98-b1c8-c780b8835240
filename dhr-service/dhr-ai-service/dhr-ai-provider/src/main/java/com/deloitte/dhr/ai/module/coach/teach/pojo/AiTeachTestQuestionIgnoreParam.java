package com.deloitte.dhr.ai.module.coach.teach.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

/**
 * 课程资源-教学课程-考核题目-忽略序列化参数
 * 前端要求把（选择题选项，文本题参考答案）都返回同一个字段，所以用Object返回，由于Object不好取值赋值，所以以下所有属性用于代码上下文传递，但是不用返回给前端
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
public class AiTeachTestQuestionIgnoreParam {

    /**
     * 选择题题目内容（选择题选项数据），不参与序列化，仅代码使用
     */
    @JsonIgnore
    private List<AiTeachChoiceOptionResponse> choiceQuestionContent;

    /**
     * 我的选择题题目内容（我的选择题选项数据），不参与序列化，仅代码使用
     */
    @JsonIgnore
    private List<AiTeachMyChoiceOptionResponse> myChoiceQuestionContent;

    /**
     * 文本题题目内容（文本题参考答案数据），不参与序列化，仅代码使用
     */
    @JsonIgnore
    private AiTeachTextSampleAnswerResponse textQuestionContent;

    /**
     * 我的文本题题目内容（我的文本题答案数据），不参与序列化，仅代码使用
     */
    @JsonIgnore
    private AiTeachMyTextSampleAnswerResponse myTextQuestionContent;

}