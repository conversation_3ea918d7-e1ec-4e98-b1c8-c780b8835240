package com.deloitte.dhr.ai.module.chat.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.chat.domain.AiChatMsg;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatMsgListRequest;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatMsgListResponse;
import com.deloitte.dhr.ai.module.chat.pojo.ChatHistoryRequest;
import com.deloitte.dhr.ai.module.chat.pojo.ChatHistoryResponse2;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI聊天-聊天-相关持久化接口
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface AiChatMsgMapper extends SuperMapper<AiChatMsg> {

    /**
     * 查询-聊天消息列表
     *
     * @param request 查询参数
     * @return List<AiChatMsgListResponse>
     */
    List<AiChatMsgListResponse> getChatList(@Param("request") AiChatMsgListRequest request);

    /**
     * 查询-聊天消息Id
     *
     * @param request 查询参数
     * @return List<Long>
     */
    List<Long> chatHistoryIds(Page<Long> page, @Param("request") ChatHistoryRequest request);
}