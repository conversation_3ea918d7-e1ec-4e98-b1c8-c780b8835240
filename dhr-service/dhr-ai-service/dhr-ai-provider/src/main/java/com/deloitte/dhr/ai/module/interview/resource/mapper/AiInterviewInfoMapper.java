package com.deloitte.dhr.ai.module.interview.resource.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.interview.resource.domain.AiInterviewInfo;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewInfoResponse;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewListRequest;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI访谈-主要信息-相关持久化接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface AiInterviewInfoMapper extends SuperMapper<AiInterviewInfo> {

    /**
     * 分页查询数据
     *
     * @param page  分页参数
     * @param param 过滤条件
     * @param order 排序条件
     * @return 列表数据
     */
    List<AiInterviewInfoResponse> findListPage(Page<AiInterviewInfo> page, @Param(value = "param") AiInterviewListRequest param, @Param(value = "order") String order);

    /**
     * 统计被引用的课程类别数
     *
     * @param categoryIds 类别ID
     * @return Long
     */
    Long countByCategoryIds(@Param("categoryIds") List<Long> categoryIds);

}