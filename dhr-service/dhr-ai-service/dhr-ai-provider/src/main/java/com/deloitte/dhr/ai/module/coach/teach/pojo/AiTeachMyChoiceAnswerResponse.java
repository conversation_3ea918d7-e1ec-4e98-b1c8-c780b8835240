package com.deloitte.dhr.ai.module.coach.teach.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 课程资源-教学课程-我的选择问题答案
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachMyChoiceAnswerResponse")
public class AiTeachMyChoiceAnswerResponse {

    /**
     * 考核题目ID
     */
    @ApiModelProperty(value = "考核题目ID", name = "questionId")
    private Long questionId;

    /**
     * 所选题目选项ID
     */
    @ApiModelProperty(value = "所选题目选项ID", name = "optionIds")
    private List<Long> optionIds;

    /**
     * 是否正确
     */
    @ApiModelProperty(value = "是否正确", name = "isRight")
    private Boolean isRight;

}