package com.deloitte.dhr.ai.module.coach.category.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.module.coach.category.domain.AiCourseCategory;
import com.deloitte.dhr.ai.module.coach.category.mapper.AiCourseCategoryMapper;
import com.deloitte.dhr.ai.module.coach.category.pojo.AiCourseCategoryResponse;
import com.deloitte.dhr.ai.module.coach.category.pojo.AiCourseCategorySaveRequest;
import com.deloitte.dhr.ai.module.coach.category.pojo.AiCourseCategoryTreeResponse;
import com.deloitte.dhr.ai.module.coach.category.service.AiCourseCategoryService;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.resource.mapper.AiCourseResourceInfoMapper;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 课程类别-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiCourseCategoryServiceImpl extends SuperServiceImpl<AiCourseCategoryMapper, AiCourseCategory> implements AiCourseCategoryService {

    @Autowired
    private AiCourseCategoryMapper aiCourseCategoryMapper;
    @Autowired
    private AiCourseResourceInfoMapper aiCourseResourceInfoMapper;

    @Override
    public AiCourseCategoryResponse getDetail(Long id) {
        return BeanUtil.copyProperties(this.get(id), AiCourseCategoryResponse.class);
    }

    @Override
    public List<AiCourseCategoryTreeResponse> getTree(String categoryName) {
        LambdaQueryWrapper<AiCourseCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(categoryName), AiCourseCategory::getCategoryName, categoryName);
        List<AiCourseCategory> list = aiCourseCategoryMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<AiCourseCategoryTreeResponse> responseList = BeanUtil.copyToList(list, AiCourseCategoryTreeResponse.class);
        Map<Long, List<AiCourseCategoryTreeResponse>> map = responseList.stream().collect(Collectors.groupingBy(AiCourseCategoryTreeResponse::getParentId));
        Set<Long> ids = responseList.stream().map(AiCourseCategoryTreeResponse::getId).collect(Collectors.toSet());
        List<AiCourseCategoryTreeResponse> rootList = responseList.stream().filter(response -> !ids.contains(response.getParentId())).collect(Collectors.toList());
        // 组装树
        rootList.forEach(rootResponse -> rootResponse.setChildren(getChildren(rootResponse, map, 1)));
        return rootList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveData(AiCourseCategorySaveRequest request) {
        AiCourseCategory aiCourseCategory = BeanUtil.copyProperties(request, AiCourseCategory.class);
        if (!this.saveOrUpdate(aiCourseCategory)) {
            return aiCourseCategory.getId();
        }
        String categoryPath = aiCourseCategory.getParentId() == 0L ? aiCourseCategory.getId() + "" : aiCourseCategoryMapper.getCategoryPathById(aiCourseCategory.getParentId()) + "," + aiCourseCategory.getId();
        // 更新"类别全路径"字段
        aiCourseCategory.setCategoryPath(categoryPath);
        this.update(aiCourseCategory);
        return aiCourseCategory.getId();
    }

    @Override
    public Boolean deleteById(Long id) {

        // 获取当前节点以及所有下级节点
        List<Long> ids = aiCourseCategoryMapper.getCurrentAndAllChildId(id);
        if (CollUtil.isEmpty(ids)) {
            return true;
        }

        // 校验类别是否被引用，被引用了则不能删除
        Long count = aiCourseResourceInfoMapper.countByCategoryIds(ids);
        if (count > 0) {
            throw new CommRunException("该类别或下级类别已被课程资源引用，无法删除");
        }
        return this.removeByIds(ids);
    }

    @Override
    public String getCategoryPathNameById(Long id) {
        AiCourseCategory courseCategory = aiCourseCategoryMapper.selectById(id);
        if (courseCategory == null) {
            return null;
        }
        List<String> ids = StrUtil.split(courseCategory.getCategoryPath(), ",");
        List<AiCourseCategory> list = aiCourseCategoryMapper.selectBatchIds(ids);
        List<String> nameList = list.stream().map(AiCourseCategory::getCategoryName).collect(Collectors.toList());
        return StrUtil.join("/", nameList);
    }

    /**
     * 获取子节点
     *
     * @param rootResponse 跟节点
     * @param map          总数据
     * @param deep         递归深度
     * @return List<AiCourseCategoryTreeResponse>
     */
    private List<AiCourseCategoryTreeResponse> getChildren(AiCourseCategoryTreeResponse rootResponse, Map<Long, List<AiCourseCategoryTreeResponse>> map, Integer deep) {
        List<AiCourseCategoryTreeResponse> childList = map.get(rootResponse.getId());
        if (childList == null || AiCoachConstant.MAX_DEEP.equals(deep)) {
            return null;
        }
        deep++;
        for (AiCourseCategoryTreeResponse child : childList) {
            child.setChildren(getChildren(child, map, deep));
        }
        return childList;
    }

}

