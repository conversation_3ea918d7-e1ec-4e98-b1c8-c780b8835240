package com.deloitte.dhr.ai.module.coach.train.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainObject;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainObjectTag;
import com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainObjectMapper;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainObjectResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainObjectSaveRequest;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainObjectService;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainObjectTagService;
import com.deloitte.dhr.common.SuperServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 课程资源-AI陪练-对象-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiTrainObjectServiceImpl extends SuperServiceImpl<AiTrainObjectMapper, AiTrainObject> implements AiTrainObjectService {

    @Autowired
    private AiTrainObjectMapper aiTrainObjectMapper;
    @Autowired
    private AiTrainObjectTagService aiTrainObjectTagService;

    @Override
    public AiTrainObjectResponse getByResourceId(Long resourceId) {

        // 查询AI陪练对象信息
        LambdaQueryWrapper<AiTrainObject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrainObject::getResourceId, resourceId);
        AiTrainObject aiTrainObject = aiTrainObjectMapper.selectOne(queryWrapper);
        AiTrainObjectResponse response = BeanUtil.copyProperties(aiTrainObject, AiTrainObjectResponse.class);

        // 查询AI陪练对象标签信息
        LambdaQueryWrapper<AiTrainObjectTag> tagQueryWrapper = new LambdaQueryWrapper<>();
        tagQueryWrapper.eq(AiTrainObjectTag::getResourceId, resourceId);
        List<AiTrainObjectTag> tagList = aiTrainObjectTagService.list(tagQueryWrapper);
        List<String> tagNameList = tagList.stream().map(AiTrainObjectTag::getTagName).distinct().collect(Collectors.toList());
        response.setTagInfoList(tagNameList);

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveData(AiTrainObjectSaveRequest request, Long resourceId) {
        // 保存/更新-培训对象信息
        LambdaQueryWrapper<AiTrainObject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrainObject::getResourceId, resourceId);
        AiTrainObject aiTrainObject = aiTrainObjectMapper.selectOne(queryWrapper);
        if (aiTrainObject == null) {
            aiTrainObject = BeanUtil.copyProperties(request, AiTrainObject.class);
        } else {
            BeanUtil.copyProperties(request, aiTrainObject);
        }
        aiTrainObject.setResourceId(resourceId);
        this.saveOrUpdate(aiTrainObject);

        // 保存/更新-培训对象标签信息
        aiTrainObjectTagService.saveData(request.getObjectBackgroundInfo(), resourceId, aiTrainObject.getId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByResourceId(Long resourceId) {
        // 删除 AI陪练-对象基本数据
        LambdaQueryWrapper<AiTrainObject> objectQueryWrapper = new LambdaQueryWrapper<>();
        objectQueryWrapper.eq(AiTrainObject::getResourceId, resourceId);
        aiTrainObjectMapper.delete(objectQueryWrapper);

        // 删除AI陪练-对象标签数据
        aiTrainObjectTagService.deleteByResourceId(resourceId);
    }
}

