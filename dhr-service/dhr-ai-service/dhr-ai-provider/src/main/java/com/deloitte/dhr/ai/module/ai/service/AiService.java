package com.deloitte.dhr.ai.module.ai.service;

import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.module.ai.pojo.AiResponse;
import com.deloitte.dhr.ai.module.ai.pojo.AiSubmitRequest;
import com.deloitte.dhr.common.base.pojo.UserDto;
import reactor.core.publisher.Flux;

/**
 * AI-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiService {

    /**
     * chat（需要保存相关聊天信息）
     *
     * @param request 请求
     * @return Flux<AiResponse
     */
    Flux<AiResponse> chat(AiRequest request);

    /**
     * work（不需要保存相关聊天信息）
     *
     * @param request 请求
     * @return Flux<AiResponse>
     */
    Flux<AiResponse> work(AiRequest request, UserDto userDto);

    /**
     * AI 提交
     *
     * @param request 请求
     * @return Boolean
     */
    Boolean submit(AiSubmitRequest request);

}
