package com.deloitte.dhr.ai.module.sr.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nls.client.AccessToken;
import com.deloitte.dhr.ai.config.AliyunSrConfig;
import com.deloitte.dhr.ai.enums.AliyunSrTypeEnum;
import com.deloitte.dhr.ai.module.sr.pojo.AliyunAsrRequest;
import com.deloitte.dhr.ai.module.sr.pojo.AliyunAsrResponse;
import com.deloitte.dhr.ai.module.sr.pojo.AliyunFlashRecognizerRequest;
import com.deloitte.dhr.ai.module.sr.pojo.AliyunFlashRecognizerResponse;
import com.deloitte.dhr.ai.module.sr.service.AliyunSrService;
import com.deloitte.dhr.common.base.exception.CommRunException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.util.Map;

/**
 * 阿里云语音识别-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Slf4j
@Service
public class AliyunSrServiceImpl implements AliyunSrService {

    /**
     * 调用成功状态码
     */
    public final static Integer SUCCESS_CODE = 20000000;

    @Autowired
    private AliyunSrConfig aliyunSrConfig;

    @Override
    public String asr(AliyunAsrRequest request) {
        request.setAppkey(aliyunSrConfig.getAppKey());
        Map<String, Object> paramMap = BeanUtil.beanToMap(request, true, true);
        try {
            AliyunAsrResponse response = JSON.parseObject(invoke(paramMap, AliyunSrTypeEnum.ASR), AliyunAsrResponse.class);
            if (!SUCCESS_CODE.equals(response.getStatus())) {
                log.error("{} 调用阿里云{}接口失败，错误信息为：{}", DateTime.now(), AliyunSrTypeEnum.ASR.getDesc(), response.getMessage());
                throw new CommRunException(response.getMessage());
            }
            return response.getResult();
        } catch (Exception e) {
            log.error("{} 调用阿里云{}接口出错，错误信息为：{}", DateTime.now(), AliyunSrTypeEnum.ASR.getDesc(), e.getMessage());
            throw new CommRunException(e.getMessage());
        }
    }

    @Override
    public String flashRecognizer(AliyunFlashRecognizerRequest request) {
        request.setAppkey(aliyunSrConfig.getAppKey());
        request.setToken(getAccessToken());
        Map<String, Object> paramMap = BeanUtil.beanToMap(request, true, true);
        try {
            AliyunFlashRecognizerResponse response = JSON.parseObject(invoke(paramMap, AliyunSrTypeEnum.FLASH_RECOGNIZER), AliyunFlashRecognizerResponse.class);
            if (!SUCCESS_CODE.equals(response.getStatus())) {
                log.error("{} 调用阿里云{}接口失败，错误信息为：{}", DateTime.now(), AliyunSrTypeEnum.FLASH_RECOGNIZER.getDesc(), response.getMessage());
                throw new CommRunException(response.getMessage());
            }
            if (response.getFlashResult() == null || CollUtil.isEmpty(response.getFlashResult().getSentences())) {
                return null;
            }
            StringBuilder sentence = new StringBuilder();
            response.getFlashResult().getSentences().stream().map(AliyunFlashRecognizerResponse.Sentence::getText).forEach(sentence::append);
            return sentence.toString();
        } catch (Exception e) {
            log.error("{} 调用阿里云{}接口出错，错误信息为：{}", DateTime.now(), AliyunSrTypeEnum.FLASH_RECOGNIZER.getDesc(), e.getMessage());
            throw new CommRunException(e.getMessage());
        }
    }

    /**
     * 调用阿里云语音识别接口
     *
     * @param paramMap 入参
     * @param type     类型
     * @return AliyunAsrResponse
     */
    public String invoke(Map<String, Object> paramMap, AliyunSrTypeEnum type) {
        String parmaStr = URLUtil.buildQuery(paramMap, Charset.defaultCharset());
        String result = null, url = null;
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("{} 调用阿里云{}接口开始，param：{}", DateTime.now(), type.getDesc(), parmaStr);
        switch (type) {
            case ASR:
                url = aliyunSrConfig.getUrl().getAsrUrl() + "?" + parmaStr;
                result = HttpUtil.createPost(url).header("X-NLS-Token", getAccessToken()).execute().body();
                break;
            case FLASH_RECOGNIZER:
                url = aliyunSrConfig.getUrl().getFlashRecognizerUrl() + "?" + parmaStr;
                result = HttpUtil.createPost(url).execute().body();
                break;
            default:
                break;
        }
        stopWatch.stop();
        log.info("{} 调用阿里云{}接口结束，url：{}，result：{}，耗时：{}", DateTime.now(), type.getDesc(), url, result, stopWatch.getTotalTimeSeconds());
        return result;
    }

    /**
     * 获取accessToken
     *
     * @return accessToken
     */
    private String getAccessToken() {
        try {
            AccessToken accessToken = new AccessToken(aliyunSrConfig.getAccessKeyId(), aliyunSrConfig.getAccessKeySecret());
            accessToken.apply();
            return accessToken.getToken();
        } catch (Exception e) {
            log.error("阿里云语音识别生成token失败");
            throw new CommRunException(e.getMessage());
        }
    }

}
