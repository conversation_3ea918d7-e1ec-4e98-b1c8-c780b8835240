package com.deloitte.dhr.ai.module.ai.strategy.digital.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 数字人(李宁)-证明申请-事务查询
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("DigitalLiningProofApplyTqResponse")
public class DigitalLiningProofApplyTqResponse {

    @ApiModelProperty(value = "证明类型", name = "proofType")
    private String proofType;

    @ApiModelProperty(value = "证明名称", name = "proofName")
    private String proofName;

    @ApiModelProperty(value = "申请时间", name = "applyTime")
    private Date applyTime;

    @ApiModelProperty(value = "申请状态", name = "applyStatus")
    private String applyStatus;

    @ApiModelProperty(value = "证明文件ID", name = "proofFileId")
    private String proofFileId;

}
