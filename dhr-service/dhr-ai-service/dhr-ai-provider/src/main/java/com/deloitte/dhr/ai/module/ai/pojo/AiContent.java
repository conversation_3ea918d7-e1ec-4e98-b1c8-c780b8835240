package com.deloitte.dhr.ai.module.ai.pojo;

import com.deloitte.dhr.ai.enums.AiContentType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * AI消息内容
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiContent {

    @ApiModelProperty(value = "消息内容", name = "content")
    private String content;

    /**
     * {@link AiContentType}
     */
    @ApiModelProperty(value = "消息内容类型", name = "contentType")
    private String contentType;

    @ApiModelProperty(value = "额外字段", name = "ext")
    private Map<String, Object> ext;

}
