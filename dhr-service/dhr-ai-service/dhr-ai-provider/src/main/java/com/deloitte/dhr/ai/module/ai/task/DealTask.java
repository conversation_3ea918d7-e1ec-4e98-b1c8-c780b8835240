package com.deloitte.dhr.ai.module.ai.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.deloitte.dhr.ai.module.chat.domain.AiChatMsgContent;
import com.deloitte.dhr.ai.module.chat.domain.AiPicDeal;
import com.deloitte.dhr.ai.module.chat.mapper.AiChatMsgContentMapper;
import com.deloitte.dhr.ai.module.chat.mapper.AiPicDealMapper;
import com.deloitte.dhr.common.base.utils.date.DateStyle;
import com.deloitte.dhr.utility.api.FileUtilClient;
import com.deloitte.dhr.utility.api.dto.file.FileResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DealTask {

    @Autowired
    private AiPicDealMapper aiPicDealMapper;
    @Autowired
    private AiChatMsgContentMapper aiChatMsgContentMapper;
    @Resource
    FileUtilClient fileUtilClient;

    /**
     * 每30分钟执行一次的定时任务，处理豆包生成的图片
     */
    @Scheduled(fixedRate = 1800000) // 30分钟 = 18000000毫秒
    public void dealAiMsg() {
        // 限制查询结果数量，避免一次性处理过多数据
        List<AiPicDeal> list = aiPicDealMapper.selectList(new LambdaQueryWrapper<AiPicDeal>()
                .eq(AiPicDeal::getStatus,1)
                .le(AiPicDeal::getNum, 3)
                .last("LIMIT 100"));

        if(list.isEmpty()){
            return ;
        }
        //修改列表状态为处理中
        List<Long> ids = list.stream().map(AiPicDeal::getId).collect(Collectors.toList());
        aiPicDealMapper.update(null, new LambdaUpdateWrapper<AiPicDeal>()
                .in(AiPicDeal::getId, ids)
                .set(AiPicDeal::getStatus, 2));

        for (AiPicDeal aiPicDeal : list) {
            String picUrl = dealPic(aiPicDeal.getPic1());
            boolean dealStatus = false;
            if (!picUrl.isEmpty()) {
                dealStatus = true;
                aiPicDeal.setStatus(3);
                aiPicDeal.setDealTime(DateUtil.format(DateUtil.date(), DateStyle.YYYY_MM_DD_HH_MM_SS.getValue()));
                aiPicDeal.setNum(aiPicDeal.getNum() + 1);
                aiPicDeal.setPic2(picUrl);
            } else {
                // 如果处理超过3次，不再重复处理
                if (aiPicDeal.getNum() >= 3) {
                    aiPicDeal.setStatus(4);
                } else {
                    aiPicDeal.setStatus(1);
                    aiPicDeal.setNum(aiPicDeal.getNum() + 1);
                }
            }
            aiPicDealMapper.updateById(aiPicDeal);

            if (dealStatus && "aiMsg".equals(aiPicDeal.getObjType())){
                List<AiChatMsgContent> list1 = aiChatMsgContentMapper.selectList(new LambdaQueryWrapper<AiChatMsgContent>()
                        .eq(AiChatMsgContent::getMsgId, aiPicDeal.getObjId()));
                List<AiChatMsgContent> list2 = list1.stream().filter(a -> "IMAGE".equals(a.getContentType())).collect(Collectors.toList());
                for (AiChatMsgContent aiChatMsgContent : list2){
                    aiChatMsgContent.setContent(picUrl);
                    aiChatMsgContentMapper.updateById(aiChatMsgContent);
                }

            }
        }
    }

    public String dealPic(String picUrl){
        if (picUrl == null) {
            return "";
        }
        // 下载网络图片并上传
        String tmpdir = System.getProperty("java.io.tmpdir");
        String fileName = IdUtil.simpleUUID() + ".jpg";
        File tempFile = new File(tmpdir, fileName);

        String downloadFilePath = tempFile.getAbsolutePath();
        log.info(StrUtil.format("图片保存路径：{}", downloadFilePath));
        try {
            //  上传文件
            FileUtils.copyURLToFile(new URL(picUrl), new File(downloadFilePath));
            FileResponseDto fileResponse = fileUtilClient.uploadFile(tempFile);

            //删除本地文件
            FileUtils.deleteQuietly(tempFile);
            return fileResponse.getPreviewUrl();
        } catch (IOException e) {
            log.error("图片下载失败", e);
        }
        return "";
    }
}
