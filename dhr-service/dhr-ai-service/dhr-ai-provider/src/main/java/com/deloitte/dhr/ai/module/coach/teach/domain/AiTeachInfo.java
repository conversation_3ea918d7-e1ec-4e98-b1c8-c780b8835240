package com.deloitte.dhr.ai.module.coach.teach.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 课程资源-教学课程-基本信息表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_teach_info")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-教学课程-基本信息表")
public class AiTeachInfo extends SuperLogicModel<AiTeachInfo> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 课程目标
     */
    @TableField(value = "course_objectives")
    private String courseObjectives;

    /**
     * 课程设计要求
     */
    @TableField(value = "course_design_requirements")
    private String courseDesignRequirements;

    /**
     * 考察知识点
     */
    @TableField(value = "examining_knowledge_points")
    private String examiningKnowledgePoints;

    /**
     * 单选题数量
     */
    @TableField(value = "single_choice_question_num")
    private Integer singleChoiceQuestionNum;

    /**
     * 判断题数量
     */
    @TableField(value = "judging_question_num")
    private Integer judgingQuestionNum;

    /**
     * 问答题数量
     */
    @TableField(value = "essay_question_num")
    private Integer essayQuestionNum;

    /**
     * 选择题正确率
     */
    @TableField(value = "choice_question_accuracy")
    private BigDecimal choiceQuestionAccuracy;

    /**
     * 问答题匹配度
     */
    @TableField(value = "text_question_matching_degree")
    private BigDecimal textQuestionMatchingDegree;

}