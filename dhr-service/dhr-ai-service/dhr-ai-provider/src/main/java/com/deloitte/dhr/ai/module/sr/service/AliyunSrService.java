package com.deloitte.dhr.ai.module.sr.service;


import com.deloitte.dhr.ai.module.sr.pojo.AliyunAsrRequest;
import com.deloitte.dhr.ai.module.sr.pojo.AliyunFlashRecognizerRequest;

/**
 * 阿里云语音识别-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AliyunSrService {

    /**
     * 一句话识别
     *
     * @param request 识别相关信息
     * @return 识别结果
     */
    String asr(AliyunAsrRequest request);

    /**
     * 录音文件识别极速版
     *
     * @param request 识别相关信息
     * @return 识别结果
     */
    String flashRecognizer(AliyunFlashRecognizerRequest request);

}
