package com.deloitte.dhr.ai.module.coach.train.service;

import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainObject;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainObjectResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainObjectSaveRequest;
import com.deloitte.dhr.common.SuperService;

/**
 * 课程资源-AI陪练-对象-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTrainObjectService extends SuperService<AiTrainObject> {

    /**
     * 查询-AI陪练-对象信息
     *
     * @param resourceId 课程资源ID
     * @return AiTrainObjectDetailResponse
     */
    AiTrainObjectResponse getByResourceId(Long resourceId);

    /**
     * 保存/更新-AI陪练-对象信息
     *
     * @param request    AI陪练-对象-保存/更新信息
     * @param resourceId 课程资源ID
     * @return Boolean
     */
    Boolean saveData(AiTrainObjectSaveRequest request, Long resourceId);

    /**
     * 删除-AI陪练-对象信息
     *
     * @param resourceId 课程资源ID
     */
    void deleteByResourceId(Long resourceId);

}
