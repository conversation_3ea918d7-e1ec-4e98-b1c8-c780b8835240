package com.deloitte.dhr.ai.module.ai.strategy.digital;


import com.deloitte.dhr.ai.enums.DigitalLiningIntentAction;
import com.deloitte.dhr.ai.enums.DigitalLiningIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.DigitalLiningActionStrategyType;
import org.springframework.stereotype.Service;

/**
 * 数字人(李宁)-证明申请-事务提交-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
@DigitalLiningActionStrategyType(item = DigitalLiningIntentItem.ZMXX, action = DigitalLiningIntentAction.TC)
public class DigitalLiningProofApplyTcStrategy extends DigitalLiningProofApplyTqStrategy {

}
