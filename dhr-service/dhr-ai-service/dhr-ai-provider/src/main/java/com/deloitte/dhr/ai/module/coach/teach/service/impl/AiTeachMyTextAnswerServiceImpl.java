package com.deloitte.dhr.ai.module.coach.teach.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.enums.AiSubType;
import com.deloitte.dhr.ai.enums.AiType;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.module.ai.pojo.AiTeachQuestionEvaluateRequest;
import com.deloitte.dhr.ai.module.ai.service.AiService;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachMyTextAnswer;
import com.deloitte.dhr.ai.module.coach.teach.mapper.AiTeachMyTextAnswerMapper;
import com.deloitte.dhr.ai.module.coach.teach.mapper.AiTeachTestQuestionMapper;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachMyCoachQuestionSaveRequest;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachMyTextAnswerResponse;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachTextQuestionResponse;
import com.deloitte.dhr.ai.module.coach.teach.service.AiTeachMyTextAnswerService;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 课程资源-教学课程-我的文本问题答案-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
@Slf4j
public class AiTeachMyTextAnswerServiceImpl extends SuperServiceImpl<AiTeachMyTextAnswerMapper, AiTeachMyTextAnswer> implements AiTeachMyTextAnswerService {

    @Autowired
    private AiTeachMyTextAnswerMapper aiTeachMyTextAnswerMapper;
    @Autowired
    private AiTeachTestQuestionMapper aiTeachTestQuestionMapper;
    @Autowired
    private AiService aiService;

    @Override
    public void deleteByTaskEmpId(Long taskEmpId) {
        LambdaQueryWrapper<AiTeachMyTextAnswer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTeachMyTextAnswer::getTaskEmpId, taskEmpId);
        this.remove(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<Long, BigDecimal> saveData(List<AiTeachMyCoachQuestionSaveRequest> requestList, Long resourceId, Long taskId, Long taskEmpId) {

        // 删除旧的题目答案信息
        this.deleteByTaskEmpId(taskEmpId);

        // 保存新的的题目答案信息
        requestList = requestList.stream().filter(question -> StrUtil.equals(question.getQuestionType(), AiCoachConstant.TeachQuestionType.ESSAY_QUESTION)).collect(Collectors.toList());
        if (CollUtil.isEmpty(requestList)) {
            return Collections.emptyMap();
        }
        Map<Long, BigDecimal> result = new HashMap<>(AiCoachConstant.LIST_INIT_SIZE);
        List<AiTeachMyTextAnswer> saveList = new ArrayList<>();

        // 查询文本题信息
        List<AiTeachTextQuestionResponse> textQuestionList = aiTeachTestQuestionMapper.getTextQuestionByIds(requestList.stream().map(AiTeachMyCoachQuestionSaveRequest::getQuestionId).collect(Collectors.toList()));
        textQuestionList.forEach(a -> a.setAiQuestionId(StrUtil.isNotBlank(a.getAiQuestionId()) ? a.getAiQuestionId() : a.getQuestionId() + ""));
        Map<Long, AiTeachTextQuestionResponse> textQuestionMap = textQuestionList.stream().collect(Collectors.toMap(AiTeachTextQuestionResponse::getQuestionId, v -> v, (k1, k2) -> k1));

        // 获取文本题答案匹配度
        Map<Long, BigDecimal> aiModelMap = this.getAiMatchingDegree(requestList, textQuestionMap);

        requestList.forEach(question -> {
            AiTeachTextQuestionResponse textQuestionResponse = textQuestionMap.get(question.getQuestionId());
            if (textQuestionResponse == null) {
                return;
            }
            BigDecimal aiMatchingDegree = aiModelMap.get(textQuestionResponse.getQuestionId());
            AiTeachMyTextAnswer answer = new AiTeachMyTextAnswer(taskEmpId, question.getQuestionId(), question.getQuestionAnswer(), aiMatchingDegree);
            saveList.add(answer);
            result.put(question.getQuestionId(), aiMatchingDegree);
        });
        this.saveBatch(saveList);
        return result;
    }

    @Override
    public List<AiTeachMyTextAnswerResponse> getByTaskEmpId(Long taskEmpId) {
        LambdaQueryWrapper<AiTeachMyTextAnswer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTeachMyTextAnswer::getTaskEmpId, taskEmpId);
        List<AiTeachMyTextAnswer> list = aiTeachMyTextAnswerMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(list, AiTeachMyTextAnswerResponse.class);
    }

    /**
     * 获取文本题答案匹配度
     *
     * @param requestList     我的答案信息
     * @param textQuestionMap 文本题信息
     * @return Map<Long, BigDecimal>
     */
    private Map<Long, BigDecimal> getAiMatchingDegree(List<AiTeachMyCoachQuestionSaveRequest> requestList, Map<Long, AiTeachTextQuestionResponse> textQuestionMap) {


        List<AiTeachQuestionEvaluateRequest> evaluateRequestList = new ArrayList<>();
        requestList.forEach(question -> {
            AiTeachTextQuestionResponse textQuestionResponse = textQuestionMap.get(question.getQuestionId());
            if (textQuestionResponse == null) {
                return;
            }
            AiTeachQuestionEvaluateRequest request = AiTeachQuestionEvaluateRequest.builder()
                    .questionId(question.getQuestionId())
                    .question(textQuestionResponse.getQuestionName())
                    .sampleAnswer(textQuestionResponse.getSampleAnswerContent())
                    .userAnswer(question.getQuestionAnswer())
                    .build();
            evaluateRequestList.add(request);
        });

        // 构建AI请求入参
        AiRequest aiRequest = AiRequest.builder()
                .aiType(AiType.COACH.getCode())
                .aiSubType(AiSubType.TEACH_QUESTION_EVALUATE_BATCH.getCode())
                .userId(LoginUtil.getLoginUserCode())
                .contents(List.of(AiContent.builder()
                        .contentType(AiContentType.JSON.getCode())
                        .content(JSON.toJSONString(Map.of("questionList", evaluateRequestList)))
                        .build()))
                .stream(false)
                .build();

        Map<Long, BigDecimal> map = new HashMap<>();
        evaluateRequestList.forEach(request -> map.put(request.getQuestionId(), BigDecimal.ZERO));
        List<JSONObject> scoreMapList = aiService.work(aiRequest, LoginUtil.getLoginUser())
                .filter(raw -> CollUtil.isNotEmpty(raw.getContents()))
                .map(raw -> {
                    String content = raw.getContents().get(0).getContent();
                    return JSON.parseObject(content);
                })
                .collectList()
                .block();
        if (CollUtil.isEmpty(scoreMapList)) {
            return map;
        }
        scoreMapList.forEach(item -> map.put(item.getLong("questionId"), item.getBigDecimal("score")));
        return map;
    }

}

