package com.deloitte.dhr.ai.module.interview.resource.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AI访谈-访谈任务-对象信息
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewObjectResponse")
public class AiInterviewObjectResponse {

    /**
     * 访谈对象ID
     */
    @ApiModelProperty(value = "访谈对象ID", name = "id")
    private Long id;

    /**
     * 课程资源ID
     */
    @ApiModelProperty(value = "课程资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * 访谈对象头像类型
     */
    @ApiModelProperty(value = "访谈对象头像类型", name = "objectAvatarType")
    private String objectAvatarType;
    
    /**
     * 访谈对象头像URL
     */
    @ApiModelProperty(value = "访谈对象头像URL", name = "objectAvatarUrl")
    private String objectAvatarUrl;

    /**
     * 访谈对象名称
     */
    @ApiModelProperty(value = "访谈对象名称", name = "objectName")
    private String objectName;

    /**
     * 访谈对象背景信息
     */
    @ApiModelProperty(value = "访谈对象背景信息", name = "objectBackgroundInfo")
    private String objectBackgroundInfo;

    /**
     * 访谈对象性别;male：男，female：女，unknown：未知
     */
    @ApiModelProperty(value = "访谈对象性别;male：男，female：女，unknown：未知", name = "objectGender")
    private String objectGender;


    /**
     * 访谈名称
     */
    @ApiModelProperty(value = "访谈名称", name = "courseName")
    private String courseName;
}