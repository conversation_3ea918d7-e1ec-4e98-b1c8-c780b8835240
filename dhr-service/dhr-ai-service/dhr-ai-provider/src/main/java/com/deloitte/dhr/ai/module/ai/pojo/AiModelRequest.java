package com.deloitte.dhr.ai.module.ai.pojo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.deloitte.dhr.ai.enums.AiAgentType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * DIFY AI
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiModelRequest {

    private Map<String, Object> inputs;

    private String query;

    private Boolean isSearch;

    @JSONField(name = "conversation_id")
    private String conversationId;

    @JSONField(name = "response_mode")
    private String responseMode;

    private String user;

    private List<AiModelFileRequest> files;

    @JsonIgnore
    private AiAgentType type;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class AiModelFileRequest {

        /**
         * 方式：remote_url(pdf/图片)，local_file（文件）
         */
        @JSONField(name = "transfer_method", alternateNames = "transferMethod")
        private String transferMethod;

        /**
         * 类型：document｜image｜audio｜video｜custom
         */
        private String type;

        /**
         * 文件链接（当transferMethod为remote_url时传值）
         */
        private String url;

        /**
         * 文件ID（当transferMethod为local_file时传值）
         */
        @JSONField(name = "upload_file_id", alternateNames = "uploadFileId")
        private String uploadFileId;

    }

}
