package com.deloitte.dhr.ai.module.ai.controller;


import com.alibaba.fastjson2.JSON;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.module.ai.pojo.AiSubmitRequest;
import com.deloitte.dhr.ai.module.ai.service.AiService;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

/**
 * AI相关接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/ai")
@Api(tags = "AI")
@Validated
public class AiController {

    @Autowired
    private AiService aiService;

    @ApiOperation(value = "chat", notes = "chat")
    @ApiOperationSupport(order = 1)
    @PostMapping(value = "/chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> chat(@Validated @RequestBody AiRequest request) {
        return aiService.chat(request).map(JSON::toJSONString);
    }

    @ApiOperation(value = "work", notes = "work")
    @ApiOperationSupport(order = 2)
    @PostMapping(value = "/work", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> work(@Validated @RequestBody AiRequest request) {
        return aiService.work(request, LoginUtil.getLoginUser()).map(JSON::toJSONString);
    }

    @ApiOperation(value = "submit", notes = "submit")
    @ApiOperationSupport(order = 3)
    @PostMapping(value = "/submit")
    public ResponseVO<Boolean> submit(@Validated @RequestBody AiSubmitRequest request) {
        return ResponseVO.success(aiService.submit(request));
    }

}
