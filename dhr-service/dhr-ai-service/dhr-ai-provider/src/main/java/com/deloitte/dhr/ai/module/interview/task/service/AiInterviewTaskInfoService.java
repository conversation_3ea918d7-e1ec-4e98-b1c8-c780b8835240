package com.deloitte.dhr.ai.module.interview.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.interview.task.domain.AiInterviewTaskInfo;
import com.deloitte.dhr.ai.module.interview.task.pojo.*;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperService;

/**
 * AI访谈-访谈任务-相关业务接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface AiInterviewTaskInfoService extends SuperService<AiInterviewTaskInfo> {

    /**
     * 详情-AI访谈任务
     *
     * @param id AI访谈任务-主键ID
     * @return AiInterviewTaskDetailResponse
     */
    AiInterviewTaskDetailResponse getInterviewTaskDetail(Long id);

    /**
     * 分页-任务列表
     *
     * @param page    分页参数
     * @param request 过滤条件
     * @param order   排序条件
     * @return ResponsePage<AiTaskDetailResponse>
     */
    ResponsePage<AiInterviewTaskResponse> findTaskListPage(Page<AiInterviewTaskInfo> page, AiInterviewTaskListRequest request, BaseOrder order);

    /**
     * 分页-我的陪练任务
     *
     * @param page    分页参数
     * @param request 过滤条件
     * @param order   排序条件
     * @return ResponsePage<AiMyTaskListResponse>
     */
    ResponsePage<AiMyInterviewTaskListResponse> myTaskList(Page<AiInterviewTaskInfo> page, AiInterviewEmpTaskListRequest request, BaseOrder order);

    /**
     * 保存-访谈任务
     *
     * @param request    AI访谈任务-保存信息
     * @param taskStatus AI访谈任务 状态 CG：草稿，JXZ：进行中，YWC：已完成
     * @return AI访谈任务ID
     */
    Long saveData(AiInterviewTaskSaveRequest request, String taskStatus);

    /**
     * 发布-访谈任务
     *
     * @param request AI访谈任务发布数据
     * @return AI访谈任务ID
     */
    Long publishData(AiInterviewTaskSaveRequest request);

    /**
     * 删除-访谈任务
     *
     * @param id AI访谈任务-主键ID
     * @return Boolean
     */
    Boolean deleteById(Long id);

    /**
     * 结束-访谈任务
     *
     * @param id AI访谈任务-主键ID
     * @return Boolean
     */
    Boolean closeTask(Long id);

}
