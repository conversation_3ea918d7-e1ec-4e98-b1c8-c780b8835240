package com.deloitte.dhr.ai.module.coach.train.mapper;

import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainDialogueQuestion;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainDialogueQuestionResponse;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程资源-AI陪练-对话问题-相关持久化接口
 *
 * <AUTHOR>
 * @date 2024-01-03
 */
public interface AiTrainDialogueQuestionMapper extends SuperMapper<AiTrainDialogueQuestion> {

    /**
     * 查询-AI问题列表
     *
     * @param taskEmpId 培训任务员工ID
     * @return AiTrainMyDialogueHistoryResponse
     */
    List<AiTrainDialogueQuestionResponse> getAiQuestionList(@Param("taskEmpId") Long taskEmpId);

}