package com.deloitte.dhr.ai.module.coach.teach.service;

import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachChoiceOption;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachChoiceOptionResponse;
import com.deloitte.dhr.common.SuperService;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 课程资源-教学课程-考核题目-选择题选项-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTeachChoiceOptionService extends SuperService<AiTeachChoiceOption> {

    /**
     * 查询数据详情
     *
     * @param resourceId 课程资源ID
     * @return Map<Long, List < AiTeachChoiceOptionDetailResponse>>
     */
    Map<Long, List<AiTeachChoiceOptionResponse>> getMapByResourceId(Long resourceId);

    /**
     * 查询每道选择题的正确选项ID
     *
     * @param resourceId 课程资源ID
     * @return Map<Long, Set < Long>>
     */
    Map<Long, Set<Long>> getMapRightOptionIdsByResourceId(Long resourceId);

    /**
     * 删除数据
     *
     * @param resourceId 课程资源ID
     */
    void deleteByResourceId(Long resourceId);

}
