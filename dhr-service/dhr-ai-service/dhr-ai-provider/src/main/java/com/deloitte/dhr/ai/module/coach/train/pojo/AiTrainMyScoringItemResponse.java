package com.deloitte.dhr.ai.module.coach.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 课程资源-AI陪练-我的得分项
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainMyScoringItemResponse")
public class AiTrainMyScoringItemResponse {

    /**
     * 得分项编码
     */
    @ApiModelProperty(value = "得分项编码", name = "scoringItemCode")
    private String scoringItemCode;

    /**
     * 得分项名称
     */
    @ApiModelProperty(value = "得分项名称", name = "scoringItemName")
    private String scoringItemName;

    /**
     * 得分项分数
     */
    @ApiModelProperty(value = "得分项分数", name = "scoringItemScore")
    private BigDecimal scoringItemScore;

    /**
     * 得分项权重
     */
    @ApiModelProperty(value = "得分项权重", name = "scoringItemWeight")
    private BigDecimal scoringItemWeight;

}