package com.deloitte.dhr.ai.module.interview.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * AI访谈-访谈任务-员工信息
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewTaskEmpResponse")
public class AiInterviewTaskEmpResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 员工编号
     */
    @ApiModelProperty(value = "员工编号", name = "empCode")
    private String empCode;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称", name = "empName")
    private String empName;

    /**
     * 员工头像
     */
    @ApiModelProperty(value = "员工头像", name = "empAvatarUrl")
    private String empAvatarUrl;

    /**
     * 员工部门名称
     */
    @ApiModelProperty(value = "员工部门名称", name = "orgName")
    private String orgName;

    /**
     * 员工岗位名称
     */
    @ApiModelProperty(value = "员工岗位名称", name = "positionName")
    private String positionName;

    /**
     * 状态;WKS：未开始，JXZ：进行中，YWC：已完成
     */
    @ApiModelProperty(value = "状态;WKS：未开始，JXZ：进行中，YWC：已完成", name = "status")
    private String status;

    /**
     * 报告生成状态;01未生成，02生成中，03生成成功，04生成失败
     */
    @ApiModelProperty(value = "报告生成状态;01未生成，02生成中，03生成成功，04生成失败", name = "bgStatus")
    private String bgStatus;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间", name = "completeTime")
    private Date completeTime;

}