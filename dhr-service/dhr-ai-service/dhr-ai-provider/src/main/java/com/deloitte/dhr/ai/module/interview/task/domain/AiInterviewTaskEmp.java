package com.deloitte.dhr.ai.module.interview.task.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * AI访谈-访谈任务-员工表
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_interview_task_emp")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AI访谈-访谈任务-员工表")
public class AiInterviewTaskEmp extends SuperLogicModel<AiInterviewTaskEmp> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * AI访谈-任务ID
     */
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * 员工编号
     */
    @TableField(value = "emp_code")
    private String empCode;

    /**
     * 员工名称
     */
    @TableField(value = "emp_name")
    private String empName;

    /**
     * 员工头像
     */
    @TableField(value = "emp_avatar_url")
    private String empAvatarUrl;

    /**
     * 员工部门编码
     */
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 员工部门名称
     */
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 员工岗位编码
     */
    @TableField(value = "position_code")
    private String positionCode;

    /**
     * 员工岗位名称
     */
    @TableField(value = "position_name")
    private String positionName;

    /**
     * 状态;WKS：未开始，JXZ：进行中，YWC：已完成，YJS：已结束
     */
    @TableField(value = "status")
    private String status;

    /**
     * 完成时间
     */
    @TableField(value = "complete_time")
    private Date completeTime;

    /**
     * 消耗时间（second）
     */
    @TableField(value = "elapsed_time")
    private Integer elapsedTime;

    /**
     * 沟通状态
     */
    @TableField(value = "gt_status")
    private String gtStatus;

    /**
     * 完成度
     */
    @TableField(value = "wcd_rate")
    private String wcdRate;

    /**
     * 回答积极度
     */
    @TableField(value = "hdjj_score")
    private String hdjjScore;

    /**
     * 表达逻辑
     */
    @TableField(value = "bdlj_score")
    private String bdljScore;

    /**
     * 评价-总体
     */
    @TableField(value = "analyse01")
    private String analyse01;

    /**
     * 访谈摘要
     */
    @TableField(value = "analyse02")
    private String analyse02;

    /**
     * 后续关注点
     */
    @TableField(value = "analyse03")
    private String analyse03;

    /**
     * 报告状态：01未生成，02生成中，03生成成功，04生成失败
     */
    @TableField(value = "bg_status")
    private String bgStatus;
}