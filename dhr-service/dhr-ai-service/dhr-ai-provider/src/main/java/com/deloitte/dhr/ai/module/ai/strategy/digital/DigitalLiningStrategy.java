package com.deloitte.dhr.ai.module.ai.strategy.digital;


import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * -策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface DigitalLiningStrategy {

    /**
     * 执行-流式
     *
     * @param param 入参
     * @return T 响应
     */
    Flux<List<AiContent>> executeStream(Map<String, Object> param);

    /**
     * 执行-阻塞
     *
     * @param param 入参
     * @return T 响应
     */
    default Flux<List<AiContent>> executeBlock(Map<String, Object> param) {
        return Flux.empty();
    }

}
