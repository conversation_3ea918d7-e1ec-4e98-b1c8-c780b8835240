package com.deloitte.dhr.ai.module.coach.train.pojo;

import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceAttachSaveRequest;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceInfoSaveRequest;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachTestQuestionSaveRequest;
import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 课程资源-AI陪练-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainSaveRequest")
public class AiTrainSaveRequest {

    /**
     * 课程资源-基本信息
     */
    @Valid
    @ApiModelProperty(value = "课程资源-基本信息", name = "resourceInfo")
    @NotNull(groups = {Save.class, Submit.class}, message = "课程资源基本信息不能为空")
    private AiCourseResourceInfoSaveRequest resourceInfo;

    /**
     * 课程资源-学习附件信息
     */
    @Valid
    @NotEmpty(groups = Submit.class, message = "学习资料不能为空")
    @ApiModelProperty(value = "课程资源-学习附件信息", name = "attachInfoList")
    private List<AiCourseResourceAttachSaveRequest> attachInfoList;

    /**
     * AI陪练-基本信息
     */
    @Valid
    @ApiModelProperty(value = "AI陪练-基本信息", name = "aiTrainInfo")
    @NotNull(groups = {Save.class, Submit.class}, message = "AI陪练基本信息不能为空")
    private AiTrainInfoSaveRequest aiTrainInfo;

    /**
     * AI陪练-培训对象信息
     */
    @Valid
    @ApiModelProperty(value = "AI陪练-培训对象信息", name = "objectInfo")
    @NotNull(groups = {Submit.class}, message = "AI陪练对象信息不能为空")
    private AiTrainObjectSaveRequest objectInfo;

    /**
     * AI陪练-沟通框架信息
     */
    @Valid
    @NotEmpty(groups = {Submit.class}, message = "AI陪练沟通框架信息不能为空")
    @ApiModelProperty(value = "AI陪练-沟通框架信息", name = "frameworkInfoList")
    private List<AiTrainCommunicateFrameworkSaveRequest> frameworkInfoList;

    /**
     * AI陪练-对话提示信息
     */
    @Valid
    @NotEmpty(groups = {Submit.class}, message = "AI陪练对话提示信息不能为空")
    @ApiModelProperty(value = "AI陪练-对话提示信息", name = "questionInfoList")
    private List<AiTeachTestQuestionSaveRequest> questionInfoList;

}