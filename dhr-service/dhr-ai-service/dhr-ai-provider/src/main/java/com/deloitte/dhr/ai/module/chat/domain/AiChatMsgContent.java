package com.deloitte.dhr.ai.module.chat.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI聊天-聊天消息内容表
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Data
@TableName("ai_chat_msg_content")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AI聊天-聊天消息内容表")
public class AiChatMsgContent extends SuperLogicModel<AiChatMsgContent> {

    /**
     * 对话ID
     */
    @TableField(value = "dialogue_id")
    private Long dialogueId;

    /**
     * 消息ID
     */
    @TableField(value = "msg_id")
    private Long msgId;

    /**
     * 消息类型
     */
    @TableField(value = "content_type")
    private String contentType;

    /**
     * 消息内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 内容排序
     */
    @TableField(value = "content_order")
    private Integer contentOrder;

}