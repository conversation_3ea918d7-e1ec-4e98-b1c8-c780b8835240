package com.deloitte.dhr.ai.module.coach.teach.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 课程资源-教学课程-我的文本问题答案表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_teach_my_text_answer")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-教学课程-我的文本问题答案表")
public class AiTeachMyTextAnswer extends SuperLogicModel<AiTeachMyTextAnswer> {

    /**
     * 培训任务员工ID
     */
    @TableField(value = "task_emp_id")
    private Long taskEmpId;

    /**
     * 考核题目ID
     */
    @TableField(value = "question_id")
    private Long questionId;

    /**
     * 文本题答案内容
     */
    @TableField(value = "answer_content")
    private String answerContent;

    /**
     * 匹配度(来源AI)
     */
    @TableField(value = "ai_matching_degree")
    private BigDecimal aiMatchingDegree;


}