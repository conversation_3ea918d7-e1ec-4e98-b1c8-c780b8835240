package com.deloitte.dhr.ai.module.coach.train.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 课程资源-AI陪练-我的能力分析
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_train_my_ability_analyse")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-AI陪练-我的能力分析表")
public class AiTrainMyAbilityAnalyse extends SuperLogicModel<AiTrainMyAbilityAnalyse> {

    /**
     * 培训任务员工ID
     */
    @TableField(value = "task_emp_id")
    private Long taskEmpId;

    /**
     * 能力编码
     */
    @TableField(value = "ability_code")
    private String abilityCode;

    /**
     * 能力名称
     */
    @TableField(value = "ability_name")
    private String abilityName;

    /**
     * 能力分数
     */
    @TableField(value = "ability_score")
    private BigDecimal abilityScore;

    /**
     * 得分权重
     */
    @TableField(value = "ability_weight")
    private BigDecimal abilityWeight;

}