package com.deloitte.dhr.ai.module.coach.resource.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 课程资源-学习附件表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_course_resource_attach")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-学习附件表")
public class AiCourseResourceAttach extends SuperLogicModel<AiCourseResourceAttach> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 文件ID
     */
    @TableField(value = "file_id")
    private String fileId;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 文件类型
     */
    @TableField(value = "file_type")
    private String fileType;

    /**
     * 文件fileMd5值
     */
    @TableField(value = "file_md5")
    private String fileMd5;

    /**
     * 文件URL
     */
    @TableField(value = "file_url")
    private String fileUrl;

    /**
     * 文件大小
     */
    @TableField(value = "file_size")
    private String fileSize;

    /**
     * 知识库文件ID
     */
    @TableField(value = "datasets_file_id")
    private String datasetsFileId;

}