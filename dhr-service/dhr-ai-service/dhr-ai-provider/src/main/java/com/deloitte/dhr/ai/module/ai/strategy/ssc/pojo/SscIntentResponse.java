package com.deloitte.dhr.ai.module.ai.strategy.ssc.pojo;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.deloitte.dhr.ai.enums.AiSscIntentAction;
import com.deloitte.dhr.ai.enums.AiSscIntentItem;
import com.deloitte.dhr.ai.enums.AiSscIntentType;
import lombok.Builder;
import lombok.Data;

/**
 * SSC-意图识别
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@Builder
public class SscIntentResponse {

    /**
     * 类型 {@link AiSscIntentType #name}
     */
    @JSONField(name = "intentType")
    private String intentType;

    /**
     * 类型子项 {@link AiSscIntentItem #name}
     */
    @J<PERSON>NField(name = "intentItem")
    private String intentItem;

    /**
     * 执行动作 {@link AiSscIntentAction #code}
     */
    @JSONField(name = "intentAction")
    private String intentAction;

    /**
     * 意图识别解析出来的数据
     */
    @JSONField(name = "intentData")
    private JSONObject intentData;

}
