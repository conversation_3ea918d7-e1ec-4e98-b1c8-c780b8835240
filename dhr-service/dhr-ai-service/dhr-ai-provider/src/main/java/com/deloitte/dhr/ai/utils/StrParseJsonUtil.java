package com.deloitte.dhr.ai.utils;

import com.alibaba.fastjson2.JSON;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.List;

/**
 * 字符串转换JSON工具类
 *
 * <AUTHOR>
 */
public class StrParseJsonUtil {

    private final static String STR_REGEX = "\\s+";

    /**
     * 从一个乱格式的字符串中提取json字符串
     *
     * @param input        待解析的字符串
     * @param isStartArray 是否从数组开始解析
     * @return List<String>
     */
    public static List<String> strParseJson(StringBuilder input, Boolean isStartArray) {
        // 处理可能包含在markdown中的JSON字符串
        if (input == null || input.length() == 0) {
            return new ArrayList<>();
        }

        String content = input.toString();
        // 检查并删除可能存在的```json前缀
        if (content.startsWith("```")) {
            content = content.substring("```".length());
            input.delete(0, "```".length());
        }

        if (content.startsWith("json")) {
            content = content.substring("json".length());
            input.delete(0, "json".length());
        }

        // 检查并删除可能存在的```后缀
        if (content.endsWith("```")) {
            content = content.substring(0, content.length() - "```".length());
            input.delete(input.length() - "```".length(), input.length());
        }

        List<String> jsonStrings = new ArrayList<>();
        Deque<Character> stack = new ArrayDeque<>();
        StringBuilder currentJson = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char currentChar = input.charAt(i);

            // 如果当前字符是 `{`或者`[`，入栈
            if ((isStartArray && currentChar == '[') || (!isStartArray && currentChar == '{')) {
                stack.push(currentChar);
            }

            // 如果栈不为空，记录当前字符
            if (!stack.isEmpty()) {
                currentJson.append(currentChar);
            }

            if (currentChar != '}' && currentChar != ']') {
                continue;
            }
            // 如果当前字符是 `}`或者`]`，出栈
            if (!stack.isEmpty() && matchesPair(stack.peek(), currentChar)) {
                stack.pop();
                if (!stack.isEmpty()) {
                    continue;
                }
                if (isStartArray) {
                    jsonStrings.addAll(JSON.parseArray(currentJson.toString(), String.class));
                } else {
                    jsonStrings.add(currentJson.toString().replaceAll(STR_REGEX, ""));
                }
                currentJson.setLength(0);
                // 删除已经解析完成的字符串
                input.delete(0, i + 1);
                // 重新开始循环
                i = -1;
            }
        }
        return jsonStrings;
    }

    /**
     * JSON的括号是否匹配
     *
     * @param open  开始括号
     * @param close 结束括号
     * @return 是否匹配
     */
    public static boolean matchesPair(char open, char close) {
        return (open == '{' && close == '}') || (open == '[' && close == ']');
    }

}

