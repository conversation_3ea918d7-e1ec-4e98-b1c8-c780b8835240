package com.deloitte.dhr.ai.module.coach.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 课程资源-AI陪练-我的能力分析-图表展示
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainMyAbilityAnalyseChartResponse")
public class AiTrainMyAbilityAnalyseChartResponse {

    /**
     * 能力编码
     */
    @ApiModelProperty(value = "能力编码", name = "abilityCode")
    private String abilityCode;

    /**
     * 能力名称
     */
    @ApiModelProperty(value = "能力名称", name = "abilityName")
    private String abilityName;

    /**
     * 能力分数
     */
    @ApiModelProperty(value = "能力分数", name = "abilityScore")
    private BigDecimal abilityScore;

    /**
     * 得分权重
     */
    @ApiModelProperty(value = "得分权重", name = "abilityWeight")
    private BigDecimal abilityWeight;

}