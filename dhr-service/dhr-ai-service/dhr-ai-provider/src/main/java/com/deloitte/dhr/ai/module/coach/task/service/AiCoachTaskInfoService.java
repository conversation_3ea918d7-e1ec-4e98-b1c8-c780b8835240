package com.deloitte.dhr.ai.module.coach.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.coach.task.domain.AiCoachTaskInfo;
import com.deloitte.dhr.ai.module.coach.task.pojo.*;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperService;

/**
 * 培训任务-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiCoachTaskInfoService extends SuperService<AiCoachTaskInfo> {

    /**
     * 详情-教学课程任务
     *
     * @param id 培训任务主键ID
     * @return AiTeachTaskDetailResponse
     */
    AiTeachTaskDetailResponse getTeachTaskDetail(Long id);

    /**
     * 详情-AI陪练任务
     *
     * @param id 培训任务主键ID
     * @return AiCoachTaskDetailResponse
     */
    AiTrainTaskDetailResponse getTrainTaskDetail(Long id);

    /**
     * 分页-任务列表
     *
     * @param page    分页参数
     * @param request 过滤条件
     * @param order   排序条件
     * @return ResponsePage<AiTaskDetailResponse>
     */
    ResponsePage<AiTaskResponse> findTaskListPage(Page<AiCoachTaskInfo> page, AiTaskListRequest request, BaseOrder order);

    /**
     * 分页-我的陪练任务
     *
     * @param page    分页参数
     * @param request 过滤条件
     * @param order   排序条件
     * @return ResponsePage<AiMyTaskListResponse>
     */
    ResponsePage<AiMyTaskListResponse> myTaskList(Page<AiCoachTaskInfo> page, AiMyTaskListRequest request, BaseOrder order);

    /**
     * 保存-培训任务
     *
     * @param request    培训任务-保存信息
     * @param taskStatus 培训任务状态 CG：草稿，PXZ：培训中，YWC：已完成
     * @return 培训任务ID
     */
    Long saveData(AiCoachTaskSaveRequest request, String taskStatus);

    /**
     * 发布-培训任务
     *
     * @param request 培训任务发布数据
     * @return 培训任务ID
     */
    Long publishData(AiCoachTaskSaveRequest request);

    /**
     * 删除-培训任务
     *
     * @param id 培训任务主键ID
     * @return Boolean
     */
    Boolean deleteById(Long id);

    /**
     * 结束-培训任务
     *
     * @param id 培训任务主键ID
     * @return Boolean
     */
    Boolean closeTask(Long id);

}
