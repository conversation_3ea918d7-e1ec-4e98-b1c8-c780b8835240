package com.deloitte.dhr.ai.module.ai.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.ai.config.AiTtsConfig;
import com.deloitte.dhr.ai.module.ai.pojo.AiChatTtsRequest;
import com.deloitte.dhr.ai.module.ai.pojo.AiTtsRequest;
import com.deloitte.dhr.ai.module.ai.service.AiTtsService;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.UUID;

/**
 * AI语音合成-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
@Slf4j
public class AiTtsServiceImpl implements AiTtsService {


    @Autowired
    private AiTtsConfig aiTtsConfig;

    @Override
    public byte[] speechSynthesis(AiChatTtsRequest request) {

        // 获取配置
        AiTtsConfig.TtsParam ttsParam = aiTtsConfig.getParamGroup().values().stream().filter(AiTtsConfig.TtsParam::getEnable)
                .findAny().orElseThrow(() -> new CommRunException("没有配置或启动大模型语音合成配置"));
        AiTtsRequest ttsRequest = AiTtsRequest.builder()
                .app(AiTtsRequest.App.builder()
                        .appid(ttsParam.getAppId())
                        .cluster(ttsParam.getCluster())
                        .build())
                .user(AiTtsRequest.User.builder()
                        .uid(LoginUtil.getLoginUserCode())
                        .build())
                .audio(AiTtsRequest.Audio.builder()
                        .encoding(ttsParam.getEncoding())
                        .voiceType(ttsParam.getVoiceType())
                        .speedRatio(request.getSpeedRatio())
                        .build())
                .request(AiTtsRequest.Request.builder()
                        .reqID(UUID.randomUUID().toString())
                        .operation("query")
                        .text(request.getText())
                        .build())
                .build();
        try {
            String result = HttpUtil.createPost(aiTtsConfig.getUrl()).body(JSON.toJSONString(ttsRequest))
                    .header("Authorization", "Bearer;" + aiTtsConfig.getToken()).execute().body();
            JSONObject resultObj = JSONObject.parseObject(result, JSONObject.class);
            if (resultObj.getInteger("code") != 3000) {
                throw new CommRunException("调用大模型TTS接口出错，错误信息为：{}", resultObj.getString("message"));
            }
            String data = resultObj.getString("data");
            return Base64.getDecoder().decode(data);
        } catch (Exception e) {
            log.error("调用大模型TTS接口出错，错误信息为：{}", e.getMessage());
            throw new CommRunException(e.getMessage());
        }

    }
}
