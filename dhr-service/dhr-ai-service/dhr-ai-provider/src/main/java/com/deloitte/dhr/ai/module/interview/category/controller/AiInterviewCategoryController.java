package com.deloitte.dhr.ai.module.interview.category.controller;

import com.deloitte.dhr.ai.module.interview.category.pojo.AiInterviewCategoryResponse;
import com.deloitte.dhr.ai.module.interview.category.pojo.AiInterviewCategorySaveRequest;
import com.deloitte.dhr.ai.module.interview.category.pojo.AiInterviewCategoryTreeResponse;
import com.deloitte.dhr.ai.module.interview.category.service.AiInterviewCategoryService;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI访谈-类别
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/AiInterviewCategory")
@Api(tags = "AI访谈助手类别")
@Validated
public class AiInterviewCategoryController extends SuperController {

    @Autowired
    private AiInterviewCategoryService aiInterviewCategoryService;

    @ApiOperation(value = "详情", notes = "详情")
    @ApiOperationSupport(order = 1)
    @GetMapping("/detail/{id}")
    public ResponseVO<AiInterviewCategoryResponse> getDetail(@PathVariable("id") Long id) {
        return success(aiInterviewCategoryService.getDetail(id));
    }

    @ApiOperation(value = "列表树", notes = "列表树")
    @ApiOperationSupport(order = 2)
    @GetMapping("/tree")
    public ResponseVO<List<AiInterviewCategoryTreeResponse>> getTree(@RequestParam(value = "categoryName", required = false) String categoryName) {
        return success(aiInterviewCategoryService.getTree(categoryName));
    }

    @ApiOperation(value = "保存", notes = "保存")
    @ApiOperationSupport(order = 3)
    @PostMapping("/save")
    public ResponseVO<Long> save(@RequestBody @Validated AiInterviewCategorySaveRequest request) {
        return success(aiInterviewCategoryService.saveData(request));
    }

    @ApiOperation(value = "删除", notes = "删除")
    @ApiOperationSupport(order = 4)
    @PostMapping("/del")
    public ResponseVO<Boolean> del(@RequestParam("id") Long id) {
        return success(aiInterviewCategoryService.deleteById(id));
    }

}
