package com.deloitte.dhr.ai.module.coach.task.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.task.pojo.*;
import com.deloitte.dhr.ai.module.coach.task.service.AiCoachTaskEmpService;
import com.deloitte.dhr.ai.module.coach.task.service.AiCoachTaskInfoService;
import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import com.deloitte.dhr.common.Request;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 培训任务
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/AiCoachTask")
@Api(tags = "培训任务")
@Validated
public class AiCoachTaskController extends SuperController {

    @Autowired
    private AiCoachTaskInfoService aiCoachTaskInfoService;
    @Autowired
    private AiCoachTaskEmpService aiCoachTaskEmpService;


    @ApiOperation(value = "详情-教学课程任务", notes = "详情-教学课程任务")
    @ApiOperationSupport(order = 1)
    @GetMapping("/teach/detail/{id}")
    public ResponseVO<AiTeachTaskDetailResponse> getTeachTaskDetail(@PathVariable("id") Long id) {
        return success(aiCoachTaskInfoService.getTeachTaskDetail(id));
    }

    @ApiOperation(value = "详情-AI陪练任务", notes = "详情-AI陪练任务")
    @ApiOperationSupport(order = 2)
    @GetMapping("/train/detail/{id}")
    public ResponseVO<AiTrainTaskDetailResponse> getTrainTaskDetail(@PathVariable("id") Long id) {
        return success(aiCoachTaskInfoService.getTrainTaskDetail(id));
    }

    @ApiOperation(value = "详情-AI陪练员工", notes = "详情-AI陪练员工")
    @ApiOperationSupport(order = 3)
    @GetMapping("/emp/detail/{taskEmpId}")
    public ResponseVO<AiCoachTaskEmpResponse> getTaskEmpDetail(@PathVariable("taskEmpId") Long taskEmpId) {
        return success(aiCoachTaskEmpService.getDetail(taskEmpId));
    }

    @ApiOperation(value = "列表-陪练任务", notes = "列表-陪练任务")
    @ApiOperationSupport(order = 4)
    @PostMapping("/list/{current}/{size}")
    public ResponseVO<ResponsePage<AiTaskResponse>> taskList(@PathVariable("current") Long current, @PathVariable("size") Long size, @RequestBody Request<AiTaskListRequest> request) {
        return success(aiCoachTaskInfoService.findTaskListPage(new Page<>(current, size), request.get(), request.getOrder()));
    }

    @ApiOperation(value = "列表-陪练员工", notes = "列表-陪练员工")
    @ApiOperationSupport(order = 5)
    @PostMapping("/emp/list/{current}/{size}")
    public ResponseVO<ResponsePage<AiCoachTaskEmpResponse>> taskEmpList(@PathVariable("current") Long current, @PathVariable("size") Long size, @RequestBody Request<AiCoachTaskEmpListRequest> request) {
        return success(aiCoachTaskEmpService.findTaskEmpListPage(new Page<>(current, size), request.get(), request.getOrder()));
    }

    @ApiOperation(value = "列表-我的陪练", notes = "列表-我的陪练")
    @ApiOperationSupport(order = 6)
    @PostMapping("/my/list/{current}/{size}")
    public ResponseVO<ResponsePage<AiMyTaskListResponse>> myTaskList(@PathVariable("current") Long current, @PathVariable("size") Long size, @RequestBody Request<AiMyTaskListRequest> request) {
        return success(aiCoachTaskInfoService.myTaskList(new Page<>(current, size), request.get(), request.getOrder()));
    }

    @ApiOperation(value = "保存", notes = "保存")
    @ApiOperationSupport(order = 7)
    @PostMapping("/save")
    public ResponseVO<Long> save(@RequestBody @Validated(Save.class) AiCoachTaskSaveRequest request) {
        return success(aiCoachTaskInfoService.saveData(request, AiCoachConstant.TaskStatus.DRAFT));
    }

    @ApiOperation(value = "发布", notes = "发布")
    @ApiOperationSupport(order = 8)
    @PostMapping("/publish")
    public ResponseVO<Long> publish(@RequestBody @Validated(Submit.class) AiCoachTaskSaveRequest request) {
        return success(aiCoachTaskInfoService.publishData(request));
    }

    @ApiOperation(value = "删除", notes = "删除")
    @ApiOperationSupport(order = 9)
    @PostMapping("/del")
    public ResponseVO<Boolean> del(@RequestParam("id") Long id) {
        return success(aiCoachTaskInfoService.deleteById(id));
    }

    @ApiOperation(value = "详情-我的陪练任务", notes = "详情-我的陪练任务")
    @ApiOperationSupport(order = 10)
    @PostMapping("/my/task/detail/{taskEmpId}")
    public ResponseVO<AiCoachTaskInfoResponse> getMyTaskInfoDetail(@PathVariable("taskEmpId") Long taskEmpId) {
        return success(aiCoachTaskEmpService.getMyTaskInfoDetail(taskEmpId));
    }

    @ApiOperation(value = "结束", notes = "结束")
    @ApiOperationSupport(order = 10)
    @PostMapping("/close/{id}")
    public ResponseVO<Boolean> closeTask(@PathVariable("id") Long id) {
        return success(aiCoachTaskInfoService.closeTask(id));
    }

}
