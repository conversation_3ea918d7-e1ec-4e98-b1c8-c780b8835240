package com.deloitte.dhr.ai.module.interview.resource.pojo;

import com.deloitte.dhr.ai.module.interview.validation.Save;
import com.deloitte.dhr.ai.module.interview.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * AI访谈-基本信息
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewInfoResponse")
public class AiInterviewInfoResponse {

    /**
     * AI访谈资源ID
     */
    @ApiModelProperty(value = "AI访谈资源ID", name = "id")
    private Long id;

    /**
     * 类别ID
     */
    @ApiModelProperty(value = "类别ID", name = "categoryId")
    private Long categoryId;

    /**
     * AI访谈名称
     */
    @ApiModelProperty(value = "AI访谈名称", name = "courseName")
    private String courseName;

    /**
     * AI访谈分类;AI访谈：AIFT
     */
    @ApiModelProperty(value = "AI访谈分类;AI访谈：AIFT", name = "courseType")
    private String courseType;

    /**
     * AI访谈封面图链接
     */
    @ApiModelProperty(value = "AI访谈封面图链接", name = "courseCoverUrl")
    private String courseCoverUrl;

    /**
     * AI访谈状态
     */
    @ApiModelProperty(value = "AI访谈状态", name = "courseStatus")
    private String courseStatus;

    /**
     * 对话场景
     */
    @ApiModelProperty(value = "对话场景", name = "dialogueScene")
    private String dialogueScene;

    /**
     * 对话目标
     */
    @ApiModelProperty(value = "对话目标", name = "dialogueObjective")
    private String dialogueObjective;

    /**
     * 对话提示问题数量
     */
    @ApiModelProperty(value = "对话提示问题数量", name = "dialogueQuestionNum")
    private Integer dialogueQuestionNum;

    /**
     * 访谈注意事项
     */
    @ApiModelProperty(value = "访谈注意事项", name = "dialogueRemark")
    private String dialogueRemark;

    /**
     * 访谈时长（minute）
     */
    @ApiModelProperty(value = "访谈时长（minute）", name = "courseDuration")
    private BigDecimal courseDuration;
}