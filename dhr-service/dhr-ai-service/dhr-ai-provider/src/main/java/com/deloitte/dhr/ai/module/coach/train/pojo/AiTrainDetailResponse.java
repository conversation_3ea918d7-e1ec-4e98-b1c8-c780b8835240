package com.deloitte.dhr.ai.module.coach.train.pojo;

import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceAttachResponse;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceInfoResponse;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachTestQuestionResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 课程资源-AI陪练-详情
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainDetailResponse")
public class AiTrainDetailResponse {

    /**
     * 课程资源-基本信息
     */
    @ApiModelProperty(value = "课程资源-基本信息", name = "resourceInfo")
    private AiCourseResourceInfoResponse resourceInfo;

    /**
     * 课程资源-学习附件信息
     */
    @ApiModelProperty(value = "课程资源-学习附件信息", name = "attachInfoList")
    private List<AiCourseResourceAttachResponse> attachInfoList;

    /**
     * AI陪练-基本信息
     */
    @ApiModelProperty(value = "AI陪练-基本信息", name = "aiTrainInfo")
    private AiTrainInfoResponse aiTrainInfo;

    /**
     * AI陪练-培训对象信息
     */
    @ApiModelProperty(value = "AI陪练-培训对象信息", name = "objectInfo")
    private AiTrainObjectResponse objectInfo;

    /**
     * AI陪练-沟通框架信息
     */
    @ApiModelProperty(value = "AI陪练-沟通框架信息", name = "frameworkInfoList")
    private List<AiTrainCommunicateFrameworkResponse> frameworkInfoList;

    /**
     * AI陪练-对话提示信息
     */
    @ApiModelProperty(value = "AI陪练-对话提示信息", name = "questionInfoList")
    private List<AiTeachTestQuestionResponse> questionInfoList;

}