package com.deloitte.dhr.ai.module.ai.strategy.transform;

import com.deloitte.dhr.ai.enums.AiTransformType;
import com.deloitte.dhr.ai.module.ai.annotation.TransformStrategyType;
import com.deloitte.dhr.common.base.exception.CommRunException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * AI=转换策略-Context
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@Slf4j
public class TransformStrategyContext {

    /**
     * 策略类集合
     */
    private Map<AiTransformType, ResponseTransformStrategy> strategyMap;

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    private void init() {
        strategyMap = applicationContext.getBeansOfType(ResponseTransformStrategy.class).values()
                .stream()
                .collect(Collectors.toMap(a -> a.getClass().getAnnotation(TransformStrategyType.class).value(),
                        Function.identity(), (k1, k2) -> k1));
    }

    /**
     * 获取转换策略
     *
     * @param type 转换类型
     * @return 类型对应的策略
     * @throws CommRunException 未找到对应的转换策略
     */
    public ResponseTransformStrategy getStrategy(AiTransformType type) throws CommRunException {
        ResponseTransformStrategy strategy = strategyMap.get(type);
        if (strategy == null) {
            log.error("转换类型：{}", type);
            throw new CommRunException("未找到对应的转换策略");
        }
        return strategyMap.get(type);
    }


}