package com.deloitte.dhr.ai.module.interview.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
/**
 * AI访谈-访谈任务-我的消息发送
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewMySendResponse")
public class AiInterviewMySendResponse {

    /**
     * 下一条AI消息
     */
    @ApiModelProperty(value = "下一条AI消息", name = "nextAiMessageInfo")
    private AiInterviewMyDialogueHistoryResponse nextAiMessageInfo;

    /**
     * 对话是否结束
     */
    @ApiModelProperty(value = "对话是否结束", name = "isDialogueEnd")
    private Boolean isDialogueEnd;

    /**
     * 本次员工对话消息ID
     */
    @ApiModelProperty(value = "本次员工对话消息ID", name = "currentEmpMessageId")
    private Long currentEmpMessageId;

}