package com.deloitte.dhr.ai.module.coach.teach.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 课程资源-教学课程-我的考核题目信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("AiTeachMyTestQuestionResponse")
public class AiTeachMyTestQuestionResponse extends AiTeachTestQuestionResponse {

    /**
     * 结果
     */
    @ApiModelProperty(value = "结果", name = "result")
    private Boolean result;

}