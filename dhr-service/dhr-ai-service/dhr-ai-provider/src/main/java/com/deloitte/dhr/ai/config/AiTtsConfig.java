package com.deloitte.dhr.ai.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * AI模型配置
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ai.tts")
public class AiTtsConfig {

    /**
     * 请求地址
     */
    private String url;

    /**
     * token
     */
    private String token;

    /**
     * 请求参数组
     */
    private Map<String, TtsParam> paramGroup;


    @Data
    public static class TtsParam {

        private String appId;

        private String cluster;

        private String voiceType;

        private String encoding;

        private Boolean enable;

    }

}
