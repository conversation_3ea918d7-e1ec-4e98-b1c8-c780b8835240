package com.deloitte.dhr.ai.enums;


import lombok.Getter;

/**
 * SSC意图识别行为枚举
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public enum DigitalLiningIntentAction {

    TQ("transaction_query", "事务查询"),
    TC("transaction_commit", "事务提交"),
    IR("information_retrieval", "信息检索");

    private final String code;
    private final String name;

    DigitalLiningIntentAction(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 通过name获取枚举
     *
     * @param name 名称
     * @return DigitalLiningIntentAction
     */
    public static DigitalLiningIntentAction getByName(String name) {
        DigitalLiningIntentAction result = null;
        for (DigitalLiningIntentAction action : DigitalLiningIntentAction.values()) {
            if (action.getName().equals(name)) {
                result = action;
            }
        }
        if (result == null) {
            return IR;
        }
        return result;
    }

}
