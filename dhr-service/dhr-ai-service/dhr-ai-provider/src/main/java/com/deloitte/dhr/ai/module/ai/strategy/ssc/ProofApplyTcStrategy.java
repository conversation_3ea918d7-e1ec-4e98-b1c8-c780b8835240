package com.deloitte.dhr.ai.module.ai.strategy.ssc;


import com.deloitte.dhr.ai.enums.AiSscIntentAction;
import com.deloitte.dhr.ai.enums.AiSscIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.ActionStrategyType;
import org.springframework.stereotype.Service;

/**
 * SSC-证明申请-事务提交-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
@ActionStrategyType(item = AiSscIntentItem.ZMXX, action = AiSscIntentAction.TC)
public class ProofApplyTcStrategy extends ProofApplyTqStrategy {

}
