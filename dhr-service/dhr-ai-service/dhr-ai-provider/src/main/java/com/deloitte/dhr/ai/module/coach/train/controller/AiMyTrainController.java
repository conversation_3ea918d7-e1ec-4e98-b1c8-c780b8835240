package com.deloitte.dhr.ai.module.coach.train.controller;

import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.task.service.AiCoachTaskEmpService;
import com.deloitte.dhr.ai.module.coach.train.pojo.*;
import com.deloitte.dhr.ai.module.coach.train.service.*;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 课程资源-AI陪练-我的培训
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/AiTrain/my")
@Api(tags = "课程资源-AI陪练-我的培训")
@Validated
public class AiMyTrainController extends SuperController {

    @Autowired
    private AiMyTrainService aiMyTrainService;
    @Autowired
    private AiTrainMyDialogueHistoryService aiTrainMyDialogueHistoryService;
    @Autowired
    private AiTrainMyTalkSpeedAnalyseService aiTrainMyTalkSpeedAnalyseService;
    @Autowired
    private AiTrainMyAbilityAnalyseService aiTrainMyAbilityAnalyseService;
    @Autowired
    private AiCoachTaskEmpService aiCoachTaskEmpService;
    @Autowired
    private AiTrainMyScoringItemService aiTrainMyScoringItemService;

    @ApiOperation(value = "陪练对象", notes = "陪练对象")
    @ApiOperationSupport(order = 1)
    @GetMapping("/object/detail/{taskEmpId}")
    public ResponseVO<AiTrainObjectResponse> getMyObjectDetail(@PathVariable("taskEmpId") Long taskEmpId) {
        return success(aiCoachTaskEmpService.getMyObjectDetail(taskEmpId));
    }

    @ApiOperation(value = "对话历史", notes = "对话历史")
    @ApiOperationSupport(order = 2)
    @PostMapping("/dialogue/history")
    public ResponseVO<List<AiTrainMyDialogueHistoryResponse>> getMyDialogueHistory(@RequestBody AiTrainMyDialogueHistoryListRequest request) {
        return success(aiTrainMyDialogueHistoryService.getMyDialogueHistory(request));
    }

    @ApiOperation(value = "语速分析", notes = "语速分析")
    @ApiOperationSupport(order = 3)
    @PostMapping("/talkSpeed/analyse/{taskEmpId}")
    public ResponseVO<List<AiTrainMyTalkSpeedAnalyseChartResponse>> getMyTalkSpeedAnalyseChart(@PathVariable("taskEmpId") Long taskEmpId) {
        return success(aiTrainMyTalkSpeedAnalyseService.getMyTalkSpeedAnalyseChart(taskEmpId));
    }

    @ApiOperation(value = "能力分析", notes = "能力分析")
    @ApiOperationSupport(order = 4)
    @GetMapping("/ability/chart/analyse/{taskEmpId}")
    public ResponseVO<List<AiTrainMyAbilityAnalyseChartResponse>> getMyAbilityAnalyseChart(@PathVariable("taskEmpId") Long taskEmpId) {
        return success(aiTrainMyAbilityAnalyseService.getMyAbilityAnalyseChart(taskEmpId));
    }

    @ApiOperation(value = "总体概况", notes = "总体概况")
    @ApiOperationSupport(order = 5)
    @GetMapping("/overview/{taskEmpId}")
    public ResponseVO<AiTrainMyOverviewResponse> getMyOverview(@PathVariable("taskEmpId") Long taskEmpId) {
        return success(aiTrainMyScoringItemService.getMyOverview(taskEmpId));
    }

    @ApiOperation(value = "发送消息", notes = "总体概况")
    @ApiOperationSupport(order = 6)
    @PostMapping("/send")
    public ResponseVO<AiTrainMySendResponse> sendMessage(@RequestBody AiTrainMySendRequest request) {
        return success(aiMyTrainService.sendMessage(request));
    }

    @ApiOperation(value = "暂停陪练", notes = "暂停陪练")
    @ApiOperationSupport(order = 7)
    @PostMapping("/pause")
    public ResponseVO<Boolean> pauseTraining(@RequestBody AiTrainMyCompetedRequest request) {
        return success(aiMyTrainService.pauseTraining(request, AiCoachConstant.TaskEmpStatus.ONGOING));
    }

    @ApiOperation(value = "结束陪练", notes = "结束陪练")
    @ApiOperationSupport(order = 8)
    @PostMapping("/end")
    public ResponseVO<Boolean> endTraining(@RequestBody AiTrainMyCompetedRequest request) {
        return success(aiMyTrainService.endTraining(request));
    }

    @ApiOperation(value = "对话分析", notes = "对话分析")
    @ApiOperationSupport(order = 9)
    @GetMapping("/dialogue/analyse/{historyId}")
    public ResponseVO<List<AiTrainMyAbilityScoreResponse>> getMyDialogueAnalyse(@PathVariable("historyId") Long historyId) {
        return success(aiTrainMyAbilityAnalyseService.getMyDialogueAnalyse(historyId));
    }

}
