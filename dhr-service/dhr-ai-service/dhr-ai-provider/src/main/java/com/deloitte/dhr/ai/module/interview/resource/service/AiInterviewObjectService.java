package com.deloitte.dhr.ai.module.interview.resource.service;

import com.deloitte.dhr.ai.module.interview.train.domain.AiInterviewObject;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewObjectResponse;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewObjectSaveRequest;
import com.deloitte.dhr.common.SuperService;

/**
 * AI访谈-访谈任务-对象-相关业务接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface AiInterviewObjectService extends SuperService<AiInterviewObject> {

    /**
     * 查询-AI陪练-对象信息
     *
     * @param resourceId 课程资源ID
     * @return AiInterviewObjectDetailResponse
     */
    AiInterviewObjectResponse getByResourceId(Long resourceId);

    /**
     * 保存/更新-AI陪练-对象信息
     *
     * @param request    AI陪练-对象-保存/更新信息
     * @param resourceId 课程资源ID
     * @return Boolean
     */
    Boolean saveData(AiInterviewObjectSaveRequest request, Long resourceId);

    /**
     * 删除-AI陪练-对象信息
     *
     * @param resourceId 课程资源ID
     */
    void deleteByResourceId(Long resourceId);

}
