package com.deloitte.dhr.ai.module.sr.controller;

import com.deloitte.dhr.ai.module.sr.pojo.AliyunAsrRequest;
import com.deloitte.dhr.ai.module.sr.pojo.AliyunFlashRecognizerRequest;
import com.deloitte.dhr.ai.module.sr.service.AliyunSrService;
import com.deloitte.dhr.common.ResponseVO;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 阿里云-语音识别
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/aliyun/sr")
@Api(tags = "阿里云-语音识别")
@Validated
public class AliyunSrController {

    @Autowired
    private AliyunSrService aliyunSrService;

    @ApiOperation(value = "一句话识别", notes = "一句话识别")
    @ApiOperationSupport(order = 1)
    @PostMapping("/asr")
    public ResponseVO<String> asr(@RequestBody @Validated AliyunAsrRequest request) {
        return ResponseVO.success(aliyunSrService.asr(request));
    }

    @ApiOperation(value = "录音文件识别极速版", notes = "录音文件识别极速版")
    @ApiOperationSupport(order = 2)
    @PostMapping("/flashRecognizer")
    public ResponseVO<String> flashRecognizer(@RequestBody @Validated AliyunFlashRecognizerRequest request) {
        return ResponseVO.success(aliyunSrService.flashRecognizer(request));
    }

}
