package com.deloitte.dhr.ai.module.ai.strategy.ssc;

import com.deloitte.dhr.ai.enums.AiSscIntentAction;
import com.deloitte.dhr.ai.enums.AiSscIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.ActionStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * SSC-公出出差-事务提交-策略
 */
@Service
@ActionStrategyType(item = AiSscIntentItem.GCCC, action = AiSscIntentAction.TC)
public class TripTcStrategy implements SscStrategy {

    @Override
    public Flux<List<AiContent>> executeStream(Map<String, Object> param) {
        return Flux.empty();
    }

}
