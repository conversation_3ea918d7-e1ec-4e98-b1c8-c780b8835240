package com.deloitte.dhr.ai.module.interview.task.pojo;

import com.deloitte.dhr.ai.module.interview.validation.Save;
import com.deloitte.dhr.ai.module.interview.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * AI访谈-访谈任务-基本信息-保存
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewTaskInfoSaveRequest")
public class AiInterviewTaskInfoSaveRequest {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 课程资源ID
     */
    @NotNull(groups = {Save.class, Submit.class}, message = "课程资源ID不能为空")
    @ApiModelProperty(value = "课程资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * AI访谈任务名称
     */
    @Length(groups = Submit.class, max = 80, message = "访谈任务名称不能超过80个字符")
    @NotBlank(groups = {Save.class, Submit.class}, message = "访谈任务名称不能为空")
    @ApiModelProperty(value = "AI访谈任务名称", name = "taskName")
    private String taskName;

    /**
     * AI访谈任务-开始时间
     */
    @ApiModelProperty(value = "AI访谈任务-开始时间", name = "startDateTime")
    private Date startDateTime;

    /**
     * AI访谈任务-结束时间
     */
    @ApiModelProperty(value = "AI访谈任务-结束时间", name = "endDateTime")
    private Date endDateTime;

    /**
     * AI访谈任务-封面URL
     */
    @Length(groups = Submit.class, max = 255, message = "访谈任务封面URL不能超过255个字符")
    @ApiModelProperty(value = "AI访谈任务封面URL", name = "taskCoverUrl")
    private String taskCoverUrl;

}