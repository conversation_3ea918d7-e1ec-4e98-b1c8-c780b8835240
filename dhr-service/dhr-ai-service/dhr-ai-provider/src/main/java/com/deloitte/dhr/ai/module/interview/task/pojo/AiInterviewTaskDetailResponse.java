package com.deloitte.dhr.ai.module.interview.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI访谈-访谈任务-详情
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("AiInterviewTaskDetailResponse")
public class AiInterviewTaskDetailResponse extends AiInterviewTaskResponse {

    /**
     * 类别ID
     */
    @ApiModelProperty(value = "类别ID", name = "categoryId")
    private Long categoryId;

    /**
     * 类别名称
     */
    @ApiModelProperty(value = "类别名称", name = "categoryName")
    private String categoryName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称", name = "courseName")
    private String courseName;

    /**
     * 课程封面图链接
     */
    @ApiModelProperty(value = "课程封面图链接", name = "courseCoverUrl")
    private String courseCoverUrl;

    /**
     * 访谈场景
     */
    @ApiModelProperty(value = "访谈场景", name = "dialogueScene")
    private String dialogueScene;

    /**
     * 访谈目标
     */
    @ApiModelProperty(value = "访谈目标", name = "dialogueObjective")
    private String dialogueObjective;

}