package com.deloitte.dhr.ai.module.interview.resource.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * AI访谈-主表
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_interview_info")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AI访谈-主表")
public class AiInterviewInfo extends SuperLogicModel<AiInterviewInfo> {

    /**
     * 类别ID
     */
    @TableField(value = "category_id")
    private Long categoryId;

    /**
     * AI访谈名称
     */
    @TableField(value = "course_name")
    private String courseName;

    /**
     * AI访谈分类;AI访谈：AIFT
     */
    @TableField(value = "course_type")
    private String courseType;

    /**
     * AI访谈封面图链接
     */
    @TableField(value = "course_cover_url")
    private String courseCoverUrl;

    /**
     * AI访谈状态：CG：草稿，TJ：提交
     */
    @TableField(value = "course_status")
    private String courseStatus;

    /**
     * 对话场景
     */
    @TableField(value = "dialogue_scene")
    private String dialogueScene;

    /**
     * 对话目标
     */
    @TableField(value = "dialogue_objective")
    private String dialogueObjective;

    /**
     * 对话提示问题数量
     */
    @TableField(value = "dialogue_question_num")
    private Integer dialogueQuestionNum;

    /**
     * 访谈注意事项
     */
    @TableField(value = "dialogue_remark")
    private String dialogueRemark;

    /**
     * 谈访时长（minute）
     */
    @TableField(value = "course_duration")
    private BigDecimal courseDuration;
}