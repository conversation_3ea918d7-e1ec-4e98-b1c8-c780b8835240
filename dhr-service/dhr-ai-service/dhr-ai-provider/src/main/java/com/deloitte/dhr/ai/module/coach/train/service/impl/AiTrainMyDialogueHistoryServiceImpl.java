package com.deloitte.dhr.ai.module.coach.train.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.ai.config.AiModelConfig;
import com.deloitte.dhr.ai.enums.AiSubType;
import com.deloitte.dhr.ai.enums.AiType;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.module.ai.service.AiService;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainMyDialogueHistory;
import com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainDialogueQuestionMapper;
import com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainMyDialogueHistoryMapper;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainDialogueQuestionResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyDialogueHistoryListRequest;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyDialogueHistoryResponse;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainMyDialogueHistoryService;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 课程资源-AI陪练-我的对话历史记录-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiTrainMyDialogueHistoryServiceImpl extends SuperServiceImpl<AiTrainMyDialogueHistoryMapper, AiTrainMyDialogueHistory> implements AiTrainMyDialogueHistoryService {

    @Autowired
    private AiTrainMyDialogueHistoryMapper aiTrainMyDialogueHistoryMapper;
    @Autowired
    private AiTrainDialogueQuestionMapper aiTrainDialogueQuestionMapper;
    @Autowired
    private AiModelConfig aiModelConfig;
    @Autowired
    private AiService aiService;

    @Override
    public List<AiTrainMyDialogueHistoryResponse> getMyDialogueHistory(AiTrainMyDialogueHistoryListRequest request) {

        // 查询历史记录信息
        List<AiTrainMyDialogueHistoryResponse> historyList = aiTrainMyDialogueHistoryMapper.getMyDialogueHistory(request);

        // 查询AI的问题信息
        List<AiTrainDialogueQuestionResponse> aiQuestionList = aiTrainDialogueQuestionMapper.getAiQuestionList(request.getTaskEmpId());
        if (CollUtil.isEmpty(aiQuestionList)) {
            return historyList;
        }

        // 获取下个问题
        List<AiTrainMyDialogueHistoryResponse> aiHistoryList = historyList.stream().filter(a -> StrUtil.equals(a.getMessageBelong(), AiCoachConstant.DialogueMessageBelong.AI)).collect(Collectors.toList());
        AiTrainDialogueQuestionResponse nextAiQuestion = this.getNextAiQuestion(CollUtil.isEmpty(historyList) ? null : aiHistoryList.get(0).getMessage(), aiQuestionList, aiHistoryList);

        historyList = historyList.stream().sorted(Comparator.comparing(AiTrainMyDialogueHistoryResponse::getSendTime)).collect(Collectors.toList());
        // 把下个问题组装到对话历史记录里面
        if (nextAiQuestion != null) {
            AiTrainMyDialogueHistoryResponse historyResponse =
                    new AiTrainMyDialogueHistoryResponse(null, nextAiQuestion.getObjectAvatarUrl(), nextAiQuestion.getQuestionName(), DateTime.now(), AiCoachConstant.DialogueMessageBelong.AI, nextAiQuestion.getAiQuestionVoiceFileUrl());
            historyList.add(historyResponse);
        }
        return historyList;
    }

    @Override
    public AiTrainDialogueQuestionResponse getNextAiQuestion(String currentQuestion, List<AiTrainDialogueQuestionResponse> aiQuestionList, List<AiTrainMyDialogueHistoryResponse> dialogueHistoryList) {
        dialogueHistoryList = dialogueHistoryList.stream().filter(a -> StrUtil.equals(a.getMessageBelong(), AiCoachConstant.DialogueMessageBelong.AI)).collect(Collectors.toList());
        AiTrainDialogueQuestionResponse questionResponse;
        if (StrUtil.isBlank(currentQuestion)) {
            // 如果没有历史记录，则插入第一条AI的对话问题
            questionResponse = aiQuestionList.get(0);
        } else {
            // 如果有相同题目名称，则需要特殊处理一下，查询已经答过的相同题目数量
            int sameQuestionAnswerNum = (int) dialogueHistoryList.stream().filter(a -> StrUtil.equals(a.getMessage(), currentQuestion)).count();
            List<Integer> sameQuestionOrderList = aiQuestionList.stream().filter(a -> StrUtil.equals(a.getQuestionName(), currentQuestion)).map(AiTrainDialogueQuestionResponse::getQuestionOrder).sorted().collect(Collectors.toList());
            int questionOrder = sameQuestionAnswerNum == 0 ? sameQuestionOrderList.get(0) : sameQuestionAnswerNum > sameQuestionOrderList.size() ? sameQuestionOrderList.get(sameQuestionOrderList.size() - 1) : sameQuestionOrderList.get(sameQuestionAnswerNum - 1);
            questionResponse = aiQuestionList.stream().filter(a -> a.getQuestionOrder() > questionOrder).findFirst().orElse(null);
        }
        // 如果该问题没有语音文件URL，则需要调用AI模型接口生成
        if (questionResponse != null && StrUtil.isBlank(questionResponse.getAiQuestionVoiceFileUrl())) {
            // 构建AI请求入参
            AiRequest aiRequest = AiRequest.builder()
                    .aiType(AiType.COACH.getCode())
                    .aiSubType(AiSubType.TRAIN_TEXT_TO_SPEECH.getCode())
                    .userId(LoginUtil.getLoginUserCode())
                    .stream(false)
                    .contents(List.of(AiContent.builder()
                            .contentType(AiContentType.JSON.getCode())
                            .content(JSON.toJSONString(Map.of("content", questionResponse.getQuestionName())))
                            .build()))
                    .build();
            List<String> urlList = aiService.work(aiRequest, LoginUtil.getLoginUser())
                    .filter(raw -> CollUtil.isNotEmpty(raw.getContents()))
                    .flatMap(raw -> {
                        // todo 临时妥协方案，后续再优化调整
                        JSONObject fileObject = JSONObject.parseObject(raw.getContents().get(0).getContent());
                        String url = aiModelConfig.getBaseUrl().replace("/v1", "").concat(fileObject.getString("url"));
                        return Flux.just(url);
                    })
                    .collectList()
                    .block();
            if (!CollUtil.isEmpty(urlList)) {
                questionResponse.setAiQuestionVoiceFileUrl(urlList.get(0));
            }
        }
        return questionResponse;
    }

}

