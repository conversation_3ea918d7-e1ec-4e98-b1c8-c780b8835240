package com.deloitte.dhr.ai.utils;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.deloitte.dhr.common.base.exception.CommRunException;

/**
 * 校验工具类
 *
 * <AUTHOR>
 */
public class CheckUtils {

    /**
     * 校验是否保存成功
     *
     * @param result  保存结果
     * @param message 报错信息
     */
    public static void checkSaveOrUpdate(boolean result, String message) {
        if (!result) {
            throw new CommRunException(message);
        }
    }

    /**
     * 校验数据是否存在
     *
     * @param model   校验对象
     * @param message 报错信息
     */
    public static <T extends Model<T>> void checkNull(T model, String message) {
        if (model == null) {
            throw new CommRunException(message);
        }
    }

}
