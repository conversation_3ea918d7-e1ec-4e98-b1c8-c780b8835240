package com.deloitte.dhr.ai.module.coach.teach.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 课程资源-教学课程-考核题目-选择题选项信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachChoiceOptionResponse")
public class AiTeachChoiceOptionResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 选项名称
     */
    @ApiModelProperty(value = "选项名称", name = "optionName")
    private String optionName;

    /**
     * 是否答案
     */
    @ApiModelProperty(value = "是否答案", name = "isAnswer")
    private Boolean isAnswer;

    /**
     * 选项序号
     */
    @ApiModelProperty(value = "选项序号", name = "optionSort")
    private Integer optionSort;

}