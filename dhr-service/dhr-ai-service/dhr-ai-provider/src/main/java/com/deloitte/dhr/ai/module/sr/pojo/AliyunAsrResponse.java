package com.deloitte.dhr.ai.module.sr.pojo;


import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * 阿里云-语音一句话识别-返回
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
public class AliyunAsrResponse {

    /**
     * 任务ID
     */
    @JSONField(name = "task_id")
    private String taskId;

    /**
     * 识别结果
     */
    @JSONField(name = "result")
    private String result;

    /**
     * 接口返回状态
     */
    @JSONField(name = "status")
    private Integer status;

    /**
     * 接口返回状态对应消息
     */
    @JSONField(name = "message")
    private String message;

}
