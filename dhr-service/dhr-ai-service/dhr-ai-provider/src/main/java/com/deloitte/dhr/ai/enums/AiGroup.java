package com.deloitte.dhr.ai.enums;

import lombok.Getter;

/**
 * AI分组枚举
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public enum AiGroup {


    TEXT_CHAT("text_chat", "文本聊天"),
    DIGITAL_HUMAN("digital_human", "数字人"),
    DIGITAL_HUMAN_LINING("digital_human_lining", "数字人_李宁"),
    ;

    private final String code;

    private final String name;


    AiGroup(String code, String name) {
        this.code = code;
        this.name = name;

    }

    /**
     * 获取AI 分组
     *
     * @param type {@link AiType}
     * @return AiGroup
     */
    public static AiGroup getByAiType(String type) {
        if (AiType.DIGITAL_HUMAN.getCode().equals(type)) {
            return DIGITAL_HUMAN;
        } if (AiType.DIGITAL_HUMAN_LINING.getCode().equals(type)) {
            return DIGITAL_HUMAN_LINING;
        } else {
            return TEXT_CHAT;
        }
    }

}
