package com.deloitte.dhr.ai.module.coach.resource.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.coach.category.mapper.AiCourseCategoryMapper;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.resource.domain.AiCourseResourceInfo;
import com.deloitte.dhr.ai.module.coach.resource.mapper.AiCourseResourceInfoMapper;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceInfoResponse;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceInfoSaveRequest;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceListRequest;
import com.deloitte.dhr.ai.module.coach.resource.service.AiCourseResourceAttachService;
import com.deloitte.dhr.ai.module.coach.resource.service.AiCourseResourceInfoService;
import com.deloitte.dhr.ai.module.coach.teach.service.AiTeachInfoService;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainInfoService;
import com.deloitte.dhr.ai.utils.CheckUtils;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 课程资源-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiCourseResourceInfoServiceImpl extends SuperServiceImpl<AiCourseResourceInfoMapper, AiCourseResourceInfo> implements AiCourseResourceInfoService {

    @Autowired
    private AiCourseResourceInfoMapper aiCourseResourceInfoMapper;
    @Autowired
    private AiCourseResourceAttachService aiCourseResourceAttachService;
    @Lazy
    @Autowired
    private AiTeachInfoService aiTeachInfoService;
    @Lazy
    @Autowired
    private AiTrainInfoService aiTrainInfoService;
    @Autowired
    private AiCourseCategoryMapper aiCourseCategoryMapper;

    @Override
    public AiCourseResourceInfoResponse getDetail(Long id) {
        AiCourseResourceInfo aiCourseResourceInfo = this.get(id);
        CheckUtils.checkNull(aiCourseResourceInfo, "数据不存在");
        return BeanUtil.copyProperties(aiCourseResourceInfo, AiCourseResourceInfoResponse.class);

    }

    @Override
    public ResponsePage<AiCourseResourceInfoResponse> findListPage(Page<AiCourseResourceInfo> page, AiCourseResourceListRequest request, BaseOrder order) {
        if (request.getCategoryId() != null) {
            request.setCategoryPath(aiCourseCategoryMapper.getCategoryPathById(request.getCategoryId()));
        }
        List<AiCourseResourceInfoResponse> list = aiCourseResourceInfoMapper.findListPage(page, request, this.adjustOrder(order));
        return new ResponsePage<>(page, list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveData(AiCourseResourceInfoSaveRequest request, String courseType, String courseStatus) {
        // 保存/更新-课程资源-基本信息
        AiCourseResourceInfo aiCourseResourceInfo = BeanUtil.copyProperties(request, AiCourseResourceInfo.class);
        aiCourseResourceInfo.setCourseType(courseType);
        aiCourseResourceInfo.setCourseStatus(courseStatus);
        this.saveOrUpdate(aiCourseResourceInfo);
        return aiCourseResourceInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // 删除草稿状态的数据
        AiCourseResourceInfo aiCourseResourceInfo = this.getById(id);
        CheckUtils.checkNull(aiCourseResourceInfo, "数据不存在");
        if (!StrUtil.equals(AiCoachConstant.CourseResourceStatus.DRAFT, aiCourseResourceInfo.getCourseStatus())) {
            throw new CommRunException("非草稿状态数据无法删除");
        }
        // 删除课程资源-基本信息数据
        aiCourseResourceInfoMapper.deleteById(id);

        // 删除课程资源-学习资料附件数据
        aiCourseResourceAttachService.deleteByResourceId(id);

        switch (aiCourseResourceInfo.getCourseType()) {
            // 删除课程资源-教学课程数据
            case AiCoachConstant.CourseResourceType.TEACH:
                aiTeachInfoService.deleteByResourceId(id);
                break;
            // 删除课程资源-AI陪练数据
            case AiCoachConstant.CourseResourceType.TRAIN:
                aiTrainInfoService.deleteByResourceId(id);
                break;
            default:
                throw new CommRunException("非法的课程类型");
        }
        return true;
    }

    @Override
    public void verify(AiCourseResourceInfoSaveRequest request) {
        LambdaQueryWrapper<AiCourseResourceInfo> queryWrapper = new LambdaQueryWrapper<>();

        // 校验-是否允许修改
        if (request.getId() != null) {
            queryWrapper.eq(AiCourseResourceInfo::getId, request.getId());
            queryWrapper.eq(AiCourseResourceInfo::getCourseStatus, AiCoachConstant.CourseResourceStatus.SUBMIT);
            Integer existSubmit = aiCourseResourceInfoMapper.selectCount(queryWrapper);
            if (existSubmit > 0) {
                throw new CommRunException("无法修改已提交数据");
            }
        }

        // 校验-课程资源名称
        queryWrapper.clear();
        queryWrapper.eq(AiCourseResourceInfo::getCourseName, request.getCourseName());
        queryWrapper.ne(request.getId() != null, AiCourseResourceInfo::getId, request.getId());
        Integer existCourseName = aiCourseResourceInfoMapper.selectCount(queryWrapper);
        if (existCourseName > 0) {
            throw new CommRunException("课程名称已存在");
        }
    }

}

