package com.deloitte.dhr.ai.module.coach.train.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 课程资源-AI陪练-对象表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_train_object")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-AI陪练-对象表")
public class AiTrainObject extends SuperLogicModel<AiTrainObject> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 陪练对象头像URL
     */
    @TableField(value = "object_avatar_url")
    private String objectAvatarUrl;

    /**
     * 陪练对象名称
     */
    @TableField(value = "object_name")
    private String objectName;

    /**
     * 陪练对象背景信息
     */
    @TableField(value = "object_background_info")
    private String objectBackgroundInfo;

    /**
     * 陪练对象性别;male：男，female：女，unknown：未知
     */
    @TableField(value = "object_gender")
    private String objectGender;


}