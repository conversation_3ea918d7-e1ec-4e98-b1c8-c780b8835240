package com.deloitte.dhr.ai.module.interview.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AI访谈-访谈任务-列表-筛选条件
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewTaskListRequest")
public class AiInterviewTaskListRequest {

    /**
     * AI访谈任务名称
     */
    @ApiModelProperty(value = "AI访谈任务名称", name = "taskName")
    private String taskName;

    /**
     * AI访谈任务-状态;CG：草稿，JXZ：进行中，YWC：已完成，YJS：已结束
     */
    @ApiModelProperty(value = "AI访谈任务-状态;CG：草稿，JXZ：进行中，YWC：已完成，YJS：已结束", name = "taskStatus")
    private String taskStatus;

}