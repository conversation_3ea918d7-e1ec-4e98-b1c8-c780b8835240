package com.deloitte.dhr.ai.module.sr.pojo;


import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 阿里云-录音文件识别极速版-返回
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
public class AliyunFlashRecognizerResponse {

    /**
     * 任务ID
     */
    @JSONField(name = "task_id")
    private String taskId;

    /**
     * 识别结果
     */
    @JSONField(name = "result")
    private String result;

    /**
     * 接口返回状态
     */
    @JSONField(name = "status")
    private Integer status;

    /**
     * 接口返回状态对应消息
     */
    @JSONField(name = "message")
    private String message;

    /**
     * 识别结果
     */
    @JSONField(name = "flash_result")
    private FlashResult flashResult;

    /**
     * 识别结果
     */
    @Data
    public static class FlashResult {

        /**
         * 音频时长，单位：毫秒。
         */
        @JSONField(name = "duration")
        private Integer duration;

        /**
         * 服务端处理时长，单位：毫秒。
         */
        @JSONField(name = "latency")
        private Integer latency;

        /**
         * 是否完成
         */
        @JSONField(name = "completed")
        private Boolean completed;

        /**
         * 句子
         */
        @JSONField(name = "sentences")
        private List<Sentence> sentences;

    }

    /**
     * 句子
     */
    @Data
    public static class Sentence {

        /**
         * 句子级别的识别结果
         */
        @JSONField(name = "text")
        private String text;

        /**
         * 句子的开始时间，单位：毫秒
         */
        @JSONField(name = "begin_time")
        private Integer beginTime;

        /**
         * 句子的结束时间，单位：毫秒
         */
        @JSONField(name = "end_time")
        private Integer endTime;

        /**
         * 多个声道的音频文件会区分返回识别结果，声道ID从0计数
         */
        @JSONField(name = "channel_id")
        private Integer channelId;

        /**
         * 每个句子包含的词
         */
        @JSONField(name = "words")
        private List<Word> words;

    }

    /**
     * 词
     */
    @Data
    public static class Word {

        /**
         * 当前句子包含的词信息
         */
        @JSONField(name = "text")
        private String text;

        /**
         * 当前词开始时间，单位：毫秒
         */
        @JSONField(name = "begin_time")
        private Integer beginTime;

        /**
         * 当前词结束时间，单位：毫秒
         */
        @JSONField(name = "end_time")
        private Integer endTime;

        /**
         * 当前词尾的标点信息，没有标点则为空
         */
        @JSONField(name = "punc")
        private String punc;
    }

}
