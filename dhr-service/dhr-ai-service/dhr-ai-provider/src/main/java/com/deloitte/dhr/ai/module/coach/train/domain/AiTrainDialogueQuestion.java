package com.deloitte.dhr.ai.module.coach.train.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 课程资源-AI陪练-对话问题表
 *
 * <AUTHOR>
 * @date 2024-01-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_train_dialogue_question")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-AI陪练-对话问题表")
public class AiTrainDialogueQuestion extends SuperLogicModel<AiTrainDialogueQuestion> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * AI题目ID（AI模型生成）
     */
    @TableField(value = "ai_question_id")
    private String aiQuestionId;

    /**
     * AI题目语音文件URL
     */
    @TableField(value = "ai_question_voice_file_url")
    private String aiQuestionVoiceFileUrl;

    /**
     * 题目名称
     */
    @TableField(value = "question_name")
    private String questionName;

    /**
     * 题目参考答案
     */
    @TableField(value = "sample_answer_content")
    private String sampleAnswerContent;

    /**
     * 题目顺序
     */
    @TableField(value = "question_order")
    private Integer questionOrder;

}