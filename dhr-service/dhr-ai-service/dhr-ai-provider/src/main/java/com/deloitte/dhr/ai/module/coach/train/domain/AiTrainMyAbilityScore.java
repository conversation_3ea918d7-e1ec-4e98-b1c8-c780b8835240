package com.deloitte.dhr.ai.module.coach.train.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 课程资源-AI陪练-我的能力分析得分表
 *
 * <AUTHOR>
 * @date 2024-01-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_train_my_ability_score")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源-AI陪练-我的能力分析得分表")
public class AiTrainMyAbilityScore extends SuperLogicModel<AiTrainMyAbilityScore> {

    /**
     * 培训任务员工ID
     */
    @TableField(value = "task_emp_id")
    private Long taskEmpId;

    /**
     * 对话记录ID
     */
    @TableField(value = "history_id")
    private Long historyId;

    /**
     * 能力分析ID
     */
    @TableField(value = "ability_analyse_id")
    private Long abilityAnalyseId;

    /**
     * 能力分析得分
     */
    @TableField(value = "ability_analyse_score")
    private BigDecimal abilityAnalyseScore;

    /**
     * 能力分析描述
     */
    @TableField(value = "ability_analyse_desc")
    private String abilityAnalyseDesc;

}