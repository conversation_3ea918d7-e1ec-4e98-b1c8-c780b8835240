package com.deloitte.dhr.ai.module.coach.teach.service;

import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachTextSampleAnswer;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachTextSampleAnswerResponse;
import com.deloitte.dhr.common.SuperService;

import java.util.Map;

/**
 * 课程资源-教学课程-考核题目-文本题参考答案-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTeachTextSampleAnswerService extends SuperService<AiTeachTextSampleAnswer> {

    /**
     * 获取文本试题的参考答案信息
     *
     * @param resourceId 课程资源ID
     * @return Map<Long, AiTeachTextSampleAnswerDetailResponse>
     */
    Map<Long, AiTeachTextSampleAnswerResponse> getMapByResourceId(Long resourceId);

    /**
     * 删除数据
     *
     * @param resourceId 课程资源ID
     */
    void deleteByResourceId(Long resourceId);


}
