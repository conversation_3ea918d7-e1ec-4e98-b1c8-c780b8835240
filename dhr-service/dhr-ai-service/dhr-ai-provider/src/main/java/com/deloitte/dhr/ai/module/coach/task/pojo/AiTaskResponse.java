package com.deloitte.dhr.ai.module.coach.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 培训任务-信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTaskResponse")
public class AiTaskResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 课程资源ID
     */
    @ApiModelProperty(value = "课程资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * 课程分类;JXKC：教学课程，AIPL：AI陪练
     */
    @ApiModelProperty(value = "课程分类;JXKC：教学课程，AIPL：AI陪练", name = "courseType")
    private String courseType;

    /**
     * 培训任务名称
     */
    @ApiModelProperty(value = "培训任务名称", name = "taskName")
    private String taskName;

    /**
     * 培训开始时间
     */
    @ApiModelProperty(value = "培训开始时间", name = "startDateTime")
    private Date startDateTime;

    /**
     * 培训结束时间
     */
    @ApiModelProperty(value = "培训结束时间", name = "endDateTime")
    private Date endDateTime;

    /**
     * 培训封面URL
     */
    @ApiModelProperty(value = "培训封面URL", name = "taskCoverUrl")
    private String taskCoverUrl;

    /**
     * 培训任务时长（hour）
     */
    @ApiModelProperty(value = "培训任务时长（hour）", name = "taskDuration")
    private BigDecimal taskDuration;

    /**
     * 培训任务状态;CG：草稿，PXZ：培训中，YWC：已完成
     */
    @ApiModelProperty(value = "培训任务状态;CG：草稿，PXZ：培训中，YWC：已完成", name = "taskStatus")
    private String taskStatus;

    /**
     * 培训员工总人数
     */
    @ApiModelProperty(value = "培训任务-员工总人数", name = "totalEmpNum")
    private Integer totalEmpNum;

    /**
     * 培训员工未完成人数
     */
    @ApiModelProperty(value = "培训任务-员工未完成人数", name = "unCompletedEmpNum")
    private Integer unCompletedEmpNum;

    /**
     * 培训员工已完成人数
     */
    @ApiModelProperty(value = "课程资源-员工已完成人数", name = "completedEmpNum")
    private Integer completedEmpNum;

}