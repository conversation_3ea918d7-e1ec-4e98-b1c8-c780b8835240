package com.deloitte.dhr.ai.module.coach.resource.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 课程资源表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_course_resource_info")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程资源表")
public class AiCourseResourceInfo extends SuperLogicModel<AiCourseResourceInfo> {

    /**
     * 类别ID
     */
    @TableField(value = "category_id")
    private Long categoryId;

    /**
     * 课程名称
     */
    @TableField(value = "course_name")
    private String courseName;

    /**
     * 课程分类;JXKC：教学课程，AIPL：AI陪练
     */
    @TableField(value = "course_type")
    private String courseType;

    /**
     * 课程封面图链接
     */
    @TableField(value = "course_cover_url")
    private String courseCoverUrl;

    /**
     * 课程时长（minute）
     */
    @TableField(value = "course_duration")
    private BigDecimal courseDuration;

    /**
     * 课程状态：CG：草稿，TJ：提交
     */
    @TableField(value = "course_status")
    private String courseStatus;

    /**
     * AI生成操作的记录ID
     */
    @TableField(value = "record_id")
    private Long recordId;

}