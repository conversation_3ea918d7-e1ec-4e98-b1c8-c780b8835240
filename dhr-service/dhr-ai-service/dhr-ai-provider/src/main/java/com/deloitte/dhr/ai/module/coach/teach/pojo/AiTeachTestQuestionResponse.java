package com.deloitte.dhr.ai.module.coach.teach.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 课程资源-教学课程-考核题目信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("AiTeachTestQuestionResponse")
public class AiTeachTestQuestionResponse extends AiTeachTestQuestionIgnoreParam {

    /**
     * 题目ID
     */
    @ApiModelProperty(value = "题目ID", name = "id")
    private Long id;

    /**
     * 课程资源ID
     */
    @ApiModelProperty(value = "课程资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * AI题目ID
     */
    @ApiModelProperty(value = "AI题目ID", name = "aiQuestionId")
    private String aiQuestionId;

    /**
     * 题目名称
     */
    @ApiModelProperty(value = "题目名称", name = "questionName")
    private String questionName;

    /**
     * 题目类型;DXT：单选题，PDT：判断题，WDT：问答题
     */
    @ApiModelProperty(value = "题目类型;DXT：单选题，PDT：判断题，WDT：问答题", name = "questionType")
    private String questionType;

    /**
     * 题目顺序
     */
    @ApiModelProperty(value = "题目顺序", name = "questionOrder")
    private Integer questionOrder;

    /**
     * 题目来源（用对象接收，直接返回AI模型吐出的数据，不做处理）
     */
    @ApiModelProperty(value = "题目来源", name = "questionSource")
    private AiTeachTestQuestionSourceResponse questionSource;

    /**
     * 题目内容（用对象接收，兼容各种题目类型）
     */
    @ApiModelProperty(value = "题目内容", name = "questionContent")
    private Object questionContent;

}