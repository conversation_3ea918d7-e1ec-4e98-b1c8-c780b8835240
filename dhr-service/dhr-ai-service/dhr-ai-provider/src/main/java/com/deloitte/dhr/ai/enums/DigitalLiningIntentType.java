package com.deloitte.dhr.ai.enums;


import lombok.Getter;

/**
 * SSC意图识别类型枚举
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public enum DigitalLiningIntentType {

    RS("RS", "人事"),
    ZZ("ZZ", "组织"),
    XC("XC", "薪酬"),
    KQ("KQ", "考勤"),
    J<PERSON>("JX", "绩效"),
    TY("TY", "通用");

    private final String code;

    private final String name;


    DigitalLiningIntentType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 通过name获取枚举
     *
     * @param name 名称
     * @return IntentRecognitionType
     */
    public static DigitalLiningIntentType getByName(String name) {
        DigitalLiningIntentType result = null;
        for (DigitalLiningIntentType type : DigitalLiningIntentType.values()) {
            if (type.getName().equals(name)) {
                result = type;
            }
        }
        if (result == null) {
            return TY;
        }
        return result;
    }
}
