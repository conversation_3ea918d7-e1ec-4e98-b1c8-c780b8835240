package com.deloitte.dhr.ai.module.coach.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课程资源-AI陪练-我的总体概括
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainMyOverviewResponse")
public class AiTrainMyOverviewResponse {

    /**
     * 最终得分
     */
    @ApiModelProperty(value = "最终得分", name = "finalScore")
    private BigDecimal finalScore;

    /**
     * 评分结果;0：不通过，1：通过
     */
    @ApiModelProperty(value = "评分结果;0：不通过，1：通过", name = "scoreResult")
    private Boolean scoreResult;

    /**
     * 消耗时间（minute）
     */
    @ApiModelProperty(value = "消耗时间（minute）", name = "elapsedTime")
    private BigDecimal elapsedTime;

    /**
     * 得分项
     */
    @ApiModelProperty(value = "得分项", name = "scoringItemInfoList")
    private List<AiTrainMyScoringItemResponse> scoringItemInfoList;

}