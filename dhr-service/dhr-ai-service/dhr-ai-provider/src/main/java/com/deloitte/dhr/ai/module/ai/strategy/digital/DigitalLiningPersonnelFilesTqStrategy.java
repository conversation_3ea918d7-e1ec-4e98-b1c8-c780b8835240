package com.deloitte.dhr.ai.module.ai.strategy.digital;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.deloitte.dhr.ai.constant.SscConst;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.DigitalLiningIntentAction;
import com.deloitte.dhr.ai.enums.DigitalLiningIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.DigitalLiningActionStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.strategy.digital.pojo.DigitalLiningPersonnelFilesTqResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 数字人(李宁)-人事档案-事务查询-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@DigitalLiningActionStrategyType(item = DigitalLiningIntentItem.RSDA, action = DigitalLiningIntentAction.TQ)
public class DigitalLiningPersonnelFilesTqStrategy implements DigitalLiningStrategy {

    @Autowired
    private DigitalLiningCommonTqService commonTqService;
    @Autowired
    private DigitalLiningCommonIrStrategy commonIrStrategy;


    @Override
    public Flux<List<AiContent>> executeStream(Map<String, Object> param) {

        // 查询人事档案信息
        DigitalLiningPersonnelFilesTqResponse response = queryPersonnelFilesInfo(param);
        AiContent basicInfoContent = AiContent.builder()
                .content(JSON.toJSONString(response.getHeaderInfo()))
                .contentType(AiContentType.JSON.getCode())
                .build();
        // 通过AI查询识别过滤出来的信息
        return Flux.just(List.of(basicInfoContent)).concatWith(commonIrStrategy.executeStream(param));
    }

    /**
     * 查询我的人事档案信息
     *
     * @param param 请求参数
     * @return PersonnelFilesTqResponse
     */
    private DigitalLiningPersonnelFilesTqResponse queryPersonnelFilesInfo(Map<String, Object> param) {
        DigitalLiningPersonnelFilesTqResponse response = new DigitalLiningPersonnelFilesTqResponse();
        Object queryTypeObj = param.get("selfOrOther");
        if (Objects.isNull(queryTypeObj)) {
            return response;
        }
        String empCode;
        switch (queryTypeObj.toString()) {
            case SscConst.OTHER:
                String staffName = String.valueOf(param.get("staffName"));
                // todo 通过员工姓名查询员工编号，暂时设置为登录用户
                empCode = commonTqService.getLoginEmpCode();
                break;
            case SscConst.MYSELF:
            default:
                empCode = commonTqService.getLoginEmpCode();
        }
        // 查询人事档案信息

        Map<String, List<Map<String, String>>> map = new HashMap<>();
        param.put("context", JSON.toJSONString(map));
        // 获取其中的头信息
        if (CollUtil.isNotEmpty(map.get("人事信息"))) {
            Map<String, String> childMap = map.get("人事信息").get(0);
            response.setHeaderInfo(new DigitalLiningPersonnelFilesTqResponse.HeaderInfo(childMap.get("员工编号"), childMap.get("员工姓名"), childMap.get("员工组")));
        }
        return response;
    }

}
