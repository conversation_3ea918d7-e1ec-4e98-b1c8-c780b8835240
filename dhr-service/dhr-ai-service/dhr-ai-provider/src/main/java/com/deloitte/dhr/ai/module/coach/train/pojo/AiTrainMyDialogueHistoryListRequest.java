package com.deloitte.dhr.ai.module.coach.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 课程资源-AI陪练-我的对话历史记录-查询条件
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainMyDialogueHistoryListRequest")
@AllArgsConstructor
@NoArgsConstructor
public class AiTrainMyDialogueHistoryListRequest {

    /**
     * AI培训任务员工ID
     */
    @NotNull(message = "培训任务员工ID不能为空")
    @ApiModelProperty(value = "AI培训任务员工ID", name = "taskEmpId")
    private Long taskEmpId;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间", name = "sendTime")
    private Date sendTime;

    /**
     * 条数，默认20条
     */
    @ApiModelProperty(value = "条数", name = "size")
    private Integer size = 20;

}