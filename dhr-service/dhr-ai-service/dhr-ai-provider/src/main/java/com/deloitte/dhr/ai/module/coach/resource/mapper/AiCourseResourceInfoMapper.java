package com.deloitte.dhr.ai.module.coach.resource.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.coach.resource.domain.AiCourseResourceInfo;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceInfoResponse;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceListRequest;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程资源-相关持久化接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiCourseResourceInfoMapper extends SuperMapper<AiCourseResourceInfo> {

    /**
     * 分页查询数据
     *
     * @param page  分页参数
     * @param param 过滤条件
     * @param order 排序条件
     * @return 列表数据
     */
    List<AiCourseResourceInfoResponse> findListPage(Page<AiCourseResourceInfo> page, @Param(value = "param") AiCourseResourceListRequest param, @Param(value = "order") String order);

    /**
     * 统计被引用的课程类别数
     *
     * @param categoryIds 类别ID
     * @return Long
     */
    Long countByCategoryIds(@Param("categoryIds") List<Long> categoryIds);

}