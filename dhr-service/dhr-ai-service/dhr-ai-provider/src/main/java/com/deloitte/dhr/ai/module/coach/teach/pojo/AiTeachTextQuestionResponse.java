package com.deloitte.dhr.ai.module.coach.teach.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 课程资源-教学课程-文本题信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachTextQuestionResponse")
public class AiTeachTextQuestionResponse {

    /**
     * 题目ID
     */
    @ApiModelProperty(value = "题目ID", name = "questionId")
    private Long questionId;

    /**
     * AI题目ID
     */
    @ApiModelProperty(value = "AI题目ID", name = "aiQuestionId")
    private String aiQuestionId;

    /**
     * 题目名称
     */
    @ApiModelProperty(value = "题目名称", name = "questionName")
    private String questionName;

    /**
     * 文本题参考答案
     */
    @ApiModelProperty(value = "文本题参考答案", name = "sampleAnswerContent")
    private String sampleAnswerContent;

}