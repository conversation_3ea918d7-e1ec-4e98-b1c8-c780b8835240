package com.deloitte.dhr.ai.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * AI模型配置
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ai.model")
public class AiModelConfig {

    /**
     * 基础路径
     */
    private String baseUrl;

    /**
     * AI agents
     */
    private Map<String, Agent> agents;

    /**
     * AI 知识库
     */
    private Datasets datasets;


    @Data
    public static class Agent {

        /**
         * 请求路径
         */
        private String suffixUrl;

        /**
         * 请求密钥
         */
        private String apiKey;

    }

    @Data
    public static class Datasets {

        /**
         * 请求密钥
         */
        private String apiKey;

        /**
         * 知识库ID
         */
        private Map<String, String> datasetIds;

        /**
         * url
         */
        private Map<String, String> suffixUrl;

    }

}
