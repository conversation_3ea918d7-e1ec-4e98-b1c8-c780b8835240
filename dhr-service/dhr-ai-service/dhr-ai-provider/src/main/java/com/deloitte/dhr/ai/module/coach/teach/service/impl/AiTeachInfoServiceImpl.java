package com.deloitte.dhr.ai.module.coach.teach.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceInfoResponse;
import com.deloitte.dhr.ai.module.coach.resource.service.AiCourseResourceAttachService;
import com.deloitte.dhr.ai.module.coach.resource.service.AiCourseResourceInfoService;
import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachInfo;
import com.deloitte.dhr.ai.module.coach.teach.mapper.AiTeachInfoMapper;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachDetailResponse;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachInfoResponse;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachInfoSaveRequest;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachSaveRequest;
import com.deloitte.dhr.ai.module.coach.teach.service.AiTeachInfoService;
import com.deloitte.dhr.ai.module.coach.teach.service.AiTeachTestQuestionService;
import com.deloitte.dhr.common.SuperServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 课程资源-教学课程-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiTeachInfoServiceImpl extends SuperServiceImpl<AiTeachInfoMapper, AiTeachInfo> implements AiTeachInfoService {

    @Autowired
    private AiTeachInfoMapper aiTeachInfoMapper;
    @Autowired
    private AiCourseResourceInfoService aiCourseResourceInfoService;
    @Autowired
    private AiTeachTestQuestionService aiTeachTestQuestionService;
    @Autowired
    private AiCourseResourceAttachService aiCourseResourceAttachService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveAll(AiTeachSaveRequest request, String courseStatus) {

        // 保存/更新前-校验
        this.verify(request);

        // 保存/更新-课程资源-基本信息
        Long resourceId = aiCourseResourceInfoService.saveData(request.getResourceInfo(), AiCoachConstant.CourseResourceType.TEACH, courseStatus);

        // 保存/更新-课程资源-学习附件信息
        aiCourseResourceAttachService.saveData(request.getAttachInfoList(), resourceId);

        // 保存/更新-教学课程-基本信息
        this.saveData(request.getTeachInfo(), resourceId);

        // 全量保存-教学课程-考核内容信息（题目数据）
        aiTeachTestQuestionService.saveData(request.getQuestionInfoList(), resourceId);

        return resourceId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitAll(AiTeachSaveRequest request) {

        // 保存数据
        Long id = this.saveAll(request, AiCoachConstant.CourseResourceStatus.SUBMIT);

        // 异步上传文件到知识库
        aiCourseResourceAttachService.uploadFile2DatasetsAsync(request.getAttachInfoList());
        return id;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveData(AiTeachInfoSaveRequest teachInfo, Long resourceId) {
        LambdaQueryWrapper<AiTeachInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTeachInfo::getResourceId, resourceId);
        AiTeachInfo aiTeachInfo = aiTeachInfoMapper.selectOne(queryWrapper);
        if (aiTeachInfo == null) {
            aiTeachInfo = BeanUtil.copyProperties(teachInfo, AiTeachInfo.class);
        } else {
            BeanUtil.copyProperties(teachInfo, aiTeachInfo);
        }
        aiTeachInfo.setResourceId(resourceId);
        return this.saveOrUpdate(aiTeachInfo);
    }

    @Override
    public AiTeachDetailResponse getTeachDetail(Long resourceId) {

        // 查询-课程资源-基本信息详情数据
        AiCourseResourceInfoResponse resourceInfo = aiCourseResourceInfoService.getDetail(resourceId);
        if (!StrUtil.equals(resourceInfo.getCourseType(), AiCoachConstant.CourseResourceType.TEACH)) {
            return null;
        }
        AiTeachDetailResponse response = new AiTeachDetailResponse();
        response.setResourceInfo(resourceInfo);

        // 查询-课程资源-学习附件信息详情数据
        response.setAttachInfoList(aiCourseResourceAttachService.getByResourceId(resourceId));

        // 查询-教学课程-基本信息数据
        response.setTeachInfo(getByResourceId(resourceId));

        // 查询-教学课程-考核内容数据（题目数据）
        response.setQuestionInfoList(aiTeachTestQuestionService.getByResourceId(resourceId));

        return response;
    }

    @Override
    public AiTeachInfoResponse getByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiTeachInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTeachInfo::getResourceId, resourceId);
        AiTeachInfo aiTeachInfo = aiTeachInfoMapper.selectOne(queryWrapper);
        return BeanUtil.copyProperties(aiTeachInfo, AiTeachInfoResponse.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByResourceId(Long resourceId) {
        // 删除教学课程-基本信息数据
        LambdaQueryWrapper<AiTeachInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTeachInfo::getResourceId, resourceId);
        aiTeachInfoMapper.delete(queryWrapper);

        // 删除课程-考核内容数据（题目相关数据）
        aiTeachTestQuestionService.deleteAllByResourceId(resourceId);
    }


    /**
     * 保存/更新前校验参数
     *
     * @param request 保存参数
     */
    private void verify(AiTeachSaveRequest request) {

        // 校验-课程资源
        aiCourseResourceInfoService.verify(request.getResourceInfo());

        // 校验-考核题目
        // aiTeachTestQuestionService.verify(request.getQuestionInfoList());

    }

}

