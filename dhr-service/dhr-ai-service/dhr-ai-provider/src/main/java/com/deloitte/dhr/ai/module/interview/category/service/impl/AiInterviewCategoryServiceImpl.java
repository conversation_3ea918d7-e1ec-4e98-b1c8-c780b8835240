package com.deloitte.dhr.ai.module.interview.category.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.module.interview.category.domain.AiInterviewCategory;
import com.deloitte.dhr.ai.module.interview.category.mapper.AiInterviewCategoryMapper;
import com.deloitte.dhr.ai.module.interview.category.pojo.AiInterviewCategoryResponse;
import com.deloitte.dhr.ai.module.interview.category.pojo.AiInterviewCategorySaveRequest;
import com.deloitte.dhr.ai.module.interview.category.pojo.AiInterviewCategoryTreeResponse;
import com.deloitte.dhr.ai.module.interview.category.service.AiInterviewCategoryService;
import com.deloitte.dhr.ai.module.interview.constant.AiInterviewConstant;
import com.deloitte.dhr.ai.module.interview.resource.mapper.AiInterviewInfoMapper;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * AI访谈-类别-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class AiInterviewCategoryServiceImpl extends SuperServiceImpl<AiInterviewCategoryMapper, AiInterviewCategory> implements AiInterviewCategoryService {

    @Autowired
    private AiInterviewCategoryMapper aiInterviewCategoryMapper;
    @Autowired
    private AiInterviewInfoMapper aiInterviewInfoMapper;

    @Override
    public AiInterviewCategoryResponse getDetail(Long id) {
        return BeanUtil.copyProperties(this.get(id), AiInterviewCategoryResponse.class);
    }

    @Override
    public List<AiInterviewCategoryTreeResponse> getTree(String categoryName) {
        LambdaQueryWrapper<AiInterviewCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(categoryName), AiInterviewCategory::getCategoryName, categoryName);
        List<AiInterviewCategory> list = aiInterviewCategoryMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<AiInterviewCategoryTreeResponse> responseList = BeanUtil.copyToList(list, AiInterviewCategoryTreeResponse.class);
        Map<Long, List<AiInterviewCategoryTreeResponse>> map = responseList.stream().collect(Collectors.groupingBy(AiInterviewCategoryTreeResponse::getParentId));
        Set<Long> ids = responseList.stream().map(AiInterviewCategoryTreeResponse::getId).collect(Collectors.toSet());
        List<AiInterviewCategoryTreeResponse> rootList = responseList.stream().filter(response -> !ids.contains(response.getParentId())).collect(Collectors.toList());
        // 组装树
        rootList.forEach(rootResponse -> rootResponse.setChildren(getChildren(rootResponse, map, 1)));
        return rootList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveData(AiInterviewCategorySaveRequest request) {
        AiInterviewCategory aiInterviewCategory = BeanUtil.copyProperties(request, AiInterviewCategory.class);
        if (!this.saveOrUpdate(aiInterviewCategory)) {
            return aiInterviewCategory.getId();
        }
        String categoryPath = aiInterviewCategory.getParentId() == 0L ? aiInterviewCategory.getId() + "" : aiInterviewCategoryMapper.getCategoryPathById(aiInterviewCategory.getParentId()) + "," + aiInterviewCategory.getId();
        // 更新"类别全路径"字段
        aiInterviewCategory.setCategoryPath(categoryPath);
        this.update(aiInterviewCategory);
        return aiInterviewCategory.getId();
    }

    @Override
    public Boolean deleteById(Long id) {

        // 获取当前节点以及所有下级节点
        List<Long> ids = aiInterviewCategoryMapper.getCurrentAndAllChildId(id);
        if (CollUtil.isEmpty(ids)) {
            return true;
        }

        // 校验类别是否被引用，被引用了则不能删除
        Long count = aiInterviewInfoMapper.countByCategoryIds(ids);
        if (count > 0) {
            throw new CommRunException("该类别或下级类别已被AI访谈引用，无法删除");
        }
        return this.removeByIds(ids);
    }

    @Override
    public String getCategoryPathNameById(Long id) {
        AiInterviewCategory InterviewCategory = aiInterviewCategoryMapper.selectById(id);
        if (InterviewCategory == null) {
            return null;
        }
        List<String> ids = StrUtil.split(InterviewCategory.getCategoryPath(), ",");
        List<AiInterviewCategory> list = aiInterviewCategoryMapper.selectBatchIds(ids);
        List<String> nameList = list.stream().map(AiInterviewCategory::getCategoryName).collect(Collectors.toList());
        return StrUtil.join("/", nameList);
    }

    /**
     * 获取子节点
     *
     * @param rootResponse 跟节点
     * @param map          总数据
     * @param deep         递归深度
     * @return List<AiInterviewCategoryTreeResponse>
     */
    private List<AiInterviewCategoryTreeResponse> getChildren(AiInterviewCategoryTreeResponse rootResponse, Map<Long, List<AiInterviewCategoryTreeResponse>> map, Integer deep) {
        List<AiInterviewCategoryTreeResponse> childList = map.get(rootResponse.getId());
        if (childList == null || AiInterviewConstant.MAX_DEEP.equals(deep)) {
            return null;
        }
        deep++;
        for (AiInterviewCategoryTreeResponse child : childList) {
            child.setChildren(getChildren(child, map, deep));
        }
        return childList;
    }

}

