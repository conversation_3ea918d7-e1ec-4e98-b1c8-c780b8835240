package com.deloitte.dhr.ai.module.interview.resource.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.interview.constant.AiInterviewConstant;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewInfoResponse;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewListRequest;
import com.deloitte.dhr.ai.module.interview.resource.service.AiInterviewInfoService;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewDetailResponse;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewSaveRequest;
import com.deloitte.dhr.ai.module.interview.validation.Save;
import com.deloitte.dhr.ai.module.interview.validation.Submit;
import com.deloitte.dhr.common.Request;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * AI访谈助手
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/AiInterviewInfo")
@Api(tags = "AI访谈助手")
@Validated
public class AiInterviewInfoController extends SuperController {

    @Autowired
    private AiInterviewInfoService aiInterviewInfoService;

    @ApiOperation(value = "列表", notes = "列表")
    @ApiOperationSupport(order = 1)
    @PostMapping("/list/{current}/{size}")
    public ResponseVO<ResponsePage<AiInterviewInfoResponse>> list(@PathVariable("current") Long current, @PathVariable("size") Long size, @RequestBody Request<AiInterviewListRequest> request) {
        return success(aiInterviewInfoService.findListPage(new Page<>(current, size), request.get(), request.getOrder()));
    }

    @ApiOperation(value = "删除", notes = "删除")
    @ApiOperationSupport(order = 2)
    @PostMapping("/del")
    public ResponseVO<Boolean> del(@RequestParam("id") Long id) {
        return success(aiInterviewInfoService.deleteById(id));
    }

    @ApiOperation(value = "详情", notes = "详情")
    @ApiOperationSupport(order = 3)
    @GetMapping("/detail/{resourceId}")
    public ResponseVO<AiInterviewDetailResponse> getInterviewDetail(@PathVariable("resourceId") Long resourceId) {
        return success(aiInterviewInfoService.getInterviewDetail(resourceId));
    }

    @ApiOperation(value = "保存", notes = "保存")
    @ApiOperationSupport(order = 4)
    @PostMapping("/save")
    public ResponseVO<Long> save(@RequestBody @Validated(Save.class) AiInterviewSaveRequest request) {
        return success(aiInterviewInfoService.saveAll(request, AiInterviewConstant.InterviewInfoStatus.DRAFT));
    }

    @ApiOperation(value = "提交", notes = "提交")
    @ApiOperationSupport(order = 5)
    @PostMapping("/submit")
    public ResponseVO<Long> submit(@RequestBody @Validated(Submit.class) AiInterviewSaveRequest request) {
        return success(aiInterviewInfoService.submitAll(request));
    }

    @ApiOperation(value = "复制", notes = "复制")
    @ApiOperationSupport(order = 3)
    @PostMapping("/copyInterview/{resourceId}")
    public ResponseVO<Long> copyInterview(@PathVariable("resourceId") Long resourceId) {
        return success(aiInterviewInfoService.copyInterview(resourceId));
    }
}
