package com.deloitte.dhr.ai.module.interview.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * AI访谈-访谈任务-基本信息
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewTaskInfoResponse")
public class AiInterviewTaskInfoResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * AI访谈资源ID
     */
    @ApiModelProperty(value = "AI访谈资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * 访谈任务名称
     */
    @ApiModelProperty(value = "访谈任务名称", name = "taskName")
    private String taskName;

    /**
     * AI访谈任务-开始时间
     */
    @ApiModelProperty(value = "AI访谈任务-开始时间", name = "startDateTime")
    private Date startDateTime;

    /**
     * AI访谈任务-结束时间
     */
    @ApiModelProperty(value = "AI访谈任务-结束时间", name = "endDateTime")
    private Date endDateTime;

    /**
     * AI访谈任务-封面URL
     */
    @ApiModelProperty(value = "AI访谈任务-封面URL", name = "taskCoverUrl")
    private String taskCoverUrl;

    /**
     * AI访谈任务-状态;CG：草稿，JXZ：进行中，YWC：已完成，YJS：已结束
     */
    @ApiModelProperty(value = "AI访谈任务-状态;CG：草稿，JXZ：进行中，YWC：已完成，YJS：已结束", name = "taskStatus")
    private String taskStatus;

}