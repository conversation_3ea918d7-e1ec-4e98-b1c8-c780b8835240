package com.deloitte.dhr.ai.config;

import cn.hutool.json.JSONUtil;
import com.deloitte.dhr.common.base.constants.CommonConstant;
import com.deloitte.dhr.common.base.constants.OauthConstant;
import com.deloitte.dhr.common.base.pojo.UserDto;
import com.deloitte.dhr.common.base.utils.EncryptUtil;
import com.deloitte.dhr.common.base.utils.LanguageUtil;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import com.deloitte.dhr.excel.parser.util.RequestHeaderHandler;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * feign调用之前拦截
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 11/05/2022
 */
@Slf4j
@Component
public class FeignInterceptor implements RequestInterceptor {


    @Override
    public void apply(RequestTemplate requestTemplate) {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        if (authentication != null) {
            Map<String, Object> jsonToken = new HashMap<>(16);
            UserDto userDto = (UserDto) authentication.getPrincipal();
            jsonToken.put(OauthConstant.OAUTH_USER_DETAIL, authentication.getPrincipal());
            List<String> authorities = authentication.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList());
            jsonToken.put(OauthConstant.OAUTH_AUTHORITIES, authorities);
            //把身份信息和权限信息放在json中，加入http的header中,转发给微服务

            requestTemplate.header(OauthConstant.JSON_TOKEN, EncryptUtil.encodeUTF8StringBase64(JSONUtil.toJsonStr(jsonToken)));
            requestTemplate.header(OauthConstant.AUTHORIZE_TOKEN, OauthConstant.AUTHORIZE_BEARER + userDto.getAuthorizationString());
        }
        requestTemplate.header(CommonConstant.ACCEPT_LANG, LanguageUtil.getLanguage());
        //request为空则为异步调用feign,则从threadLocal里取值
        HttpServletRequest httpServletRequest = LoginUtil.getHttpServletRequest();
        requestTemplate.header(OauthConstant.ACCEPT_TRACEID, httpServletRequest != null
                ? httpServletRequest.getHeader(OauthConstant.ACCEPT_TRACEID)
                : RequestHeaderHandler.getHeaderMap().get("traceid"));
        requestTemplate.header(OauthConstant.PAGE_CODE, httpServletRequest != null
                ? httpServletRequest.getHeader(OauthConstant.PAGE_CODE)
                : RequestHeaderHandler.getHeaderMap().get(OauthConstant.PAGE_CODE));
        requestTemplate.header(OauthConstant.AUTH_FLAG, httpServletRequest != null
                ? httpServletRequest.getHeader(OauthConstant.AUTH_FLAG)
                : RequestHeaderHandler.getHeaderMap().get(OauthConstant.AUTH_FLAG));
    }
}
