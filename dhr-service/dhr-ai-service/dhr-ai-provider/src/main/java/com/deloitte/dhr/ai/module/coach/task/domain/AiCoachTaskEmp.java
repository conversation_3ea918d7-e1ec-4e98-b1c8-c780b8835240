package com.deloitte.dhr.ai.module.coach.task.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 培训任务-员工表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_coach_task_emp")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("培训任务-员工表")
public class AiCoachTaskEmp extends SuperLogicModel<AiCoachTaskEmp> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 培训任务ID
     */
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * 员工编号
     */
    @TableField(value = "emp_code")
    private String empCode;

    /**
     * 员工名称
     */
    @TableField(value = "emp_name")
    private String empName;

    /**
     * 员工头像
     */
    @TableField(value = "emp_avatar_url")
    private String empAvatarUrl;

    /**
     * 员工部门编码
     */
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 员工部门名称
     */
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 员工岗位编码
     */
    @TableField(value = "position_code")
    private String positionCode;

    /**
     * 员工岗位名称
     */
    @TableField(value = "position_name")
    private String positionName;

    /**
     * 状态;WKS：未开始，JXZ：进行中，YWC：已完成，YJS：已结束
     */
    @TableField(value = "status")
    private String status;

    /**
     * 完成时间
     */
    @TableField(value = "complete_time")
    private Date completeTime;

    /**
     * 最终得分
     */
    @TableField(value = "final_score")
    private BigDecimal finalScore;

    /**
     * 评分结果;0：不通过，1：通过
     */
    @TableField(value = "score_result")
    private Boolean scoreResult;

    /**
     * 消耗时间（elapsed_time）
     */
    @TableField(value = "elapsed_time")
    private BigDecimal elapsedTime;

    /**
     * 视频文件Url
     */
    @TableField(value = "video_file_url")
    private String videoFileUrl;

}