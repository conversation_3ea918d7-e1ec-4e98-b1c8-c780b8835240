package com.deloitte.dhr.ai.module.ai.controller;


import cn.hutool.core.io.IoUtil;
import com.deloitte.dhr.ai.module.ai.pojo.AiChatTtsRequest;
import com.deloitte.dhr.ai.module.ai.service.AiTtsService;
import com.deloitte.dhr.common.SuperController;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


/**
 * AI 语音合成
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/tts")
@Api(tags = "AI TTS")
@Validated
public class AiTtsController extends SuperController {

    @Autowired
    private AiTtsService aiTtsService;

    @ApiOperation(value = "语音合成", notes = "语音合成")
    @ApiOperationSupport(order = 1)
    @PostMapping("/speechSynthesis")
    public void speechSynthesis(@Validated @RequestBody AiChatTtsRequest ttsRequest, HttpServletResponse response) {
        response.setContentType("audio/mpeg");
        response.setHeader("Content-Disposition", "inline; filename=output.mp3");
        try {
            IoUtil.write(response.getOutputStream(), true, aiTtsService.speechSynthesis(ttsRequest));
        } catch (IOException e) {
            throw new CommRunException(e.getMessage());
        }
    }

}
