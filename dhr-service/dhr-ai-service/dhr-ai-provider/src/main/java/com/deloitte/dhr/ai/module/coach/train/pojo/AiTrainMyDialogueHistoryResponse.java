package com.deloitte.dhr.ai.module.coach.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 课程资源-AI陪练-我的对话历史记录
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("AiTrainMyDialogueHistoryResponse")
public class AiTrainMyDialogueHistoryResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 头像链接
     */
    @ApiModelProperty(value = "头像链接", name = "avatarUrl")
    private String avatarUrl;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容", name = "message")
    private String message;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间", name = "sendTime")
    private Date sendTime;

    /**
     * 内容所属：AI-AI培训对象，EMP-AI培训员工
     */
    @ApiModelProperty(value = "内容所属：AI-AI培训对象，EMP-AI培训员工", name = "messageBelong")
    private String messageBelong;

    /**
     * 语音文件Url
     */
    @ApiModelProperty(value = "语音文件Url", name = "voiceFileUrl")
    private String voiceFileUrl;

}