package com.deloitte.dhr.ai.module.coach.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 培训任务-基本信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiCoachTaskInfoResponse")
public class AiCoachTaskInfoResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 课程资源ID
     */
    @ApiModelProperty(value = "课程资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * 培训任务名称
     */
    @ApiModelProperty(value = "培训任务名称", name = "taskName")
    private String taskName;

    /**
     * 培训开始时间
     */
    @ApiModelProperty(value = "培训开始时间", name = "startDateTime")
    private Date startDateTime;

    /**
     * 培训结束时间
     */
    @ApiModelProperty(value = "培训结束时间", name = "endDateTime")
    private Date endDateTime;

    /**
     * 培训封面URL
     */
    @ApiModelProperty(value = "培训封面URL", name = "taskCoverUrl")
    private String taskCoverUrl;

    /**
     * 培训任务时长（hour）
     */
    @ApiModelProperty(value = "培训任务时长（hour）", name = "taskDuration")
    private BigDecimal taskDuration;

    /**
     * 培训任务状态;CG：草稿，PXZ：培训中，YWC：已完成
     */
    @ApiModelProperty(value = "培训任务状态;CG：草稿，PXZ：培训中，YWC：已完成", name = "taskStatus")
    private String taskStatus;

}