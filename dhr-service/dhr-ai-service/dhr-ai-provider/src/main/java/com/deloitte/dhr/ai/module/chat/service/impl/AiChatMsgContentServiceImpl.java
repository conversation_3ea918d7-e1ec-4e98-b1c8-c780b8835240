package com.deloitte.dhr.ai.module.chat.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.deloitte.dhr.ai.module.chat.domain.AiChatMsgContent;
import com.deloitte.dhr.ai.module.chat.mapper.AiChatMsgContentMapper;
import com.deloitte.dhr.ai.module.chat.pojo.AiChatMsgContentSaveRequest;
import com.deloitte.dhr.ai.module.chat.service.AiChatMsgContentService;
import com.deloitte.dhr.common.SuperServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * AI聊天-聊天消息内容-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class AiChatMsgContentServiceImpl extends SuperServiceImpl<AiChatMsgContentMapper, AiChatMsgContent> implements AiChatMsgContentService {

    @Autowired
    private AiChatMsgContentMapper aiChatMsgContentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchData(List<AiChatMsgContentSaveRequest> contents) {
        if (CollUtil.isEmpty(contents)) {
            return;
        }
        List<AiChatMsgContent> msgContents = BeanUtil.copyToList(contents, AiChatMsgContent.class);
        AtomicInteger contentOrder = new AtomicInteger(1);
        msgContents.forEach(msgContent -> {
            if (msgContent.getContentOrder() == null) {
                msgContent.setContentOrder(contentOrder.getAndIncrement());
            }
        });
        this.saveBatch(msgContents);
    }

}

