package com.deloitte.dhr.ai.module.interview.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * AI访谈-访谈任务-我的访谈-完成
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewMyCompetedRequest")
public class AiInterviewMyCompetedRequest {

    /**
     * 访谈任务员工ID
     */
    @NotNull(message = "访谈任务员工ID不能为空")
    @ApiModelProperty(value = "访谈任务员工ID", name = "taskEmpId")
    private Long taskEmpId;

}