package com.deloitte.dhr.ai.module.coach.category.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 课程类别-列表树
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiCourseCategoryTreeResponse")
public class AiCourseCategoryTreeResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 类别名称
     */
    @ApiModelProperty(value = "类别名称", name = "categoryName")
    private String categoryName;

    /**
     * 类别父级ID
     */
    @ApiModelProperty(value = "类别父级ID", name = "parentId")
    private Long parentId;

    /**
     * 类别全路径
     */
    @ApiModelProperty(value = "类别全路径", name = "categoryPath")
    private String categoryPath;

    /**
     * 子集
     */
    @ApiModelProperty(value = "子集", name = "children")
    private List<AiCourseCategoryTreeResponse> children;

}