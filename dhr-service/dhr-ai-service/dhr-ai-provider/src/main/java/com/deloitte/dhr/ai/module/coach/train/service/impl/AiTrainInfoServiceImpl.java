package com.deloitte.dhr.ai.module.coach.train.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceInfoResponse;
import com.deloitte.dhr.ai.module.coach.resource.service.AiCourseResourceAttachService;
import com.deloitte.dhr.ai.module.coach.resource.service.AiCourseResourceInfoService;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachTestQuestionResponse;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachTextSampleAnswerResponse;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainDialogueQuestion;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainInfo;
import com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainInfoMapper;
import com.deloitte.dhr.ai.module.coach.train.pojo.*;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainCommunicateFrameworkService;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainDialogueQuestionService;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainInfoService;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainObjectService;
import com.deloitte.dhr.common.SuperServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 课程资源-AI陪练-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiTrainInfoServiceImpl extends SuperServiceImpl<AiTrainInfoMapper, AiTrainInfo> implements AiTrainInfoService {

    @Autowired
    private AiTrainInfoMapper aiTrainInfoMapper;
    @Autowired
    private AiCourseResourceInfoService aiCourseResourceInfoService;
    @Autowired
    private AiTrainObjectService aiTrainObjectService;
    @Autowired
    private AiTrainCommunicateFrameworkService aiTrainCommunicateFrameworkService;
    @Autowired
    private AiCourseResourceAttachService aiCourseResourceAttachService;
    @Autowired
    private AiTrainDialogueQuestionService aiTrainDialogueQuestionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveAll(AiTrainSaveRequest request, String courseStatus) {

        // 保存前校验
        this.verify(request);

        // 保存/更新-课程资源-基本信息
        Long resourceId = aiCourseResourceInfoService.saveData(request.getResourceInfo(), AiCoachConstant.CourseResourceType.TRAIN, courseStatus);

        // 保存/更新-课程资源-学习附件信息
        aiCourseResourceAttachService.saveData(request.getAttachInfoList(), resourceId);

        // 保存/更新-AI陪练-基本信息
        this.saveData(request.getAiTrainInfo(), resourceId);

        // 保存/更新-AI陪练-培训对象信息
        aiTrainObjectService.saveData(request.getObjectInfo(), resourceId);

        // 保存/更新-AI陪练-沟通框架信息
        aiTrainCommunicateFrameworkService.saveData(request.getFrameworkInfoList(), resourceId);

        // 保存/更新-AI陪练-对话提示信息
        List<AiTrainDialogueQuestion> questionList = aiTrainDialogueQuestionService.saveData(request.getQuestionInfoList(), resourceId);

        // 保存-AI问题语音信息
        if (StrUtil.equals(courseStatus, AiCoachConstant.CourseResourceStatus.SUBMIT)) {
            aiTrainDialogueQuestionService.saveAiQuestionVoice(questionList);
        }
        return resourceId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitAll(AiTrainSaveRequest request) {

        // 保存数据
        Long id = this.saveAll(request, AiCoachConstant.CourseResourceStatus.SUBMIT);

        // 异步上传文件到知识库
        aiCourseResourceAttachService.uploadFile2DatasetsAsync(request.getAttachInfoList());
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveData(AiTrainInfoSaveRequest request, Long resourceId) {
        LambdaQueryWrapper<AiTrainInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrainInfo::getResourceId, resourceId);
        AiTrainInfo aiTrainInfo = aiTrainInfoMapper.selectOne(queryWrapper);
        if (aiTrainInfo == null) {
            aiTrainInfo = BeanUtil.copyProperties(request, AiTrainInfo.class);
        } else {
            BeanUtil.copyProperties(request, aiTrainInfo);
        }
        aiTrainInfo.setResourceId(resourceId);
        return this.saveOrUpdate(aiTrainInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByResourceId(Long resourceId) {

        // 删除AI陪练-基本信息
        LambdaQueryWrapper<AiTrainInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrainInfo::getResourceId, resourceId);
        aiTrainInfoMapper.delete(queryWrapper);

        // 删除AI陪练-培训对象信息
        aiTrainObjectService.deleteByResourceId(resourceId);

        // 删除AI陪练-沟通框架信息
        aiTrainCommunicateFrameworkService.deleteByResourceId(resourceId);

        // 删除AI陪练-对话提示信息
        aiTrainDialogueQuestionService.deleteByResourceId(resourceId);

    }

    @Override
    public AiTrainDetailResponse getTrainDetail(Long resourceId) {

        // 查询-课程资源-基本信息详情数据
        AiCourseResourceInfoResponse resourceInfo = aiCourseResourceInfoService.getDetail(resourceId);
        if (!StrUtil.equals(resourceInfo.getCourseType(), AiCoachConstant.CourseResourceType.TRAIN)) {
            return null;
        }
        AiTrainDetailResponse response = new AiTrainDetailResponse();
        response.setResourceInfo(resourceInfo);

        // 查询-课程资源-学习附件信息详情数据
        response.setAttachInfoList(aiCourseResourceAttachService.getByResourceId(resourceId));

        // 查询-AI陪练-基本信息
        response.setAiTrainInfo(this.getByResourceId(resourceId));

        // 查询-AI陪练-培训对象信息
        response.setObjectInfo(aiTrainObjectService.getByResourceId(resourceId));

        // 查询-AI陪练-沟通框架信息
        response.setFrameworkInfoList(aiTrainCommunicateFrameworkService.getByResourceId(resourceId));

        // 查询-AI陪练-对话提示信息
        List<AiTrainDialogueQuestionResponse> questionList = aiTrainDialogueQuestionService.getByResourceId(resourceId);
        List<AiTeachTestQuestionResponse> questionResponseList = new ArrayList<>();
        questionList.forEach(question -> {
            AiTeachTestQuestionResponse questionResponse = BeanUtil.copyProperties(question, AiTeachTestQuestionResponse.class);
            questionResponse.setQuestionContent(new AiTeachTextSampleAnswerResponse(null, question.getSampleAnswerContent()));
            questionResponse.setQuestionType(AiCoachConstant.TeachQuestionType.ESSAY_QUESTION);
            questionResponseList.add(questionResponse);
        });
        response.setQuestionInfoList(questionResponseList);

        return response;
    }

    @Override
    public AiTrainInfoResponse getByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiTrainInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrainInfo::getResourceId, resourceId);
        AiTrainInfo aiTrainInfo = aiTrainInfoMapper.selectOne(queryWrapper);
        return BeanUtil.copyProperties(aiTrainInfo, AiTrainInfoResponse.class);
    }

    /**
     * 校验AI陪练保存参数
     *
     * @param request AI陪练保存参数
     */
    private void verify(AiTrainSaveRequest request) {

        // 校验-课程资源
        aiCourseResourceInfoService.verify(request.getResourceInfo());

        // 校验-沟通框架
        aiTrainCommunicateFrameworkService.verify(request.getFrameworkInfoList());

    }

}

