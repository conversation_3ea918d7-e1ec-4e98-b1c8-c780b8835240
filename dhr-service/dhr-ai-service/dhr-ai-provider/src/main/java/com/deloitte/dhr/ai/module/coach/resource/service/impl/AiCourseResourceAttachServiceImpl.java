package com.deloitte.dhr.ai.module.coach.resource.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.deloitte.dhr.ai.config.AiModelConfig;
import com.deloitte.dhr.ai.module.ai.pojo.AiDatasetsRequest;
import com.deloitte.dhr.ai.module.coach.resource.domain.AiCourseResourceAttach;
import com.deloitte.dhr.ai.module.coach.resource.mapper.AiCourseResourceAttachMapper;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceAttachResponse;
import com.deloitte.dhr.ai.module.coach.resource.pojo.AiCourseResourceAttachSaveRequest;
import com.deloitte.dhr.ai.module.coach.resource.service.AiCourseResourceAttachService;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.utility.api.CommonFileDfsInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 课程资源-附件-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
@Slf4j
public class AiCourseResourceAttachServiceImpl extends SuperServiceImpl<AiCourseResourceAttachMapper, AiCourseResourceAttach> implements AiCourseResourceAttachService {

    /**
     * 异步执行线程池
     */
    private static final ExecutorService CACHED_THREAD_POOL = new ThreadPoolExecutor(10, 20, 300, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), ThreadFactoryBuilder.create().setNamePrefix("upload-file-to-datasets-").build());

    @Autowired
    private AiCourseResourceAttachMapper aiCourseResourceAttachMapper;
    @Autowired
    private CommonFileDfsInterface commonFileDfsInterface;
    @Autowired
    private AiModelConfig aiModelConfig;


    @Override
    public List<AiCourseResourceAttachResponse> getByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiCourseResourceAttach> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCourseResourceAttach::getResourceId, resourceId);
        List<AiCourseResourceAttach> attachList = aiCourseResourceAttachMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(attachList, AiCourseResourceAttachResponse.class);
    }

    @Override
    public void deleteByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiCourseResourceAttach> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCourseResourceAttach::getResourceId, resourceId);
        List<AiCourseResourceAttach> attachList = aiCourseResourceAttachMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(attachList)) {
            return;
        }
        List<Long> ids = attachList.stream().map(AiCourseResourceAttach::getId).collect(Collectors.toList());
        aiCourseResourceAttachMapper.deleteBatchIds(ids);

        // 远程调用文件服务删除数据接口
        List<String> fileIds = attachList.stream().map(AiCourseResourceAttach::getFileId).collect(Collectors.toList());
        fileIds.forEach(fileId -> commonFileDfsInterface.deleteFileById(fileId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveData(List<AiCourseResourceAttachSaveRequest> requestList, Long resourceId) {
        // 全量保存 课程资源-学习资料文件信息
        LambdaQueryWrapper<AiCourseResourceAttach> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCourseResourceAttach::getResourceId, resourceId);
        // 删除该课程资源下所有学习资料文件
        this.remove(queryWrapper);
        if (CollUtil.isEmpty(requestList)) {
            return false;
        }
        List<AiCourseResourceAttach> attachList = BeanUtil.copyToList(requestList, AiCourseResourceAttach.class);
        attachList.forEach(attach -> attach.setResourceId(resourceId));
        // 重新保存新的课程资源学习资料文件
        return this.saveBatch(attachList);
    }

    @Override
    public void uploadFile2DatasetsAsync(List<AiCourseResourceAttachSaveRequest> requestList) {
        if (CollUtil.isEmpty(requestList)) {
            return;
        }
        try {
            // 上传知识库接口url
            String url = aiModelConfig.getBaseUrl() + aiModelConfig.getDatasets().getSuffixUrl().get("create_by_file")
                    .replace("{dataset_id}", aiModelConfig.getDatasets().getDatasetIds().get("coach"));
            // 构建知识库请求参数
            AiDatasetsRequest datasetsParam = AiDatasetsRequest.builder()
                    .indexingTechnique("high_quality")
                    .processRule(AiDatasetsRequest.ProcessRule.builder()
                            .mode("custom")
                            .rules(AiDatasetsRequest.Rules.builder()
                                    .preProcessingRules(
                                            List.of(AiDatasetsRequest.PreProcessingRule.builder()
                                                            .id("remove_extra_spaces")
                                                            .enabled(true)
                                                            .build(),
                                                    AiDatasetsRequest.PreProcessingRule.builder()
                                                            .id("remove_urls_emails")
                                                            .enabled(true)
                                                            .build())
                                    )
                                    .segmentation(AiDatasetsRequest.Segmentation.builder()
                                            .separator("\n\n")
                                            .maxTokens(500)
                                            .build())
                                    .build())
                            .build())
                    .build();
            requestList.forEach(file ->
                    CACHED_THREAD_POOL.execute(() -> {
                        // 下载文件
                        byte[] bytes = HttpUtil.downloadBytes(file.getFileUrl());
                        // 上传文件到知识库
                        String result = HttpUtil.createPost(url)
                                .form("file", bytes, file.getFileName())
                                .form("data", JSON.toJSONString(datasetsParam))
                                .bearerAuth(aiModelConfig.getDatasets().getApiKey())
                                .execute().body();
                        log.debug("上传文件到知识库成功，返回结果为：{}", result);
                        // 更新关联关系到文件表
                        updateDatasetsFileIdByFileId(file.getFileId(), JSON.parseObject(result).getJSONObject("document").getString("id"), 3);
                    }));
        } catch (Exception e) {
            log.error("上传文件到知识库失败：{}", e.getMessage());
        }
    }

    @Override
    public List<AiCourseResourceAttachResponse> getByDatasetsFileIds(List<String> datasetsFileIds) {
        if (CollUtil.isEmpty(datasetsFileIds)) {
            return List.of();
        }
        LambdaQueryWrapper<AiCourseResourceAttach> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AiCourseResourceAttach::getDatasetsFileId, datasetsFileIds);
        List<AiCourseResourceAttach> attachList = aiCourseResourceAttachMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(attachList)) {
            return List.of();
        }
        return BeanUtil.copyToList(attachList, AiCourseResourceAttachResponse.class);
    }


    /**
     * 更新知识库文件ID到附件表
     *
     * @param fileId         文件ID
     * @param datasetsFileId 知识库文件ID
     * @param retryNum       重试次数
     */
    private void updateDatasetsFileIdByFileId(String fileId, String datasetsFileId, Integer retryNum) {
        if (retryNum == 0) {
            log.error("更新关联关系到文件表失败，fileId：{}，fileName：{}", fileId, fileId);
            return;
        }
        LambdaUpdateWrapper<AiCourseResourceAttach> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(AiCourseResourceAttach::getDatasetsFileId, datasetsFileId);
        updateWrapper.eq(AiCourseResourceAttach::getFileId, fileId);
        boolean updateFlag = this.update(updateWrapper);
        if (!updateFlag) {
            updateDatasetsFileIdByFileId(fileId, datasetsFileId, retryNum - 1);
        }
    }

}
