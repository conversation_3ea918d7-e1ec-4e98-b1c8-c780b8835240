package com.deloitte.dhr.ai.module.interview.category.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * AI访谈-类别
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_interview_category")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("课程类别表")
public class AiInterviewCategory extends SuperLogicModel<AiInterviewCategory> {

    /**
     * 类别名称
     */
    @TableField(value = "category_name")
    private String categoryName;

    /**
     * 类别父级ID，顶级节点默认值为0
     */
    @TableField(value = "parent_id")
    private Long parentId;

    /**
     * 类别全路径，用,隔开
     */
    @TableField(value = "category_path")
    private String categoryPath;

}