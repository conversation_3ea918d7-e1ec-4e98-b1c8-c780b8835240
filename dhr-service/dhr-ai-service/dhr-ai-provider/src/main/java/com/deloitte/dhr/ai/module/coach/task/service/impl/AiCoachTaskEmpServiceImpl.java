package com.deloitte.dhr.ai.module.coach.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.task.domain.AiCoachTaskEmp;
import com.deloitte.dhr.ai.module.coach.task.mapper.AiCoachTaskEmpMapper;
import com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskEmpListRequest;
import com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskEmpResponse;
import com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskEmpSaveRequest;
import com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskInfoResponse;
import com.deloitte.dhr.ai.module.coach.task.service.AiCoachTaskEmpService;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainObjectTag;
import com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainObjectTagMapper;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainObjectResponse;
import com.deloitte.dhr.ai.utils.CheckUtils;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 培训任务-员工-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiCoachTaskEmpServiceImpl extends SuperServiceImpl<AiCoachTaskEmpMapper, AiCoachTaskEmp> implements AiCoachTaskEmpService {

    @Autowired
    private AiCoachTaskEmpMapper aiCoachTaskEmpMapper;
    @Autowired
    private AiTrainObjectTagMapper aiTrainObjectTagMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveData(List<AiCoachTaskEmpSaveRequest> requestList, Long taskId, Long resourceId) {
        // 删除旧的培训员工信息
        this.deleteByTaskId(taskId);

        // 保存新的培训员工信息
        if (CollUtil.isEmpty(requestList)) {
            return true;
        }
        List<AiCoachTaskEmp> taskEmpList = BeanUtil.copyToList(requestList, AiCoachTaskEmp.class);
        taskEmpList.forEach(taskEmp -> {
            taskEmp.setStatus(AiCoachConstant.TaskEmpStatus.NOT_START);
            taskEmp.setTaskId(taskId);
            taskEmp.setResourceId(resourceId);
        });
        return this.saveBatch(taskEmpList);
    }

    @Override
    public void deleteByTaskId(Long taskId) {
        LambdaQueryWrapper<AiCoachTaskEmp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCoachTaskEmp::getTaskId, taskId);
        this.remove(queryWrapper);
    }

    @Override
    public AiCoachTaskEmpResponse getDetail(Long taskEmpId) {
        AiCoachTaskEmp taskEmp = this.get(taskEmpId);
        CheckUtils.checkNull(taskEmp, "该数据不存在");
        AiCoachTaskEmpResponse empResponse = BeanUtil.copyProperties(taskEmp, AiCoachTaskEmpResponse.class);
        empResponse.setElapsedTime(empResponse.getElapsedTime() == null ? null : empResponse.getElapsedTime().setScale(1, RoundingMode.HALF_UP));
        return empResponse;
    }

    @Override
    public ResponsePage<AiCoachTaskEmpResponse> findTaskEmpListPage(Page<AiCoachTaskEmp> page, AiCoachTaskEmpListRequest request, BaseOrder order) {
        List<AiCoachTaskEmpResponse> list = aiCoachTaskEmpMapper.findListPage(page, request, this.adjustOrder(order));
        return new ResponsePage<>(page, list);
    }

    @Override
    public AiTrainObjectResponse getMyObjectDetail(Long taskEmpId) {
        AiTrainObjectResponse myObjectDetail = aiCoachTaskEmpMapper.getMyObjectDetail(taskEmpId);
        // 查询陪练对象-标签信息
        if (myObjectDetail != null) {
            LambdaQueryWrapper<AiTrainObjectTag> tagQueryWrapper = new LambdaQueryWrapper<>();
            tagQueryWrapper.eq(AiTrainObjectTag::getObjectId, myObjectDetail.getId());
            List<AiTrainObjectTag> tagList = aiTrainObjectTagMapper.selectList(tagQueryWrapper);
            List<String> tagNameList = tagList.stream().map(AiTrainObjectTag::getTagName).distinct().collect(Collectors.toList());
            myObjectDetail.setTagInfoList(tagNameList);
        }
        return myObjectDetail;
    }

    @Override
    public void verifyTaskEmp(List<AiCoachTaskEmpSaveRequest> saveRequestList) {

        // 校验-保存的数据是否有重复数据
        Map<String, List<AiCoachTaskEmpSaveRequest>> empMap = saveRequestList.stream().collect(Collectors.groupingBy(AiCoachTaskEmpSaveRequest::getEmpCode));
        List<String> errorEmpList = new ArrayList<>();
        empMap.forEach((empCode, list) -> {
            if (list.size() == 1) {
                return;
            }
            errorEmpList.add(empCode);
        });
        if (CollUtil.isNotEmpty(errorEmpList)) {
            throw new CommRunException("不能添加重复的员工数据【" + StrUtil.join("，", errorEmpList) + "】");
        }
    }

    @Override
    public AiCoachTaskInfoResponse getMyTaskInfoDetail(Long taskEmpId) {
        return aiCoachTaskEmpMapper.getMyTaskInfoDetail(taskEmpId);
    }

}

