package com.deloitte.dhr.ai.module.ai.strategy.digital;

import cn.hutool.core.util.StrUtil;
import com.deloitte.dhr.ai.enums.DigitalLiningIntentAction;
import com.deloitte.dhr.ai.enums.DigitalLiningIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.DigitalLiningActionStrategyType;
import com.deloitte.dhr.ai.module.ai.strategy.digital.pojo.DigitalLiningIntentResponse;
import com.deloitte.dhr.common.base.exception.CommRunException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数字人-李宁
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Component
@Slf4j
public class DigitalLiningStrategyContext {

    /**
     * 策略类集合
     */
    private Map<DigitalLiningIntentItem, Map<DigitalLiningIntentAction, DigitalLiningStrategy>> digitalStrategyMap;

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    private void init() {
        digitalStrategyMap = applicationContext.getBeansOfType(DigitalLiningStrategy.class).values()
                .stream()
                .collect(Collectors.groupingBy(a -> a.getClass().getAnnotation(DigitalLiningActionStrategyType.class).item(),
                        Collectors.toMap(a -> a.getClass().getAnnotation(DigitalLiningActionStrategyType.class).action(), a -> a)));
    }

    /**
     * 获取策略
     *
     * @param item   策略类型
     * @param action 策略行为
     * @return 类型对应的策略
     * @throws CommRunException 异常
     */
    public DigitalLiningStrategy getStrategy(DigitalLiningIntentItem item, DigitalLiningIntentAction action) throws CommRunException {
        // 参数校验
        if (item == null || action == null) {
            throw new CommRunException("类型或行为参数为空，无法获取策略");
        }
        return digitalStrategyMap.get(item).get(action);
    }

    /**
     * 获取策略
     *
     * @param intent 数字人-李宁:意图识别结果
     */
    public DigitalLiningStrategy getOrDefaultStrategy(DigitalLiningIntentResponse intent) {
        log.info("数字人-李宁:意图识别结果：intentType：{}，intentItem：{}，intentAction：{}", intent.getIntentType(), intent.getIntentItem(), intent.getIntentAction());
        DigitalLiningIntentItem itemEnum = DigitalLiningIntentItem.getByName(intent.getIntentItem());
        DigitalLiningIntentAction actionEnum = DigitalLiningIntentAction.getByName(intent.getIntentAction());
        if (!StrUtil.equals(intent.getIntentItem(), itemEnum.getName()) || !StrUtil.equals(intent.getIntentAction(), actionEnum.getName())) {
            log.info("数字人-李宁:意图识别枚举未定义，执行兜底枚举：intentType：{}，intentItem：{}，intentAction：{}", itemEnum.getType().getName(), itemEnum.getName(), actionEnum.getName());
            intent.setIntentType(itemEnum.getType().getName());
            intent.setIntentItem(itemEnum.getName());
            intent.setIntentAction(actionEnum.getName());
        }
        // 判断兜底策略
        if (digitalStrategyMap.get(itemEnum) == null || digitalStrategyMap.get(itemEnum).get(actionEnum) == null) {
            switch (actionEnum) {
                case TQ:
                case TC:
                    itemEnum = DigitalLiningIntentItem.TY;
                    actionEnum = DigitalLiningIntentAction.IR;
                    break;
                case IR:
                    itemEnum = DigitalLiningIntentItem.TY;
                    break;
                default:
            }
            log.info("数字人-李宁:策略未定义，执行兜底策略：intentType：{}，intentItem：{}，intentAction：{}", itemEnum.getType().getName(), itemEnum.getName(), actionEnum.getName());
            // 更新数字人-李宁:意图识别结果
            intent.setIntentType(itemEnum.getType().getName());
            intent.setIntentItem(itemEnum.getName());
            intent.setIntentAction(actionEnum.getName());
        }
        return digitalStrategyMap.get(itemEnum).get(actionEnum);
    }

}