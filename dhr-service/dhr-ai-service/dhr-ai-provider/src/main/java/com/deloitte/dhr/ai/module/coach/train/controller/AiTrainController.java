package com.deloitte.dhr.ai.module.coach.train.controller;

import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainDetailResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainSaveRequest;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainInfoService;
import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 课程资源-AI陪练
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/AiTrain")
@Api(tags = "课程资源-AI陪练")
@Validated
public class AiTrainController extends SuperController {

    @Autowired
    private AiTrainInfoService aiTrainInfoService;

    @ApiOperation(value = "详情", notes = "详情")
    @ApiOperationSupport(order = 1)
    @GetMapping("/detail/{resourceId}")
    public ResponseVO<AiTrainDetailResponse> getTrainDetail(@PathVariable("resourceId") Long resourceId) {
        return success(aiTrainInfoService.getTrainDetail(resourceId));
    }

    @ApiOperation(value = "保存", notes = "保存")
    @ApiOperationSupport(order = 2)
    @PostMapping("/save")
    public ResponseVO<Long> save(@RequestBody @Validated(Save.class) AiTrainSaveRequest request) {
        return success(aiTrainInfoService.saveAll(request, AiCoachConstant.CourseResourceStatus.DRAFT));
    }

    @ApiOperation(value = "提交", notes = "提交")
    @ApiOperationSupport(order = 3)
    @PostMapping("/submit")
    public ResponseVO<Long> submit(@RequestBody @Validated(Submit.class) AiTrainSaveRequest request) {
        return success(aiTrainInfoService.submitAll(request));
    }

}
