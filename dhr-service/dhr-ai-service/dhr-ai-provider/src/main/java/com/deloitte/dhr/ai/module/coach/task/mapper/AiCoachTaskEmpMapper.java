package com.deloitte.dhr.ai.module.coach.task.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.coach.task.domain.AiCoachTaskEmp;
import com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskEmpListRequest;
import com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskEmpResponse;
import com.deloitte.dhr.ai.module.coach.task.pojo.AiCoachTaskInfoResponse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainObjectResponse;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 培训任务-员工-相关持久化接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiCoachTaskEmpMapper extends SuperMapper<AiCoachTaskEmp> {

    /**
     * 分页-任务员工列表
     *
     * @param page  分页参数
     * @param param 过滤条件
     * @param order 排序条件
     * @return 列表数据
     */
    List<AiCoachTaskEmpResponse> findListPage(Page<AiCoachTaskEmp> page, @Param("param") AiCoachTaskEmpListRequest param, @Param("order") String order);

    /**
     * 查询我的陪练对象详情
     *
     * @param id 培训任务员工ID
     * @return AiCoachObjectDetailResponse
     */
    AiTrainObjectResponse getMyObjectDetail(@Param("id") Long id);

    /**
     * 查询-我的陪练任务详情
     *
     * @param taskEmpId 培训任务员工ID
     * @return AiCoachTaskInfoResponse
     */
    AiCoachTaskInfoResponse getMyTaskInfoDetail(@Param("taskEmpId") Long taskEmpId);

    /**
     * 获取员工对话问题数量
     *
     * @param taskEmpId 培训任务员工ID
     * @return 对话问题数量
     */
    Integer getDialogueQuestionNum(@Param("taskEmpId") Long taskEmpId);

}