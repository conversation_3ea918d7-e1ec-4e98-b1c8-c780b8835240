package com.deloitte.dhr.ai.module.ai.annotation;


import com.deloitte.dhr.ai.enums.AiTransformType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * AI输出转换策略-自定义注解
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface TransformStrategyType {

    /**
     * 转换类型
     *
     * @return {@link AiTransformType};
     */
    AiTransformType value() default AiTransformType.TEXT;

}
