package com.deloitte.dhr.ai.module.interview.train.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
/**
 * AI访谈-访谈任务-我的对话历史记录表
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_interview_my_dialogue_history")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AI访谈-我的对话历史记录表")
public class AiInterviewMyDialogueHistory extends SuperLogicModel<AiInterviewMyDialogueHistory> {

    /**
     * AI访谈任务员工ID
     */
    @TableField(value = "task_emp_id")
    private Long taskEmpId;

    /**
     * AI访谈对象ID
     */
    @TableField(value = "object_id")
    private Long objectId;

    /**
     * 所属问题ID
     */
    @TableField(value = "question_id")
    private Long questionId;

    /**
     * 问题
     */
    @TableField(value = "question")
    private String question;

    /**
     * 消息内容
     */
    @TableField(value = "message")
    private String message;

    /**
     * 提问时间
     */
    @TableField(value = "ask_time")
    private Date askTime;

    /**
     * 发送时间
     */
    @TableField(value = "send_time")
    private Date sendTime;

    /**
     * AI语音文件Url
     */
    @TableField(value = "ai_voice_file_url")
    private String aiVoiceFileUrl;

    /**
     * 员工语音文件Url
     */
    @TableField(value = "emp_voice_file_url")
    private String empVoiceFileUrl;

    /**
     * 答复状态，01通过，02不通过，03跳过
     */
    @TableField(value = "status")
    private String status;

    /**
     * 匹配率
     */
    @TableField(value = "match_rate")
    private String matchRate;

    /**
     * 简要分析匹配/不匹配的原因
     */
    @TableField(value = "analysis")
    private String analysis;

    /**
     * 乐观锁
     */
    @TableField(value = "revision")
    private Integer revision;
}