package com.deloitte.dhr.ai.module.sr.pojo;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 阿里云-录音文件识别极速版-请求
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AliyunFlashRecognizerRequest {

    /**
     * 应用appKey
     */
    @ApiModelProperty(value = "应用appKey")
    private String appkey;

    /**
     * 鉴权token
     */
    @ApiModelProperty(value = "token")
    private String token;

    /**
     * 单声道（mono）16bit采样位数音频，包括无压缩的PCM、WAV、OPUS、AMR、SPEEX、MP3、AAC格式
     */
    @ApiModelProperty(value = "单声道（mono）16bit采样位数音频：PCM、WAV、OPUS、AMR、SPEEX、MP3、AAC")
    private String format = "WAV";

    /**
     * 音频采样率：16000 Hz、8000 Hz。默认值：16000 Hz
     */
    @ApiModelProperty(value = "音频采样率：16000 Hz、8000 Hz。默认值：16000 Hz")
    private Integer sampleRate;

    /**
     * 添加热词表ID。默认：不添加
     */
    @ApiModelProperty(value = "添加热词表ID")
    private String vocabularyId;

    /**
     * 添加自学习模型ID。默认：不添加
     */
    @ApiModelProperty(value = "添加自学习模型ID")
    private String customizationId;

    /**
     * ITN（逆文本inverse text normalization）中文数字转换阿拉伯数字。设置为True时，中文数字将转为阿拉伯数字输出，默认值：False
     */
    @ApiModelProperty(value = "中文数字转换阿拉伯数字")
    private Boolean enableInverseTextNormalization;

    /**
     * 是否返回词级别信息。取值：true或false。默认：false（不开启）
     */
    @ApiModelProperty(value = "是否返回词级别信息")
    private Boolean enableWordLevelResult;

    /**
     * 是否启用时间戳校准功能，取值：true或false，默认：false（不开启）
     */
    @ApiModelProperty(value = "是否启用时间戳校准功能")
    private Boolean enableTimestampAlignment;

    /**
     * 是否只识别首个声道，取值：true/false。（如果录音识别结果重复，您可以开启此参数。）
     * 默认为空：8k处理双声道，16k处理单声道。
     * false：8k处理双声道，16k处理双声道。
     * true：8k处理单声道，16k处理单声道。
     */
    @ApiModelProperty(value = "是否只识别首个声道")
    private Boolean firstChannelOnly;

    /**
     * 噪音参数阈值，取值范围：[-1, 1]。取值说明如下：
     * 取值越趋于-1，噪音被判定为语音的概率越大。
     * 取值越趋于+1，语音被判定为噪音的概率越大。
     */
    @ApiModelProperty(value = "噪音参数阈值")
    private Float speechNoiseThreshold;

    /**
     * 可通过公网访问的音频文件下载链接
     */
    @ApiModelProperty(value = "可通过公网访问的音频文件下载链接")
    private String audioAddress;


}
