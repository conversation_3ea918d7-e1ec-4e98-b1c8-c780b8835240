package com.deloitte.dhr.ai.module.ai.strategy.ssc.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * SSC-考勤异常-事务查询
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AttendanceExceptionTqResponse")
public class AttendanceExceptionTqResponse {

    @ApiModelProperty(value = "考勤异常类型", name = "exceptionType")
    private String exceptionType;

    @ApiModelProperty(value = "考勤异常名称", name = "exceptionName")
    private String exceptionName;

    @ApiModelProperty(value = "考勤异常时间", name = "exceptionTime")
    private Date exceptionTime;

    @ApiModelProperty(value = "考勤异常备注", name = "remark")
    private String remark;

}
