package com.deloitte.dhr.ai.module.coach.teach.pojo;

import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 课程资源-教学课程-我的培训-题目-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachMyCoachQuestionSaveRequest")
public class AiTeachMyCoachQuestionSaveRequest {

    /**
     * 教学课程-考核题目ID
     */
    @NotNull(groups = {Save.class, Submit.class}, message = "考核题目ID不能为空")
    @ApiModelProperty(value = "教学课程-考核题目ID", name = "questionId")
    private Long questionId;

    /**
     * 题目类型;DXT：单选题，PDT：判断题，WDT：问答题
     */
    @NotBlank(groups = {Save.class, Submit.class}, message = "题目类型不能为空")
    @Length(max = 10, message = "题目类型不能超过10个字符")
    @ApiModelProperty(value = "题目类型;DXT：单选题，PDT：判断题，WDT：问答题", name = "questionType")
    private String questionType;

    /**
     * 教学课程-考核题目答案，选择题：所选选项的id，多个用','隔开；文本题：填写的文本内容
     */
    @ApiModelProperty(value = "教学课程-考核题目答案，选择题：所选选项的id，多个用','隔开；文本题：填写的文本内容", name = "questionAnswer")
    private String questionAnswer;

}