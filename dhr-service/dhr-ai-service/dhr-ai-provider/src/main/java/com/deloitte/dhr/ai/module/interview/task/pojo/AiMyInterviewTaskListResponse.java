package com.deloitte.dhr.ai.module.interview.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * AI访谈-访谈任务-我的访谈-列表
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiMyInterviewTaskListResponse")
public class AiMyInterviewTaskListResponse {

    /**
     * AI访谈任务ID
     */
    @ApiModelProperty(value = "AI访谈任务ID", name = "taskId")
    private Long taskId;

    /**
     * AI访谈任务员工ID
     */
    @ApiModelProperty(value = "AI访谈任务员工ID", name = "taskEmpId")
    private Long taskEmpId;

    /**
     * AI访谈资源ID
     */
    @ApiModelProperty(value = "AI访谈资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * AI访谈分类;AIFT：AI访谈
     */
    @ApiModelProperty(value = "AI访谈分类;AIFT：AI访谈", name = "courseType")
    private String courseType;

    /**
     * AI访谈任务名称
     */
    @ApiModelProperty(value = "AI访谈任务名称", name = "taskName")
    private String taskName;

    /**
     * AI访谈任务-开始时间
     */
    @ApiModelProperty(value = "AI访谈任务-开始时间", name = "startDateTime")
    private Date startDateTime;

    /**
     * AI访谈任务-结束时间
     */
    @ApiModelProperty(value = "AI访谈任务-结束时间", name = "endDateTime")
    private Date endDateTime;

    /**
     * AI访谈任务-封面URL
     */
    @ApiModelProperty(value = "AI访谈任务-封面URL", name = "taskCoverUrl")
    private String taskCoverUrl;

    /**
     * 员工状态;WKS：未开始，JXZ：进行中，YWC：已完成，YJS：已结束
     */
    @ApiModelProperty(value = "员工状态;WKS：未开始，JXZ：进行中，YWC：已完成，YJS：已结束", name = "status")
    private String status;

    /**
     * 报告生成状态;01未生成，02生成中，03生成成功，04生成失败
     */
    @ApiModelProperty(value = "报告生成状态;01未生成，02生成中，03生成成功，04生成失败", name = "bgStatus")
    private String bgStatus;
}