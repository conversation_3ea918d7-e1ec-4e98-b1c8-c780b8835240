package com.deloitte.dhr.ai.module.ai.strategy.transform;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiSubType;
import com.deloitte.dhr.ai.enums.AiTransformType;
import com.deloitte.dhr.ai.module.ai.annotation.TransformStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.utils.StrParseJsonUtil;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.stream.Collectors;

/**
 * AI转换-COACH-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@TransformStrategyType(AiTransformType.COACH)
public class CoachTransformStrategy implements ResponseTransformStrategy {

    @Override
    public Flux<List<AiContent>> transformStream(Flux<String> rawStream, AiRequest aiRequest) {
        StringBuilder builder = new StringBuilder();
        return rawStream
                .filter(raw -> StrUtil.equals(JSONObject.parseObject(raw).getString("event"), getFilterEvent(aiRequest.getAiSubType())))
                .flatMap(raw -> {
                    // 处理成json格式
                    return Flux.fromIterable(parse(builder, raw, aiRequest.getAiSubType(), aiRequest.getStream()));
                })
                .map(List::of);
    }

    @Override
    public Flux<List<AiContent>> transformBlock(Flux<String> rawStream, AiRequest aiRequest) {
        StringBuilder builder = new StringBuilder();
        return rawStream
                .flatMap(raw -> {
                    // 处理成json格式
                    return Flux.fromIterable(parse(builder, raw, aiRequest.getAiSubType(), aiRequest.getStream()));
                })
                .map(List::of);
    }

    /**
     * 获取过滤事件
     *
     * @param subType 子类型
     * @return 过滤事件
     */
    private String getFilterEvent(String subType) {
        String filterEvent = "";
        switch (AiSubType.getByCode(subType)) {
            case TEACH_QUESTION_GENERATE:
            case TRAIN_QUESTION_GENERATE:
            case TRAIN_FRAMEWORK_GENERATE:
            case TEACH_QUESTION_EVALUATE:
            case TRAIN_TAG_EXTRACTOR:
                filterEvent = "text_chunk";
                break;
            case TRAIN_CHAT_ANALYZE:
            case TRAIN_TEXT_TO_SPEECH:
            case TEACH_QUESTION_EVALUATE_BATCH:
                filterEvent = "workflow_finished";
                break;
            default:
        }
        return filterEvent;
    }

    /**
     * 解析数据
     *
     * @param builder 缓存字符串
     * @param raw     原始数据
     * @param item    类型子项
     * @param stream  是否流式
     * @return List<AiContent>
     */
    private List<AiContent> parse(StringBuilder builder, String raw, String item, Boolean stream) {
        List<AiContent> result = List.of();
        String text;
        switch (AiSubType.getByCode(item)) {
            case TEACH_QUESTION_GENERATE:
            case TRAIN_QUESTION_GENERATE:
            case TRAIN_FRAMEWORK_GENERATE:
            case TEACH_QUESTION_EVALUATE:
                text = stream ? JSON.parseObject(raw).getJSONObject("data").getString("text") :
                        JSON.parseObject(raw).getJSONObject("data").getJSONObject("outputs").getString("text");
                builder.append(text);
                result = StrParseJsonUtil.strParseJson(builder, false).stream().map(jsonStr -> AiContent.builder()
                        .contentType(AiContentType.JSON.getCode())
                        .content(jsonStr)
                        .build()).collect(Collectors.toList());
                break;
            case TRAIN_TAG_EXTRACTOR:
                text = stream ? JSON.parseObject(raw).getJSONObject("data").getString("text") :
                        JSON.parseObject(raw).getJSONObject("data").getJSONObject("outputs").getString("text");
                builder.append(text);
                result = StrParseJsonUtil.strParseJson(builder, true).stream()
                        .map(jsonStr -> AiContent.builder()
                                .contentType(AiContentType.TEXT.getCode())
                                .content(jsonStr)
                                .build())
                        .collect(Collectors.toList());
                break;
            case TRAIN_CHAT_ANALYZE:
            case TRAIN_TEXT_TO_SPEECH:
            case TEACH_QUESTION_EVALUATE_BATCH:
                text = JSON.parseObject(raw).getJSONObject("data").getJSONObject("outputs").getString("text");
                builder.append(text);
                result = JSON.parseArray(builder.toString(), String.class).stream().map(jsonStr -> AiContent.builder()
                        .contentType(AiContentType.JSON.getCode())
                        .content(jsonStr)
                        .build()).collect(Collectors.toList());
                break;
            default:
        }
        return result;
    }


}

