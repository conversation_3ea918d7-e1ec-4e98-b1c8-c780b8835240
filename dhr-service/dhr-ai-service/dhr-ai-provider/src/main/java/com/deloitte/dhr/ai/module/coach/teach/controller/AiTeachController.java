package com.deloitte.dhr.ai.module.coach.teach.controller;

import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachDetailResponse;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachSaveRequest;
import com.deloitte.dhr.ai.module.coach.teach.service.AiTeachInfoService;
import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 课程资源-教学课程
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/AiTeach")
@Api(tags = "课程资源-教学课程")
@Validated
public class AiTeachController extends SuperController {

    @Autowired
    private AiTeachInfoService aiTeachInfoService;

    @ApiOperation(value = "详情", notes = "详情")
    @ApiOperationSupport(order = 1)
    @GetMapping("/detail/{resourceId}")
    public ResponseVO<AiTeachDetailResponse> getTeachDetail(@PathVariable("resourceId") Long resourceId) {
        return success(aiTeachInfoService.getTeachDetail(resourceId));
    }

    @ApiOperation(value = "保存", notes = "保存")
    @ApiOperationSupport(order = 2)
    @PostMapping("/save")
    public ResponseVO<Long> save(@RequestBody @Validated(Save.class) AiTeachSaveRequest request) {
        return success(aiTeachInfoService.saveAll(request, AiCoachConstant.CourseResourceStatus.DRAFT));
    }

    @ApiOperation(value = "提交", notes = "提交")
    @ApiOperationSupport(order = 3)
    @PostMapping("/submit")
    public ResponseVO<Long> submit(@RequestBody @Validated(Submit.class) AiTeachSaveRequest request) {
        return success(aiTeachInfoService.submitAll(request));
    }

}
