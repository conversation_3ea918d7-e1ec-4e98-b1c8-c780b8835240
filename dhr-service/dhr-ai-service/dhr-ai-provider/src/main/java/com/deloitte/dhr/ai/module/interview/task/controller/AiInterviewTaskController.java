package com.deloitte.dhr.ai.module.interview.task.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.interview.constant.AiInterviewConstant;
import com.deloitte.dhr.ai.module.interview.task.pojo.*;
import com.deloitte.dhr.ai.module.interview.task.service.AiInterviewTaskEmpService;
import com.deloitte.dhr.ai.module.interview.task.service.AiInterviewTaskInfoService;
import com.deloitte.dhr.ai.module.interview.validation.Save;
import com.deloitte.dhr.ai.module.interview.validation.Submit;
import com.deloitte.dhr.common.Request;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * AI访谈-访谈任务
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/AiInterviewTask")
@Api(tags = "AI访谈任务")
@Validated
public class AiInterviewTaskController extends SuperController {

    @Autowired
    private AiInterviewTaskInfoService aiInterviewTaskInfoService;
    @Autowired
    private AiInterviewTaskEmpService aiInterviewTaskEmpService;


    @ApiOperation(value = "详情-AI访谈任务", notes = "详情-AI访谈任务")
    @ApiOperationSupport(order = 2)
    @GetMapping("/interview/detail/{id}")
    public ResponseVO<AiInterviewTaskDetailResponse> getInterviewTaskDetail(@PathVariable("id") Long id) {
        return success(aiInterviewTaskInfoService.getInterviewTaskDetail(id));
    }

    @ApiOperation(value = "详情-AI访谈任务-员工", notes = "详情-AI访谈任务-员工")
    @ApiOperationSupport(order = 3)
    @GetMapping("/emp/detail/{taskEmpId}")
    public ResponseVO<AiInterviewTaskEmpResponse> getTaskEmpDetail(@PathVariable("taskEmpId") Long taskEmpId) {
        return success(aiInterviewTaskEmpService.getDetail(taskEmpId));
    }

    @ApiOperation(value = "列表-AI访谈任务", notes = "列表-AI访谈任务")
    @ApiOperationSupport(order = 4)
    @PostMapping("/list/{current}/{size}")
    public ResponseVO<ResponsePage<AiInterviewTaskResponse>> taskList(@PathVariable("current") Long current, @PathVariable("size") Long size, @RequestBody Request<AiInterviewTaskListRequest> request) {
        return success(aiInterviewTaskInfoService.findTaskListPage(new Page<>(current, size), request.get(), request.getOrder()));
    }

    @ApiOperation(value = "列表-AI访谈任务-员工", notes = "列表-AI访谈任务-员工")
    @ApiOperationSupport(order = 5)
    @PostMapping("/emp/list/{current}/{size}")
    public ResponseVO<ResponsePage<AiInterviewTaskEmpResponse>> taskEmpList(@PathVariable("current") Long current, @PathVariable("size") Long size, @RequestBody Request<AiInterviewTaskEmpListRequest> request) {
        return success(aiInterviewTaskEmpService.findTaskEmpListPage(new Page<>(current, size), request.get(), request.getOrder()));
    }

    @ApiOperation(value = "列表-我的访谈", notes = "列表-我的访谈")
    @ApiOperationSupport(order = 6)
    @PostMapping("/my/list/{current}/{size}")
    public ResponseVO<ResponsePage<AiMyInterviewTaskListResponse>> myTaskList(@PathVariable("current") Long current, @PathVariable("size") Long size, @RequestBody Request<AiInterviewEmpTaskListRequest> request) {
        return success(aiInterviewTaskInfoService.myTaskList(new Page<>(current, size), request.get(), request.getOrder()));
    }

    @ApiOperation(value = "保存", notes = "保存")
    @ApiOperationSupport(order = 7)
    @PostMapping("/save")
    public ResponseVO<Long> save(@RequestBody @Validated(Save.class) AiInterviewTaskSaveRequest request) {
        return success(aiInterviewTaskInfoService.saveData(request, AiInterviewConstant.TaskStatus.DRAFT));
    }

    @ApiOperation(value = "发布", notes = "发布")
    @ApiOperationSupport(order = 8)
    @PostMapping("/publish")
    public ResponseVO<Long> publish(@RequestBody @Validated(Submit.class) AiInterviewTaskSaveRequest request) {
        return success(aiInterviewTaskInfoService.publishData(request));
    }

    @ApiOperation(value = "删除", notes = "删除")
    @ApiOperationSupport(order = 9)
    @PostMapping("/del")
    public ResponseVO<Boolean> del(@RequestParam("id") Long id) {
        return success(aiInterviewTaskInfoService.deleteById(id));
    }

    @ApiOperation(value = "详情-我的访谈任务", notes = "详情-我的访谈任务")
    @ApiOperationSupport(order = 10)
    @PostMapping("/my/task/detail/{taskEmpId}")
    public ResponseVO<AiInterviewTaskInfoResponse> getMyTaskInfoDetail(@PathVariable("taskEmpId") Long taskEmpId) {
        return success(aiInterviewTaskEmpService.getMyTaskInfoDetail(taskEmpId));
    }

    @ApiOperation(value = "结束", notes = "结束")
    @ApiOperationSupport(order = 10)
    @PostMapping("/close/{id}")
    public ResponseVO<Boolean> closeTask(@PathVariable("id") Long id) {
        return success(aiInterviewTaskInfoService.closeTask(id));
    }

}
