package com.deloitte.dhr.ai.module.interview.train.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.config.AiModelConfig;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiSubType;
import com.deloitte.dhr.ai.enums.AiType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.module.ai.service.AiService;
import com.deloitte.dhr.ai.module.interview.constant.AiInterviewConstant;
import com.deloitte.dhr.ai.module.interview.task.domain.AiInterviewTaskEmp;
import com.deloitte.dhr.ai.module.interview.task.domain.AiInterviewTaskInfo;
import com.deloitte.dhr.ai.module.interview.task.mapper.AiInterviewTaskEmpMapper;
import com.deloitte.dhr.ai.module.interview.task.mapper.AiInterviewTaskInfoMapper;
import com.deloitte.dhr.ai.module.interview.train.domain.AiInterviewMyDialogueHistory;
import com.deloitte.dhr.ai.module.interview.train.domain.AiInterviewObject;
import com.deloitte.dhr.ai.module.interview.train.mapper.AiInterviewDialogueQuestionMapper;
import com.deloitte.dhr.ai.module.interview.train.mapper.AiInterviewMyDialogueHistoryMapper;
import com.deloitte.dhr.ai.module.interview.resource.mapper.AiInterviewObjectMapper;
import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewDialogueQuestionResponse;
import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewMyDialogueHistoryListRequest;
import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewMyDialogueHistoryResponse;
import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewMyQuestionListResponse;
import com.deloitte.dhr.ai.module.interview.train.service.AiInterviewMyDialogueHistoryService;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.*;
import java.util.stream.Collectors;

/**
 * AI访谈-访谈任务-我的对话历史记录-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class AiInterviewMyDialogueHistoryServiceImpl extends SuperServiceImpl<AiInterviewMyDialogueHistoryMapper, AiInterviewMyDialogueHistory> implements AiInterviewMyDialogueHistoryService {

    @Autowired
    private AiInterviewMyDialogueHistoryMapper aiInterviewMyDialogueHistoryMapper;
    @Autowired
    private AiInterviewDialogueQuestionMapper aiInterviewDialogueQuestionMapper;
    @Autowired
    private AiInterviewTaskEmpMapper aiInterviewTaskEmpMapper;
    @Autowired
    private AiInterviewTaskInfoMapper aiInterviewTaskInfoMapper;
    @Autowired
    private AiInterviewObjectMapper aiInterviewObjectMapper;
    @Autowired
    private AiModelConfig aiModelConfig;
    @Autowired
    private AiService aiService;

    @Override
    public List<AiInterviewMyDialogueHistoryResponse> getMyDialogueHistory(AiInterviewMyDialogueHistoryListRequest request) {
        List<AiInterviewMyDialogueHistoryResponse> dialogueHistoryList = new ArrayList<>();
        AiInterviewTaskEmp emp = aiInterviewTaskEmpMapper.selectById(request.getTaskEmpId());
        AiInterviewTaskInfo taskInfo = aiInterviewTaskInfoMapper.selectById(emp.getTaskId());

        LambdaQueryWrapper<AiInterviewMyDialogueHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiInterviewMyDialogueHistory::getTaskEmpId, request.getTaskEmpId());
        if (request.getSendTime() != null){
            queryWrapper.le(AiInterviewMyDialogueHistory::getAskTime, request.getSendTime());
        }
        queryWrapper.orderByAsc(AiInterviewMyDialogueHistory::getId);
        if(request.getSize() !=null){
            queryWrapper.last("limit " + request.getSize());
        }
        // 查询历史记录信息
        List<AiInterviewMyDialogueHistory> historyList = aiInterviewMyDialogueHistoryMapper.selectList(queryWrapper);
        // 查询AI的问题信息
        List<AiInterviewDialogueQuestionResponse> aiQuestionList = aiInterviewDialogueQuestionMapper.getAiQuestionList(request.getTaskEmpId());

        if (historyList.isEmpty() && aiQuestionList.isEmpty()){
            return new ArrayList<>();
        }
        historyList.stream().forEach(history -> {
            AiInterviewMyDialogueHistoryResponse historyResponse = new AiInterviewMyDialogueHistoryResponse();
            historyResponse.setMessageBelong(AiInterviewConstant.DialogueMessageBelong.AI);
            historyResponse.setId(history.getId());
            historyResponse.setQuestionId(history.getQuestionId());
            historyResponse.setMessage(history.getQuestion());
            historyResponse.setVoiceFileUrl(history.getAiVoiceFileUrl());
            historyResponse.setSendTime(history.getAskTime());
            historyResponse.setStatus(history.getStatus());
            dialogueHistoryList.add(historyResponse);

            if(StrUtil.isNotEmpty(history.getMessage()) || StrUtil.isNotEmpty(history.getEmpVoiceFileUrl())){
                historyResponse = new AiInterviewMyDialogueHistoryResponse();
                historyResponse.setMessageBelong(AiInterviewConstant.DialogueMessageBelong.EMP);
                historyResponse.setId(history.getId());
                historyResponse.setMessage(history.getMessage());
                historyResponse.setVoiceFileUrl(history.getEmpVoiceFileUrl());
                historyResponse.setSendTime(history.getSendTime());
                historyResponse.setStatus(history.getStatus());
                dialogueHistoryList.add(historyResponse);
            }
        });

        if (CollUtil.isEmpty(aiQuestionList)) {
            return dialogueHistoryList;
        }

        // 如果任务为进行中，需要校验
        if (AiInterviewConstant.TaskStatus.JXZ.equals(taskInfo.getTaskStatus())){
            if(AiInterviewConstant.TaskEmpStatus.NOT_START.equals(emp.getStatus())){
                emp.setStatus(AiInterviewConstant.TaskEmpStatus.ONGOING);
                aiInterviewTaskEmpMapper.updateById(emp);
            }
            // 判断访谈任务沟通是否为空
            if (CollUtil.isEmpty(historyList)) {
                AiInterviewObject infoObject = aiInterviewObjectMapper.selectOne(new LambdaQueryWrapper<AiInterviewObject>()
                        .eq(AiInterviewObject::getResourceId, taskInfo.getResourceId()));
                AiInterviewMyDialogueHistory dialogueHistorysaveRequest = new AiInterviewMyDialogueHistory();
                dialogueHistorysaveRequest.setTaskEmpId(request.getTaskEmpId());
                dialogueHistorysaveRequest.setObjectId(infoObject.getId());
                AiInterviewMyDialogueHistory nextAiQuestion = this.getNextAiQuestion(aiQuestionList, new ArrayList<>(), dialogueHistorysaveRequest, historyList);

                if (nextAiQuestion != null) {
                    if (Objects.isNull(nextAiQuestion.getId())){
                        aiInterviewMyDialogueHistoryMapper.insert(nextAiQuestion);
                    }

                    //返回下一个问题信息
                    AiInterviewMyDialogueHistoryResponse nextAiMessageInfo = new AiInterviewMyDialogueHistoryResponse();
                    nextAiMessageInfo.setId(nextAiQuestion.getId());
                    nextAiMessageInfo.setQuestionId(nextAiQuestion.getQuestionId());
                    nextAiMessageInfo.setMessage(nextAiQuestion.getQuestion());
                    nextAiMessageInfo.setSendTime(nextAiQuestion.getSendTime());
                    nextAiMessageInfo.setVoiceFileUrl(nextAiQuestion.getAiVoiceFileUrl());
                    nextAiMessageInfo.setMessageBelong(AiInterviewConstant.DialogueMessageBelong.AI);
                    nextAiMessageInfo.setStatus(AiInterviewConstant.EmpTaskQuestionStatus.NO_PASS);

                    dialogueHistoryList.add(nextAiMessageInfo);
                }
            } else {
                if (StrUtil.isEmpty(historyList.get(historyList.size()-1).getMessage()) && StrUtil.isEmpty(historyList.get(historyList.size()-1).getEmpVoiceFileUrl())){
                    // 修改问题的最后一次提问时间
                    historyList.get(historyList.size()-1).setAskTime(DateTime.now());
                    aiInterviewMyDialogueHistoryMapper.updateById(historyList.get(historyList.size()-1));
                    dialogueHistoryList.get(dialogueHistoryList.size()-1).setSendTime(DateTime.now());
                    // 获取问题的答复次数
                    Long dialogueNum = aiInterviewMyDialogueHistoryMapper.getMyDialogueNum(request.getTaskEmpId(), historyList.get(historyList.size()-1).getQuestionId());
                    dialogueHistoryList.get(dialogueHistoryList.size()-1).setDfNum(dialogueNum);
                }
            }
        } else if(AiInterviewConstant.TaskStatus.YWC.equals(taskInfo.getTaskStatus())) {
            // 如果任务为完成，则最后生成AI结束语
            AiInterviewMyDialogueHistoryResponse historyResponse = new AiInterviewMyDialogueHistoryResponse();
            historyResponse.setMessageBelong(AiInterviewConstant.DialogueMessageBelong.AI);
            historyResponse.setMessage(AiInterviewConstant.AiEndMessage);
            historyResponse.setStatus(AiInterviewConstant.EmpTaskQuestionStatus.END);
            dialogueHistoryList.add(historyResponse);
        }
        return dialogueHistoryList;
    }

    @Override
    public AiInterviewMyDialogueHistory getNextAiQuestion(List<AiInterviewDialogueQuestionResponse> aiQuestionList, List<JSONObject> analyzeList, AiInterviewMyDialogueHistory dialogueHistorysaveRequest, List<AiInterviewMyDialogueHistory> dialogueHistoryList) {

        AiInterviewMyDialogueHistory history;
        if (analyzeList.isEmpty() && dialogueHistoryList.isEmpty()) {
            // 如果没有历史记录，则插入第一条AI的对话问题
            history = new AiInterviewMyDialogueHistory();
            history.setId(dialogueHistorysaveRequest.getId());
            history.setQuestionId(aiQuestionList.get(0).getId());
            history.setTaskEmpId(dialogueHistorysaveRequest.getTaskEmpId());
            history.setObjectId(dialogueHistorysaveRequest.getObjectId());
            history.setQuestion(aiQuestionList.get(0).getInterviewQuestion());
            history.setAiVoiceFileUrl(aiQuestionList.get(0).getAiQuestionVoiceFileUrl());
            history.setStatus(AiInterviewConstant.EmpTaskQuestionStatus.NO_PASS);
            history.setAskTime(DateTime.now());
        } else {
            history = new AiInterviewMyDialogueHistory();
            history.setTaskEmpId(dialogueHistorysaveRequest.getTaskEmpId());
            history.setObjectId(dialogueHistorysaveRequest.getObjectId());
            history.setQuestionId(dialogueHistorysaveRequest.getQuestionId());
            history.setAskTime(DateTime.now());
            // 如果 follow_up_question不为空，存在跟随问题
            if (!analyzeList.isEmpty() && StrUtil.isNotEmpty(analyzeList.get(0).getStr("follow_up_question"))){
                history.setQuestion(analyzeList.get(0).getStr("follow_up_question"));
            } else {
                // 如果为空，则需要根据AI的问题列表生成新的问题
                // 获取已经回答过的问题ID集合
                Set<Long> answeredQuestionIds = dialogueHistoryList.stream()
                        .map(AiInterviewMyDialogueHistory::getQuestionId)
                        .collect(Collectors.toSet());

                // 查找第一个未被回答的问题
                Optional<AiInterviewDialogueQuestionResponse> nextQuestionOpt = aiQuestionList.stream()
                        .filter(question -> !answeredQuestionIds.contains(question.getId()))
                        .findFirst();

                AiInterviewDialogueQuestionResponse nextQuestion = nextQuestionOpt.orElse(null);

                if (Objects.isNull(nextQuestion)) {
                    return null;
                }
                history.setQuestion(nextQuestion.getInterviewQuestion());
                history.setQuestionId(nextQuestion.getId());
                history.setStatus(AiInterviewConstant.EmpTaskQuestionStatus.NO_PASS);
                history.setAiVoiceFileUrl(nextQuestion.getAiQuestionVoiceFileUrl());
            }
        }
        // 如果该问题没有语音文件URL，则需要调用AI模型接口生成
        // 取消AI问题的文字转语言-20250620
        /*if (StrUtil.isBlank(history.getAiVoiceFileUrl())) {
            // 构建AI请求入参
            AiRequest aiRequest = AiRequest.builder()
                    .aiType(AiType.COACH.getCode())
                    .aiSubType(AiSubType.TRAIN_TEXT_TO_SPEECH.getCode())
                    .userId(LoginUtil.getLoginUserCode())
                    .stream(false)
                    .contents(List.of(AiContent.builder()
                            .contentType(AiContentType.JSON.getCode())
                            .content(JSONUtil.toJsonStr(Map.of("content", history.getQuestion())))
                            .build()))
                    .build();
            List<String> urlList = aiService.work(aiRequest)
                    .filter(raw -> CollUtil.isNotEmpty(raw.getContents()))
                    .flatMap(raw -> {
                        // todo 临时妥协方案，后续再优化调整
                        JSONObject fileObject = JSONUtil.parseObj(raw.getContents().get(0).getContent());
                        String url = aiModelConfig.getBaseUrl().replace("/v1", "").concat(fileObject.getStr("url"));
                        return Flux.just(url);
                    })
                    .collectList()
                    .block();
            if (!CollUtil.isEmpty(urlList)) {
                history.setAiVoiceFileUrl(urlList.get(0));
            }
        }*/
        return history;
    }

    @Override
    public List<AiInterviewMyQuestionListResponse> getMyQuestionList(Long taskEmpId) {
        // 查询AI的问题信息
        List<AiInterviewDialogueQuestionResponse> aiQuestionList = aiInterviewDialogueQuestionMapper.getAiQuestionList(taskEmpId);
        if (CollUtil.isEmpty(aiQuestionList)) {
            return new ArrayList<>();
        }

        //获取当前员工已答题列表
        LambdaQueryWrapper<AiInterviewMyDialogueHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiInterviewMyDialogueHistory::getTaskEmpId, taskEmpId);
        queryWrapper.orderByDesc(AiInterviewMyDialogueHistory::getAskTime);

        // 查询历史记录信息
        List<AiInterviewMyDialogueHistory> historyList = aiInterviewMyDialogueHistoryMapper.selectList(queryWrapper);
        //根据问题进行分组
        Map<Long, List<AiInterviewMyDialogueHistory>> historyMap = historyList.stream().collect(Collectors.groupingBy(AiInterviewMyDialogueHistory::getQuestionId));

        List<AiInterviewMyQuestionListResponse> dialogueHistoryList = new ArrayList<>();
        aiQuestionList.stream().forEach(aiQuestion -> {
            List<AiInterviewMyDialogueHistory> historys = historyMap.get(aiQuestion.getId());

            // 如果没有历史记录，则该问题未进行回答
            if (CollUtil.isEmpty(historys)) {
                AiInterviewMyQuestionListResponse response = new AiInterviewMyQuestionListResponse();
                response.setQuestionId(aiQuestion.getId());
                response.setQuestion(aiQuestion.getInterviewQuestion());
                response.setStatus(AiInterviewConstant.EmpTaskQuestionStatus.NO_PASS);
                dialogueHistoryList.add(response);
            } else {
                //获取答题结果为通过或者跳过，如果没有则认为该题未答复
                List<AiInterviewMyDialogueHistory> passHistorys = historys.stream().filter(a -> AiInterviewConstant.EmpTaskQuestionStatus.PASS.equals(a.getStatus()) || AiInterviewConstant.EmpTaskQuestionStatus.SKIP.equals(a.getStatus())).collect(Collectors.toList());
                if (!CollUtil.isEmpty(passHistorys)){
                    AiInterviewMyQuestionListResponse response = new AiInterviewMyQuestionListResponse();
                    response.setQuestionId(aiQuestion.getId());
                    response.setQuestion(aiQuestion.getInterviewQuestion());
                    response.setStatus(AiInterviewConstant.EmpTaskQuestionStatus.PASS);
                    dialogueHistoryList.add(response);
                } else {
                    AiInterviewMyQuestionListResponse response = new AiInterviewMyQuestionListResponse();
                    response.setQuestionId(aiQuestion.getId());
                    response.setQuestion(aiQuestion.getInterviewQuestion());
                    response.setStatus(AiInterviewConstant.EmpTaskQuestionStatus.NO_PASS);
                    dialogueHistoryList.add(response);
                }
            }
        });
        return dialogueHistoryList;
    }
}

