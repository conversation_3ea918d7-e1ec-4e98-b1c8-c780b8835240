package com.deloitte.dhr.ai.module.coach.task.pojo;

import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 培训任务-员工-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiCoachTaskEmpSaveRequest")
public class AiCoachTaskEmpSaveRequest {

    /**
     * 员工编号
     */
    @Length(groups = {Save.class, Submit.class}, max = 20, message = "员工编号不能超过20个字符")
    @NotBlank(groups = {Save.class, Submit.class}, message = "员工编号不能为空")
    @ApiModelProperty(value = "员工编号", name = "empCode")
    private String empCode;

    /**
     * 员工名称
     */
    @Length(groups = {Save.class, Submit.class}, max = 40, message = "员工名称不能超过40个字符")
    @NotBlank(groups = {Save.class, Submit.class}, message = "员工名称不能为空")
    @ApiModelProperty(value = "员工名称", name = "empName")
    private String empName;

    /**
     * 员工部门编码
     */
    @Length(groups = {Save.class, Submit.class}, max = 20, message = "员工部门编码不能超过20个字符")
    @ApiModelProperty(value = "员工部门编码", name = "orgCode")
    private String orgCode;

    /**
     * 员工部门名称
     */
    @Length(groups = {Save.class, Submit.class}, max = 20, message = "员工部门名称不能超过20个字符")
    @ApiModelProperty(value = "员工部门名称", name = "orgName")
    private String orgName;

    /**
     * 员工岗位编码
     */
    @Length(groups = {Save.class, Submit.class}, max = 20, message = "员工岗位编码不能超过20个字符")
    @ApiModelProperty(value = "员工岗位编码", name = "positionCode")
    private String positionCode;

    /**
     * 员工岗位名称
     */
    @Length(groups = {Save.class, Submit.class}, max = 20, message = "员工岗位名称不能超过20个字符")
    @ApiModelProperty(value = "员工岗位名称", name = "positionName")
    private String positionName;

    /**
     * 员工头像
     */
    @Length(groups = {Save.class, Submit.class}, max = 255, message = "员工头像不能超过255个字符")
    @ApiModelProperty(value = "员工头像", name = "empAvatarUrl")
    private String empAvatarUrl;

}