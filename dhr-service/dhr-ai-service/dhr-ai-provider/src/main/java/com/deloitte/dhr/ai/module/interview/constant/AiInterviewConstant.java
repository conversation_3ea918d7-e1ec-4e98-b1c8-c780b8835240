package com.deloitte.dhr.ai.module.interview.constant;

import java.math.BigDecimal;

/**
 * AI访谈-相关常量
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public class AiInterviewConstant {
    /**
     * 递归最大深度
     */
    public static final Integer MAX_DEEP = 5;

    public static final String AiEndMessage = "感谢您的分享！您提供的信息非常宝贵，将会帮助到我们开展后续的工作。祝您一切顺利！";

    /**
     * AI访谈助手状态
     */
    public static class InterviewInfoStatus {
        /**
         * 草稿状态
         */
        public static final String DRAFT = "CG";
        /**
         * 提交状态
         */
        public static final String SUBMIT = "TJ";
    }

    /**
     * 访谈任务状态：CG：草稿，JXZ：进行中，YWC：已完成
     */
    public static class TaskStatus {
        /**
         * 草稿
         */
        public static final String DRAFT = "CG";
        /**
         * 进行中
         */
        public static final String JXZ = "JXZ";
        /**
         * 已完成
         */
        public static final String YWC = "YWC";
        /**
         * 已结束
         */
        public static final String YJS = "YJS";
    }

    /**
     * AI访谈问题类别
     */
    public static class InterviewQuestionType {
        /**
         * 单选题（选择题）
         */
        public static final String SINGLE_CHOICE_QUESTION = "DXT";
        /**
         * 判断题（选择题）
         */
        public static final String TRUE_OR_FALSE_QUESTION = "PDT";
        /**
         * 问答题（文本题）
         */
        public static final String ESSAY_QUESTION = "WDT";
    }
    /**
     * 课程资源类型
     */
    public static class InterviewInfoType {
        /**
         * AI访谈
         */
        public static final String AIFT = "AIFT";
    }

    /**
     * 访谈任务员工状态
     */
    public static class TaskEmpStatus {
        /**
         * 未开始
         */
        public static final String NOT_START = "WKS";
        /**
         * 进行中
         */
        public static final String ONGOING = "JXZ";
        /**
         * 已完成
         */
        public static final String COMPLETED = "YWC";
    }

    /**
     * 访谈任务员工状态
     */
    public static class EmpTaskQuestionStatus {
        /**
         * 通过
         */
        public static final String PASS = "01";
        /**
         * 不通过
         */
        public static final String NO_PASS = "02";
        /**
         * 跳过
         */
        public static final String SKIP = "03";
        /**
         * 结束
         */
        public static final String END = "99";
    }

    /**
     * 对话消息所属
     */
    public static class DialogueMessageBelong {
        /**
         * AI
         */
        public static final String AI = "AI";
        /**
         * 员工
         */
        public static final String EMP = "EMP";
    }

    /**
     * 访谈任务员工报告生成状态
     * 报告状态：01未生成，02生成中，03生成成功，04生成失败
     */
    public static class EmpTaskReportStatus {
        /**
         * 未生成
         */
        public static final String NO_GENERATED = "01";
        /**
         * 生成中
         */
        public static final String GENERATING = "02";
        /**
         * 生成成功
         */
        public static final String GENERATED_SUCCESS = "03";
        /**
         * 生成失败
         */
        public static final String GENERATED_FAIL = "04";
    }
}
