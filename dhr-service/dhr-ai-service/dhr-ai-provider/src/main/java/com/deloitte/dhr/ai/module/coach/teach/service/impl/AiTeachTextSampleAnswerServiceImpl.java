package com.deloitte.dhr.ai.module.coach.teach.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachTextSampleAnswer;
import com.deloitte.dhr.ai.module.coach.teach.mapper.AiTeachTextSampleAnswerMapper;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachTextSampleAnswerResponse;
import com.deloitte.dhr.ai.module.coach.teach.service.AiTeachTextSampleAnswerService;
import com.deloitte.dhr.common.SuperServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 课程资源-教学课程-考核题目-文本题参考答案-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiTeachTextSampleAnswerServiceImpl extends SuperServiceImpl<AiTeachTextSampleAnswerMapper, AiTeachTextSampleAnswer> implements AiTeachTextSampleAnswerService {

    @Autowired
    private AiTeachTextSampleAnswerMapper aiTeachTextSampleAnswerMapper;

    @Override
    public Map<Long, AiTeachTextSampleAnswerResponse> getMapByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiTeachTextSampleAnswer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTeachTextSampleAnswer::getResourceId, resourceId);
        List<AiTeachTextSampleAnswer> list = aiTeachTextSampleAnswerMapper.selectList(queryWrapper);
        return list.stream().collect(Collectors.toMap(AiTeachTextSampleAnswer::getQuestionId, a -> BeanUtil.copyProperties(a, AiTeachTextSampleAnswerResponse.class)));
    }

    @Override
    public void deleteByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiTeachTextSampleAnswer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTeachTextSampleAnswer::getResourceId, resourceId);
        aiTeachTextSampleAnswerMapper.delete(queryWrapper);
    }


}

