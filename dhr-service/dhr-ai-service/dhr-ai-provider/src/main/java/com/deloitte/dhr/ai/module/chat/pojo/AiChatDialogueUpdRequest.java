package com.deloitte.dhr.ai.module.chat.pojo;

import com.deloitte.dhr.common.validation.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * 对话历史记录-编辑
 *
 * <AUTHOR>
 * @date 2024-03-27
 */
@Data
@ApiModel("AiChatDialogueHistoryUpdRequest")
public class AiChatDialogueUpdRequest {

    /**
     * 主键
     */
    @NotNull(groups = {Update.class}, message = "id不能为空")
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 对话标题;默认为聊天的第一句话
     */
    @Length(max = 40, message = "对话标题不能超过40个字符")
    @ApiModelProperty(value = "对话标题", name = "dialogueTitle")
    private String dialogueTitle;

}