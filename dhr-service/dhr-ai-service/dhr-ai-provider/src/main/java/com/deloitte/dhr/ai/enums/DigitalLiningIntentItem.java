package com.deloitte.dhr.ai.enums;


import lombok.Getter;

import static com.deloitte.dhr.ai.enums.DigitalLiningIntentType.*;


/**
 * SSC意图识别类型项目枚举
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Getter
public enum DigitalLiningIntentItem {

    HTXX("HTXX", "合同信息", RS),
    ZMXX("ZMXX", "证明信息", RS),
    RSDA("RSDA", "人事档案", RS),

    ZZJG("ZZJG", "组织机构", ZZ),

    GZXX("GZXX", "工资信息", XC),
    JJXX("JJXX", "奖金信息", XC),

    XJXX("XJXX", "休假信息", KQ),
    KQYC("KQYC", "考勤异常", KQ),

    JXMB("JXMB", "绩效目标", JX),
    JXJG("JXJG", "绩效结果", JX),

    TY("TY", "通用", DigitalLiningIntentType.TY);

    private final String code;

    private final String name;

    private final DigitalLiningIntentType type;

    DigitalLiningIntentItem(String code, String name, DigitalLiningIntentType type) {
        this.code = code;
        this.name = name;
        this.type = type;
    }


    /**
     * 通过name获取枚举
     *
     * @param name 名称
     * @return IntentRecognitionTypeItem
     */
    public static DigitalLiningIntentItem getByName(String name) {
        DigitalLiningIntentItem result = null;
        for (DigitalLiningIntentItem item : values()) {
            if (item.getName().equals(name)) {
                result = item;
            }
        }
        if (result == null) {
            return TY;
        }
        return result;

    }

}
