package com.deloitte.dhr.ai.module.coach.teach.controller;

import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachMyCoachDetailResponse;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachMyCoachSaveRequest;
import com.deloitte.dhr.ai.module.coach.teach.service.AiMyTeachService;
import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperController;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 课程资源-教学课程-我的培训
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/AiTeach/my")
@Api(tags = "课程资源-教学课程-我的培训")
@Validated
public class AiMyTeachController extends SuperController {

    @Autowired
    private AiMyTeachService aiMyTeachService;

    @ApiOperation(value = "详情", notes = "详情")
    @ApiOperationSupport(order = 1)
    @GetMapping("/detail/{taskEmpId}")
    public ResponseVO<AiTeachMyCoachDetailResponse> getDetail(@PathVariable("taskEmpId") Long taskEmpId) {
        return success(aiMyTeachService.getDetail(taskEmpId));
    }

    @ApiOperation(value = "保存", notes = "保存")
    @ApiOperationSupport(order = 2)
    @PostMapping("/save")
    public ResponseVO<Boolean> saveAnswer(@RequestBody @Validated(Save.class) AiTeachMyCoachSaveRequest request) {
        return success(aiMyTeachService.saveAnswer(request, AiCoachConstant.TaskEmpStatus.ONGOING));
    }

    @ApiOperation(value = "提交", notes = "提交")
    @ApiOperationSupport(order = 3)
    @PostMapping("/submit")
    public ResponseVO<Boolean> submitAnswer(@RequestBody @Validated(Submit.class) AiTeachMyCoachSaveRequest request) {
        return success(aiMyTeachService.submitAnswer(request));
    }

}
