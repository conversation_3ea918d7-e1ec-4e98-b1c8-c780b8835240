package com.deloitte.dhr.ai.module.interview.resource.pojo;

import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewTestQuestionSaveRequest;
import com.deloitte.dhr.ai.module.interview.validation.Save;
import com.deloitte.dhr.ai.module.interview.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * AI访谈助手-保存
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewSaveRequest")
public class AiInterviewSaveRequest {

    /**
     * 课程资源-基本信息
     */
    @Valid
    @ApiModelProperty(value = "课程资源-基本信息", name = "resourceInfo")
    @NotNull(groups = {Save.class, Submit.class}, message = "课程资源基本信息不能为空")
    private AiInterviewInfoSaveRequest resourceInfo;

    /**
     * 课程资源-学习附件信息
     */
    @Valid
    @NotEmpty(groups = Submit.class, message = "学习资料不能为空")
    @ApiModelProperty(value = "课程资源-学习附件信息", name = "attachInfoList")
    private List<AiInterviewResourceAttachSaveRequest> attachInfoList;

    /**
     * AI访谈对象信息
     */
    @Valid
    @ApiModelProperty(value = "AI访谈对象信息", name = "objectInfo")
    @NotNull(groups = {Submit.class}, message = "AI访谈对象信息不能为空")
    private AiInterviewObjectSaveRequest objectInfo;

    /**
     * AI访谈-AI访谈沟通框架信息
     */
    @Valid
    @NotEmpty(groups = {Submit.class}, message = "AI访谈沟通框架信息不能为空")
    @ApiModelProperty(value = "AI访谈-AI访谈沟通框架信息", name = "questionInfoList")
    private List<AiInterviewTestQuestionSaveRequest> questionInfoList;

}