package com.deloitte.dhr.ai.module.chat.pojo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 聊天消息-列表-查询参数
 *
 * <AUTHOR>
 * @date 2024-03-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AiChatMsgListRequest")
public class AiChatMsgListRequest {

    @NotNull(message = "聊天对话ID不能为空")
    @ApiModelProperty(value = "聊天对话ID", name = "dialogueId")
    private Long dialogueId;

    @ApiModelProperty(value = "发送时间", name = "sendTime")
    private Date sendTime;

    @ApiModelProperty(value = "条数", name = "size")
    private Integer size = 20;

    @ApiModelProperty(value = "AI类型", name = "aiType")
    private String aiType;

    @ApiModelProperty(value = "AI子类型", name = "aiSubType")
    private String aiSubType;

}