package com.deloitte.dhr.ai.module.coach.teach.pojo;

import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 课程资源-教学课程-我的培训-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTeachMyCoachSaveRequest")
public class AiTeachMyCoachSaveRequest {

    /**
     * 培训任务员工ID
     */
    @NotNull(groups = {Save.class, Submit.class}, message = "培训任务员工ID不能为空")
    @ApiModelProperty(value = "培训任务员工ID", name = "taskEmpId")
    private Long taskEmpId;

    /**
     * 消耗时间（minute）
     */
    @ApiModelProperty(value = "消耗时间（minute）", name = "elapsedTime")
    private BigDecimal elapsedTime;

    /**
     * 考核问题答案信息
     */
    @Valid
    @NotEmpty(groups = {Submit.class}, message = "考核问题答案信息不能为空")
    @ApiModelProperty(value = "问题答案信息", name = "answerInfoList")
    private List<AiTeachMyCoachQuestionSaveRequest> answerInfoList;

}