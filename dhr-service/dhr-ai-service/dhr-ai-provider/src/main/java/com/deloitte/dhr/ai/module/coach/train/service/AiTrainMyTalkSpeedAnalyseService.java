package com.deloitte.dhr.ai.module.coach.train.service;

import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainMyTalkSpeedAnalyse;
import com.deloitte.dhr.ai.module.coach.train.pojo.AiTrainMyTalkSpeedAnalyseChartResponse;
import com.deloitte.dhr.common.SuperService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课程资源-AI陪练-我的语速分析-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTrainMyTalkSpeedAnalyseService extends SuperService<AiTrainMyTalkSpeedAnalyse> {

    /**
     * 查询-我的语速分析
     *
     * @param taskEmpId 培训任务员工ID
     * @return List<AiTrainMyTalkSpeedAnalyseResponse>
     */
    List<AiTrainMyTalkSpeedAnalyseChartResponse> getMyTalkSpeedAnalyseChart(Long taskEmpId);

    /**
     * 保存-我的语速分析信息
     *
     * @param taskEmpId  培训任务员工ID
     * @param historyId  本次员工对话历史ID
     * @param speechRate 语速：正常，较快，较慢
     * @return Boolean
     */
    Boolean saveData(Long taskEmpId, Long historyId, String speechRate);

    /**
     * 计算语速得分
     * 语速分析得分 = 正常语速的占比，比如正常语速占比67%，那么语速分=67分
     *
     * @param taskEmpId 培训任务员工ID
     * @return 语速得分
     */
    BigDecimal calculateScore(Long taskEmpId);

}
