package com.deloitte.dhr.ai.module.coach.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 培训任务-员工-列表-筛选条件
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiCoachTaskEmpListRequest")
public class AiCoachTaskEmpListRequest {

    /**
     * 培训任务ID
     */
    @NotNull(message = "培训任务ID不能为空")
    @ApiModelProperty(value = "培训任务ID", name = "taskId")
    private Long taskId;

    /**
     * 是否完成
     */
    @NotNull(message = "是否完成不能为空")
    @ApiModelProperty(value = "是否完成", name = "isCompleted")
    private Boolean isCompleted;

    /**
     * 员工编号
     */
    @ApiModelProperty(value = "员工编号", name = "empCode")
    private String empCode;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称", name = "empName")
    private String empName;

    /**
     * 员工部门编码
     */
    @ApiModelProperty(value = "员工部门编码", name = "orgCode")
    private String orgCode;

    /**
     * 员工部门名称
     */
    @ApiModelProperty(value = "员工部门名称", name = "orgName")
    private String orgName;

    /**
     * 员工岗位编码
     */
    @ApiModelProperty(value = "员工岗位编码", name = "positionCode")
    private String positionCode;

    /**
     * 员工岗位名称
     */
    @ApiModelProperty(value = "员工岗位名称", name = "positionName")
    private String positionName;

    /**
     * 评分结果;0：不通过，1：通过
     */
    @ApiModelProperty(value = "评分结果;0：不通过，1：通过", name = "scoreResult")
    private Boolean scoreResult;

}