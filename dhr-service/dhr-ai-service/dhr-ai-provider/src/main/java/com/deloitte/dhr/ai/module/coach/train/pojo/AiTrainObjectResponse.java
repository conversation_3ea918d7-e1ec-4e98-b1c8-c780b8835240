package com.deloitte.dhr.ai.module.coach.train.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 课程资源-AI陪练-对象信息
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainObjectResponse")
public class AiTrainObjectResponse {

    /**
     * 陪练对象ID
     */
    @ApiModelProperty(value = "陪练对象ID", name = "id")
    private Long id;

    /**
     * 课程资源ID
     */
    @ApiModelProperty(value = "课程资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * 陪练对象头像URL
     */
    @ApiModelProperty(value = "陪练对象头像URL", name = "objectAvatarUrl")
    private String objectAvatarUrl;

    /**
     * 陪练对象名称
     */
    @ApiModelProperty(value = "陪练对象名称", name = "objectName")
    private String objectName;

    /**
     * 陪练对象背景信息
     */
    @ApiModelProperty(value = "陪练对象背景信息", name = "objectBackgroundInfo")
    private String objectBackgroundInfo;

    /**
     * 陪练对象性别;male：男，female：女，unknown：未知
     */
    @ApiModelProperty(value = "陪练对象性别;male：男，female：女，unknown：未知", name = "objectGender")
    private String objectGender;

    /**
     * 陪练对象标签信息
     */
    @ApiModelProperty(value = "陪练对象标签信息", name = "tagInfoList")
    private List<String> tagInfoList;

}