package com.deloitte.dhr.ai.module.ai.annotation;


import com.deloitte.dhr.ai.enums.AiSscIntentAction;
import com.deloitte.dhr.ai.enums.AiSscIntentItem;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 行为动作策略-自定义注解
 * 用于新建一个策略的时候，设置这个策略的类型和动作
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ActionStrategyType {

    /**
     * 意图识别类型
     *
     * @return {@link AiSscIntentAction};
     */
    AiSscIntentItem item() default AiSscIntentItem.TY;

    /**
     * 意图识别动作
     *
     * @return {@link AiSscIntentAction};
     */
    AiSscIntentAction action() default AiSscIntentAction.IR;

}
