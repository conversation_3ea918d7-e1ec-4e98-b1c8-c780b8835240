package com.deloitte.dhr.ai.module.coach.teach.service;

import com.deloitte.dhr.ai.module.coach.teach.domain.AiTeachMyChoiceAnswer;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachMyChoiceAnswerResponse;
import com.deloitte.dhr.ai.module.coach.teach.pojo.AiTeachMyCoachQuestionSaveRequest;
import com.deloitte.dhr.common.SuperService;

import java.util.List;
import java.util.Map;

/**
 * 课程资源-教学课程-我的选择问题答案-相关业务接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface AiTeachMyChoiceAnswerService extends SuperService<AiTeachMyChoiceAnswer> {

    /**
     * 删除-选择题答案信息
     *
     * @param taskEmpId 培训任务员工ID
     */
    void deleteByTaskEmpId(Long taskEmpId);

    /**
     * 保存/更新-我的选择题答案信息
     *
     * @param requestList 我的选择题答案-保存/更新信息
     * @param resourceId  课程资源ID
     * @param taskId      培训任务Id
     * @param taskEmpId   培训任务员工ID
     * @return Map<Boolean, Integer>，key值：题目ID，value值：是否答题正确
     */
    Map<Long, Boolean> saveData(List<AiTeachMyCoachQuestionSaveRequest> requestList, Long resourceId, Long taskId, Long taskEmpId);

    /**
     * 查询-我的选择题答案信息
     *
     * @param taskEmpId 培训任务员工ID
     * @return List<AiTeachMyChoiceAnswerDetailResponse>
     */
    List<AiTeachMyChoiceAnswerResponse> getByTaskEmpId(Long taskEmpId);

}
