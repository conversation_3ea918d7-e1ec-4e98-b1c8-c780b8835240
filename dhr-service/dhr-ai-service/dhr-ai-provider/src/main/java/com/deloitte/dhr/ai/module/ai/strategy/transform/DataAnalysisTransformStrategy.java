package com.deloitte.dhr.ai.module.ai.strategy.transform;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiTransformType;
import com.deloitte.dhr.ai.module.ai.annotation.TransformStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Set;


/**
 * AI转换-数据分析-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@TransformStrategyType(AiTransformType.DATA_ANALYSIS)
public class DataAnalysisTransformStrategy implements ResponseTransformStrategy {

    Set<String> llmTitles = Set.of("问题理解", "分析结果", "生成图表");
    Set<String> codeTitles = Set.of("提取VANNA的SQL", "提取VANNA的SQL执行结果");
    Set<String> irrelevantTitles = Set.of("无关问题");

    @Override
    public Flux<List<AiContent>> transformStream(Flux<String> rawStream, AiRequest aiRequest) {
        StringBuilder contentTypeBuilder = new StringBuilder();
        return rawStream
                .map(raw -> {
                    AiContent aiContent = AiContent.builder().build();
                    JSONObject rawObj = JSONObject.parseObject(raw);
                    String event = rawObj.getString("event");
                    JSONObject data = rawObj.getJSONObject("data");
                    String nodeType = data != null ? data.getString("node_type") : "";
                    String title = data != null ? data.getString("title") : "";

                    // 每个节点开始时候重新赋值contentType
                    if (isNodeStartedEvent(event, nodeType, title)) {
                        contentTypeBuilder.append(title);
                    }

                    // 获取内容
                    if ("message".equals(event) && contentTypeBuilder.length() > 0) {
                        aiContent.setContent(rawObj.getString("answer"));
                        aiContent.setContentType(getContentType(contentTypeBuilder));
                    }

                    if ("node_finished".equals(event) && "code".equals(nodeType) && codeTitles.contains(title)) {
                        aiContent.setContent(data.getJSONObject("outputs").getString("result"));
                        aiContent.setContentType(getContentType(contentTypeBuilder));
                    }

                    if ("node_finished".equals(event) && "answer".equals(nodeType) && irrelevantTitles.contains(title)) {
                        aiContent.setContent(data.getJSONObject("outputs").getString("answer"));
                        aiContent.setContentType(getContentType(contentTypeBuilder));
                    }

                    // 每个节点完成时候清除contentType
                    if ("node_finished".equals(event)) {
                        contentTypeBuilder.setLength(0);
                    }

                    return aiContent;
                })
                .filter(aiContent -> StrUtil.isNotBlank(aiContent.getContentType()))
                .map(List::of);
    }

    @Override
    public Flux<List<AiContent>> transformBlock(Flux<String> rawStream, AiRequest aiRequest) {
        return Flux.empty();
    }

    /**
     * 是否是节点开始事件
     *
     * @param event    事件
     * @param nodeType 节点类型
     * @param title    节点标题
     * @return boolean
     */
    private boolean isNodeStartedEvent(String event, String nodeType, String title) {
        if (!"node_started".equals(event)) {
            return false;
        }
        if ("llm".equals(nodeType)) {
            return llmTitles.contains(title);
        }
        if ("code".equals(nodeType)) {
            return codeTitles.contains(title);
        }
        if ("answer".equals(nodeType)) {
            return irrelevantTitles.contains(title);
        }
        return false;
    }

    /**
     * 获取内容类型
     *
     * @param contentTypeBuilder 内容类型
     * @return 内容类型
     */
    private String getContentType(StringBuilder contentTypeBuilder) {
        return AiContentType.getByName(contentTypeBuilder.toString()).getCode();
    }

}



