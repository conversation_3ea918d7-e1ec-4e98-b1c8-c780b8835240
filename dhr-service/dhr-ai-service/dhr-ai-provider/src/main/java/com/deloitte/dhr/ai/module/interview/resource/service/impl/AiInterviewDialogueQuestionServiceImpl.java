package com.deloitte.dhr.ai.module.interview.resource.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.config.AiModelConfig;
import com.deloitte.dhr.ai.enums.AiSubType;
import com.deloitte.dhr.ai.enums.AiType;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.module.ai.service.AiService;
import com.deloitte.dhr.ai.module.interview.train.domain.AiInterviewDialogueQuestion;
import com.deloitte.dhr.ai.module.interview.train.mapper.AiInterviewDialogueQuestionMapper;
import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewDialogueQuestionResponse;
import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewTestQuestionSaveRequest;
import com.deloitte.dhr.ai.module.interview.resource.service.AiInterviewDialogueQuestionService;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * AI访谈-访谈任务-对话问题-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Slf4j
@Service
public class AiInterviewDialogueQuestionServiceImpl extends SuperServiceImpl<AiInterviewDialogueQuestionMapper, AiInterviewDialogueQuestion> implements AiInterviewDialogueQuestionService {


    @Autowired
    private AiInterviewDialogueQuestionMapper aiInterviewDialogueQuestionMapper;
    @Autowired
    private AiModelConfig aiModelConfig;
    @Autowired
    private AiService aiService;


    @Override
    public List<AiInterviewDialogueQuestionResponse> getByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiInterviewDialogueQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiInterviewDialogueQuestion::getResourceId, resourceId);
        queryWrapper.orderByAsc(AiInterviewDialogueQuestion::getQuestionOrder);
        List<AiInterviewDialogueQuestion> list = aiInterviewDialogueQuestionMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(list, AiInterviewDialogueQuestionResponse.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AiInterviewDialogueQuestion> saveData(List<AiInterviewTestQuestionSaveRequest> requestList, Long resourceId) {

        // 删除旧的AI陪练-对话问题数据
        this.deleteByResourceId(resourceId);

        // 保存新的AI陪练-对话问题数据
        if (CollUtil.isEmpty(requestList)) {
            return Collections.emptyList();
        }
        List<AiInterviewDialogueQuestion> questionList = new ArrayList<>();
        int index = 1;
        for (AiInterviewTestQuestionSaveRequest request : requestList){
            AiInterviewDialogueQuestion question = new AiInterviewDialogueQuestion();
            question.setResourceId(resourceId);
            question.setInterviewQuestion(request.getInterviewQuestion());
            question.setQuestionOrder(index);
            question.setInterviewObjective(request.getInterviewObjective());

            questionList.add(question);
            index = index + 1;
        }
        this.saveBatch(questionList);
        return questionList;
    }

    @Override
    public void deleteByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiInterviewDialogueQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiInterviewDialogueQuestion::getResourceId, resourceId);
        aiInterviewDialogueQuestionMapper.delete(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAiQuestionVoice(List<AiInterviewDialogueQuestion> questionList) {

        // 构建AI请求入参
        AiContent aiContent = AiContent.builder().contentType(AiContentType.JSON.getCode()).build();
        AiRequest aiRequest = AiRequest.builder()
                .aiType(AiType.COACH.getCode())
                .aiSubType(AiSubType.TRAIN_TEXT_TO_SPEECH.getCode())
                .userId(LoginUtil.getLoginUserCode())
                .stream(false)
                .contents(List.of(aiContent))
                .build();


        SecurityContext context = SecurityContextHolder.getContext();
        Flux.fromIterable(questionList)
                .delayElements(Duration.ofSeconds(1))
                .flatMap(question -> {
                    SecurityContextHolder.setContext(context);
                    aiContent.setContent(JSONUtil.toJsonStr(Map.of("content", question.getInterviewQuestion())));
                    return aiService.work(aiRequest, LoginUtil.getLoginUser())
                            .filter(raw -> CollUtil.isNotEmpty(raw.getContents()))
                            .flatMap(raw -> {
                                // todo 临时妥协方案，后续再优化调整
                                JSONObject fileObject = JSONUtil.parseObj(raw.getContents().get(0).getContent());
                                String url = aiModelConfig.getBaseUrl().replace("/v1", "").concat(fileObject.getStr("url"));
                                question.setAiQuestionVoiceFileUrl(url);
                                return Flux.just(question);
                            })
                            .onErrorResume(e -> {
                                log.error("问题生成语音出错：{}，原因：{}", question.getInterviewQuestion(), e.getMessage());
                                return Mono.empty();
                            });
                })
                .subscribe(entity -> {
                    SecurityContextHolder.setContext(context);
                    update(entity);
                }, error -> log.error("生成问题语音出错: {}", error.getMessage()));
    }

}

