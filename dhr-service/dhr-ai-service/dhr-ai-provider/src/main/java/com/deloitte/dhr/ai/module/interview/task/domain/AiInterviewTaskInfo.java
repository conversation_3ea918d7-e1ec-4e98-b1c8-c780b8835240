package com.deloitte.dhr.ai.module.interview.task.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperLogicModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * AI访谈-访谈任务-基本信息表
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_interview_task_info")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("访谈任务表")
public class AiInterviewTaskInfo extends SuperLogicModel<AiInterviewTaskInfo> {

    /**
     * 课程资源ID
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 访谈任务名称
     */
    @TableField(value = "task_name")
    private String taskName;

    /**
     * 访谈任务开始时间
     */
    @TableField(value = "start_date_time")
    private Date startDateTime;

    /**
     * 访谈任务结束时间
     */
    @TableField(value = "end_date_time")
    private Date endDateTime;

    /**
     * 访谈任务封面URL
     */
    @TableField(value = "task_cover_url")
    private String taskCoverUrl;

    /**
     * 访谈任务任务状态;CG：草稿，JXZ：进行中，YWC：已完成，YJS：已结束
     */
    @TableField(value = "task_status")
    private String taskStatus;

}