package com.deloitte.dhr.ai.module.chat.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI聊天-聊天对话-保存
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("AiChatDialogueRequest")
public class AiChatDialogueSaveRequest {

    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    @ApiModelProperty(value = "AI分组：数字人-digital_human，文本聊天-text_chat", name = "aiGroup")
    private String aiGroup;

    @ApiModelProperty(value = "对话标题", name = "dialogueTitle")
    private String dialogueTitle;

}