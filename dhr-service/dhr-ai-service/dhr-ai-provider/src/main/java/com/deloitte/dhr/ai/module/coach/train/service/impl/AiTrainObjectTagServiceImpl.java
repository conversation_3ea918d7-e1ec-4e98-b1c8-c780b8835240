package com.deloitte.dhr.ai.module.coach.train.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiSubType;
import com.deloitte.dhr.ai.enums.AiType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.module.ai.service.AiService;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainObjectTag;
import com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainObjectTagMapper;
import com.deloitte.dhr.ai.module.coach.train.service.AiTrainObjectTagService;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 课程资源-AI陪练-对象标签-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiTrainObjectTagServiceImpl extends SuperServiceImpl<AiTrainObjectTagMapper, AiTrainObjectTag> implements AiTrainObjectTagService {

    @Autowired
    private AiTrainObjectTagMapper aiTrainObjectTagMapper;
    @Autowired
    private AiService aiService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(String objectBackgroundInfo, Long resourceId, Long objectId) {

        // 先删除旧的标签信息
        this.deleteByResourceId(resourceId);

        // 再保存新的数据
        if (StrUtil.isBlank(objectBackgroundInfo)) {
            return;
        }
        // 调用背景信息标签抽取AI接口
        // 构建AI请求入参
        AiRequest aiRequest = AiRequest.builder()
                .aiType(AiType.COACH.getCode())
                .aiSubType(AiSubType.TRAIN_TAG_EXTRACTOR.getCode())
                .userId(LoginUtil.getLoginUserCode())
                .stream(false)
                .contents(List.of(AiContent.builder()
                        .contentType(AiContentType.JSON.getCode())
                        .content(JSON.toJSONString(Map.of("content", objectBackgroundInfo)))
                        .build()))
                .build();
        aiService.work(aiRequest, LoginUtil.getLoginUser())
                .filter(raw -> CollUtil.isNotEmpty(raw.getContents()))
                .collectList()
                .subscribe(rawList -> {
                    List<AiTrainObjectTag> list = new ArrayList<>();
                    rawList.forEach(raw -> list.add(new AiTrainObjectTag(resourceId, objectId, raw.getContents().get(0).getContent(), null)));
                    if (CollUtil.isNotEmpty(list)) {
                        this.saveBatch(list);
                    }
                });
    }

    @Override
    public void deleteByResourceId(Long resourceId) {
        LambdaQueryWrapper<AiTrainObjectTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrainObjectTag::getResourceId, resourceId);
        aiTrainObjectTagMapper.delete(queryWrapper);
    }

}

