package com.deloitte.dhr.ai.module.ai.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI陪练-对话分析
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AiInterviewChatAnalyzeRequest")
public class AiInterviewChatAnalyzeRequest {

    @ApiModelProperty(value = "题目名称", name = "interviewQuestion")
    private String interviewQuestion;

    @ApiModelProperty(value = "问题评估", name = "interviewObjective")
    private String interviewObjective;

    @ApiModelProperty(value = "用户答案", name = "interviewAnswer")
    private String interviewAnswer;
}
