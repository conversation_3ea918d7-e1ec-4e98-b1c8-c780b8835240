package com.deloitte.dhr.ai.module.coach.train.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiSubType;
import com.deloitte.dhr.ai.enums.AiType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.module.ai.pojo.AiTrainChatAnalyzeRequest;
import com.deloitte.dhr.ai.module.ai.service.AiService;
import com.deloitte.dhr.ai.module.coach.constant.AiCoachConstant;
import com.deloitte.dhr.ai.module.coach.constant.AiTrainAbilityAnalyseEnum;
import com.deloitte.dhr.ai.module.coach.constant.AiTrainScoreItemEnum;
import com.deloitte.dhr.ai.module.coach.task.domain.AiCoachTaskEmp;
import com.deloitte.dhr.ai.module.coach.task.mapper.AiCoachTaskEmpMapper;
import com.deloitte.dhr.ai.module.coach.task.mapper.AiCoachTaskInfoMapper;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainMyDialogueHistory;
import com.deloitte.dhr.ai.module.coach.train.domain.AiTrainMyScoringItem;
import com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainDialogueQuestionMapper;
import com.deloitte.dhr.ai.module.coach.train.mapper.AiTrainMyDialogueHistoryMapper;
import com.deloitte.dhr.ai.module.coach.train.pojo.*;
import com.deloitte.dhr.ai.module.coach.train.service.*;
import com.deloitte.dhr.ai.utils.CheckUtils;
import com.deloitte.dhr.ai.utils.SpeechSpeedCalculatorUtil;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.deloitte.dhr.ai.module.coach.constant.AiTrainAbilityAnalyseEnum.logical_expression;
import static com.deloitte.dhr.ai.module.coach.constant.AiTrainAbilityAnalyseEnum.professional_knowledge;

/**
 * 课程资源-AI陪练-我的培训-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class AiMyTrainServiceImpl implements AiMyTrainService {

    @Autowired
    private AiCoachTaskInfoMapper aiCoachTaskInfoMapper;
    @Autowired
    private AiCoachTaskEmpMapper aiCoachTaskEmpMapper;
    @Autowired
    private AiTrainMyTalkSpeedAnalyseService aiTrainMyTalkSpeedAnalyseService;
    @Autowired
    private AiTrainMyScoringItemService aiTrainMyScoringItemService;
    @Autowired
    private AiTrainMyDialogueHistoryService aiTrainMyDialogueHistoryService;
    @Autowired
    private AiTrainMyAbilityAnalyseService aiTrainMyAbilityAnalyseService;
    @Autowired
    private AiTrainMyDialogueHistoryMapper aiTrainMyDialogueHistoryMapper;
    @Autowired
    private AiTrainDialogueQuestionMapper aiTrainDialogueQuestionMapper;
    @Autowired
    private AiService aiService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AiTrainMySendResponse sendMessage(AiTrainMySendRequest request) {
        AiTrainMySendResponse response = new AiTrainMySendResponse();

        // 查询培训对象信息
        AiTrainObjectResponse object = aiCoachTaskEmpMapper.getMyObjectDetail(request.getTaskEmpId());
        if (object == null) {
            throw new CommRunException("培训对象不存在，数据异常");
        }

        // 查询AI对话问题信息
        List<AiTrainDialogueQuestionResponse> aiQuestionList = aiTrainDialogueQuestionMapper.getAiQuestionList(request.getTaskEmpId());
        if (CollUtil.isEmpty(aiQuestionList)) {
            throw new CommRunException("AI生成对话提示信息为空，数据异常");
        }
        // 当前AI对话问题
        AiTrainDialogueQuestionResponse questionResponse = aiQuestionList.stream().filter(a -> StrUtil.equals(a.getQuestionName(), request.getAiMessage())).findAny().orElseThrow(() -> new CommRunException("未在AI对话提示题库里找到本次AI提问问题"));

        // 获取AI维度分析信息
        List<JSONObject> analyzeList = this.getAiChatAnalyze(questionResponse, request.getEmpMessage());
        analyzeList = analyzeList == null ? List.of() : analyzeList;

        // 保存相关分析信息
        Long empMessageId = this.saveChatAnalyseInfo(request, object.getId(), analyzeList);
        response.setCurrentEmpMessageId(empMessageId);

        // 返回AI下个问题
        List<AiTrainMyDialogueHistoryResponse> dialogueHistoryList = aiTrainMyDialogueHistoryMapper.getMyDialogueHistory(new AiTrainMyDialogueHistoryListRequest(request.getTaskEmpId(), null, null));
        AiTrainDialogueQuestionResponse nextAiQuestion = aiTrainMyDialogueHistoryService.getNextAiQuestion(request.getAiMessage(), aiQuestionList, dialogueHistoryList);
        if (nextAiQuestion != null) {
            response.setIsDialogueEnd(false);
            response.setNextAiMessageInfo(new AiTrainMyDialogueHistoryResponse(null, nextAiQuestion.getObjectAvatarUrl(), nextAiQuestion.getQuestionName(), DateTime.now(), AiCoachConstant.DialogueMessageBelong.AI, nextAiQuestion.getAiQuestionVoiceFileUrl()));
        } else {
            response.setIsDialogueEnd(true);
        }

        // 返回分析信息
        response.setSpeedName(SpeechSpeedCalculatorUtil.calculateSpeechRate(request.getEmpMessage(), request.getEmpMessageVoiceDurationSeconds()));
        List<AiTrainMyAbilityScoreResponse> abilityAnalyseInfoList = analyzeList.stream().map(analyze -> {
            String type = analyze.getString("type");
            return AiTrainMyAbilityScoreResponse.builder()
                    .abilityName(type)
                    .abilityCode(AiTrainAbilityAnalyseEnum.getCodeByName(type))
                    .abilityAnalyseScore(analyze.getBigDecimal("score"))
                    .abilityAnalyseDesc(analyze.getString("desc")).build();
        }).collect(Collectors.toList());
        response.setAbilityAnalyseInfoList(abilityAnalyseInfoList);

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean pauseTraining(AiTrainMyCompetedRequest request, String status) {
        AiCoachTaskEmp taskEmp = aiCoachTaskEmpMapper.selectById(request.getTaskEmpId());
        // 校验
        this.verify(taskEmp);

        // 更新-培训任务员工相关信息
        taskEmp.setElapsedTime(request.getElapsedTime());
        taskEmp.setStatus(status);
        if (!StrUtil.equals(status, AiCoachConstant.TaskEmpStatus.COMPLETED)) {
            aiCoachTaskEmpMapper.updateById(taskEmp);
            return true;
        }
        // 如果是提交（结束）AI陪练，则需要计算每项得分和最总得分
        taskEmp.setCompleteTime(DateTime.now());

        // 计算并更新培训员工相关得分结果
        this.calculateResult(taskEmp);

        // 更新员工相关信息
        aiCoachTaskEmpMapper.updateById(taskEmp);

        // 更新-培训任务状态
        aiCoachTaskInfoMapper.updateTaskStatus(taskEmp.getTaskId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean endTraining(AiTrainMyCompetedRequest request) {
        return this.pauseTraining(request, AiCoachConstant.TaskEmpStatus.COMPLETED);
    }

    /**
     * 计算AI陪练最终结果
     *
     * @param taskEmp 培训任务员工
     */
    private void calculateResult(AiCoachTaskEmp taskEmp) {

        // 语速分析得分 = 正常语速的占比，比如正常语速占比67%，那么语速分=67分
        BigDecimal normalSpeedScore = aiTrainMyTalkSpeedAnalyseService.calculateScore(taskEmp.getId());

        // 能力项得分 = (每项能力项分数 * 每项能力项权重)之和
        BigDecimal abilityScore = aiTrainMyAbilityAnalyseService.calculateScore(taskEmp.getId());

        // 当语速分为空的时候，总得分 = 能力分；否则：总得分 = 语速分析得分 * 20% + 能力项得分 * 80%
        BigDecimal totalScore = normalSpeedScore == null ? abilityScore :
                normalSpeedScore.multiply(AiTrainScoreItemEnum.speech_speed.getWeight()).add(abilityScore.multiply(AiTrainScoreItemEnum.ability.getWeight())).multiply(BigDecimal.valueOf(0.01));

        // 保存最终得分项信息
        List<AiTrainMyScoringItem> itemList = new ArrayList<>();
        itemList.add(new AiTrainMyScoringItem(taskEmp.getId(), AiTrainScoreItemEnum.speech_speed.getCode(), AiTrainScoreItemEnum.speech_speed.getName(), normalSpeedScore, AiTrainScoreItemEnum.speech_speed.getWeight()));
        itemList.add(new AiTrainMyScoringItem(taskEmp.getId(), AiTrainScoreItemEnum.ability.getCode(), AiTrainScoreItemEnum.ability.getName(), abilityScore, AiTrainScoreItemEnum.ability.getWeight()));
        aiTrainMyScoringItemService.saveBatch(itemList);

        // 评分结果：总体评分 >= 65分，通过 else：不通过
        taskEmp.setScoreResult(totalScore.compareTo(BigDecimal.valueOf(65)) >= 0);
        taskEmp.setFinalScore(totalScore);

    }

    /**
     * 保存相关分析信息
     *
     * @param request  消息信息
     * @param objectId 对象ID
     * @return 员工消息ID
     */
    private Long saveChatAnalyseInfo(AiTrainMySendRequest request, Long objectId, List<JSONObject> analyzeList) {

        // 保存-聊天信息
        AiTrainMyDialogueHistory aiDialogue = new AiTrainMyDialogueHistory(request.getTaskEmpId(), objectId, request.getAiMessage(), DateTime.now(), AiCoachConstant.DialogueMessageBelong.AI, request.getAiMessageVoiceFileUrl());
        AiTrainMyDialogueHistory empDialogue = new AiTrainMyDialogueHistory(request.getTaskEmpId(), objectId, request.getEmpMessage(), DateTime.now(), AiCoachConstant.DialogueMessageBelong.EMP, request.getEmpMessageVoiceFileUrl());
        aiTrainMyDialogueHistoryService.saveBatch(Arrays.asList(aiDialogue, empDialogue));
        Long historyId = empDialogue.getId();

        // 保存-我的能力分析信息
        Integer dialogueQuestionNum = aiCoachTaskEmpMapper.getDialogueQuestionNum(request.getTaskEmpId());
        BigDecimal eachQuestionScore = BigDecimal.valueOf(100).divide(BigDecimal.valueOf(dialogueQuestionNum), 1, RoundingMode.HALF_UP);
        List<AiTrainMyAbilityScoreSaveRequest> saveRequestList = new ArrayList<>();
        analyzeList.forEach(analyze -> {
            // 本次回答指标得分 = 每道题分数 * 指标得分比例
            BigDecimal abilityAnalyseScore = eachQuestionScore.multiply(analyze.getBigDecimal("score")).multiply(BigDecimal.valueOf(0.01));
            saveRequestList.add(new AiTrainMyAbilityScoreSaveRequest(analyze.getString("type"), abilityAnalyseScore, analyze.getString("desc")));
        });
        aiTrainMyAbilityAnalyseService.saveData(request.getTaskEmpId(), historyId, saveRequestList);

        // 保存-我的语速分析信息
        aiTrainMyTalkSpeedAnalyseService.saveData(request.getTaskEmpId(), historyId, SpeechSpeedCalculatorUtil.calculateSpeechRate(request.getEmpMessage(), request.getEmpMessageVoiceDurationSeconds()));

        return historyId;
    }

    /**
     * 校验
     *
     * @param taskEmp 员工信息
     */
    private void verify(AiCoachTaskEmp taskEmp) {

        // 校验-数据是否存在
        CheckUtils.checkNull(taskEmp, "该数据不存在");

        // 校验-是否可以修改
        if (StrUtil.equals(taskEmp.getStatus(), AiCoachConstant.TaskEmpStatus.COMPLETED)) {
            throw new CommRunException("培训已完成，请勿继续作答");
        }

        if (StrUtil.equals(taskEmp.getStatus(), AiCoachConstant.TaskEmpStatus.CLOSE)) {
            throw new CommRunException("培训已结束，请勿继续作答");
        }

    }

    /**
     * 获取AI聊天分析信息
     *
     * @param questionResponse 问题
     * @param empMessage       用户回答
     * @return List<JSONObject>
     */
    private List<JSONObject> getAiChatAnalyze(AiTrainDialogueQuestionResponse questionResponse, String empMessage) {
        AiTrainChatAnalyzeRequest chatAnalyzeRequest = AiTrainChatAnalyzeRequest.builder()
                .question(questionResponse.getQuestionName())
                .sampleAnswer(questionResponse.getSampleAnswerContent())
                .userAnswer(empMessage)
                .dimensions(JSON.toJSONString(List.of(logical_expression.getName(), professional_knowledge.getName())))
                .build();
        // 构建AI请求入参
        AiRequest aiRequest = AiRequest.builder()
                .aiType(AiType.COACH.getCode())
                .aiSubType(AiSubType.TRAIN_CHAT_ANALYZE.getCode())
                .userId(LoginUtil.getLoginUserCode())
                .contents(List.of(AiContent.builder()
                        .contentType(AiContentType.JSON.getCode())
                        .content(JSON.toJSONString(chatAnalyzeRequest))
                        .build()))
                .stream(false)
                .build();

        return aiService.work(aiRequest, LoginUtil.getLoginUser())
                .filter(raw -> CollUtil.isNotEmpty(raw.getContents()))
                .map(raw -> JSON.parseObject(raw.getContents().get(0).getContent()))
                .collectList()
                .block();
    }

}

