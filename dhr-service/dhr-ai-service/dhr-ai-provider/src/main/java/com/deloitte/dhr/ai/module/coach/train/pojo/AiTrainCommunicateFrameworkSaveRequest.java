package com.deloitte.dhr.ai.module.coach.train.pojo;

import com.deloitte.dhr.ai.module.coach.validation.Save;
import com.deloitte.dhr.ai.module.coach.validation.Submit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 课程资源-AI陪练-沟通框架-保存
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainCommunicateFrameworkSaveRequest")
public class AiTrainCommunicateFrameworkSaveRequest {

    /**
     * 沟通主题
     */
    @Length(groups = {Save.class, Submit.class}, max = 255, message = "沟通主题不能超过255个字符")
    @NotBlank(groups = {Save.class, Submit.class}, message = "沟通主题不能为空")
    @ApiModelProperty(value = "沟通主题", name = "communicateSubject")
    private String communicateSubject;

    /**
     * 沟通目标
     */
    @Length(groups = {Save.class, Submit.class}, max = 500, message = "沟通目标不能超过500个字符")
    @NotBlank(groups = {Save.class, Submit.class}, message = "沟通目标不能为空")
    @ApiModelProperty(value = "沟通目标", name = "communicateObjective")
    private String communicateObjective;

    /**
     * 评分标准
     */
    @Length(groups = {Save.class, Submit.class}, max = 500, message = "评分标准不能超过500个字符")
    @NotBlank(groups = {Save.class, Submit.class}, message = "评分标准不能为空")
    @ApiModelProperty(value = "评分标准", name = "gradingCriteria")
    private String gradingCriteria;

    /**
     * 评分百分比
     */
    @NotNull(groups = {Save.class, Submit.class}, message = "评分百分比不能为空")
    @ApiModelProperty(value = "评分百分比", name = "weight")
    private BigDecimal weight;

}