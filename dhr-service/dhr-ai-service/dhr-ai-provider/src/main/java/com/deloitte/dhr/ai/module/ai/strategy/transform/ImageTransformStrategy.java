package com.deloitte.dhr.ai.module.ai.strategy.transform;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiTransformType;
import com.deloitte.dhr.ai.module.ai.annotation.TransformStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.common.base.exception.CommRunException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * AI转换-图片-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@TransformStrategyType(AiTransformType.IMAGE)
@Slf4j
public class ImageTransformStrategy implements ResponseTransformStrategy {

    @Override
    public Flux<List<AiContent>> transformStream(Flux<String> rawStream, AiRequest aiRequest) {

        //  Dify-DUO 工作伙伴-图片豆包，生成图片
        return rawStream
                .filter(raw -> StrUtil.equals(JSONObject.parseObject(raw).getString("event"), "message"))
                .map(raw -> {
                    JSONObject rawObj = JSONObject.parseObject(raw);
                    String answer = rawObj.getString("answer");

                    AiContent content = AiContent.builder()
                            .contentType(AiContentType.IMAGE.getCode())
                            .content(answer)
                            .ext(Map.of("picSource", "doubao"))
                            .build();
                    return List.of(content);
                });
    }

    @Override
    public Flux<List<AiContent>> transformBlock(Flux<String> rawStream, AiRequest aiRequest) {
        return transformStream(rawStream, aiRequest);
    }

    /**
     * 截取URL
     *
     * @param str 待截取字符串
     * @return URL
     */
    private String splitUrl(String str) {
        // 找到左括号和右括号的位置
        int startIndex = str.indexOf("(");
        int endIndex = str.indexOf(")");

        // 判断是否找到括号
        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            // 提取括号内的内容
            return str.substring(startIndex + 1, endIndex);
        }
        throw new CommRunException("AI模型返回图片结构格式有误");
    }

    /**
     * 截取name
     *
     * @param str 待截取字符串
     * @return name
     */
    private String splitName(String str) {
        // 找到左括号和右括号的位置
        int startIndex = str.indexOf("[");
        int endIndex = str.indexOf("]");

        // 判断是否找到括号
        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            // 提取括号内的内容
            return str.substring(startIndex + 1, endIndex);
        }
        throw new CommRunException("AI模型返回图片结构格式有误");
    }

}
