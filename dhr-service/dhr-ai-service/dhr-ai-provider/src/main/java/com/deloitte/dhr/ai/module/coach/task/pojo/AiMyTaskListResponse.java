package com.deloitte.dhr.ai.module.coach.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 培训任务-我的陪练-列表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiMyTaskListResponse")
public class AiMyTaskListResponse {

    /**
     * 培训任务ID
     */
    @ApiModelProperty(value = "培训任务ID", name = "taskId")
    private Long taskId;

    /**
     * 培训任务员工ID
     */
    @ApiModelProperty(value = "培训任务员工ID", name = "taskEmpId")
    private Long taskEmpId;

    /**
     * 课程资源ID
     */
    @ApiModelProperty(value = "课程资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * 课程分类;JXKC：教学课程，AIPL：AI陪练
     */
    @ApiModelProperty(value = "课程分类;JXKC：教学课程，AIPL：AI陪练", name = "courseType")
    private String courseType;

    /**
     * 培训任务名称
     */
    @ApiModelProperty(value = "培训任务名称", name = "taskName")
    private String taskName;

    /**
     * 培训开始时间
     */
    @ApiModelProperty(value = "培训开始时间", name = "startDateTime")
    private Date startDateTime;

    /**
     * 培训结束时间
     */
    @ApiModelProperty(value = "培训结束时间", name = "endDateTime")
    private Date endDateTime;

    /**
     * 培训封面URL
     */
    @ApiModelProperty(value = "培训封面URL", name = "taskCoverUrl")
    private String taskCoverUrl;

    /**
     * 培训任务时长（hour）
     */
    @ApiModelProperty(value = "培训任务时长（hour）", name = "taskDuration")
    private BigDecimal taskDuration;

    /**
     * 员工状态;WKS：未开始，JXZ：进行中，YWC：已完成
     */
    @ApiModelProperty(value = "员工状态;WKS：未开始，JXZ：进行中，YWC：已完成", name = "status")
    private String status;

}