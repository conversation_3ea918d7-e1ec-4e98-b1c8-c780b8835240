package com.deloitte.dhr.ai.module.coach.train.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 课程资源-AI陪练-我的语速分析-图表
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("AiTrainMyTalkSpeedAnalyseChartResponse")
@AllArgsConstructor
@NoArgsConstructor
public class AiTrainMyTalkSpeedAnalyseChartResponse {

    /**
     * 语速编码
     */
    @ApiModelProperty(value = "语速编码", name = "speedCode")
    private String speedCode;

    /**
     * 语速名称
     */
    @ApiModelProperty(value = "语速名称", name = "speedName")
    private String speedName;

    /**
     * 权重
     */
    @ApiModelProperty(value = "权重", name = "speedWeight")
    private BigDecimal speedWeight;

    /**
     * 排序
     */
    @JsonIgnore
    private Integer sort;

}