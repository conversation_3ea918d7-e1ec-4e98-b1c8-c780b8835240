package com.deloitte.dhr.ai.module.ai.strategy.digital;


import com.deloitte.dhr.ai.enums.DigitalLiningIntentAction;
import com.deloitte.dhr.ai.enums.DigitalLiningIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.DigitalLiningActionStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 数字人(李宁)-组织机构-事务查询-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@DigitalLiningActionStrategyType(item = DigitalLiningIntentItem.ZZJG, action = DigitalLiningIntentAction.TQ)
public class DigitalLiningOrgStructureTqStrategy implements DigitalLiningStrategy {

    @Override
    public Flux<List<AiContent>> executeStream(Map<String, Object> param) {
        Object queryTypeObj = param.get("selfOrOther");
        if (Objects.isNull(queryTypeObj)) {
            return Flux.empty();
        }

        return Flux.just(new ArrayList<>());
    }

}
