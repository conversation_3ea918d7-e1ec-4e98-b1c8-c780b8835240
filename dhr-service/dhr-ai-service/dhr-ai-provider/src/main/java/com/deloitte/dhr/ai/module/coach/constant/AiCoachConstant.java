package com.deloitte.dhr.ai.module.coach.constant;

import java.math.BigDecimal;

/**
 * Ai Coach 相关常量
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public class AiCoachConstant {

    /**
     * 最大权重值常量
     */
    public static final BigDecimal MAX_WEIGHT = BigDecimal.valueOf(100);

    /**
     * 递归最大深度
     */
    public static final Integer MAX_DEEP = 5;

    /**
     * 集合初始化容量大小
     */
    public static final Integer LIST_INIT_SIZE = 16;

    /**
     * 课程资源状态
     */
    public static class CourseResourceStatus {

        /**
         * 草稿状态
         */
        public static final String DRAFT = "CG";

        /**
         * 提交状态
         */
        public static final String SUBMIT = "TJ";
    }

    /**
     * 培训任务状态：CG：草稿，PXZ：培训中，YWC：已完成
     */
    public static class TaskStatus {

        /**
         * 草稿
         */
        public static final String DRAFT = "CG";

        /**
         * 培训中
         */
        public static final String ONGOING = "PXZ";

        /**
         * 已完成
         */
        public static final String COMPLETED = "YWC";

        /**
         * 已结束
         */
        public static final String CLOSE = "YJS";

    }

    /**
     * 课程资源类型
     */
    public static class CourseResourceType {

        /**
         * 教学课程
         */
        public static final String TEACH = "JXKC";

        /**
         * AI陪练
         */
        public static final String TRAIN = "AIPL";
    }

    /**
     * 教学课程题目类别
     */
    public static class TeachQuestionType {

        /**
         * 单选题（选择题）
         */
        public static final String SINGLE_CHOICE_QUESTION = "DXT";

        /**
         * 判断题（选择题）
         */
        public static final String TRUE_OR_FALSE_QUESTION = "PDT";

        /**
         * 问答题（文本题）
         */
        public static final String ESSAY_QUESTION = "WDT";

    }

    /**
     * 培训任务员工状态
     */
    public static class TaskEmpStatus {

        /**
         * 未开始
         */
        public static final String NOT_START = "WKS";

        /**
         * 进行中
         */
        public static final String ONGOING = "JXZ";

        /**
         * 已完成
         */
        public static final String COMPLETED = "YWC";

        /**
         * 已结束
         */
        public static final String CLOSE = "YJS";

    }

    /**
     * 对话消息所属
     */
    public static class DialogueMessageBelong {

        /**
         * AI
         */
        public static final String AI = "AI";

        /**
         * 员工
         */
        public static final String EMP = "EMP";

    }


}
