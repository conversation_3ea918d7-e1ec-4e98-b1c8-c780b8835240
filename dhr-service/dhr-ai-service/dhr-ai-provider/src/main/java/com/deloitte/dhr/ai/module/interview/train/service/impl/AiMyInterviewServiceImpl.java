package com.deloitte.dhr.ai.module.interview.train.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deloitte.dhr.ai.enums.AiAgentType;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.pojo.AiInterviewChatAnalyzeRequest;
import com.deloitte.dhr.ai.module.ai.pojo.AiInterviewTaskResultRequest;
import com.deloitte.dhr.ai.module.ai.pojo.AiRequest;
import com.deloitte.dhr.ai.module.ai.service.AiService;
import com.deloitte.dhr.ai.module.interview.constant.AiInterviewConstant;
import com.deloitte.dhr.ai.module.interview.resource.domain.AiInterviewInfo;
import com.deloitte.dhr.ai.module.interview.resource.domain.AiInterviewResourceAttach;
import com.deloitte.dhr.ai.module.interview.resource.mapper.AiInterviewInfoMapper;
import com.deloitte.dhr.ai.module.interview.resource.mapper.AiInterviewObjectMapper;
import com.deloitte.dhr.ai.module.interview.resource.mapper.AiInterviewResourceAttachMapper;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewObjectResponse;
import com.deloitte.dhr.ai.module.interview.task.domain.AiInterviewTaskEmp;
import com.deloitte.dhr.ai.module.interview.task.domain.AiInterviewTaskInfo;
import com.deloitte.dhr.ai.module.interview.task.mapper.AiInterviewTaskEmpMapper;
import com.deloitte.dhr.ai.module.interview.task.mapper.AiInterviewTaskInfoMapper;
import com.deloitte.dhr.ai.module.interview.train.domain.AiInterviewMyDialogueHistory;
import com.deloitte.dhr.ai.module.interview.train.domain.AiInterviewObject;
import com.deloitte.dhr.ai.module.interview.train.mapper.AiInterviewDialogueQuestionMapper;
import com.deloitte.dhr.ai.module.interview.train.mapper.AiInterviewMyDialogueHistoryMapper;
import com.deloitte.dhr.ai.module.interview.train.pojo.*;
import com.deloitte.dhr.ai.module.interview.train.service.AiInterviewMyDialogueHistoryService;
import com.deloitte.dhr.ai.module.interview.train.service.AiMyInterviewService;
import com.deloitte.dhr.ai.utils.CheckUtils;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.pojo.UserDto;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import com.deloitte.dhr.common.base.utils.date.DateStyle;
import com.deloitte.dhr.common.base.utils.date.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * AI访谈-访谈任务-我的访谈-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class AiMyInterviewServiceImpl implements AiMyInterviewService {

    @Autowired
    private AiInterviewTaskInfoMapper aiInterviewTaskInfoMapper;
    @Autowired
    private AiInterviewTaskEmpMapper aiInterviewTaskEmpMapper;
    @Autowired
    private AiInterviewInfoMapper aiInterviewInfoMapper;
    @Autowired
    private AiInterviewObjectMapper aiInterviewObjectMapper;
    @Autowired
    private AiInterviewResourceAttachMapper aiInterviewResourceAttachMapper;
    @Autowired
    private AiInterviewMyDialogueHistoryService aiInterviewMyDialogueHistoryService;
    @Autowired
    private AiInterviewMyDialogueHistoryMapper aiInterviewMyDialogueHistoryMapper;
    @Autowired
    private AiInterviewDialogueQuestionMapper aiInterviewDialogueQuestionMapper;
    @Autowired
    private AiService aiService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AiInterviewMySendResponse sendMessage(AiInterviewMySendRequest request) {
        AiInterviewMySendResponse response = new AiInterviewMySendResponse();

        //查询当前对话记录
        AiInterviewMyDialogueHistory currentDialogueHistory = aiInterviewMyDialogueHistoryMapper.selectById(request.getId());
        if (currentDialogueHistory == null) {
            throw new CommRunException("当前对话信息不存在，数据异常");
        }
        if (StrUtil.isNotEmpty(currentDialogueHistory.getMessage()) || StrUtil.isNotEmpty(currentDialogueHistory.getEmpVoiceFileUrl())) {
            throw new CommRunException("当前对话已提交，请勿重复提交");
        }

        AiInterviewTaskEmp taskEmp = aiInterviewTaskEmpMapper.selectById(currentDialogueHistory.getTaskEmpId());
        // 校验
        this.verify(taskEmp);

        AiInterviewTaskInfo task = aiInterviewTaskInfoMapper.selectById(taskEmp.getTaskId());
        // 校验
        this.verifyTask(task);

        // 查询AI对话问题信息
        List<AiInterviewDialogueQuestionResponse> aiQuestionList = aiInterviewDialogueQuestionMapper.getAiQuestionList(currentDialogueHistory.getTaskEmpId());
        if (CollUtil.isEmpty(aiQuestionList)) {
            throw new CommRunException("AI生成对话提示信息为空，数据异常");
        }

        // 当前AI对话问题
        AiInterviewDialogueQuestionResponse questionResponse = aiQuestionList.stream().filter(a -> a.getId().equals(currentDialogueHistory.getQuestionId())).findAny().orElseThrow(() -> new CommRunException("未在AI对话提示题库里找到本次AI提问问题"));

        // 获取访谈任务的AI对话记录
        List<AiInterviewMyDialogueHistory> dialogueHistoryList = aiInterviewMyDialogueHistoryMapper.selectList(new LambdaQueryWrapper<AiInterviewMyDialogueHistory>().eq(
                AiInterviewMyDialogueHistory::getTaskEmpId, currentDialogueHistory.getTaskEmpId()
        ));
        /*AiInterviewMyDialogueHistory dialogueHistorysaveRequest = new AiInterviewMyDialogueHistory();
        List<String> questionList = new ArrayList<>();
        List<String> answerList = new ArrayList<>();
        int i = 1;
        for (AiInterviewMyDialogueHistory dialogueHistory : dialogueHistoryList){
            if (Objects.nonNull(dialogueHistory.getMessage())){
                questionList.add("第"+i+"轮问题："+dialogueHistory.getQuestion());
                answerList.add("第"+i+"轮回答："+dialogueHistory.getMessage());
            } else {
                dialogueHistorysaveRequest = dialogueHistory;
                dialogueHistorysaveRequest.setMessage(request.getEmpMessage());
                dialogueHistorysaveRequest.setEmpVoiceFileUrl(request.getEmpMessageVoiceFileUrl());

                questionList.add("第"+i+"轮问题："+dialogueHistory.getQuestion());
                answerList.add("第"+i+"轮回答："+request.getEmpMessage());
            }
            i = i + 1;
        }

        String answers = StrUtil.join(";", answerList);
        String questions = StrUtil.join(";", questionList);*/

        String questions = currentDialogueHistory.getQuestion();
        String answers = request.getEmpMessage();

        AiInterviewMyDialogueHistory dialogueHistorysaveRequest = BeanUtil.copyProperties(currentDialogueHistory, AiInterviewMyDialogueHistory.class);
        dialogueHistorysaveRequest.setMessage(request.getEmpMessage());
        dialogueHistorysaveRequest.setEmpVoiceFileUrl(request.getEmpMessageVoiceFileUrl());

        // AI 设置了字段长度，只有1024哥字符，如果超长了，截取最后1024个字符
        answers = answers.length() > 1024 ? answers.substring(answers.length() - 1024) : answers;
        questions = questions.length() > 1024 ? questions.substring(questions.length() - 1024) : questions;
        AiInterviewChatAnalyzeRequest chatAnalyzeRequest = AiInterviewChatAnalyzeRequest.builder()
                .interviewQuestion(questions)
                .interviewObjective(questionResponse.getInterviewObjective())
                .interviewAnswer(answers)
                .build();

        // 获取AI维度分析信息
        List<JSONObject> analyzeList = this.getAiChatAnalyze(chatAnalyzeRequest);
        analyzeList = analyzeList == null ? List.of() : analyzeList;

        // 保存分析信息
        this.saveChatAnalyseInfo(analyzeList, dialogueHistorysaveRequest);

        // 返回AI下个问题
        AiInterviewMyDialogueHistory nextAiQuestion = aiInterviewMyDialogueHistoryService.getNextAiQuestion(aiQuestionList, analyzeList, dialogueHistorysaveRequest, dialogueHistoryList);

        if (nextAiQuestion != null) {
            if (Objects.isNull(nextAiQuestion.getId())){
                // 插入新记录时设置初始 revision 为 0
                nextAiQuestion.setRevision(0); // 初始化版本号
                aiInterviewMyDialogueHistoryMapper.insert(nextAiQuestion);
            }

            response.setIsDialogueEnd(false);

            //返回下一个问题信息
            AiInterviewMyDialogueHistoryResponse nextAiMessageInfo = new AiInterviewMyDialogueHistoryResponse();
            nextAiMessageInfo.setId(nextAiQuestion.getId());
            nextAiMessageInfo.setQuestionId(nextAiQuestion.getQuestionId());
            nextAiMessageInfo.setMessage(nextAiQuestion.getQuestion());
            nextAiMessageInfo.setSendTime(nextAiQuestion.getSendTime());
            nextAiMessageInfo.setVoiceFileUrl(nextAiQuestion.getAiVoiceFileUrl());
            nextAiMessageInfo.setMessageBelong(AiInterviewConstant.DialogueMessageBelong.AI);

            Long dialogueNum = aiInterviewMyDialogueHistoryMapper.getMyDialogueNum(currentDialogueHistory.getTaskEmpId(), nextAiMessageInfo.getQuestionId());
            nextAiMessageInfo.setDfNum(dialogueNum);
            nextAiMessageInfo.setStatus(AiInterviewConstant.EmpTaskQuestionStatus.NO_PASS);

            response.setCurrentEmpMessageId(nextAiMessageInfo.getId());
            response.setNextAiMessageInfo(nextAiMessageInfo);
        } else {
            response.setIsDialogueEnd(true);

            // 访谈对话中，结束该访谈，需要AI生成结束语
            AiInterviewMyDialogueHistoryResponse historyResponse = new AiInterviewMyDialogueHistoryResponse();
            historyResponse.setMessageBelong(AiInterviewConstant.DialogueMessageBelong.AI);
            historyResponse.setMessage(AiInterviewConstant.AiEndMessage);
            historyResponse.setStatus(AiInterviewConstant.EmpTaskQuestionStatus.END);
            response.setNextAiMessageInfo(historyResponse);

            // 回答结束
            taskEmp.setStatus(AiInterviewConstant.TaskEmpStatus.COMPLETED);
            taskEmp.setBgStatus(AiInterviewConstant.EmpTaskReportStatus.GENERATING);
            // 如果是提交（结束）AI访谈，提交结束时间
            taskEmp.setCompleteTime(DateTime.now());
            aiInterviewTaskEmpMapper.updateById(taskEmp);
            // 更新-访谈任务状态
            aiInterviewTaskInfoMapper.updateTaskStatus(taskEmp.getTaskId());

            UserDto userDto = LoginUtil.getLoginUser();
            // 异步创建洞察报告
            ThreadUtil.execAsync(() -> {
                createReportAsync(taskEmp, task, userDto);
            });

        }

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AiInterviewMySendResponse skip(Long id) {
        AiInterviewMySendResponse response = new AiInterviewMySendResponse();
        if (id == null) {
            throw new CommRunException("请求数据异常");
        }

        //查询当前对话记录
        AiInterviewMyDialogueHistory currentDialogueHistory = aiInterviewMyDialogueHistoryMapper.selectById(id);
        if (currentDialogueHistory == null) {
            throw new CommRunException("当前对话信息不存在，数据异常");
        }

        AiInterviewTaskEmp taskEmp = aiInterviewTaskEmpMapper.selectById(currentDialogueHistory.getTaskEmpId());
        // 校验
        this.verify(taskEmp);

        AiInterviewTaskInfo task = aiInterviewTaskInfoMapper.selectById(taskEmp.getTaskId());
        // 校验
        this.verifyTask(task);

        // 查询AI对话问题信息
        List<AiInterviewDialogueQuestionResponse> aiQuestionList = aiInterviewDialogueQuestionMapper.getAiQuestionList(currentDialogueHistory.getTaskEmpId());
        if (CollUtil.isEmpty(aiQuestionList)) {
            throw new CommRunException("AI生成对话提示信息为空，数据异常");
        }

        // 保存信息
        currentDialogueHistory.setMessage("跳过当前问题");
        currentDialogueHistory.setStatus(AiInterviewConstant.EmpTaskQuestionStatus.SKIP);
        currentDialogueHistory.setSendTime(DateTime.now());
        currentDialogueHistory.setUpdateTime(DateTime.now());
        aiInterviewMyDialogueHistoryMapper.updateById(currentDialogueHistory);

        // 获取访谈任务的AI对话记录
        List<AiInterviewMyDialogueHistory> dialogueHistoryList = aiInterviewMyDialogueHistoryMapper.selectList(new LambdaQueryWrapper<AiInterviewMyDialogueHistory>().eq(
                AiInterviewMyDialogueHistory::getTaskEmpId, currentDialogueHistory.getTaskEmpId()
        ));

        // 返回AI下个问题
        AiInterviewMyDialogueHistory nextAiQuestion = aiInterviewMyDialogueHistoryService.getNextAiQuestion(aiQuestionList, new ArrayList<>(), currentDialogueHistory, dialogueHistoryList);

        if (nextAiQuestion != null) {
            if (Objects.isNull(nextAiQuestion.getId())){
                nextAiQuestion.setRevision(0); // 初始化版本号
                aiInterviewMyDialogueHistoryMapper.insert(nextAiQuestion);
            }

            response.setIsDialogueEnd(false);

            //返回下一个问题信息
            AiInterviewMyDialogueHistoryResponse nextAiMessageInfo = new AiInterviewMyDialogueHistoryResponse();
            nextAiMessageInfo.setId(nextAiQuestion.getId());
            nextAiMessageInfo.setQuestionId(nextAiQuestion.getQuestionId());
            nextAiMessageInfo.setMessage(nextAiQuestion.getQuestion());
            nextAiMessageInfo.setSendTime(nextAiQuestion.getSendTime());
            nextAiMessageInfo.setVoiceFileUrl(nextAiQuestion.getAiVoiceFileUrl());
            nextAiMessageInfo.setMessageBelong(AiInterviewConstant.DialogueMessageBelong.AI);

            Long dialogueNum = aiInterviewMyDialogueHistoryMapper.getMyDialogueNum(currentDialogueHistory.getTaskEmpId(), nextAiMessageInfo.getQuestionId());
            nextAiMessageInfo.setDfNum(dialogueNum);
            nextAiMessageInfo.setStatus(null);

            response.setCurrentEmpMessageId(nextAiMessageInfo.getId());
            response.setNextAiMessageInfo(nextAiMessageInfo);
        } else {
            response.setIsDialogueEnd(true);

            // 访谈对话中，结束该访谈，需要AI生成结束语
            AiInterviewMyDialogueHistoryResponse historyResponse = new AiInterviewMyDialogueHistoryResponse();
            historyResponse.setMessageBelong(AiInterviewConstant.DialogueMessageBelong.AI);
            historyResponse.setMessage(AiInterviewConstant.AiEndMessage);
            historyResponse.setStatus(AiInterviewConstant.EmpTaskQuestionStatus.END);
            response.setNextAiMessageInfo(historyResponse);

            // 回答结束
            taskEmp.setStatus(AiInterviewConstant.TaskEmpStatus.COMPLETED);
            taskEmp.setBgStatus(AiInterviewConstant.EmpTaskReportStatus.GENERATING);
            // 如果是提交（结束）AI访谈，提交结束时间
            taskEmp.setCompleteTime(DateTime.now());
            aiInterviewTaskEmpMapper.updateById(taskEmp);
            // 更新-访谈任务状态
            aiInterviewTaskInfoMapper.updateTaskStatus(taskEmp.getTaskId());

            UserDto userDto = LoginUtil.getLoginUser();
            // 异步创建洞察报告
            ThreadUtil.execAsync(() -> {
                createReportAsync(taskEmp, task, userDto);
            });
        }

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createReport(Long taskEmpId) {
        AiInterviewTaskEmp taskEmp = aiInterviewTaskEmpMapper.selectById(taskEmpId);
        if(AiInterviewConstant.EmpTaskReportStatus.GENERATING.equals(taskEmp.getBgStatus())){
            throw new CommRunException("当前任务正在生成报告中，请勿重复操作");
        }
        if(AiInterviewConstant.EmpTaskReportStatus.GENERATED_SUCCESS.equals(taskEmp.getBgStatus())){
            throw new CommRunException("报告已生成成功，请勿重复操作");
        }
        if(!AiInterviewConstant.TaskEmpStatus.COMPLETED.equals(taskEmp.getStatus())){
            throw new CommRunException("当前访谈任务未完成，不能生成报告");
        }

        AiInterviewTaskInfo taskInfo = aiInterviewTaskInfoMapper.selectById(taskEmp.getTaskId());

        UserDto userDto = LoginUtil.getLoginUser();
        // 异步创建洞察报告
        ThreadUtil.execAsync(() -> {
            taskEmp.setBgStatus(AiInterviewConstant.EmpTaskReportStatus.GENERATING);
            aiInterviewTaskEmpMapper.updateById(taskEmp);

            createReportAsync(taskEmp, taskInfo, userDto);
        });
    }

    /**
     * 异步生成洞察报告
     *
     * @param taskEmp  员工的访谈任务
     * @param taskInfo 访谈任务
     * @param userDto
     */
    public void createReportAsync(AiInterviewTaskEmp taskEmp, AiInterviewTaskInfo taskInfo, UserDto userDto){

        // 获取访谈任务的AI对话记录
        List<AiInterviewMyDialogueHistory> dialogueHistoryList = aiInterviewMyDialogueHistoryMapper.selectList(new LambdaQueryWrapper<AiInterviewMyDialogueHistory>().eq(
                AiInterviewMyDialogueHistory::getTaskEmpId, taskEmp.getId()
        ));
        //查询访谈助手信息
        AiInterviewInfo info = aiInterviewInfoMapper.selectById(taskInfo.getResourceId());
        //查询访谈对象信息
        AiInterviewObject object = aiInterviewObjectMapper.selectOne(new LambdaQueryWrapper<AiInterviewObject>().eq(AiInterviewObject::getResourceId, info.getId()));
        //查询访谈资料
        List<AiInterviewResourceAttach> attachList = aiInterviewResourceAttachMapper.selectList(new LambdaQueryWrapper<AiInterviewResourceAttach>().eq(AiInterviewResourceAttach::getResourceId, info.getId()));

        AiInterviewTaskResultRequest aiInterviewTaskResultRequest = new AiInterviewTaskResultRequest();
        aiInterviewTaskResultRequest.setInterviewScene(info.getDialogueScene());
        aiInterviewTaskResultRequest.setInterviewObjective(info.getDialogueObjective());
        aiInterviewTaskResultRequest.setInterviewCaveats(info.getDialogueRemark());
        aiInterviewTaskResultRequest.setInterviewGender("female".equals(object.getObjectGender())?"女":"男");
        aiInterviewTaskResultRequest.setInterviewRole(object.getObjectBackgroundInfo());

        List<AiInterviewTaskResultRequest.AiInterviewFile> fileList = attachList.stream().map(attach -> {
            return aiInterviewTaskResultRequest.createAiInterviewFile(attach.getFileUrl());
        }).collect(Collectors.toList());
        aiInterviewTaskResultRequest.setFileList(fileList);
        List<JSONObject> jsonList = dialogueHistoryList.stream().map(history -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("question", history.getQuestion());
            jsonObject.set("answer", history.getMessage());
            return jsonObject;
        }).collect(Collectors.toList());

        aiInterviewTaskResultRequest.setInterviewContent(JSONUtil.toJsonStr(jsonList));

        // 获取AI维度分析信息
        List<JSONObject> analyzeList = this.getAiInterviewResult(aiInterviewTaskResultRequest, userDto);
        analyzeList = analyzeList == null ? List.of() : analyzeList;
        if(analyzeList.isEmpty() || !analyzeList.get(0).containsKey("interviewAttitude")){
            // 报告生成失败
            taskEmp.setBgStatus(AiInterviewConstant.EmpTaskReportStatus.GENERATED_FAIL);
            aiInterviewTaskEmpMapper.updateById(taskEmp);
            throw new CommRunException("AI访谈任务-答案分析失败");
        }
        // 更新员工相关信息
        taskEmp.setGtStatus(analyzeList.get(0).getStr("interviewAttitude"));
        taskEmp.setWcdRate(analyzeList.get(0).getStr("interviewCompleteness"));
        taskEmp.setHdjjScore(analyzeList.get(0).getStr("interviewProactivity"));
        taskEmp.setBdljScore(analyzeList.get(0).getStr("interviewLogic"));
        taskEmp.setAnalyse01(analyzeList.get(0).getStr("overview"));
        taskEmp.setAnalyse02(analyzeList.get(0).getStr("summary"));
        taskEmp.setAnalyse03(analyzeList.get(0).getStr("followUpPoints"));
        taskEmp.setBgStatus(AiInterviewConstant.EmpTaskReportStatus.GENERATED_SUCCESS);

        // 计算问题回答时长
        int s = 0;
        for (AiInterviewMyDialogueHistory dialogueHistory : dialogueHistoryList) {
            if (dialogueHistory.getSendTime() != null && dialogueHistory.getUpdateTime() != null) {
                s += (int) DateUtil.between(dialogueHistory.getSendTime(), dialogueHistory.getAskTime(), DateUnit.SECOND);
            }
        }
        taskEmp.setElapsedTime(s);

        aiInterviewTaskEmpMapper.updateById(taskEmp);
    }

    /**
     * 保存相关分析信息
     */
    private void saveChatAnalyseInfo(List<JSONObject> analyzeList, AiInterviewMyDialogueHistory myDialogueHistory) {

        if (analyzeList.isEmpty() || !analyzeList.get(0).containsKey("match_rate")){
            throw new CommRunException("AI访谈任务-答案分析失败");
        }
        String status = AiInterviewConstant.EmpTaskQuestionStatus.NO_PASS;
        if (StrUtil.isEmpty(analyzeList.get(0).getStr("follow_up_question")) || "null".equals(analyzeList.get(0).getStr("follow_up_question"))){
            status = AiInterviewConstant.EmpTaskQuestionStatus.PASS;
        }

        // 保存-聊天信息
        myDialogueHistory.setSendTime(DateTime.now());
        myDialogueHistory.setUpdateTime(DateTime.now());
        myDialogueHistory.setMatchRate(analyzeList.get(0).getStr("match_rate"));
        myDialogueHistory.setAnalysis(analyzeList.get(0).getStr("analysis"));
        myDialogueHistory.setStatus(status);
        int rowsAffected = aiInterviewMyDialogueHistoryMapper.updateById(myDialogueHistory);
        if (rowsAffected == 0) {
            throw new CommRunException("该问题已回答，请勿重复答复");
        }
    }

    /**
     * 校验
     *
     * @param task 访谈任务
     */
    private void verifyTask(AiInterviewTaskInfo task) {

        // 校验-数据是否存在
        CheckUtils.checkNull(task, "该数据不存在");

        // 校验-是否可以修改
        if (!StrUtil.equals(task.getTaskStatus(), AiInterviewConstant.TaskStatus.JXZ)) {
            throw new CommRunException("访谈任务状态不符合，请勿继续作答");
        }
    }

    /**
     * 校验
     *
     * @param taskEmp 员工信息
     */
    private void verify(AiInterviewTaskEmp taskEmp) {

        // 校验-数据是否存在
        CheckUtils.checkNull(taskEmp, "该数据不存在");

        // 校验-是否可以修改
        if (StrUtil.equals(taskEmp.getStatus(), AiInterviewConstant.TaskEmpStatus.COMPLETED)) {
            throw new CommRunException("访谈已完成，请勿继续作答");
        }
    }

    /**
     * 获取AI聊天分析信息
     *
     * @param chatAnalyzeRequest       用户回答列表
     * @return List<JSONObject>
     */
    private List<JSONObject> getAiChatAnalyze(AiInterviewChatAnalyzeRequest chatAnalyzeRequest) {
        List<AiContent> aiContents = List.of(AiContent.builder()
                .contentType(AiContentType.JSON.getCode())
                .content(JSONUtil.toJsonStr(chatAnalyzeRequest))
                .build());
        // 构建AI请求入参
        AiRequest aiRequest = AiRequest.builder()
                .aiType(AiType.INTERVIEW.getCode())
                .aiSubType(AiAgentType.INTERVIEW_ANSWER_ANALYZE.getCode())
                .userId(LoginUtil.getLoginUserCode())
                .contents(aiContents)
                .stream(false)
                .build();

        return aiService.work(aiRequest, LoginUtil.getLoginUser())
                .filter(raw -> CollUtil.isNotEmpty(raw.getContents()))
                .map(raw -> JSONUtil.parseObj(raw.getContents().get(0).getContent()))
                .collectList()
                .block();
    }

    /**
     * 获取AI访谈结果分析
     *
     * @param aiInterviewTaskResultRequest 访谈结果内容
     * @param userDto
     * @return List<JSONObject>
     */
    private List<JSONObject> getAiInterviewResult(AiInterviewTaskResultRequest aiInterviewTaskResultRequest, UserDto userDto) {
        List<AiContent> aiContents = List.of(AiContent.builder()
                .contentType(AiContentType.JSON.getCode())
                .content(JSONUtil.toJsonStr(aiInterviewTaskResultRequest))
                .build());
        // 构建AI请求入参
        AiRequest aiRequest = AiRequest.builder()
                .aiType(AiType.INTERVIEW.getCode())
                .aiSubType(AiAgentType.INTERVIEW_REPORT_ANALYZE.getCode())
                .userId(userDto.getEmployeeNumber())
                .contents(aiContents)
                .stream(false)
                .build();

        return aiService.work(aiRequest, userDto)
                .filter(raw -> CollUtil.isNotEmpty(raw.getContents()))
                .map(raw -> JSONUtil.parseObj(raw.getContents().get(0).getContent()))
                .collectList()
                .block();
    }


    @Override
    public AiInterviewTaskMyReportResponse getMyReport(Long taskEmpId) {
        AiInterviewTaskMyReportResponse taskMyReportResponse = new AiInterviewTaskMyReportResponse();
        AiInterviewTaskEmp emp = aiInterviewTaskEmpMapper.selectById(taskEmpId);
        if (!AiInterviewConstant.TaskEmpStatus.COMPLETED.equals(emp.getStatus())){
            throw new CommRunException("访谈任务未完成");
        }
        if (AiInterviewConstant.EmpTaskReportStatus.GENERATING.equals(emp.getBgStatus())){
            throw new CommRunException("洞察报告正在生成，请等待");
        }

        if (!AiInterviewConstant.EmpTaskReportStatus.GENERATED_SUCCESS.equals(emp.getBgStatus())){
            throw new CommRunException("洞察报告生成失败，请重试");
        }

        taskMyReportResponse.setInterviewTime(convertToMinutesSeconds(emp.getElapsedTime()));
        taskMyReportResponse.setDoneTime(DateUtil.DateToString(emp.getCompleteTime(), DateStyle.YYYY_MM_DD_HH_MM_SS));
        taskMyReportResponse.setGtSatstus(emp.getGtStatus());
        taskMyReportResponse.setWcdRate(emp.getWcdRate());
        taskMyReportResponse.setHdjjScore(emp.getHdjjScore());
        taskMyReportResponse.setBdljScore(emp.getBdljScore());
        taskMyReportResponse.setAnalyse01(emp.getAnalyse01());
        taskMyReportResponse.setAnalyse02(emp.getAnalyse02());
        taskMyReportResponse.setAnalyse03(emp.getAnalyse03());

        AiInterviewInfo info = aiInterviewInfoMapper.selectById(emp.getResourceId());
        taskMyReportResponse.setCourseName(info.getCourseName());

        //获取聊天记录
        List<AiInterviewMyDialogueHistory> historyList = aiInterviewMyDialogueHistoryMapper.selectList(new LambdaQueryWrapper<AiInterviewMyDialogueHistory>().eq(
                AiInterviewMyDialogueHistory::getTaskEmpId, taskEmpId
        ));
        List<AiInterviewMyDialogueHistoryResponse> dialogueHistoryList = new ArrayList<>();
        historyList.stream().forEach(history -> {
            AiInterviewMyDialogueHistoryResponse historyResponse = new AiInterviewMyDialogueHistoryResponse();
            historyResponse.setMessageBelong(AiInterviewConstant.DialogueMessageBelong.AI);
            historyResponse.setId(history.getId());
            historyResponse.setMessage(history.getQuestion());
            historyResponse.setSendTime(history.getAskTime());
            historyResponse.setStatus(history.getStatus());
            historyResponse.setVoiceFileUrl(history.getAiVoiceFileUrl());
            dialogueHistoryList.add(historyResponse);

            if(StrUtil.isNotEmpty(history.getMessage()) || StrUtil.isNotEmpty(history.getEmpVoiceFileUrl())){
                historyResponse = new AiInterviewMyDialogueHistoryResponse();
                historyResponse.setMessageBelong(AiInterviewConstant.DialogueMessageBelong.EMP);
                historyResponse.setId(history.getId());
                historyResponse.setMessage(history.getMessage());
                historyResponse.setSendTime(history.getSendTime());
                historyResponse.setStatus(history.getStatus());
                historyResponse.setVoiceFileUrl(history.getEmpVoiceFileUrl());
                dialogueHistoryList.add(historyResponse);
            }
        });

        // 如果任务为完成，则最后生成AI结束语
        AiInterviewMyDialogueHistoryResponse historyResponse = new AiInterviewMyDialogueHistoryResponse();
        historyResponse.setMessageBelong(AiInterviewConstant.DialogueMessageBelong.AI);
        historyResponse.setMessage(AiInterviewConstant.AiEndMessage);
        historyResponse.setStatus(AiInterviewConstant.EmpTaskQuestionStatus.END);
        dialogueHistoryList.add(historyResponse);

        taskMyReportResponse.setHistoryList(dialogueHistoryList);
        return taskMyReportResponse;
    }

    public static String convertToMinutesSeconds(int seconds) {
        if (seconds <= 0) {
            return "0分0秒";
        }
        int minutes = seconds / 60;
        int remainingSeconds = seconds % 60;
        return String.format("%d分%d秒", minutes, remainingSeconds);
    }

}

