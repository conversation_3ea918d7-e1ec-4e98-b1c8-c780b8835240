package com.deloitte.dhr.ai.module.interview.train.mapper;

import com.deloitte.dhr.ai.module.interview.train.domain.AiInterviewDialogueQuestion;
import com.deloitte.dhr.ai.module.interview.train.pojo.AiInterviewDialogueQuestionResponse;
import com.deloitte.dhr.common.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI访谈-访谈任务-对话问题-相关持久化接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface AiInterviewDialogueQuestionMapper extends SuperMapper<AiInterviewDialogueQuestion> {

    /**
     * 查询-AI问题列表
     *
     * @param taskEmpId AI访谈任务-员工ID
     * @return AiInterviewMyDialogueHistoryResponse
     */
    List<AiInterviewDialogueQuestionResponse> getAiQuestionList(@Param("taskEmpId") Long taskEmpId);

}