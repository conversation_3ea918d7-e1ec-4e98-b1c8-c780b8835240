package com.deloitte.dhr.ai.module.ai.strategy.ssc;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.deloitte.dhr.ai.constant.SscConst;
import com.deloitte.dhr.ai.enums.AiContentType;
import com.deloitte.dhr.ai.enums.AiSscIntentAction;
import com.deloitte.dhr.ai.enums.AiSscIntentItem;
import com.deloitte.dhr.ai.module.ai.annotation.ActionStrategyType;
import com.deloitte.dhr.ai.module.ai.pojo.AiContent;
import com.deloitte.dhr.ai.module.ai.strategy.ssc.pojo.PersonnelFilesTqResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * SSC-人事档案-事务查询-策略
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Component
@ActionStrategyType(item = AiSscIntentItem.RSDA, action = AiSscIntentAction.TQ)
public class PersonnelFilesTqStrategy implements SscStrategy {

    @Autowired
    private CommonTqService commonTqService;
    @Autowired
    private CommonIrStrategy commonIrStrategy;

    @Override
    public Flux<List<AiContent>> executeStream(Map<String, Object> param) {

        // 查询人事档案信息
        PersonnelFilesTqResponse response = queryPersonnelFilesInfo(param);
        AiContent basicInfoContent = AiContent.builder()
                .content(JSON.toJSONString(response.getHeaderInfo()))
                .contentType(AiContentType.JSON.getCode())
                .build();
        // 通过AI查询识别过滤出来的信息
        return Flux.just(List.of(basicInfoContent)).concatWith(commonIrStrategy.executeStream(param));
    }

    /**
     * 查询我的人事档案信息
     *
     * @param param 请求参数
     * @return PersonnelFilesTqResponse
     */
    private PersonnelFilesTqResponse queryPersonnelFilesInfo(Map<String, Object> param) {
        PersonnelFilesTqResponse response = new PersonnelFilesTqResponse();
        Object queryTypeObj = param.get("selfOrOther");
        if (Objects.isNull(queryTypeObj)) {
            return response;
        }
        String empCode;
        switch (queryTypeObj.toString()) {
            case SscConst.OTHER:
                String staffName = String.valueOf(param.get("staffName"));
                // todo 通过员工姓名查询员工编号，暂时设置为登录用户
                empCode = commonTqService.getLoginEmpCode();
                break;
            case SscConst.MYSELF:
            default:
                empCode = commonTqService.getLoginEmpCode();
        }

        Map<String, List<Map<String, String>>> map = new HashMap<>();

        param.put("context", JSON.toJSONString(map));
        // 获取其中的头信息
        if (CollUtil.isNotEmpty(map.get("人事信息"))) {
            Map<String, String> childMap = map.get("人事信息").get(0);
            response.setHeaderInfo(new PersonnelFilesTqResponse.HeaderInfo(childMap.get("员工编号"), childMap.get("员工姓名"), childMap.get("员工组")));
        }
        return response;
    }

}
