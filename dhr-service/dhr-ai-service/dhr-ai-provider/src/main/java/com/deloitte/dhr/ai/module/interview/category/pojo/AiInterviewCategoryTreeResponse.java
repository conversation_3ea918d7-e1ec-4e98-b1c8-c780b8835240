package com.deloitte.dhr.ai.module.interview.category.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * AI访谈-类别-列表树
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewCategoryTreeResponse")
public class AiInterviewCategoryTreeResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 类别名称
     */
    @ApiModelProperty(value = "类别名称", name = "categoryName")
    private String categoryName;

    /**
     * 类别父级ID
     */
    @ApiModelProperty(value = "类别父级ID", name = "parentId")
    private Long parentId;

    /**
     * 类别全路径
     */
    @ApiModelProperty(value = "类别全路径", name = "categoryPath")
    private String categoryPath;

    /**
     * 子集
     */
    @ApiModelProperty(value = "子集", name = "children")
    private List<AiInterviewCategoryTreeResponse> children;

}