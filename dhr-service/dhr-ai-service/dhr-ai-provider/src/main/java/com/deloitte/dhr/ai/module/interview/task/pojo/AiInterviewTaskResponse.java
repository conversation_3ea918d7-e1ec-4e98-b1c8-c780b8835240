package com.deloitte.dhr.ai.module.interview.task.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * AI访谈-访谈任务-信息
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("AiInterviewTaskResponse")
public class AiInterviewTaskResponse {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * AI访谈资源ID
     */
    @ApiModelProperty(value = "AI访谈资源ID", name = "resourceId")
    private Long resourceId;

    /**
     * AI访谈分类;AIFT:AI访谈
     */
    @ApiModelProperty(value = "AI访谈分类;AIFT:AI访谈", name = "courseType")
    private String courseType;

    /**
     * AI访谈-任务名称
     */
    @ApiModelProperty(value = "AI访谈-任务名称", name = "taskName")
    private String taskName;

    /**
     * AI访谈-开始时间
     */
    @ApiModelProperty(value = "AI访谈-开始时间", name = "startDateTime")
    private Date startDateTime;

    /**
     * AI访谈-结束时间
     */
    @ApiModelProperty(value = "AI访谈-结束时间", name = "endDateTime")
    private Date endDateTime;

    /**
     * AI访谈-封面URL
     */
    @ApiModelProperty(value = "AI访谈-封面URL", name = "taskCoverUrl")
    private String taskCoverUrl;

    /**
     * AI访谈-任务状态;CG：草稿，JXZ：进行中，YWC：已完成，YJS：已结束
     */
    @ApiModelProperty(value = "AI访谈-任务状态;CG：草稿，JXZ：进行中，YWC：已完成，YJS：已结束", name = "taskStatus")
    private String taskStatus;

    /**
     * AI访谈-员工总人数
     */
    @ApiModelProperty(value = "AI访谈任务-员工总人数", name = "totalEmpNum")
    private Integer totalEmpNum;

    /**
     * AI访谈-员工未开始人数
     */
    @ApiModelProperty(value = "AI访谈任务-员工未开始人数", name = "wksEmpNum")
    private Integer wksEmpNum;

    /**
     * AI访谈-员工进行中人数
     */
    @ApiModelProperty(value = "AI访谈任务-员工进行中人数", name = "jxzEmpNum")
    private Integer jxzEmpNum;

    /**
     * AI访谈-员工已完成人数
     */
    @ApiModelProperty(value = "AI访谈任务-员工已完成人数", name = "ywcEmpNum")
    private Integer ywcEmpNum;
}