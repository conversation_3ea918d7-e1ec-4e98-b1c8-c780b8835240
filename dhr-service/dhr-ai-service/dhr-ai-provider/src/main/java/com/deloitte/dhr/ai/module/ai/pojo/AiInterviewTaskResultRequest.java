package com.deloitte.dhr.ai.module.ai.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * AI访谈任务-结果分析
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AiInterviewTaskResultRequest")
public class AiInterviewTaskResultRequest {

    @ApiModelProperty(value = "访谈场景", name = "interviewScene")
    private String interviewScene;

    @ApiModelProperty(value = "访谈目标", name = "interviewObjective")
    private String interviewObjective;

    @ApiModelProperty(value = "访谈注意事项", name = "interviewCaveats")
    private String interviewCaveats;
    @ApiModelProperty(value = "访谈对象设定", name = "interviewRole")
    private String interviewRole;
    @ApiModelProperty(value = "访谈对象性别", name = "interviewGender")
    private String interviewGender;

    @ApiModelProperty(value = "访谈资料", name = "interviewGender")
    private List<AiInterviewFile> fileList;

    @ApiModelProperty(value = "访谈内容", name = "interviewGender")
    private String interviewContent;

    @Data
    @ApiModel("AiInterviewFile")
    public static class AiInterviewFile {
        @ApiModelProperty(value = "固定为：remote_url", name = "transfer_method")
        private String transfer_method;
        @ApiModelProperty(value = "文件类型，固定为：document", name = "type")
        private String type;
        @ApiModelProperty(value = "文件地址", name = "url")
        private String url;
    }
    public AiInterviewFile createAiInterviewFile(String fileUrl) {
        AiInterviewFile file = new AiInterviewFile();
        file.setTransfer_method("remote_url");
        file.setType("document");
        file.setUrl(fileUrl);
        return file;
    }

    public static class AiInterviewTaskContent {
        @ApiModelProperty(value = "问题", name = "interviewQuestion")
        private String interviewQuestion;
        @ApiModelProperty(value = "答案", name = "interviewAnswer")
        private String interviewAnswer;
    }
}
