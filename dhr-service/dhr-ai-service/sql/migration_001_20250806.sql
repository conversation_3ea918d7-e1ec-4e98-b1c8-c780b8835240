-- =====================================================
-- 迁移脚本：修改 ai_chat_msg_content 表的 content_type 字段长度
-- 创建时间：2025-08-06
-- 描述：将 content_type 字段从 varchar(20) 修改为 varchar(64)
-- =====================================================

-- 修改 ai_chat_msg_content 表的 content_type 字段
ALTER TABLE `dhr_ai_test`.`ai_chat_msg_content`
MODIFY COLUMN `content_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息类型' AFTER `msg_id`;

