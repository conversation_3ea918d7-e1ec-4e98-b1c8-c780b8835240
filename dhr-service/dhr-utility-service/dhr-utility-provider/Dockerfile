# 多阶段构建 - 构建阶段
# 使用官方 Maven 镜像，确保 AMD64 架构兼容性
FROM maven:3.8.6-openjdk-11 AS builder

# 设置工作目录
WORKDIR /build

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置Maven配置，优化内存使用（降低内存使用避免JVM崩溃）
ENV MAVEN_OPTS="-Xmx512m -XX:MaxMetaspaceSize=128m -Dmaven.repo.local=/root/.m2/repository"

# 配置阿里云Maven镜像仓库，提升依赖下载速度
RUN mkdir -p /root/.m2 && \
    echo '<?xml version="1.0" encoding="UTF-8"?>' > /root/.m2/settings.xml && \
    echo '<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"' >> /root/.m2/settings.xml && \
    echo '          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"' >> /root/.m2/settings.xml && \
    echo '          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0' >> /root/.m2/settings.xml && \
    echo '          http://maven.apache.org/xsd/settings-1.0.0.xsd">' >> /root/.m2/settings.xml && \
    echo '  <mirrors>' >> /root/.m2/settings.xml && \
    echo '    <mirror>' >> /root/.m2/settings.xml && \
    echo '      <id>aliyunmaven</id>' >> /root/.m2/settings.xml && \
    echo '      <mirrorOf>*</mirrorOf>' >> /root/.m2/settings.xml && \
    echo '      <name>阿里云公共仓库</name>' >> /root/.m2/settings.xml && \
    echo '      <url>https://maven.aliyun.com/repository/public</url>' >> /root/.m2/settings.xml && \
    echo '    </mirror>' >> /root/.m2/settings.xml && \
    echo '  </mirrors>' >> /root/.m2/settings.xml && \
    echo '</settings>' >> /root/.m2/settings.xml

# 第一步：复制整个项目结构（用于多模块构建）
COPY . .

# 第二步：下载依赖并构建特定的工具服务模块
RUN mvn clean package -pl dhr-service/dhr-utility-service/dhr-utility-provider -am -DskipTests

# 运行阶段 - 使用更小更安全的基础镜像
FROM openjdk:11-jre-slim

# 设置维护者信息
LABEL maintainer="Deloitte DHR Team"
LABEL description="DHR Utility Service - 工具服务，提供文件、短信、邮件等功能"
LABEL version="3.7.0"
LABEL architecture="amd64"
LABEL build.maven-version="3.8.6"
LABEL build.java-version="11"

# 设置工作目录
WORKDIR /app

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装必要工具（Debian使用apt）
RUN apt-get update && apt-get install -y curl netcat && rm -rf /var/lib/apt/lists/*

# 创建应用用户(安全考虑)
RUN addgroup --system app && adduser --system --group app

# 设置优化的JVM环境变量（针对容器环境优化）
ENV JAVA_OPTS="-Xms256m -Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0"
ENV CONFIG_PROFILE=prod
ENV CONFIG_ENABLE=true

# 从构建阶段复制JAR文件
COPY --from=builder /build/dhr-service/dhr-utility-service/dhr-utility-provider/target/dhr-utility-service.jar app.jar

# 复制启动脚本
COPY dhr-service/dhr-utility-service/dhr-utility-provider/docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# 创建日志目录和文件存储目录
RUN mkdir -p /app/logs/dhr-utility-service/info && \
    mkdir -p /app/files && \
    chown -R app:app /app

# 切换到应用用户
USER app

# 暴露端口(端口待确认，使用默认的8080)
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["sh", "-c", "java $JAVA_OPTS -jar /app/app.jar"]
