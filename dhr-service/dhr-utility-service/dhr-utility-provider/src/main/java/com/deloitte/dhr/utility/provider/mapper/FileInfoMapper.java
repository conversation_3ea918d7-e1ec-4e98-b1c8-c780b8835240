package com.deloitte.dhr.utility.provider.mapper;


import com.deloitte.dhr.utility.provider.entity.FileInfo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <AUTHOR>
@org.apache.ibatis.annotations.Mapper
public interface FileInfoMapper extends Mapper<FileInfo> {
    /**
     * 根据文件组唯一id删除记录
     *
     * @param fileGroupId
     * @return
     */
    int deleteByFileGroupId(@Param("fileGroupId") String fileGroupId);

    /**
     * 批量插入记录
     *
     * @param list
     * @return
     */
    int insertBatch(@Param("list") List<FileInfo> list);

    /**
     * 插入单条记录
     *
     * @param fileInfo
     * @return
     */
    int insertOne(@Param("fileInfo") FileInfo fileInfo);

    /**
     * 根据文件组唯一id查询记录
     *
     * @param fileGroupId
     * @return
     */
    List<FileInfo> findByFileGroupId(@Param("fileGroupId") String fileGroupId);

    List<FileInfo> findById(@Param("uid") Long uid);

    int updateByid(@Param("info") FileInfo info);


    void deleteByKey(@Param("key") String key);

    /**
     * 批量查询
     *
     * @param ids
     * @return
     */
    List<FileInfo> findByIds(@Param("ids") List<Long> ids);
}