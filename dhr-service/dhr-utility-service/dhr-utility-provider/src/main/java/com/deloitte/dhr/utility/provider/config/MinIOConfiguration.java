package com.deloitte.dhr.utility.provider.config;


import com.deloitte.dhr.utility.provider.entity.MinIOProperties;
import io.minio.MinioClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
@EnableConfigurationProperties(MinIOProperties.class)
public class MinIOConfiguration {

    @Bean
    public MinioClient getMinioClient(MinIOProperties minIOProperties) throws Exception {
        MinioClient minioClient = new MinioClient(minIOProperties.getEndpoint(), minIOProperties.getPort(), minIOProperties.getAccessKey(), minIOProperties.getSecretKey(), minIOProperties.getSecure());
        boolean isExist = minioClient.bucketExists(minIOProperties.getBucketName());
        // 创建 bucket
        if (!isExist) {
            minioClient.makeBucket(minIOProperties.getBucketName());
        }
        return minioClient;
    }
}
