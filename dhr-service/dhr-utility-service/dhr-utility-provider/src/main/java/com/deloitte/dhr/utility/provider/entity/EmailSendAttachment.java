package com.deloitte.dhr.utility.provider.entity;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Table(name = "T_EMAIL_SEND_ATTACHMENT")
public class EmailSendAttachment {
    @Id
    @Column(name = "ID")
    private Long id;

    @Column(name = "FILE_NAME")
    private String fileName;

    @Column(name = "FILE_SIZE")
    private Long fileSize;

    @Column(name = "STORAGE_ADDRESS")
    private String storageAddress;

    @Column(name = "SUFFIX")
    private String suffix;

    @Column(name = "EMAIL_ID")
    private Long emailId;

    /**
     * @return ID
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return FILE_NAME
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * @param fileName
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * @return FILE_SIZE
     */
    public Long getFileSize() {
        return fileSize;
    }

    /**
     * @param fileSize
     */
    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    /**
     * @return STORAGE_ADDRESS
     */
    public String getStorageAddress() {
        return storageAddress;
    }

    /**
     * @param storageAddress
     */
    public void setStorageAddress(String storageAddress) {
        this.storageAddress = storageAddress;
    }

    /**
     * @return SUFFIX
     */
    public String getSuffix() {
        return suffix;
    }

    /**
     * @param suffix
     */
    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    /**
     * @return EMAIL_ID
     */
    public Long getEmailId() {
        return emailId;
    }

    /**
     * @param emailId
     */
    public void setEmailId(Long emailId) {
        this.emailId = emailId;
    }
}