package com.deloitte.dhr.utility.provider.controller;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.CommonFileConvertInterface;
import com.deloitte.dhr.utility.api.dto.word.Word2PDFDto;
import com.deloitte.dhr.utility.api.dto.word.Word2PDFResponseDto;
import com.deloitte.dhr.utility.provider.service.impl.CommonMinioFileDfsServiceImpl;
import com.deloitte.dhr.utility.provider.utils.FileUtil;
import com.deloitte.dhr.utility.provider.utils.Word2PdfUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.poi.xwpf.converter.pdf.PdfOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Map;

/**
 * 文件转换相关方法
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
@Slf4j
@RestController
@CrossOrigin
@RequestMapping(value = "/utility/file/convert")
@Api(value = "/utility/file/convert", tags = "文件转换")
public class CommonFileConvertController implements CommonFileConvertInterface {
    private static final String FILE_TYPE_PDF = "pdf";
    @Autowired
    private CommonMinioFileDfsServiceImpl commonMinioFileDfsService;

    @Override
    @ApiOperation(value = "word转pdf")
    public ResponseVO<Word2PDFResponseDto> word2pdf(@RequestBody Word2PDFDto dto) throws Exception {
        InputStream source = null;
        ByteArrayOutputStream target = null;
        Word2PDFResponseDto fileResponseDto = new Word2PDFResponseDto();
        try {
            source = new ByteArrayInputStream(dto.getFileBytes());
            target = new ByteArrayOutputStream();
            PdfOptions options = PdfOptions.create();
            ByteArrayOutputStream fillWordOs = Word2PdfUtil.wordConverterToPdf(source, target, options, dto.getParam());
            fileResponseDto.setPdfFileBytes(target.toByteArray());
            String[] split = dto.getFileName().split("\\.");
            fileResponseDto.setPdfFileName(split[0] + ".pdf");
            fileResponseDto.setWordFileName(split[0] + ".docx");
            fileResponseDto.setWordFileBytes(fillWordOs.toByteArray());
            fileResponseDto.setPdfFileBytes(target.toByteArray());
        } catch (Exception e) {
            log.error(e.toString());
        } finally {
            if (source != null) {
                source.close();
            }
            if (target != null) {
                target.close();
            }
        }
        return ResponseVO.success(fileResponseDto);
    }

    @ApiOperation(value = "word转pdf")
    @GetMapping("preview/{fileId}")
    public void word2Prefile(@PathVariable("fileId") String fileId, HttpServletResponse response) throws Exception {
        Map<String, byte[]> stringMap = commonMinioFileDfsService.downloadFileByIdKeyName(fileId);
      if(stringMap.isEmpty()){
          throw new Exception("预览文件不存在或错误");
      }
        stringMap.forEach((k,v)->{
            try {
                if (FILE_TYPE_PDF.equalsIgnoreCase(FileUtil.getSuffix(k))) {
                    response.setHeader("Content-Type", "application/pdf");
                    OutputStream outputStream = response.getOutputStream();
                    IOUtils.write(v, outputStream);
                } else {
                    Word2PdfUtil.wordToPdf(k, v, response);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });


    }
}
