package com.deloitte.dhr.utility.provider.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 18/11/2021 14:27
 */
@Data
public class ParamShortInfo implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 短网址编码
     */
    private String shorter;
    /**
     * 原始参数
     */
    private String parameter;
    /**
     * 是否启用， 1-是, 0-否
     */
    private Boolean enabled;
    /**
     * 数据创建者id
     */
    private String createBy;
    /**
     * 数据创建时间
     */
    private Date createTime;
    /**
     * 数据上一次修改人id
     */
    private String updateBy;
    /**
     * 数据上一次修改时间
     */
    private Date updateTime;
    /**
     * 逻辑删除标识符 true --> 已删除 | false --> 未删除
     */
    private Boolean deleteFlag;
}
