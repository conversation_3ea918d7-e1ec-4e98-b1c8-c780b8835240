package com.deloitte.dhr.utility.provider.service.impl;

import com.alibaba.fastjson2.JSON;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.deloitte.dhr.utility.provider.config.properties.AliyunSmsInstanceProperties;
import com.deloitte.dhr.utility.provider.constant.AliyunSmsActionEnum;
import com.deloitte.dhr.utility.api.dto.sms.AliYunSmsResponseVo;
import com.deloitte.dhr.utility.provider.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * SMS接口实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
@Service
@Slf4j
public class SmsServiceImpl implements SmsService {
    @Autowired
    private IAcsClient client;
    @Autowired
    private AliyunSmsInstanceProperties aliyunSmsInstanceProperties;

    @Override
    public AliYunSmsResponseVo sendSms(String phoneNumbers, String templateCode, String templateParam, String smsUpExtendCode, String outId) {
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain(AliyunSmsInstanceProperties.DOMAIN);
        request.setSysVersion(AliyunSmsInstanceProperties.VERSION);
        request.setSysAction(AliyunSmsActionEnum.SEND_SMS.getValue());
        request.putQueryParameter("RegionId", AliyunSmsInstanceProperties.REGION_ID);
        request.putQueryParameter("PhoneNumbers", phoneNumbers);
        request.putQueryParameter("SignName", aliyunSmsInstanceProperties.getSignName());
        request.putQueryParameter("TemplateCode", templateCode);
        if (null != templateParam && !templateParam.isEmpty()) {
            request.putQueryParameter("TemplateParam", templateParam);
        }
        if (null != smsUpExtendCode) {
            request.putQueryParameter("SmsUpExtendCode", smsUpExtendCode);
        }
        if (null != outId) {
            request.putQueryParameter("OutId", outId);
        }
        AliYunSmsResponseVo valueObject = new AliYunSmsResponseVo();
        try {
            CommonResponse response = this.client.getCommonResponse(request);
            valueObject = JSON.parseObject(response.getData(), AliYunSmsResponseVo.class);
            log.info("短信发送结果，code：{}，message：{}", valueObject.getCode(), valueObject.getMessage());
            if (valueObject.requestSuccessful()) {
                log.info("短信发送成功");
//                smsSendLogService.saveSmsSendLog(
//                        phoneNumbers, signName, templateCode, templateParam, smsUpExtendCode, outId,
//                        valueObject.getBizId(), valueObject.getRequestId());
            } else {
                log.error("短信发送失败-{}", valueObject.toString());
            }
        } catch (Exception e) {
            log.error("短信发送发生异常", e);
            valueObject.setMessage(e.getMessage());
        }
        return valueObject;
    }

    @Override
    public AliYunSmsResponseVo sendSmsToGlobe(String phoneNumbers,String templateParam,String message) {
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain(AliyunSmsInstanceProperties.DOMAIN);
        request.setSysVersion(AliyunSmsInstanceProperties.VERSION);
        request.setSysAction(AliyunSmsActionEnum.SEND_SMS_GLOBE.getValue());
        request.putQueryParameter("RegionId", AliyunSmsInstanceProperties.REGION_ID);
        request.putQueryParameter("To", phoneNumbers);
        request.putQueryParameter("SignName", aliyunSmsInstanceProperties.getSignName());
        //发送方senderId，选填
        request.putQueryParameter("From", "Alirich");
        //短信内容，必填
        request.putQueryParameter("Type", "OTP");
        request.putQueryParameter("Message", message);

        if (null != templateParam && !templateParam.isEmpty()) {
            request.putQueryParameter("TemplateParam", templateParam);
        }
        AliYunSmsResponseVo valueObject = new AliYunSmsResponseVo();
        try {
            CommonResponse response = this.client.getCommonResponse(request);
            valueObject = JSON.parseObject(response.getData(), AliYunSmsResponseVo.class);
            log.info("短信发送结果，code：{}，message：{}", valueObject.getCode(), valueObject.getMessage());
            if (valueObject.requestSuccessful()) {
                log.info("短信发送成功");
                log.info("短信发送成功phone：{}",phoneNumbers);
            } else {
                log.error("短信发送失败-{}", valueObject);
            }
        } catch (Exception e) {
            log.error("短信发送发生异常", e);
            valueObject.setMessage(e.getMessage());
        }
        return valueObject;
    }


}
