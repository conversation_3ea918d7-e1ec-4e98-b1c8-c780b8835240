package com.deloitte.dhr.utility.provider.service;

import com.deloitte.dhr.utility.api.dto.file.FileInfoDto;
import com.deloitte.dhr.utility.api.dto.file.FileResponseDto;

import java.util.List;

/**
 * 文件服务业务表相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
public interface FileBusinessService {

    /**
     * 获取传入文件组的唯一ID
     *
     * @param fileInfoDtoList
     * @param fileGroupId
     * @param empCode
     * @return
     */
    String getFileGroupId(List<FileInfoDto> fileInfoDtoList, String fileGroupId, String empCode);

    /**
     * 通过文件组ID获取文件组
     *
     * @param fileGroupId
     * @return
     */
    List<FileResponseDto> getFileGroup(String fileGroupId);

    /**
     * 通过文件ID获取文件
     *
     * @param id
     * @return
     */
    FileResponseDto findById(String id);

    /**
     * 通过文件IDs批量获取文件
     *
     * @param ids
     * @return
     */
    List<FileResponseDto> findByIds(List<Long> ids);

    /**
     * 通过文件ID删除文件
     *
     * @param id
     * @return
     */
    Integer deleteById(String id);

}
