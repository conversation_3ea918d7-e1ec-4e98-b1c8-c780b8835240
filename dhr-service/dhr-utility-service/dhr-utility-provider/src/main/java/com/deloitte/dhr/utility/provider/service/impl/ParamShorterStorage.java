package com.deloitte.dhr.utility.provider.service.impl;


import com.alibaba.cloud.commons.lang.StringUtils;
import com.deloitte.dhr.common.base.utils.RedisUtils;
import com.deloitte.dhr.utility.provider.entity.ParamShortInfo;
import com.deloitte.dhr.utility.provider.entity.ShorterString;
import com.deloitte.dhr.utility.provider.mapper.ParamShortInfoMapper;
import com.deloitte.dhr.utility.provider.service.ShorterGetter;
import com.deloitte.dhr.utility.provider.service.ShorterStorage;
import com.deloitte.dhr.utility.provider.utils.LoginInfoUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 18/11/2021 14:08
 */
@Service
public class ParamShorterStorage<T extends ShorterGetter> implements ShorterStorage<T> {
    @Autowired
    private ParamShortInfoMapper paramShortInfoMapper;
    @Autowired
    private RedisUtils redisUtils;

    private static final String KEY = "PARAM:SHORTER";

    private final static long EXPIRE_TIME = 24 *60 * 60;

    @Override
    public String get(String shorter) {
        //先从redis中进行查找，如果存在短码对应的参数信息，则直接返回，否则从数据库进行查询
       String origin = redisUtils.get(KEY + ":" + shorter);
       if(!StringUtils.isEmpty(origin)){
           return origin;
       }else {
           ParamShortInfo queryDto = new ParamShortInfo();
           queryDto.setShorter(shorter);
           ParamShortInfo paramShortInfo = paramShortInfoMapper.selectOne(queryDto);
           if (paramShortInfo != null) {
               redisUtils.set(KEY + ":" + shorter,paramShortInfo.getParameter(),EXPIRE_TIME);
               return paramShortInfo.getParameter();
           }
           return null;
       }

    }

    @Override
    public void clean(String url) {

    }

    @Override
    public void cleanShorter(String shorter) {

    }

    @Override
    public void save(String origin, T shorter) {
        ParamShortInfo paramShortInfo = new ParamShortInfo();
        paramShortInfo.setParameter(origin);
        paramShortInfo.setShorter(shorter.getShorter());
        paramShortInfo.setEnabled(true);
        paramShortInfo.setCreateBy(LoginInfoUtil.getLoginUserByToken() == null ? "-1" :LoginInfoUtil.getLoginUserByToken().getUsername());
        paramShortInfo.setUpdateBy(LoginInfoUtil.getLoginUserByToken() == null ? "-1" :LoginInfoUtil.getLoginUserByToken().getUsername());
        paramShortInfoMapper.insertSelective(paramShortInfo);
    }

    @Override
    public ShorterGetter getByOrigin(String origin) {
        ParamShortInfo queryDto = new ParamShortInfo();
        queryDto.setParameter(origin);
        ParamShortInfo paramShortInfo = paramShortInfoMapper.selectOne(queryDto);
        if (paramShortInfo != null) {
            return new ShorterString(paramShortInfo.getShorter());
        }
        return null;
    }
}
