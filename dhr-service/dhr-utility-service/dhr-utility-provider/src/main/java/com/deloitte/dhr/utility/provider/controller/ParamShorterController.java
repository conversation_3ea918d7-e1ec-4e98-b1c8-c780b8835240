package com.deloitte.dhr.utility.provider.controller;


import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.provider.generator.StringGeneratorRandom;
import com.deloitte.dhr.utility.provider.generator.UrlShorterGeneratorSimple;
import com.deloitte.dhr.utility.provider.service.ShorterGetter;
import com.deloitte.dhr.utility.provider.service.impl.ParamShorterStorage;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 短码服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 18/11/2021 14:04
 */
@RestController
@Slf4j
@RequestMapping("/param")
@Api(value = "/param", tags = "短码服务")
public class ParamShorterController {

    @Autowired
    private ParamShorterStorage paramShorterStorage;

    @PostMapping("/shorter/info")
    public ResponseVO createShortUrl(@RequestParam("parameter") String parameter) {
        String shorter = "";
        ShorterGetter shorterGetter = paramShorterStorage.getByOrigin(parameter);
        if (shorterGetter == null) {
            log.info("不存在该参数信息，产生新的短码");
            UrlShorterGeneratorSimple simple = new UrlShorterGeneratorSimple();
            simple.setGenerator(new StringGeneratorRandom(4));
            simple.setShorterStorage(paramShorterStorage);
            shorter = simple.generate(parameter).getShorter();
        } else {
            log.info("已经存在相同的参数信息");
            shorter = shorterGetter.getShorter();
        }
        Map returnMap = new HashMap(16);
        returnMap.put("short", shorter);
        return ResponseVO.success(returnMap);
    }

    @GetMapping("/{code}")
    public ResponseVO getParameter(@PathVariable("code") String code) {
        String parameter = paramShorterStorage.get(code);
        Map returnMap = new HashMap(16);
        returnMap.put("parameter", parameter);
        return ResponseVO.success(returnMap);
    }
}
