package com.deloitte.dhr.utility.provider.service.impl;

import com.deloitte.dhr.common.base.utils.IdWorkerUtil;
import com.deloitte.dhr.utility.api.dto.email.EmailParamDto;
import com.deloitte.dhr.utility.api.dto.file.Attachment;
import com.deloitte.dhr.utility.provider.entity.EmailSendAttachment;
import com.deloitte.dhr.utility.provider.entity.EmailSendLog;
import com.deloitte.dhr.utility.provider.mapper.EmailSendAttachmentMapper;
import com.deloitte.dhr.utility.provider.mapper.EmailSendLogMapper;
import com.deloitte.dhr.utility.provider.service.EmailSendLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 邮件发送日志接口实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
@Slf4j
@Service
public class EmailSendLogServiceImpl implements EmailSendLogService {

    private final EmailSendLogMapper emailSendLogMapper;
    private final EmailSendAttachmentMapper emailSendAttachmentMapper;

    public EmailSendLogServiceImpl(EmailSendLogMapper emailSendLogMapper, EmailSendAttachmentMapper emailSendAttachmentMapper) {
        this.emailSendLogMapper = emailSendLogMapper;
        this.emailSendAttachmentMapper = emailSendAttachmentMapper;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveSendLog(EmailParamDto paramDto, List<Attachment> attachmentList) {
        EmailSendLog emailSendLog = new EmailSendLog();
        emailSendLog.setId(IdWorkerUtil.getId());
        emailSendLog.setReceiver(paramDto.getReceivers());
        emailSendLog.setCopyTo(paramDto.getCopyTo());
        emailSendLog.setSubject(paramDto.getSubject());
        emailSendLog.setContent(paramDto.getContent());
        emailSendLogMapper.insert(emailSendLog);

        if (attachmentList != null && !attachmentList.isEmpty()) {
            List<EmailSendAttachment> emailSendAttachmentList = new ArrayList<>();
            for (Attachment attachment : attachmentList) {
                EmailSendAttachment sendAttachment = new EmailSendAttachment();
                sendAttachment.setId(IdWorkerUtil.getId());
                sendAttachment.setFileName(attachment.getFileName());
                sendAttachment.setEmailId(emailSendLog.getId());
                sendAttachment.setSuffix(attachment.getSuffix());
                sendAttachment.setFileSize(attachment.getSize());
                sendAttachment.setStorageAddress(attachment.getFileAddress());
                emailSendAttachmentList.add(sendAttachment);
            }
            emailSendAttachmentMapper.insertBatch(emailSendAttachmentList);
        }

    }
}
