package com.deloitte.dhr.utility.provider.config;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.deloitte.dhr.utility.provider.config.properties.AliyunSmsInstanceProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * TODO 类注释
 *
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(AliyunSmsInstanceProperties.class)
public class AliyunSmsInstanceAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean(IAcsClient.class)
    public IAcsClient iAcsClient(AliyunSmsInstanceProperties properties) {
        return new DefaultAcsClient(
                DefaultProfile.getProfile(AliyunSmsInstanceProperties.REGION_ID,
                        properties.getAccessKeyId(),
                        properties.getAccessSecret()
                )
        );
    }
}
