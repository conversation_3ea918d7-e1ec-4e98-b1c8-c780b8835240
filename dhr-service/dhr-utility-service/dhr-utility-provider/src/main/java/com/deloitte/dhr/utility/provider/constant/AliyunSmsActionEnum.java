package com.deloitte.dhr.utility.provider.constant;

import lombok.Getter;

/**
 * 阿里云短信发送枚举类
 * <AUTHOR>
 */
public enum AliyunSmsActionEnum {

    /**
     * 短信发送
     */
    SEND_SMS("SendSms"),
    /**
     * 批量发送短信
     */
    SEND_BATCH_SMS("SendBatchSms"),
    /**
     * 查询短信发送明细
     */
    QUERY_SEND_DETAILS("QuerySendDetails"),

    /**
     * 国际地区短信发送
     */
    SEND_SMS_GLOBE("SendMessageToGlobe");

    @Getter
    private final String value;


    AliyunSmsActionEnum(String value) {
        this.value = value;
    }
}
