package com.deloitte.dhr.utility.provider.controller;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.FileBusinessInterface;
import com.deloitte.dhr.utility.api.dto.file.FileInfoDto;
import com.deloitte.dhr.utility.api.dto.file.FileResponseDto;
import com.deloitte.dhr.utility.provider.service.FileBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 文件服务业务表相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
@Slf4j
@RestController
@CrossOrigin
@RequestMapping(value = "/utility/file/bus")
@Api(value = "/utility/file/bus", tags = "文件业务表服务")
public class FileBusinessController implements FileBusinessInterface {
    private final FileBusinessService fileBusinessService;

    public FileBusinessController(FileBusinessService fileBusinessService) {
        this.fileBusinessService = fileBusinessService;
    }

    @Override
    @ApiOperation(value = "获取传入文件组的唯一ID")
    @PostMapping(value = "/getGroupId")
    public ResponseVO<String> getFileGroupId(List<FileInfoDto> fileInfoDtoList, String fileGroupId, String empCode) {
        return ResponseVO.success(fileBusinessService.getFileGroupId(fileInfoDtoList, fileGroupId, empCode));
    }

    @Override
    @ApiOperation(value = "通过文件组ID获取文件组")
    @PostMapping(value = "/getFileGroup")
    public ResponseVO<List<FileResponseDto>> getFileGroup(String fileGroupId) {
        return ResponseVO.success(fileBusinessService.getFileGroup(fileGroupId));
    }

    @Override
    @ApiOperation(value = "通过文件ID获取文件")
    @PostMapping(value = "/{id}")
    public ResponseVO<FileResponseDto> findById(@PathVariable("id") String id) {
        return ResponseVO.success(fileBusinessService.findById(id));
    }

}
