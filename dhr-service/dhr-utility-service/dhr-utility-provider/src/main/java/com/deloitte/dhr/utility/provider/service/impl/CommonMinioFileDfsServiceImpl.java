package com.deloitte.dhr.utility.provider.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.MD5;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.utils.IdWorkerUtil;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import com.deloitte.dhr.utility.api.dto.file.FileRequestDto;
import com.deloitte.dhr.utility.api.dto.file.FileResponseDto;
import com.deloitte.dhr.utility.api.exception.ExceptionEnum;
import com.deloitte.dhr.utility.api.exception.FileException;
import com.deloitte.dhr.utility.provider.entity.FileInfo;
import com.deloitte.dhr.utility.provider.entity.MinIOProperties;
import com.deloitte.dhr.utility.provider.mapper.FileInfoMapper;
import com.deloitte.dhr.utility.provider.service.CommonFileDfsService;
import com.deloitte.dhr.utility.provider.service.FileBusinessService;
import com.deloitte.dhr.utility.provider.service.MinioService;
import com.deloitte.dhr.utility.provider.utils.FileUtil;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 文件服务实现
 * @date 27/07/2021 14:18
 */
@Slf4j
@Primary
@Service("minIOService")
public class CommonMinioFileDfsServiceImpl implements CommonFileDfsService {
    private static final String STATUS_SUCCESS = "success";
    private static final String STR_FULL_TIME = "yyyy-MM-dd HH:mm:ss";
    private final FileInfoMapper fileInfoMapper;
    private final FileBusinessService fileBusinessService;
    private final MinioService minIOService;
    @Value("${file-view-url}")
    private String minioNginxUrl;
    @Autowired
    private MinIOProperties minIOProperties;

    public CommonMinioFileDfsServiceImpl(FileInfoMapper fileInfoMapper, FileBusinessService fileBusinessService, MinioService minioservice) {
        this.fileInfoMapper = fileInfoMapper;
        this.fileBusinessService = fileBusinessService;
        this.minIOService = minioservice;
    }

    @Override
    public FileResponseDto uploadFile(FileRequestDto fileRequestDto) {
        return this.batchUploadFile(Collections.singletonList(fileRequestDto)).get(0);
    }

    /**
     * 保存文件业务数据
     *
     * @param fileResponseDtoList
     * @param fileRequestDtoList
     */
    private void saveFileInfoList(List<FileResponseDto> fileResponseDtoList, List<FileRequestDto> fileRequestDtoList) {
        if (fileRequestDtoList.get(0).getFileGroupId() != null) {
            fileInfoMapper.deleteByFileGroupId(fileRequestDtoList.get(0).getFileGroupId());
        }
        final String userName = LoginUtil.getLoginUser() != null ? LoginUtil.getLoginUser().getUsername() : "";
        String fileGroupId = UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
        List<FileInfo> fileInfoList = fileResponseDtoList.stream().map(fileResponseDto -> {
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileSize(fileResponseDto.getSize());
            fileInfo.setAttachName(fileResponseDto.getName());
            fileInfo.setKey(fileResponseDto.getUrl());
            fileInfo.setAttachType(fileResponseDto.getType());
            fileInfo.setId(IdWorkerUtil.getId());
            fileInfo.setCreatedDate(new Date());
            fileInfo.setCreateUser(userName);
            fileInfo.setUpdatedDate(new Date());
            fileInfo.setUpdateUser(userName);
            fileInfo.setFileGroupId(fileGroupId);
            fileResponseDto.setId(fileInfo.getId().toString());
            fileResponseDto.setFileGroupId(fileGroupId);
            return fileInfo;
        }).collect(Collectors.toList());
        fileInfoMapper.insertBatch(fileInfoList);
    }

    @Override
    public Boolean deleteFileByUrl(String url) {
        boolean result = false;
//        try {
//            this.minIOService.delete(url);
//            //删除业务表数据
//            fileInfoMapper.deleteByKey(url);
//            result = true;
//        } catch (Exception e) {
//            log.error("删除文件", e);
//            throw new CommRunException(ExceptionEnum.DELETE_FILE_FAILURE_ERR.getCode(), ExceptionEnum.DELETE_FILE_FAILURE_ERR.getMessage());
//        }
        return result;
    }

    public Boolean deleteFile(FileResponseDto fileResponseDto) {
        boolean result = false;
        try {
            this.minIOService.delete(fileResponseDto.getUrl());
            //删除业务表数据
            fileInfoMapper.deleteByKey(fileResponseDto.getId());
            result = true;
        } catch (Exception e) {
            log.error("删除文件", e);
            throw new CommRunException(ExceptionEnum.DELETE_FILE_FAILURE_ERR.getCode(), ExceptionEnum.DELETE_FILE_FAILURE_ERR.getMessage());
        }
        return result;
    }


    @Override
    public FileResponseDto uploadFile(MultipartFile uploadFile, String token) {
        return this.batchUploadFile(Collections.singletonList(uploadFile), token).get(0);
    }

    @Override
    public void downloadFileByUrl(String url, String fileName, HttpServletResponse response) {

    }

    @Override
    public Map<String, byte[]> downloadFileById(String id) {
        Map<String, byte[]> map = new HashMap<>(16);
        FileResponseDto fileResponseDto = fileBusinessService.findById(id);
        if (fileResponseDto == null) {
            return map;
        }
        //调用fastDfs下载文件
        byte[] fileByte = new byte[0];
        try {
            fileByte = this.minIOService.download(fileResponseDto.getUrl());
        } catch (Exception e) {
            log.error("系统异常", e);
        }
        map.put("fileByte", fileByte);
        return map;
    }

    public Map<String, byte[]> downloadFileByIdKeyName(String id) {
        Map<String, byte[]> map = new HashMap<>(16);
        FileResponseDto fileResponseDto = fileBusinessService.findById(id);
        if (fileResponseDto == null) {
            return map;
        }
        //调用fastDfs下载文件
        byte[] fileByte = new byte[0];
        try {
            fileByte = this.minIOService.download(fileResponseDto.getUrl());
        } catch (Exception e) {
            log.error("系统异常", e);
        }
        map.put(fileResponseDto.getName(), fileByte);
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<FileResponseDto> batchUploadFile(List<FileRequestDto> fileRequestDtoList) {
        List<FileResponseDto> fileResponseDtoList = new ArrayList<>();
        List<String> errorFileNameList = new ArrayList<>();
        // 校验上传文件是否合法
        if (!FileUtil.verifyFile(fileRequestDtoList, errorFileNameList)) {
            //不合法的文件名称
            String errorFileName = Joiner.on(",").join(errorFileNameList);
            throw new CommRunException(ExceptionEnum.UPLOAD_FILE_ILLEGAL.getMessage() + ":" + errorFileName);
        }
        String result;
        //获取客户端
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String tempDir = sdf.format(new Date()) + "/" + UUID.randomUUID().toString().replaceAll("-", "").toUpperCase() + "/";
            for (FileRequestDto fileRequestDto : fileRequestDtoList) {
                FileResponseDto fileResponseDto = new FileResponseDto();
                fileResponseDto.setCreateDate(DateUtil.format(new Date(), STR_FULL_TIME));
                //2、文件名
                String fileName = fileRequestDto.getFileName();
                //3、文件后缀
                String fileType = FileUtil.getSuffix(fileRequestDto.getFileName());
                byte[] fileBytes = fileRequestDto.getFileBytes();
                fileResponseDto.setName(fileName);
                fileResponseDto.setUrl(tempDir + fileName);
                fileResponseDto.setSize(FileUtil.getPrintSize(fileBytes.length));
                fileResponseDto.setType(fileType);
                //4、如果是图片文件
                if (FileUtil.isFileImage(fileType)) {
                    ByteArrayInputStream bis = new ByteArrayInputStream(fileBytes);
                    BufferedImage image;
                    image = ImageIO.read(bis);
                    String width = String.valueOf(image.getWidth());
                    String height = String.valueOf(image.getHeight());
                    fileResponseDto.setFormatSize(width + "*" + height);
                    bis.close();
                }
                //5、上传
                log.info("文件上传，开始：【{}】", Instant.now().toString());
                // 调用方法获取正确的 Content-Type
                String contentType = determineContentType(fileType);
                result = minIOService.upload(fileResponseDto.getUrl(), fileBytes, contentType);

                log.info("文件上传，结束：【{}】", Instant.now().toString());
                result = StrUtil.replace(result, minIOProperties.getMinIOUrl(), minioNginxUrl);
                fileResponseDto.setUrl(result);
                fileResponseDto.setDownloadUrl(result);
                fileResponseDto.setPreviewUrl(result);
                fileResponseDto.setStatus(STATUS_SUCCESS);
                fileResponseDto.setMsg("上传成功");
                fileResponseDtoList.add(fileResponseDto);
            }
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw new CommRunException(ExceptionEnum.UPLOAD_FILE_FAILURE_ERR.getCode(), ExceptionEnum.UPLOAD_FILE_FAILURE_ERR.getMessage());
        }
        //保存文件信息到数据库
        this.saveFileInfoList(fileResponseDtoList, fileRequestDtoList);
        return fileResponseDtoList;
    }

    private String determineContentType(String fileType) {
        if (StrUtil.isBlank(fileType)) {
            return "application/octet-stream";
        }

        fileType = fileType.toLowerCase();
        switch (fileType) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "pdf":
                return "application/pdf";
            case "doc":
            case "docx":
                return "application/msword";
            case "xls":
            case "xlsx":
                return "application/vnd.ms-excel";
            case "ppt":
            case "pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case "txt":
                return "text/plain";
            case "csv":
                return "text/csv";
            case "md":
                return "text/markdown";
            default:
                return "application/octet-stream";
        }
    }


    @Override
    public List<FileResponseDto> batchUploadFile(List<MultipartFile> uploadFileList, String token) {
        List<FileRequestDto> fileRequestDtoList = new ArrayList<>();
        uploadFileList.forEach(uploadFile -> {
            FileRequestDto fileRequestDto = new FileRequestDto();
            fileRequestDto.setFileName(uploadFile.getOriginalFilename());
            fileRequestDto.setToken(token);
            try {
                fileRequestDto.setFileBytes(uploadFile.getBytes());
            } catch (IOException e) {
                log.error("上传文件异常", e);
                throw new FileException(ExceptionEnum.UPLOAD_FILE_FAILURE_ERR.getCode(), ExceptionEnum.UPLOAD_FILE_FAILURE_ERR.getMessage());
            }
            fileRequestDtoList.add(fileRequestDto);
        });

        return this.batchUploadFile(fileRequestDtoList);
    }

    @Override
    public void downloadFileById(String id, HttpServletResponse response) {
        FileResponseDto fileResponseDto = fileBusinessService.findById(id);
        if (fileResponseDto == null) {
            throw new FileException(ExceptionEnum.DOWNLOAD_FILE_NOT_FOUND.getCode(), ExceptionEnum.DOWNLOAD_FILE_NOT_FOUND.getMessage());

        }
        //获取文件流
        InputStream is = null;
        OutputStream os = null;
        try {
            //设置response参数
            FileUtil.setResponse(response, fileResponseDto.getName());
            //调用fastDfs下载文件
            byte[] fileByte = this.minIOService.download(fileResponseDto.getUrl().replace(minIOProperties.getMinIOUrl(), ""));
            if (fileByte != null) {
                os = response.getOutputStream();
                is = new ByteArrayInputStream(fileByte);
                byte[] buffer = new byte[1024 * 5];
                int len;
                while ((len = is.read(buffer)) > 0) {
                    os.write(buffer, 0, len);
                }
                os.flush();
            }
        } catch (Exception e) {
            throw new FileException(ExceptionEnum.DOWNLOAD_FILE_FAILURE_ERR.getCode(), ExceptionEnum.DOWNLOAD_FILE_FAILURE_ERR.getMessage(), e);
        } finally {
            // 关闭流
            try {
                if (is != null) {
                    is.close();
                }
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                log.error("系统异常", e);
            }
        }

    }

    @Override
    public void previewFileById(String id, HttpServletResponse response) {
        FileResponseDto fileResponseDto = fileBusinessService.findById(id);
        if (fileResponseDto == null) {
            throw new FileException(ExceptionEnum.DOWNLOAD_FILE_NOT_FOUND.getCode(), ExceptionEnum.DOWNLOAD_FILE_NOT_FOUND.getMessage());

        }
        //获取文件流
        InputStream is = null;
        OutputStream os = null;
        try {
            //设置response参数
            FileUtil.setPreviewResponse(response, fileResponseDto.getName());
            //调用fastDfs下载文件
            byte[] fileByte = this.minIOService.download(fileResponseDto.getUrl());
            if (fileByte != null) {
                os = response.getOutputStream();
                is = new ByteArrayInputStream(fileByte);
                byte[] buffer = new byte[1024 * 5];
                int len;
                while ((len = is.read(buffer)) > 0) {
                    os.write(buffer, 0, len);
                }
                os.flush();
            }
        } catch (Exception e) {
            throw new FileException(ExceptionEnum.DOWNLOAD_FILE_FAILURE_ERR.getCode(), ExceptionEnum.DOWNLOAD_FILE_FAILURE_ERR.getMessage(), e);
        } finally {
            // 关闭流
            try {
                if (is != null) {
                    is.close();
                }
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                log.error("系统异常", e);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteFileById(String id) {
        FileResponseDto fileResponseDto = fileBusinessService.findById(id);
        if (fileResponseDto == null) {
            return false;
        }
        return this.deleteFile(fileResponseDto);
    }

    @Override
    public FileResponseDto previewFile(String id) {
        FileResponseDto responseDto = fileBusinessService.findById(id);
        if (responseDto == null) {
            throw new FileException(ExceptionEnum.PREVIEW_FILE_NOT_FOUND.getCode(), ExceptionEnum.PREVIEW_FILE_NOT_FOUND.getMessage());
        }
        return responseDto;
    }

    @Override
    public void batchDownloadToZip(List<Long> fileIds, String zipFileName, HttpServletResponse response) {
        try {
            //获取文件字节数组
            Map<String, byte[]> map = this.batchGetFileBytes(fileIds);
            InputStream[] fileStreams = new InputStream[map.size()];
            String[] fileNames = new String[map.size()];
            int i = 0;
            for (Map.Entry<String, byte[]> entry : map.entrySet()) {
                fileNames[i] = entry.getKey();
                fileStreams[i] = new ByteArrayInputStream(entry.getValue());
                i++;
            }
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipFileName + ".zip", "UTF-8"));
            //多个文件压缩成压缩包返回
            ZipUtil.zip(response.getOutputStream(), fileNames, fileStreams);
        } catch (Exception e) {
            log.error("调用minio下载文件, 错误信息为：{}", e.getMessage());
        }
    }

    @Override
    public Map<String, byte[]> internalBatchDownload(List<Long> fileIds) {
        return this.batchGetFileBytes(fileIds);
    }

    /**
     * 批量获取文件字节流
     *
     * @param fileIds
     * @return
     */
    private Map<String, byte[]> batchGetFileBytes(List<Long> fileIds) {
        Map<String, byte[]> map = new HashMap<>(16);
        List<FileResponseDto> dtoList = fileBusinessService.findByIds(fileIds);
        if (CollectionUtils.isEmpty(dtoList)) {
            return map;
        }
        //处理重名的文件
        Map<String, List<FileResponseDto>> fileNameMap = dtoList.stream().collect(Collectors.groupingBy(FileResponseDto::getName));
        fileNameMap.forEach((fileName, list) -> {
            for (int i = 0; i < list.size(); i++) {
                if (i > 0) {
                    String prefix = fileName.substring(0, fileName.lastIndexOf("."));
                    String suffix = fileName.substring(fileName.lastIndexOf("."));
                    list.get(i).setName(prefix + "(" + i + ")" + suffix);
                }
            }
        });
        try {
            for (FileResponseDto fileResponseDto : dtoList) {
                //调用minio下载文件
                byte[] fileByte = this.minIOService.download(fileResponseDto.getUrl());
                map.put(fileResponseDto.getName(), fileByte);
            }
        } catch (Exception e) {
            log.error("调用minio下载文件, 错误信息为：{}", e.getMessage());
        }
        return map;
    }
}

