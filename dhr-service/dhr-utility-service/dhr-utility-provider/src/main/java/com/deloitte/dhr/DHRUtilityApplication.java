package com.deloitte.dhr;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import tk.mybatis.spring.annotation.MapperScan;

@SpringBootApplication
@EnableDiscoveryClient
@EnableScheduling // 开启定时任务
@EnableCaching    // 开启缓存
@MapperScan(basePackages = "com.deloitte.dhr.utility.provider.mapper")
@EnableTransactionManagement
@EnableFeignClients(basePackages = "com.deloitte.dhr.utility.api")
public class DHRUtilityApplication {

    public static void main(String[] args) {
        SpringApplication.run(DHRUtilityApplication.class, args);
    }

}
