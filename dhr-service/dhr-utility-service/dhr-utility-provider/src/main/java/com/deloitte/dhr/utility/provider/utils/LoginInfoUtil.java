package com.deloitte.dhr.utility.provider.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.deloitte.dhr.common.base.constants.OauthConstant;
import com.deloitte.dhr.common.base.pojo.UserDto;
import com.deloitte.dhr.common.base.utils.SpringUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * API服务
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 07/06/2022
 */
public class LoginInfoUtil {

    public static UserDto getLoginUserByToken() {
        String userString = getUserString();
        if (StringUtils.isEmpty(userString)) {
            return null;
        }
        UserDto userDTO = null;
        try {
            userDTO = JSON.parseObject(userString, UserDto.class);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return userDTO;
    }

    private static String getUserString() {
        HttpServletRequest httpServletRequest = SpringUtils.getHttpServletRequest();
        String userToken = httpServletRequest.getHeader(OauthConstant.AUTHORIZE_TOKEN);
        if (!StrUtil.isEmpty(userToken)) {
            userToken = userToken.split(" ")[1];
        } else {
            return null;
        }
        Map<String, Claim> claims = null;
        try {
            Algorithm algorithm = Algorithm.HMAC256(OauthConstant.OAUTH_SECRET);
            JWTVerifier verifier = JWT.require(algorithm).build();
            DecodedJWT jwt = verifier.verify(userToken);
            claims = jwt.getClaims();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        //取出用户身份信息
        String principal = claims.get(OauthConstant.OAUTH_USER).asString();
        return principal;
    }

}
