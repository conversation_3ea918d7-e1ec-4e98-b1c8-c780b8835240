package com.deloitte.dhr.utility.provider.utils;

import com.deloitte.dhr.utility.api.dto.file.FileRequestDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;


/**
 * 文件工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
@Slf4j
public class FileUtil {
    private static final String[] SUPPORT_IMAGE_TYPE = {"JPG", "JPEG", "PNG", "GIF", "BMP", "WBMP"};
    private static final List<String> SUPPORT_IMAGE_LIST = Arrays.asList(SUPPORT_IMAGE_TYPE);
    private static final String[] FILE_TYPE = {"JPG", "JPEG", "PNG", "GIF", "BMP", "WEBM", "TXT", "MD", "DOC",
            "DOCX", "PPT", "PPTX", "XLS", "XLSX", "PDF", "RAR", "ZIP", "CSV", "HTML", "MP3", "MP4", "WAV", "WMA",
            "MP2", "FLAC", "MIDI", "RA", "APE", "AAC", "CDA", "MOV", "MD"};
    private static final List<String> FILE_TYPE_LIST = Arrays.asList(FILE_TYPE);

    /**
     * 简单获取后缀名
     *
     * @param fileName 文件
     * @return 文件格式
     * @throws Exception
     */
    public static String getSuffix(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            //如果图片地址为null或者地址中没有"."就返回""
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    /**
     * 字节转kb/mb/gb
     *
     * @param size
     * @return
     */
    public static String getPrintSize(long size) {
        //如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
        if (size < 1024) {
            return size + "B";
        } else {
            size = size / 1024;
        }
        //如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
        //因为还没有到达要使用另一个单位的时候
        //接下去以此类推
        if (size < 1024) {
            return size + "KB";
        } else {
            size = size / 1024;
        }
        if (size < 1024) {
            //因为如果以MB为单位的话，要保留最后1位小数，
            //因此，把此数乘以100之后再取余
            size = size * 100;
            return size / 100 + "."
                    + size % 100 + "MB";
        } else {
            //否则如果要以GB为单位的，先除于1024再作同样的处理
            size = size * 100 / 1024;
            return size / 100 + "."
                    + size % 100 + "GB";
        }
    }

    /**
     * 是否是支持的文件
     *
     * @param fileExtName
     * @return
     */
    public static boolean isFileType(String fileExtName) {
        return FILE_TYPE_LIST.contains(fileExtName.toUpperCase());
    }

    /**
     * 是否是支持的图片文件
     *
     * @param fileExtName
     * @return
     */
    public static boolean isFileImage(String fileExtName) {
        return SUPPORT_IMAGE_LIST.contains(fileExtName.toUpperCase());
    }

    /**
     * 根据文件类型不同进行预览
     *
     * @param ext
     * @return
     */
    public static String getContentType(String ext) {
        /* 根据文件类型不同进行预览 */
        /* 预览图片 */
        String contType = "";
        if ("PNG".equals(ext) || "JPEG".equals(ext) || "JPG".equals(ext)) {
            contType = "image/jpeg";
            //预览BMP格式的文件
        } else if ("BMP".equals(ext)) {
            contType = "image/bmp";
            //预览pdf
        } else if ("PDF".equals(ext)) {
            contType = "application/pdf";
        } else if ("DOC".equals(ext) || "DOCX".equals(ext) || "XLS".equals(ext) || "XLSX".equals(ext) || "PPT".equals(ext) || "PPTX".equals(ext)) {
            contType = "application/octet-stream";
//            contType = "application/pdf";
        } else if ("HTML".equals(ext)) {
            contType = "text/html";
        }
        return contType;
    }

    /**
     * 校验上传文件是否合法
     *
     * @param fileRequestDtoList
     * @param errorFileNameList
     * @return
     */
    public static Boolean verifyFile(List<FileRequestDto> fileRequestDtoList, List<String> errorFileNameList) {
        if (CollectionUtils.isEmpty(fileRequestDtoList)) {
            return Boolean.FALSE;
        }
        for (FileRequestDto fileRequestDto : fileRequestDtoList) {
            // 文件名
            String fileName = fileRequestDto.getFileName();
            // 文件后缀
            String fileType = FileUtil.getSuffix(fileName);
            // 验证附件类型
            if (!isFileType(fileType)) {
                errorFileNameList.add(fileName);
            }
        }

        if (!CollectionUtils.isEmpty(errorFileNameList)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 下载文件设置response
     *
     * @param response
     * @param fileName
     */
    public static void setResponse(HttpServletResponse response, String fileName) {
        /* 获得文件名后缀 */
        String ext = "";
        if (!"".equals(fileName) && fileName.contains(".")) {
            ext = fileName.substring(fileName.lastIndexOf(".") + 1).toUpperCase();
        }
        //浏览器不要进行缓存
        response.reset();
        response.setDateHeader("Expires", -1);
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Pragma", "no-cache");
        //设置响应头，控制浏览器下载该文件，并设置文件名
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        String contentType = FileUtil.getContentType(ext);
        response.setContentType(contentType);

    }

    /**
     * 预览文件设置response
     *
     * @param response
     * @param fileName
     */
    public static void setPreviewResponse(HttpServletResponse response, String fileName) {
        /* 获得文件名后缀 */
        String ext = "";
        if (!"".equals(fileName) && fileName.contains(".")) {
            ext = fileName.substring(fileName.lastIndexOf(".") + 1).toUpperCase();
        }
        //浏览器不要进行缓存
        response.reset();
        response.setDateHeader("Expires", -1);
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Pragma", "no-cache");
        //设置响应头，控制浏览器下载该文件，并设置文件名
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "inline;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        String contentType = FileUtil.getContentType(ext);
        response.setContentType(contentType);
    }
}
