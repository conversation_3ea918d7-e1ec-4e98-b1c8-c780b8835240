package com.deloitte.dhr.utility.provider.service.impl;

import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.utility.api.dto.email.EmailParamDto;
import com.deloitte.dhr.utility.api.dto.file.Attachment;
import com.deloitte.dhr.utility.api.exception.ExceptionEnum;
import com.deloitte.dhr.utility.provider.service.CommonEmailService;
import com.deloitte.dhr.utility.provider.service.EmailSendLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 邮件服务接口实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
@Slf4j
@Service
public class CommonEmailServiceImpl implements CommonEmailService {

    @Value("${spring.mail.username}")
    private String from;

    @Value("${spring.mail.attachment-path}")
    private String attachBasePath;

    private static String MULTI_SPLIT_CHAR = ",";

    private final JavaMailSender mailSender;
    private final EmailSendLogService emailSendLogService;

    public CommonEmailServiceImpl(JavaMailSender mailSender, EmailSendLogService emailSendLogService) {
        this.mailSender = mailSender;
        this.emailSendLogService = emailSendLogService;
    }

    @Override
    public void sendEmail(EmailParamDto emailParamDto, MultipartFile[] files) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setFrom(from);
            if (StringUtils.isBlank(emailParamDto.getReceivers())) {
                throw new CommRunException(ExceptionEnum.EMAIL_RECIEVER_NONE.getCode(), ExceptionEnum.EMAIL_RECIEVER_NONE.getMessage());
            }
            helper.setTo(emailParamDto.getReceivers().split(MULTI_SPLIT_CHAR));
            if (!StringUtils.isEmpty(emailParamDto.getCopyTo())) {
                helper.setCc(emailParamDto.getCopyTo().split(MULTI_SPLIT_CHAR));
            }
            if (!StringUtils.isEmpty(emailParamDto.getBcc())) {
                helper.setBcc(emailParamDto.getBcc().split(MULTI_SPLIT_CHAR));
            }
            helper.setSubject(emailParamDto.getSubject());
            helper.setText(emailParamDto.getContent(), true);
            //判断附件是否为空
            List<Attachment> attachmentList = new ArrayList<>();
            if (files != null && files.length > 0) {
                //多附件处理
                for (MultipartFile file : files) {
                    try {
                        String storeFile = downloadAttachFile(file);
                        helper.addAttachment(MimeUtility.encodeText(Objects.requireNonNull(file.getOriginalFilename())), file);
                        Attachment po = new Attachment();
                        po.setFileName(file.getOriginalFilename());
                        String contentType = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
                        if (contentType.length() > 10) {
                            contentType = contentType.substring(contentType.length() - 10);
                        }
                        po.setSuffix(contentType);
                        po.setSize(file.getSize());
                        po.setFileAddress(storeFile);
                        attachmentList.add(po);
                    } catch (Exception e) {
                        log.error("邮件发送前，添加附件出现异常", e);
                        throw new CommRunException(ExceptionEnum.EMAIL_ATTACH_ADD_EX.getCode(), ExceptionEnum.EMAIL_ATTACH_ADD_EX.getMessage());
                    }
                }
            }
            // 发送邮件
            mailSender.send(message);
            // 保存邮件信息
            emailSendLogService.saveSendLog(emailParamDto, attachmentList);
        } catch (Exception e) {
            log.error("邮件发送异常", e);
            throw new CommRunException(ExceptionEnum.EMAIL_SEND_EX.getCode(), ExceptionEnum.EMAIL_SEND_EX.getMessage());
        }
    }

    /**
     * 保存文件
     *
     * @param file
     * @return
     */
    private String downloadAttachFile(MultipartFile file) {
        String oriFile = file.getOriginalFilename();
        int doxIndex = Objects.requireNonNull(oriFile).lastIndexOf(".");
        if (doxIndex < 0) {
            doxIndex = oriFile.length();
        }
        String filePreName = oriFile.substring(0, doxIndex);
        String suffix = oriFile.substring(doxIndex + 1);
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        String storeFileName = filePreName + "_" + uuid + "." + suffix;
        String storePathFile = attachBasePath + storeFileName;
        byte[] temp = new byte[1024];
        int len = -1;
        try (BufferedInputStream buf = new BufferedInputStream(file.getInputStream());) {
            try (BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(new File(storePathFile)));) {
                while ((len = buf.read(temp, 0, temp.length)) >= 0) {
                    bos.write(temp, 0, len);
                    bos.flush();
                }
            } catch (Exception e) {
                log.error(e.toString());
            }
        } catch (Exception e) {
            log.error("保存附件异常", e);
            throw new CommRunException(ExceptionEnum.EMAIL_ATTACH_SAVE_EX.getCode(), ExceptionEnum.EMAIL_ATTACH_SAVE_EX.getMessage());
        }
        return storePathFile;
    }
}
