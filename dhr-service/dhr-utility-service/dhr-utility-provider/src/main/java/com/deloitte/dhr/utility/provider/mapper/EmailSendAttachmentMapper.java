package com.deloitte.dhr.utility.provider.mapper;


import com.deloitte.dhr.utility.provider.entity.EmailSendAttachment;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
@org.apache.ibatis.annotations.Mapper
public interface EmailSendAttachmentMapper extends Mapper<EmailSendAttachment> {

    int insertBatch(@Param("list")List<EmailSendAttachment> list);
}