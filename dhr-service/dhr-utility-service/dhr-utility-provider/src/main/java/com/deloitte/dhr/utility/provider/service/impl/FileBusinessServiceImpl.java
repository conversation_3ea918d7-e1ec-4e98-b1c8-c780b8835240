package com.deloitte.dhr.utility.provider.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.utils.IdWorkerUtil;
import com.deloitte.dhr.utility.api.dto.file.FileInfoDto;
import com.deloitte.dhr.utility.api.dto.file.FileResponseDto;
import com.deloitte.dhr.utility.api.exception.ExceptionEnum;
import com.deloitte.dhr.utility.provider.entity.FileInfo;
import com.deloitte.dhr.utility.provider.entity.MinIOProperties;
import com.deloitte.dhr.utility.provider.mapper.FileInfoMapper;
import com.deloitte.dhr.utility.provider.service.FileBusinessService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 文件服务业务表实现接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
@Service
public class FileBusinessServiceImpl implements FileBusinessService {
    @Autowired
    private FileInfoMapper fileInfoMapper;
    @Value("${dhr.env.downloadUrl}")
    private String env;

    @Value("${dhr.env.previewUrl}")
    private String previewUrl;

    @Autowired
    private MinIOProperties minIOProperties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String getFileGroupId(List<FileInfoDto> fileInfoDtoList, String fileGroupId, String empCode) {
        //当文件组ID为空的时候，执行新增操作
        if (StringUtils.isBlank(fileGroupId)) {
            fileGroupId = UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
        }
        //当文件组ID不为空的时候，覆盖之前数据
        else {
            fileInfoMapper.deleteByFileGroupId(fileGroupId);
        }
        List<FileInfo> list = new ArrayList<>();
        for (FileInfoDto fileInfoDto : fileInfoDtoList) {
            FileInfo fileInfo = new FileInfo();
            BeanUtil.copyProperties(fileInfoDto, fileInfo);
            fileInfo.setId(IdWorkerUtil.getId());
            fileInfo.setFileGroupId(fileGroupId);
            fileInfo.setCreatedDate(new Date());
            fileInfo.setCreateUser(empCode);
            fileInfo.setUpdatedDate(new Date());
            fileInfo.setUpdateUser(empCode);
            list.add(fileInfo);
        }
        fileInfoMapper.insertBatch(list);
        return fileGroupId;
    }

    @Override
    public List<FileResponseDto> getFileGroup(String fileGroupId) {
        if (StrUtil.isEmpty(fileGroupId)) {
            throw new CommRunException(ExceptionEnum.FILE_GROUPID_IS_NOT_NULL.getCode(), ExceptionEnum.FILE_GROUPID_IS_NOT_NULL.getMessage());
        }

        List<FileInfo> fileInfos = fileInfoMapper.findByFileGroupId(fileGroupId);
        if (CollUtil.isEmpty(fileInfos)) {
            throw new CommRunException(ExceptionEnum.NOT_FOUND_FILE_GROUP.getCode(), ExceptionEnum.NOT_FOUND_FILE_GROUP.getMessage());
        }
        List<FileResponseDto> dtoList = new ArrayList<>();
        fileInfos.forEach(fileInfo -> {
            FileResponseDto dto = new FileResponseDto();
            BeanUtil.copyProperties(fileInfo, dto);
            dto.setName(fileInfo.getAttachName());
            dto.setId(fileInfo.getId().toString());
            dto.setUrl(fileInfo.getKey());
            dto.setType(fileInfo.getAttachType());
            dto.setSize(fileInfo.getFileSize());
            dtoList.add(dto);
        });
        return dtoList;
    }

    @Override
    public FileResponseDto findById(String id) {
        List<FileInfo> fileInfos = this.fileInfoMapper.findById(Long.parseLong(id));
        if (fileInfos != null && fileInfos.size() > 0) {
            Optional<FileResponseDto> responseDto = fileInfos.stream().findFirst().map(this::buildDto);
            return responseDto.orElseGet(responseDto::get);
        }
        return null;
    }

    @Override
    public List<FileResponseDto> findByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        List<FileInfo> fileInfoList = fileInfoMapper.findByIds(ids);
        return fileInfoList.stream().map(this::buildDto).collect(Collectors.toList());
    }

    @Override
    public Integer deleteById(String id) {
        FileInfo fileInfo = new FileInfo();
        fileInfo.setId(Long.parseLong(id));
        return fileInfoMapper.delete(fileInfo);
    }

    private String getDateString(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(date);
    }

    /**
     * 返回dto
     *
     * @param fileInfo
     * @return
     */
    private FileResponseDto buildDto(FileInfo fileInfo) {
        FileResponseDto dto = new FileResponseDto();
        BeanUtil.copyProperties(fileInfo, dto);
        dto.setName(fileInfo.getAttachName());
        dto.setId(fileInfo.getId().toString());
        dto.setUrl(fileInfo.getKey());
        dto.setType(fileInfo.getAttachType());
        dto.setSize(fileInfo.getFileSize());
        dto.setCreateDate(getDateString(fileInfo.getCreatedDate()));
        dto.setDownloadUrl(fileInfo.getKey());
        dto.setPreviewUrl(env + "?fileId=" + fileInfo.getId());
        if (fileInfo.getKey() != null && (fileInfo.getKey().endsWith(".doc") || fileInfo.getKey().endsWith(".docx"))) {
            dto.setPreviewUrl(previewUrl + fileInfo.getId());
        }
        return dto;
    }
}
