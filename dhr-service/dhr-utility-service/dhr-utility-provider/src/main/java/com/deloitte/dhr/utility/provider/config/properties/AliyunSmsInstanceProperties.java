package com.deloitte.dhr.utility.provider.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.io.Serializable;

/**
 * TODO 类注释
 *
 * <AUTHOR>
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = AliyunSmsInstanceProperties.PREFIX)
public class AliyunSmsInstanceProperties implements Serializable {

    private static final long serialVersionUID = 3393629593384505741L;

    public static final String PREFIX = "deloitte.sms.aliyun";

    public static final String REGION_ID = "cn-hangzhou";

    public static final String DOMAIN = "dysmsapi.aliyuncs.com";

    public static final String VERSION = "2017-05-25";

    private String accessKeyId;

    private String accessSecret;

    private String signName;

}
