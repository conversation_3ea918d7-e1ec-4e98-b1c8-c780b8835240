package com.deloitte.dhr.utility.provider.service;

import com.deloitte.dhr.utility.api.dto.email.EmailParamDto;
import org.springframework.web.multipart.MultipartFile;

/**
 * 邮件服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
public interface CommonEmailService {

    /**
     * 发送邮件
     *
     * @param emailParamDto
     * @param files
     * @return
     */
    void sendEmail(EmailParamDto emailParamDto, MultipartFile[] files);
}
