package com.deloitte.dhr.utility.provider.service;

import com.deloitte.dhr.utility.api.dto.file.FileRequestDto;
import com.deloitte.dhr.utility.api.dto.file.FileResponseDto;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 文件服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
public interface CommonFileDfsService {

    /**
     * 上传文件（内部调用）
     *
     * @param fileRequestDto
     * @return
     */
    FileResponseDto uploadFile(FileRequestDto fileRequestDto);

    /**
     * 上传文件（外部调用）
     *
     * @param uploadFile
     * @param token
     * @return
     */
    FileResponseDto uploadFile(MultipartFile uploadFile, String token);

    /**
     * 删除文件-通过url
     *
     * @param url
     * @return
     */
    Boolean deleteFileByUrl(String url);

    /**
     * 下载文件（外部调用）-通过url
     *
     * @param url
     * @param fileName
     * @param response
     */
    void downloadFileByUrl(String url, String fileName, HttpServletResponse response);

    /**
     * 下载文件（内部调用）
     *
     * @param url
     * @return
     */
    Map<String, byte[]> downloadFileById(String url);

    /**
     * 批量上传文件（内部调用）
     *
     * @param fileRequestDtoList
     * @return
     */
    List<FileResponseDto> batchUploadFile(List<FileRequestDto> fileRequestDtoList);

    /**
     * 批量上传文件（外部调用）
     *
     * @param uploadFileList
     * @param token
     * @return
     */
    List<FileResponseDto> batchUploadFile(List<MultipartFile> uploadFileList, String token);

    /**
     * 下载文件(外部调用)-通过id
     *
     * @param id
     * @param response
     */
    void downloadFileById(String id, HttpServletResponse response);

    /**
     * 预览文件(外部调用)-通过id
     *
     * @param id
     * @param response
     */
    void previewFileById(String id, HttpServletResponse response);

    /**
     * 删除文件-通过id
     *
     * @param id
     * @return
     */
    Boolean deleteFileById(String id);

    /**
     * 预览文件
     *
     * @param id
     * @return
     */
    FileResponseDto previewFile(String id);

    /**
     * 批量下载文件
     *
     * @param fileIds
     * @param zipFileName
     * @param response
     */
    void batchDownloadToZip(List<Long> fileIds, String zipFileName, HttpServletResponse response);

    /**
     * 批量下载文件
     *
     * @param fileIds
     * @return
     */
    Map<String, byte[]> internalBatchDownload(List<Long> fileIds);
}
