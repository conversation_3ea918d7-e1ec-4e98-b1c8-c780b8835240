package com.deloitte.dhr.utility.provider.entity;


import com.deloitte.dhr.utility.provider.service.ShorterGetter;

/**
 * 返回短码和密码
 * <AUTHOR>
public class ShorterString implements ShorterGetter {
    private String shorter;

    public ShorterString() {
    }

    public ShorterString(String shorter) {
        setShorter(shorter);
    }

    @Override
    public String getShorter() {
        return shorter;
    }

    public void setShorter(String shorter) {
        this.shorter = shorter;
    }


}
