package com.deloitte.dhr.utility.provider.controller;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.utility.api.CommonEmailInterface;
import com.deloitte.dhr.utility.api.dto.email.EmailParamDto;
import com.deloitte.dhr.utility.api.exception.ExceptionEnum;
import com.deloitte.dhr.utility.provider.service.CommonEmailService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.http.entity.ContentType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 邮件发送相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
@Slf4j
@RestController
@CrossOrigin
@RequestMapping(value = "/utility/email")
@Api(value = "/com/deloitte/dhr/utility/email",tags = "邮件发送相关接口")
public class CommonEmailController implements CommonEmailInterface {

    private final CommonEmailService commonEmailService;

    public CommonEmailController(CommonEmailService commonEmailService) {
        this.commonEmailService = commonEmailService;
    }

    @Override
    @PostMapping(value = "/send")
    public ResponseVO<String> sendSimpleEmail(@RequestParam(value = "receivers") String receivers,
                                              @RequestParam(required = false, value = "copyTo") String copyTo,
                                              @RequestParam(required = false, value = "bcc") String bcc,
                                              @RequestParam(value = "subject") String subject,
                                              @RequestParam(value = "content") String content,
                                              @RequestPart(required = false, value = "attachments") MultipartFile[] attachments) {
        try {
            EmailParamDto emailParamDto = EmailParamDto.builder().build();
            emailParamDto.setReceivers(receivers);
            emailParamDto.setCopyTo(copyTo);
            emailParamDto.setSubject(subject);
            emailParamDto.setContent(content);
            emailParamDto.setBcc(bcc);
            commonEmailService.sendEmail(emailParamDto, attachments);
            return ResponseVO.success();
        } catch (Exception e) {
            log.error("发送邮件异常", e);
            throw new CommRunException(ExceptionEnum.EMAIL_SEND_ERR.getCode(), ExceptionEnum.EMAIL_SEND_ERR.getMessage());
        }
    }

    @Override
    @PostMapping(value = "/send/internal")
    public ResponseVO sendEmailInternal(@RequestBody EmailParamDto emailParamDto) {
        List<MultipartFile> multipartFiles = new ArrayList<>();
        if (MapUtils.isNotEmpty(emailParamDto.getFileMap())) {
            emailParamDto.getFileMap().forEach((fileName, fileByte) -> {
                MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, String.valueOf(ContentType.APPLICATION_OCTET_STREAM), fileByte);
                multipartFiles.add(multipartFile);
            });
        }
        MultipartFile[] files = new MultipartFile[multipartFiles.size()];
        commonEmailService.sendEmail(emailParamDto, multipartFiles.toArray(files));
        return ResponseVO.success();
    }

}
