package com.deloitte.dhr.utility.provider.controller;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.CommonMessageInterface;
import com.deloitte.dhr.utility.api.dto.sms.AliYunSmsResponseVo;
import com.deloitte.dhr.utility.api.dto.sms.AliyunSmsDto;
import com.deloitte.dhr.utility.provider.service.SmsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 发送短信相关服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
@Slf4j
@RestController
@CrossOrigin
@RequestMapping(value = "/utility/message")
@Api(value = "/com/deloitte/dhr/utility/message", tags = "短信服务")
public class CommonMessageController implements CommonMessageInterface {

    @Autowired
    private SmsService smsService;

    @Override
    @PostMapping(value = "/SendMessage")
    @ApiOperation(value = "发送短信")
    public ResponseVO sendMessage(@RequestBody AliyunSmsDto aliyunSmsDto) {
        AliYunSmsResponseVo vo = smsService.sendSms(aliyunSmsDto.getPhoneNumbers(), aliyunSmsDto.getTemplateCode(), aliyunSmsDto.getTemplateParam(), aliyunSmsDto.getSmsUpExtendCode(), aliyunSmsDto.getOutId());
        if (vo.requestSuccessful()) {
            return ResponseVO.successMessage(vo.getMessage());
        } else {
            return ResponseVO.fail(vo.getMessage());
        }
    }

    @Override
    @PostMapping(value = "/SendMessageGlobe")
    @ApiOperation(value = "发送国际短信")
    public ResponseVO sendMessageGlobe(@RequestBody AliyunSmsDto aliyunSmsDto) {
        AliYunSmsResponseVo vo = smsService.sendSmsToGlobe(aliyunSmsDto.getPhoneNumbers(), aliyunSmsDto.getTemplateParam(), aliyunSmsDto.getMessage());
        if (vo.requestSuccessful()) {
            return ResponseVO.successMessage(vo.getMessage());
        } else {
            return ResponseVO.fail(vo.getMessage());
        }
    }
}
