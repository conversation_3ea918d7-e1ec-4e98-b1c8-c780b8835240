package com.deloitte.dhr.utility.provider.controller.poc;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.utility.api.dto.file.FileItemInfoVo;
import com.deloitte.dhr.utility.api.dto.file.FileResponseDto;
import com.deloitte.dhr.utility.provider.service.CommonFileDfsService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/upload/ufs")
public class FileUploadUFSController {


    @Autowired
    private CommonFileDfsService commonFileDfsService;

    @Autowired
    private static final String STATUS_SUCCESS = "success";
    private static final String STATUS_ERROR = "error";
    private static final String STR_FULL_TIME = "yyyy-MM-dd HH:mm:ss";
    private static final String[] FILE_TYPE = { "JPG", "JPEG", "PNG", "GIF", "BMP", "WBMP", "TXT", "DOC",
            "DOCX", "PPT", "PPTX", "XLS", "XLSX", "PDF", "RAR", "ZIP", "MP3", "MPEG", "WMA", "WAV" };
    private static final List<String> FILE_TYPE_LIST = Arrays.asList(FILE_TYPE);
    /** 支持的图片类型 */
    private static final String[] SUPPORT_IMAGE_TYPE = { "JPG", "JPEG", "PNG", "GIF", "BMP", "WBMP" };
    private static final List<String> SUPPORT_IMAGE_LIST = Arrays.asList(SUPPORT_IMAGE_TYPE);

    private static final String[] FILE_TYPE_OFFICE = {"TEXT","TXT", "DOC","DOCX", "PPT", "PPTX", "XLS", "XLSX","PDF"};
    private static final List<String> FILE_TYPE_LIST_OFFICE = Arrays.asList(FILE_TYPE_OFFICE);

    /** 支持的音频文件 */
    private static final String[] FILE_TYPE_VIDEO = { "MP3", "MPEG", "WMA", "WAV" };
    private static final List<String> FILE_TYPE_LIST_VIDEO = Arrays.asList(FILE_TYPE_VIDEO);

    /** 日志 */
    protected static Logger logger = LoggerFactory.getLogger(FileUploadUFSController.class);

	@Value("${spring.profiles.active}")
	private String profiles;

    /**
	 * 上传附件
	 *
	 * @param file
	 * @return
	 */
	@PostMapping(value = "/uploadFile2")
	public FileUploadVO uploadFile(MultipartFile file) {
		return uploadFile(file, null);
	}
    @RequestMapping(value = "/uploadFile")
    public FileUploadVO uploadFile(MultipartFile file,MultipartFile m) {
        logger.info("----------uploadFile------");
        SimpleDateFormat df = new SimpleDateFormat(STR_FULL_TIME);// 设置日期格式
        FileUploadVO uploadVO = new FileUploadVO();
        uploadVO.setCreatedate(df.format(new Date()));
        try {
        	if(file==null && m!=null) {
        		file=m;
        	}
            if (!file.isEmpty()) {
                String fileName = file.getOriginalFilename();
                String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
                if (!this.isFileType(fileType)) {
                    uploadVO.setStatus(STATUS_ERROR);
                    uploadVO.setMsg("上传附件类型不合法！");
                    return uploadVO;
                }
                if (isFileImage(fileType)) {
                    BufferedImage image = ImageIO.read(file.getInputStream());
                    String width = String.valueOf(image.getWidth());
                    String height = String.valueOf(image.getHeight());
                    uploadVO.setFormatSize(width + "*" + height);
                }

                uploadVO.setName(file.getOriginalFilename());
                uploadVO.setSize(String.valueOf(file.getSize()));
                uploadVO.setType(fileType);
                FileResponseDto fileResponseDto = commonFileDfsService.uploadFile(file, "");
                uploadVO.setId(fileResponseDto.getId());
                uploadVO.setDownloadurl(fileResponseDto.getUrl());
                uploadVO.setPreviewurl(fileResponseDto.getUrl());
                uploadVO.setStatus(STATUS_SUCCESS);
                uploadVO.setMsg("上传成功");
            } else {
                uploadVO.setStatus(STATUS_ERROR);
                uploadVO.setMsg("不能上传空附件");
                return uploadVO;
            }
        } catch (Exception e) {
            logger.error("uploadFile--异常：{}", e);
            uploadVO.setStatus(STATUS_ERROR);
            uploadVO.setMsg("上传失败:" + e.getMessage());
            e.printStackTrace();
            return uploadVO;
        }
        return uploadVO;
    }
    /**@version 1.0
     * @date 2020-02-07
     * @Describe：批量上传附件
     */
    @RequestMapping("/batchUploadFile")
    @ApiOperation(value = "批量上传附件")
    public List uploadFiled(List<MultipartFile> file) {
        logger.info("----------batchUploadFile------"+ file.toString());
        SimpleDateFormat df = new SimpleDateFormat(STR_FULL_TIME);// 设置日期格式
        FileUploadVO uploadVO = new FileUploadVO();
        List resultList = new ArrayList();
        try {
            if (!file.isEmpty()) {
                for (int i = 0; i< file.size(); i++){
                    uploadVO = new FileUploadVO();
                    uploadVO.setCreatedate(df.format(new Date()));
                    MultipartFile m = file.get(i);
                    String fileName = m.getOriginalFilename();
                    String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
                    //解决IE下有时候文件名包含路径
                    String temp[] = fileName.split("\\\\");
                    if(temp.length > 0) {
                    	fileName = temp[temp.length-1];
                    }

                    if (!this.isFileType(fileType)) {
                        uploadVO.setStatus(STATUS_ERROR);
                        uploadVO.setMsg("上传附件类型不合法！");
                        resultList.add(uploadVO);
                        break;
                    }
                    if (isFileImage(fileType)) {
                        BufferedImage image = ImageIO.read(m.getInputStream());
                        String width = String.valueOf(image.getWidth());
                        String height = String.valueOf(image.getHeight());
                        uploadVO.setFormatSize(width + "*" + height);
                    }
                    uploadVO.setName(fileName);
                    uploadVO.setSize(String.valueOf(m.getSize()));
                    uploadVO.setType(fileType);

                    FileResponseDto fileResponseDto = commonFileDfsService.uploadFile(m, "");
                    uploadVO.setId(fileResponseDto.getId());
                    uploadVO.setDownloadurl(fileResponseDto.getUrl());
                    uploadVO.setPreviewurl(fileResponseDto.getUrl());
                    uploadVO.setStatus(STATUS_SUCCESS);
                    uploadVO.setMsg("上传成功");

                    resultList.add(uploadVO);
                }
            } else {
                uploadVO.setStatus(STATUS_ERROR);
                uploadVO.setMsg("不能上传空附件");
                resultList.add(uploadVO);
            }
        } catch (Exception e) {
    		logger.info("上传失败，异常={}", e);
            uploadVO.setStatus(STATUS_ERROR);
            uploadVO.setMsg("上传失败异常:" + e.getMessage());
            resultList.add(uploadVO);
        }
        return resultList;
    }

    /**
     * 下载附件返回数据流方式
     * @param fileId
     * @param response
     * @throws Exception
     */
    @RequestMapping(value = "/download")
    public void download(@RequestParam(value = "fileId") String fileId , @RequestParam(value = "fileName")String fileName, HttpServletResponse response) throws Exception{
        logger.info("usf api download ------: {}, {}",fileId,fileName);
        if(StringUtils.isNotEmpty(fileId) && StringUtils.isNotEmpty(fileName) && !"undefined".equals(fileId)){
//            ufsutils.downloadFile(fileId,fileName, response);
            commonFileDfsService.downloadFileById(fileId,response);
            logger.info("usf api download------下载附件完成");
        }
    }
    /**
             * 下载附件直接返回返回URL
     * @param fileId
     * @throws Exception
     */
    @RequestMapping(value = "/getDownloadUrl")
    public FileUploadVO getDownloadUrl(String fileId) throws Exception{
        logger.info("getDownloadUrl------: {}",fileId);
        FileResponseDto fileResponseDto = commonFileDfsService.previewFile(fileId);

    	FileUploadVO vo = new FileUploadVO();
    	if(fileResponseDto != null) {
    	   vo.setDownloadurl(fileResponseDto.getUrl());
    	   vo.setPreviewurl(fileResponseDto.getUrl());
    	}else {
		   vo.setStatus(STATUS_ERROR);
           vo.setMsg("附件ID不存在");
           return vo;
    	}
    	vo.setStatus(STATUS_SUCCESS);
		return vo;
    }
    /**
	     * 下载附件直接返回返回URL
	* @param jsonStr
	* @throws Exception
	*/
	@RequestMapping(value = "/getDownloadUrlList")
	public String getDownloadUrlList(@RequestBody String jsonStr) throws Exception{
		logger.info("查询附件输入参数： "+jsonStr);
		if(null != jsonStr) {
			JSONObject jsonObj =  JSONObject.parseObject(jsonStr);
			List<FileItemInfoVo> lists = new ArrayList<FileItemInfoVo>();
			JSONArray jsonArray = jsonObj.getJSONArray("FILEIDS");
			for(Object obj :jsonArray){
				   JSONObject jsonObject = ((JSONObject)obj);
				   String fileid = jsonObject.getString("FILEID");
				    FileItemInfoVo bean = new FileItemInfoVo();
                FileResponseDto fileResponseDto = commonFileDfsService.previewFile(fileid);
                if(fileResponseDto != null) {
				    	bean.setFileId(fileid);
					    bean.setFileUrl(fileResponseDto.getUrl());
					    bean.setStatus(STATUS_SUCCESS);
					    bean.setMsg("成功");
				    }else {
				    	bean.setStatus(STATUS_ERROR);
				    	bean.setMsg("附件ID不存在");
				    }
				    lists.add(bean);
            }
			return JSON.toJSON(lists).toString();
		}
		return jsonStr;
	}
    /**
     * 预览
     * @param fileId
     * @param fileName
     * @throws Exception
     */
    @RequestMapping(value = "/previewFile")
    public FileUploadVO previewFile(@RequestParam String fileId,@RequestParam String fileName) throws Exception{
        logger.info("previewFile------: {} ,{}",fileId,fileName);
        FileUploadVO uploadVO = new FileUploadVO();
        SimpleDateFormat df = new SimpleDateFormat(STR_FULL_TIME);// 设置日期格式
        uploadVO.setCreatedate(df.format(new Date()));
        if(StringUtils.isEmpty(fileId) || StringUtils.isEmpty(fileName) || "undefined".equals(fileId)){
            uploadVO.setStatus(STATUS_ERROR);
            uploadVO.setMsg("非法的文件参数！");
            return uploadVO;
        }

        FileResponseDto fileResponseDto = commonFileDfsService.previewFile(fileId);

        if(StringUtils.isNotEmpty(fileResponseDto.getPreviewUrl())){
            uploadVO.setPreviewurl(fileResponseDto.getPreviewUrl());
        }else {
            uploadVO.setPreviewurl(fileResponseDto.getUrl());
        }

    	uploadVO.setStatus(STATUS_SUCCESS);
    	return uploadVO;
    }


    /**
     * 加水印预览
     * @param fileId
     * @param fileName
     * @throws Exception
     */
    @RequestMapping(value = "/previewFileWatermark")
    public FileUploadVO previewFile(@RequestParam String fileId,@RequestParam String fileName,@RequestParam String watermark) throws Exception{
        logger.info("previewFileWatermark------: {} ,{}",fileId,fileName);
        FileUploadVO uploadVO = new FileUploadVO();
        SimpleDateFormat df = new SimpleDateFormat(STR_FULL_TIME);// 设置日期格式
        uploadVO.setCreatedate(df.format(new Date()));
        if(StringUtils.isEmpty(fileId) || StringUtils.isEmpty(fileName) || "undefined".equals(fileId)){
            uploadVO.setStatus(STATUS_ERROR);
            uploadVO.setMsg("非法的文件参数！");
            return uploadVO;
        }

        FileResponseDto fileResponseDto = commonFileDfsService.previewFile(fileId);

        if(StringUtils.isNotEmpty(fileResponseDto.getPreviewUrl())){
            uploadVO.setPreviewurl(fileResponseDto.getPreviewUrl());
        }else {
            uploadVO.setPreviewurl(fileResponseDto.getUrl());
        }

        uploadVO.setStatus(STATUS_SUCCESS);
        return uploadVO;
    }

    /**
     *  上传附件
     *
     * @param map
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/uploadOffer")
    public FileUploadVO uploadOffer(@RequestBody Map<String,byte[]> map) throws IOException {
    	logger.info("uploadOffer------: ,{}",map.toString());
    	byte[] testFile = map.get("fileByte");
    	InputStream inputStream = new ByteArrayInputStream(testFile);
    	MultipartFile m = new MockMultipartFile(new String(map.get("fileName")),new String(map.get("fileName")),ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);//带后缀的fileName
        SimpleDateFormat df = new SimpleDateFormat(STR_FULL_TIME);// 设置日期格式
        FileUploadVO uploadVO = new FileUploadVO();
        uploadVO.setCreatedate(df.format(new Date()));
        try {
            if (!m.isEmpty()) {
                String fileName = m.getName();
                String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
                if (!this.isFileType(fileType)) {
                    uploadVO.setStatus(STATUS_ERROR);
                    uploadVO.setMsg("上传附件类型不合法！");
                    return uploadVO;
                }
                if (isFileImage(fileType)) {
                    BufferedImage image = ImageIO.read(m.getInputStream());
                    String width = String.valueOf(image.getWidth());
                    String height = String.valueOf(image.getHeight());
                    uploadVO.setFormatSize(width + "*" + height);
                }

                uploadVO.setName(m.getName());
                uploadVO.setSize(String.valueOf(m.getSize()));
                uploadVO.setType(fileType);


                FileResponseDto fileResponseDto = commonFileDfsService.uploadFile(m, "");
                uploadVO.setId(fileResponseDto.getId());
                uploadVO.setDownloadurl(fileResponseDto.getUrl());
                uploadVO.setPreviewurl(fileResponseDto.getUrl());
                uploadVO.setStatus(STATUS_SUCCESS);
                uploadVO.setMsg("上传成功");


            } else {
                uploadVO.setStatus(STATUS_ERROR);
                uploadVO.setMsg("不能上传空附件");
                return uploadVO;
            }
        } catch (Exception e) {
            //log.error("--异常：{}", e);
            uploadVO.setStatus(STATUS_ERROR);
            uploadVO.setMsg("上传失败:" + e.getMessage());
            e.printStackTrace();
            return uploadVO;
        }
        return uploadVO;
    }

    /**
     *  上传附件证明
     * @param map
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/uploadProve")
    public FileUploadVO uploadProve(@RequestBody Map<String,byte[]> map) throws IOException {
        logger.info("-----------uploadProve------");
        byte[] testFile = map.get("fileByte");
        InputStream inputStream = new ByteArrayInputStream(testFile);
        //带后缀的fileName
        MultipartFile m = new MockMultipartFile(new String(map.get("fileName")),new String(map.get("fileName")),
                ContentType.APPLICATION_OCTET_STREAM.toString(),inputStream);
        SimpleDateFormat df = new SimpleDateFormat(STR_FULL_TIME);// 设置日期格式
        FileUploadVO uploadVO = new FileUploadVO();
        uploadVO.setCreatedate(df.format(new Date()));
        try {
            if (!m.isEmpty()) {
                String fileName = m.getName();
                String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
                if (!this.isFileType(fileType)) {
                    uploadVO.setStatus(STATUS_ERROR);
                    uploadVO.setMsg("上传附件类型不合法！");
                    return uploadVO;
                }
                if (isFileImage(fileType)) {
                    BufferedImage image = ImageIO.read(m.getInputStream());
                    String width = String.valueOf(image.getWidth());
                    String height = String.valueOf(image.getHeight());
                    uploadVO.setFormatSize(width + "*" + height);
                }

                uploadVO.setName(m.getName());
                uploadVO.setSize(String.valueOf(m.getSize()));
                uploadVO.setType(fileType);

                FileResponseDto fileResponseDto = commonFileDfsService.uploadFile(m, "");
                uploadVO.setId(fileResponseDto.getId());
                uploadVO.setDownloadurl(fileResponseDto.getUrl());
                uploadVO.setPreviewurl(fileResponseDto.getUrl());
                uploadVO.setStatus(STATUS_SUCCESS);
                uploadVO.setMsg("上传成功");

            } else {
                uploadVO.setStatus(STATUS_ERROR);
                uploadVO.setMsg("不能上传空附件");
                return uploadVO;
            }
        } catch (Exception e) {
            uploadVO.setStatus(STATUS_ERROR);
            uploadVO.setMsg("上传失败:" + e.getMessage());
            e.printStackTrace();
            return uploadVO;
        }
        return uploadVO;
    }

    /**
     * 删除服务器上的文件
     * @param fileId
     * @throws Exception
     */
    @RequestMapping(value = "/deleteFile")
    public void deleteFile(@RequestParam(value = "fileId") String fileId) throws Exception{
        logger.info("deleteFile------: ,{}",fileId);
    	   if(StringUtils.isNotBlank(fileId) && "undefined" != fileId) {
//    		   ufsutils.deleteFile(fileId);
               logger.info("文件ID:"+fileId+" 删除完成");
    	   }
    }
    /**
     * 是否是支持的文件
     *
     * @param fileExtName
     * @return
     */
    private boolean isFileType(String fileExtName) {
        return FILE_TYPE_LIST.contains(fileExtName.toUpperCase());
    }
    /**
     * 是否是支持的图片文件
     *
     * @param fileExtName
     * @return
     */
    private boolean isFileImage(String fileExtName) {
        return SUPPORT_IMAGE_LIST.contains(fileExtName.toUpperCase());
    }
    /**
     * 是否office 文件
     *
     * @param fileExtName
     * @return
     */
    private boolean isFileOffice(String fileExtName) {
        return FILE_TYPE_LIST_OFFICE.contains(fileExtName.toUpperCase());
    }

    /**
     * 是否音频 文件
     *
     * @param fileExtName
     * @return
     */
    private boolean isFileVideo(String fileExtName) {
        return FILE_TYPE_LIST_VIDEO.contains(fileExtName.toUpperCase());
    }


    public static byte[] fileTobyte(String filePath)
    {
        byte[] buffer = null;
        try
        {
            File file = new File(filePath);
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] b = new byte[1024];
            int n;
            while ((n = fis.read(b)) != -1)
            {
                bos.write(b, 0, n);
            }
            fis.close();
            bos.close();
            buffer = bos.toByteArray();
        }
        catch (FileNotFoundException e)
        {
            e.printStackTrace();
        }
        catch (IOException e)
        {
            e.printStackTrace();
        }
        return buffer;
    }

    /**
     * 下载附件返回数据流方式
     * @param json
     * @throws Exception
     */
    @PostMapping("/downloadEmailFile")
    public Map<String,byte[]> downloadEmailFile(@RequestBody JSONObject json, HttpServletResponse response) throws Exception{
        logger.info("downloadEmailFile------: ,{}",json.toJSONString());
//        Map<String,byte[]> map = ufsutils.downloadEmailFile(json.getString("fileId"),json.getString("fileName"));
        commonFileDfsService.downloadFileById(json.getString("fileId"),response);
        logger.info("下载附件完成");
        return null;
    }


    /**
     * 添加ufs分享文件
     * @param fileId
     * @return
     */
    @GetMapping(value = "/addShare")
    public FileUploadVO addShare(@RequestParam(value = "fileId") String fileId){
        logger.info("addShare------fileId: {}",fileId);
//        ShareResource shareInfo = null;
        FileUploadVO vo = new FileUploadVO();
//        try {
//            shareInfo = ufsutils.addShare(fileId,"APP");
//
//            if(shareInfo.getFileId() != null) {
//                vo.setId(shareInfo.getFileId());
//                logger.info("addShare success------:[fileId: {},shareId: {}]",fileId,shareInfo.getId());
//                vo.setStatus(STATUS_SUCCESS);
//                vo.setMsg(CommonConstant.SHARE_SUCCESS_TXT);
//            }else {
//                vo.setStatus(STATUS_ERROR);
//                vo.setMsg(CommonConstant.SHARE_ERROR_TXT);
//                return vo;
//            }
//        } catch (Exception e) {
//            logger.info("addShare error------:[fileId: {},shareId: {}]",fileId,e.getMessage());
//            vo.setStatus(STATUS_ERROR);
//            vo.setMsg(CommonConstant.SHARE_ERROR_TXT + e.getMessage());
//            return vo;
//        }

        return vo;
    }


    /**
     * 根据附件ID下载附件返回数据流
     * @param json
     * @throws Exception
     */
    @PostMapping("/getFileByteByFileId")
    public Map<String,byte[]> getFileByteByFileId(@RequestBody JSONObject json, HttpServletResponse response) throws Exception{
        logger.info("getFileByteByFileId------: ,{}",json.toJSONString());
//        Map<String,byte[]> map = ufsutils.getFileByteByFileId(json.getString("fileId"),json.getString("fileName"));
        commonFileDfsService.downloadFileById(json.getString("fileId"),response);
        logger.info("下载附件返回数据流");
        return null;
    }


}
