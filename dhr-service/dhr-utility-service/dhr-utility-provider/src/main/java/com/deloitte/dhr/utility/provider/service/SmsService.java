package com.deloitte.dhr.utility.provider.service;

import com.deloitte.dhr.utility.api.dto.sms.AliYunSmsResponseVo;

/**
 * SMS短信相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
public interface SmsService {
    /**
     *短信发送
     * @param phoneNumbers
     * @param templateCode
     * @param templateParam
     * @param smsUpExtendCode
     * @param outId
     * @return
     */
    AliYunSmsResponseVo sendSms(String phoneNumbers, String templateCode,
                                String templateParam, String smsUpExtendCode, String outId);

    /**
     * 国际短信
     * @param phoneNumbers
     * @param templateParam
     * @param message
     * @return
     */
    AliYunSmsResponseVo sendSmsToGlobe(String phoneNumbers,String templateParam,String message);
}
