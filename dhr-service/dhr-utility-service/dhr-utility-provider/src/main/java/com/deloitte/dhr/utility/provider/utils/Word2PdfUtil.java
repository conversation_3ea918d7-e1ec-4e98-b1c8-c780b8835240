package com.deloitte.dhr.utility.provider.utils;

import com.aspose.words.Document;
import com.aspose.words.License;
import com.aspose.words.SaveFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.poi.xwpf.converter.core.utils.StringUtils;
import org.apache.poi.xwpf.converter.pdf.PdfConverter;
import org.apache.poi.xwpf.converter.pdf.PdfOptions;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.core.io.ClassPathResource;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.Map;

@Slf4j
public class Word2PdfUtil {

    private static final String PDF_HOME = File.separator + "home" + File.separator + "dhr" + File.separator + "PdfHome" + File.separator;

    public static void wordToPdf(String fileName, byte[] fileByte, HttpServletResponse response) throws Exception {
        OutputStream os = response.getOutputStream();
        try {
            //凭证 不然切换后有水印
            InputStream is = new ClassPathResource("/license.xml").getInputStream();
            License aposeLic = new License();
            aposeLic.setLicense(is);

            byteToFile(fileByte, PDF_HOME + fileName);
            log.info(" word文件获取成功 ");
            //要转换的word文件 InputStream isDoc=new FileInputStream(PDF_HOME + fileName);
            Document doc = new Document(PDF_HOME + fileName);
            log.info(" word转换加载成功 ");
            doc.save(os, SaveFormat.PDF);
            log.info(" word转换pdf成功 ");
        } catch (Exception e) {
            log.error(e.toString());
            e.printStackTrace();
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * @param fileByte byte文件数组
     * @param filePath 文件存放目录及文件名，包括文件名及其后缀
     * @Title: byteToFile
     */
    public static void byteToFile(byte[] fileByte, String filePath) {
        BufferedInputStream bis = null;
        FileOutputStream fos = null;
        BufferedOutputStream output = null;
        try {
            ByteArrayInputStream byteInputStream = new ByteArrayInputStream(fileByte);
            bis = new BufferedInputStream(byteInputStream);
            File file = new File(filePath);
            // 获取文件的父路径字符串
            File path = file.getParentFile();
            if (!path.exists()) {

                boolean isCreated = path.mkdirs();
                if (!isCreated) {
                    log.error("==========> create Fail");
                }
            }
            fos = new FileOutputStream(file);
            // 实例化OutputString 对象
            output = new BufferedOutputStream(fos);
            byte[] buffer = new byte[1024];
            int length = bis.read(buffer);
            while (length != -1) {
                output.write(buffer, 0, length);
                length = bis.read(buffer);
            }
            output.flush();
        } catch (Exception e) {
            log.error("==========> create Fail");
        } finally {
            try {
                bis.close();
                fos.close();
                output.close();
            } catch (IOException e0) {
                log.error("==========> create Fail");
            }
        }
    }

    /**
     * 将word文档， 转换成pdf, 中间替换掉变量
     *
     * @param source  源为word文档， 必须为docx文档
     * @param target  目标输出
     * @param params  需要替换的变量
     * @param options PdfOptions.create().fontEncoding( "windows-1250" ) 或者其他
     * @return 已填充的word文档output对象
     * @throws Exception
     */
    public static ByteArrayOutputStream wordConverterToPdf(InputStream source, OutputStream target,
                                                           PdfOptions options,
                                                           Map<String, String> params) throws Exception {
        XWPFDocument doc = new XWPFDocument(source);
        paragraphReplace(doc.getParagraphs(), params);
        for (XWPFTable table : doc.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    paragraphReplace(cell.getParagraphs(), params);
                }
            }
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        doc.write(out);
        PdfConverter.getInstance().convert(doc, target, options);
        return out;
    }


    /**
     * 替换段落中内容
     */
    private static void paragraphReplace(List<XWPFParagraph> paragraphs, Map<String, String> params) {
        if (MapUtils.isNotEmpty(params)) {
            for (XWPFParagraph p : paragraphs) {
                for (XWPFRun r : p.getRuns()) {
                    String content = r.getText(r.getTextPosition());
                    if (org.apache.commons.lang3.StringUtils.equals("$", content)) {
                        r.setText("", 0);
                    }
                    if (StringUtils.isNotEmpty(content) && params.containsKey(content)) {
                        r.setText(params.get(content), 0);
                    }
                }
            }
        }
    }

}