package com.deloitte.dhr.utility.provider.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.csource.fastdfs.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * FastDFS 连接池
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
@Slf4j
@Component
@Data
public class FastDFSConnectionPool {
    /**
     * 文件访问地址
     */
    @Value("${file.access.server}")
    private String accessServer;

    /**
     * 文件访问端口
     */
    @Value("${file.access.port}")
    private Integer accessPort;

    /**
     * 最大连接数
     */
    @Value("${file.pool.size}")
    private Integer poolSize;

    /**
     * 链接超时时间
     */
    @Value("${file.connect.timeout}")
    private Integer connectTimeout;

    /**
     * 网络超时时间
     */
    @Value("${file.network.timeout}")
    private Integer networkTimeout;

    /**
     * 文件编码格式
     */
    @Value("${file.charset}")
    private String fileCharset;

    /**
     * 是否开启Token验证
     */
    @Value("${file.anti.steal.token}")
    private Boolean antiStealToken;

    /**
     * tracker地址
     */
    @Value("${file.tracker.server}")
    private String trackerServer;

    /**
     * 被使用的连接
     */
    private ConcurrentHashMap<StorageClient1, Object> busyConnectionPool = null;

    /**
     * 空闲的连接
     */
    private ArrayBlockingQueue<StorageClient1> idleConnectionPool = null;

    /**
     * 初始化连接池
     */
    @PostConstruct
    public void init() {
        //初始化 被使用的连接
        busyConnectionPool = new ConcurrentHashMap<>();
        //初始化 空闲的连接
        idleConnectionPool = new ArrayBlockingQueue<>(poolSize);
        log.info("-----------------------开始初始化连接池-----------------------");
        //初始化客户端
        initClientGlobal();
        //初始化链接池
        TrackerServer trackerServer = null;
        try {
            TrackerClient trackerClient = new TrackerClient();
            //只需要一个tracker server连接
            trackerServer = trackerClient.getConnection();
            StorageServer storageServer = null;
            StorageClient1 storageClient1 = null;
            for (int i = 0; i < poolSize; i++) {
                storageClient1 = new StorageClient1(trackerServer, storageServer);
                idleConnectionPool.add(storageClient1);
            }
            log.info("-----------------------初始化连接池成功-----------------------");
        } catch (IOException e) {
            log.error("初始化文件服务连接池失败,{}", e.getMessage());
        } finally {
            if (trackerServer != null) {
                try {
                    trackerServer.close();
                } catch (IOException e) {
                }
            }
        }
    }

    /**
     * 初始化客户端
     */
    private void initClientGlobal() {
        try {
            ClientGlobal.setG_tracker_http_port(accessPort);
            ClientGlobal.setG_connect_timeout(connectTimeout);
            ClientGlobal.setG_network_timeout(networkTimeout);
            ClientGlobal.setG_charset(fileCharset);
            ClientGlobal.setG_anti_steal_token(antiStealToken);
            InetSocketAddress[] trackerServers;
            if (trackerServer.contains(",")) {
                String[] trackerServerArray = trackerServer.split(",");
                trackerServers = new InetSocketAddress[trackerServerArray.length];
                for (int i = 0; i < trackerServerArray.length; i++) {
                    String array = trackerServerArray[i];
                    String server = array.split(":")[0];
                    String port = array.split(":")[1];
                    trackerServers[i] = new InetSocketAddress(server, Integer.parseInt(port));
                }
            } else {
                trackerServers = new InetSocketAddress[1];
                String server = trackerServer.split(":")[0];
                String port = trackerServer.split(":")[1];
                trackerServers[0] = new InetSocketAddress(server, Integer.parseInt(port));
            }
            ClientGlobal.setG_tracker_group(new TrackerGroup(trackerServers));
        } catch (Exception e) {
            log.error("初始化文件服务客户端失败,{}", e.getMessage());
        }
    }

    /**
     * 获取链接
     *
     * @return 链接
     */
    public StorageClient1 get() {
        return this.get(3);
    }

    private Object obj = new Object();

    public StorageClient1 get(int waitTime) {
        StorageClient1 storageClient1 = null;
        try {
            storageClient1 = idleConnectionPool.poll(waitTime, TimeUnit.SECONDS);
            if (storageClient1 != null) {
                busyConnectionPool.put(storageClient1, obj);
            }
        } catch (Exception e) {
            storageClient1 = null;
            log.error("获取连接失败,{}", e.getMessage());
        }
        return storageClient1;
    }

    /**
     * 回收连接
     *
     * @param storageClient1 链接
     */
    public void put(StorageClient1 storageClient1) {
        if (busyConnectionPool.remove(storageClient1) != null) {
            idleConnectionPool.add(storageClient1);
        }
    }

    /**
     * 如果连接无效则抛弃，新建连接来补充到池里
     *
     * @param storageClient1
     */
    public void drop(StorageClient1 storageClient1) {
        if (busyConnectionPool.remove(storageClient1) != null) {
            TrackerServer trackerServer = null;
            TrackerClient trackerClient = new TrackerClient();
            try {
                trackerServer = trackerClient.getConnection();
                StorageClient1 newStorageClient1 = new StorageClient1(trackerServer, null);
                idleConnectionPool.add(newStorageClient1);
            } catch (IOException e) {
                log.error(e.toString());
            } finally {
                if (trackerServer != null) {
                    try {
                        trackerServer.close();
                    } catch (IOException e) {
                        log.error(e.toString());
                    }
                }
            }
        }
    }
}
