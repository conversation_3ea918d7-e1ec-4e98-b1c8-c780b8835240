package com.deloitte.dhr.utility.provider.service.impl;

import cn.hutool.core.util.URLUtil;
import com.deloitte.dhr.utility.provider.entity.MinIOProperties;
import com.deloitte.dhr.utility.provider.service.MinioService;
import io.minio.MinioClient;
import io.minio.PutObjectOptions;
import io.minio.errors.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

@Service
public class MinIOServiceImpl implements MinioService {

    private final MinioClient minioClient;

    private final MinIOProperties minIOProperties;

    private static final String FORWARD_SLASH="/";

    @Value("${file-view-url}")
    private String minioNginxUrl;


    public MinIOServiceImpl(MinioClient minioClient, MinIOProperties minIOProperties) {
        this.minioClient = minioClient;
        this.minIOProperties = minIOProperties;
    }

    @Override
    public String upload(String fileName, InputStream is, String contentType) throws Exception {
        PutObjectOptions po = new PutObjectOptions(is.available(), 0);
        po.setContentType(contentType);
        minioClient.putObject(minIOProperties.getBucketName(), fileName, is, po);
        is.close();
        return this.getFilePath(fileName);
    }

    @Override
    public String upload(String fileName, byte[] objectByte, String contentType) throws Exception {
        InputStream is = new ByteArrayInputStream(objectByte);
        PutObjectOptions po = new PutObjectOptions(is.available(), 0);
        po.setContentType(contentType);
        minioClient.putObject(minIOProperties.getBucketName(), fileName, is, po);
        is.close();
        return this.getFilePath(fileName);
    }

    @Override
    public byte[] download(String fileName) throws Exception {
        String minioPrefix = minioNginxUrl+FORWARD_SLASH+minIOProperties.getBucketName()+FORWARD_SLASH;
        InputStream is = minioClient.getObject(minIOProperties.getBucketName(), URLUtil.decode(fileName.replace(minioPrefix,"")));
        return toByteArray(is);
    }

    @Override
    public void delete(String fileName) throws Exception {
        minioClient.removeObject(minIOProperties.getBucketName(), fileName);
    }

    public static byte[] toByteArray(InputStream input) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096];
        int n = 0;
        while (-1 != (n = input.read(buffer))) {
            output.write(buffer, 0, n);
        }

        return output.toByteArray();
    }
    private String getFilePath(String fileName) throws IOException, InvalidKeyException, InvalidResponseException, InsufficientDataException, NoSuchAlgorithmException, InternalException, XmlParserException, InvalidBucketNameException, ErrorResponseException {
        return minioClient.getObjectUrl(minIOProperties.getBucketName(),fileName);
    }
}
