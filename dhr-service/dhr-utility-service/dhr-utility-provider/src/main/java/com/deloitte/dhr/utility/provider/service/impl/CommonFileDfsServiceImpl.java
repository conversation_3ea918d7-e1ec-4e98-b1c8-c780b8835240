package com.deloitte.dhr.utility.provider.service.impl;

import cn.hutool.core.date.DateUtil;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.utils.IdWorkerUtil;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import com.deloitte.dhr.utility.api.dto.file.FileRequestDto;
import com.deloitte.dhr.utility.api.dto.file.FileResponseDto;
import com.deloitte.dhr.utility.api.exception.ExceptionEnum;
import com.deloitte.dhr.utility.api.exception.FileException;
import com.deloitte.dhr.utility.provider.config.FastDFSConnectionPool;
import com.deloitte.dhr.utility.provider.entity.FileInfo;
import com.deloitte.dhr.utility.provider.entity.MinIOProperties;
import com.deloitte.dhr.utility.provider.mapper.FileInfoMapper;
import com.deloitte.dhr.utility.provider.service.CommonFileDfsService;
import com.deloitte.dhr.utility.provider.service.FileBusinessService;
import com.deloitte.dhr.utility.provider.utils.FileUtil;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.csource.common.MyException;
import org.csource.fastdfs.StorageClient1;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 文件服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
@Slf4j
@Service("fastDFSService")
public class CommonFileDfsServiceImpl implements CommonFileDfsService {
    private static final String STATUS_SUCCESS = "success";
    private static final String STR_FULL_TIME = "yyyy-MM-dd HH:mm:ss";
    private final FastDFSConnectionPool fastDfsConnectionPool;
    private final FileInfoMapper fileInfoMapper;
    private final FileBusinessService fileBusinessService;

    @Autowired
    private MinIOProperties minIOProperties;



    public CommonFileDfsServiceImpl(FastDFSConnectionPool fastDfsConnectionPool, FileInfoMapper fileInfoMapper, FileBusinessService fileBusinessService) {
        this.fastDfsConnectionPool = fastDfsConnectionPool;
        this.fileInfoMapper = fileInfoMapper;
        this.fileBusinessService = fileBusinessService;
    }

    @Override
    public FileResponseDto uploadFile(FileRequestDto fileRequestDto) {
        return this.batchUploadFile(Collections.singletonList(fileRequestDto)).get(0);
    }

    /**
     * 保存文件业务数据
     *
     * @param fileResponseDtoList
     * @param fileRequestDtoList
     */
    private void saveFileInfoList(List<FileResponseDto> fileResponseDtoList, List<FileRequestDto> fileRequestDtoList) {
        if (fileRequestDtoList.get(0).getFileGroupId() != null) {
            fileInfoMapper.deleteByFileGroupId(fileRequestDtoList.get(0).getFileGroupId());
        }
        final String userName= LoginUtil.getLoginUser()!=null?LoginUtil.getLoginUser().getUsername():"";
        String fileGroupId = UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
        List<FileInfo> fileInfoList = fileResponseDtoList.stream().map(fileResponseDto -> {
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileSize(fileResponseDto.getSize());
            fileInfo.setAttachName(fileResponseDto.getName());
            fileInfo.setKey(fileResponseDto.getUrl());
            fileInfo.setAttachType(fileResponseDto.getType());
            fileInfo.setId(IdWorkerUtil.getId());
            fileInfo.setCreatedDate(new Date());
            fileInfo.setCreateUser(userName);
            fileInfo.setUpdatedDate(new Date());
            fileInfo.setUpdateUser(userName);
            fileInfo.setFileGroupId(fileGroupId);
            fileResponseDto.setId(fileInfo.getId().toString());
            fileResponseDto.setFileGroupId(fileGroupId);
            return fileInfo;
        }).collect(Collectors.toList());
        fileInfoMapper.insertBatch(fileInfoList);
    }

    @Override
    public Boolean deleteFileByUrl(String url) {
        boolean result = false;
        //获取客户端
        StorageClient1 storageClient1 = null;
        try {
            //1、获取客户端
            storageClient1 = fastDfsConnectionPool.get();
            //2、删除文件
            int delNum = storageClient1.delete_file1(url);
            if (delNum == 0) {
                result = true;
            }
            //3、链接放回文件连接池
            fastDfsConnectionPool.put(storageClient1);

            //4、删除业务表数据
            fileInfoMapper.deleteByKey(url);
        } catch (Exception e) {
            log.error("删除文件", e);
            throw new CommRunException(ExceptionEnum.DELETE_FILE_FAILURE_ERR.getCode(), ExceptionEnum.DELETE_FILE_FAILURE_ERR.getMessage());
        } finally {
            try {
                fastDfsConnectionPool.drop(storageClient1);
            } catch (Exception e1) {
                log.error("删除文件-关闭文件连接池失败", e1);
            }
        }
        return result;
    }

    @Override
    public FileResponseDto uploadFile(MultipartFile uploadFile, String token) {
        return this.batchUploadFile(Collections.singletonList(uploadFile), token).get(0);
    }

    @Override
    public void downloadFileByUrl(String url, String fileName, HttpServletResponse response) {
        //获取文件流
        InputStream is = null;
        OutputStream os = null;
        try {
            //设置response参数
            FileUtil.setResponse(response, fileName);
            //调用fastDfs下载文件
            byte[] fileByte = this.download(url);
            if (fileByte != null) {
                os = response.getOutputStream();
                is = new ByteArrayInputStream(fileByte);
                byte[] buffer = new byte[1024 * 5];
                int len;
                while ((len = is.read(buffer)) > 0) {
                    os.write(buffer, 0, len);
                }
                os.flush();
            }
        } catch (IOException e) {
            throw new FileException(ExceptionEnum.DOWNLOAD_FILE_FAILURE_ERR.getCode(), ExceptionEnum.DOWNLOAD_FILE_FAILURE_ERR.getMessage(), e);
        } finally {
            // 关闭流
            try {
                if (is != null) {
                    is.close();
                }
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                log.error(e.toString());
            }
        }

    }

    @Override
    public Map<String, byte[]> downloadFileById(String id) {
        Map<String, byte[]> map = new HashMap<>(16);
        FileResponseDto fileResponseDto = fileBusinessService.findById(id);
        if (fileResponseDto == null) {
            return map;
        }
        //调用fastDfs下载文件
        byte[] fileByte = this.download(fileResponseDto.getUrl());
        map.put("fileByte", fileByte);
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<FileResponseDto> batchUploadFile(List<FileRequestDto> fileRequestDtoList) {
        List<FileResponseDto> fileResponseDtoList = new ArrayList<>();
        List<String> errorFileNameList = new ArrayList<>();
        // 校验上传文件是否合法
        if (!FileUtil.verifyFile(fileRequestDtoList, errorFileNameList)) {
            //不合法的文件名称
            String errorFileName = Joiner.on(",").join(errorFileNameList);
            throw new FileException(ExceptionEnum.UPLOAD_FILE_ILLEGAL.getCode(), ExceptionEnum.UPLOAD_FILE_ILLEGAL.getMessage() + ":" + errorFileName);
        }
        String result;
        //获取客户端
        StorageClient1 storageClient1 = null;
        try {
            //1、获取客户端
            storageClient1 = fastDfsConnectionPool.get();
            for (FileRequestDto fileRequestDto : fileRequestDtoList) {
                FileResponseDto fileResponseDto = new FileResponseDto();
                fileResponseDto.setCreateDate(DateUtil.format(new Date(), STR_FULL_TIME));
                //2、文件名
                String fileName = fileRequestDto.getFileName();
                //3、文件后缀
                String fileType = FileUtil.getSuffix(fileName);
                byte[] fileBytes = fileRequestDto.getFileBytes();
                fileResponseDto.setName(fileName);
                fileResponseDto.setSize(FileUtil.getPrintSize(fileBytes.length));
                fileResponseDto.setType(fileType);
                //4、如果是图片文件
                if (FileUtil.isFileImage(fileType)) {
                    BufferedImage image;
                    image = ImageIO.read(new ByteArrayInputStream(fileBytes));
                    String width = String.valueOf(image.getWidth());
                    String height = String.valueOf(image.getHeight());
                    fileResponseDto.setFormatSize(width + "*" + height);
                }
                //5、上传
                log.info("文件上传，开始：【{}】", Instant.now().toString());
                result = storageClient1.upload_file1(fileBytes, fileType, null);
                log.info("文件上传，结束：【{}】", Instant.now().toString());
                fileResponseDto.setUrl(result);
                fileResponseDto.setDownloadUrl(result);
                fileResponseDto.setPreviewUrl(result);
                fileResponseDto.setStatus(STATUS_SUCCESS);
                fileResponseDto.setMsg("上传成功");
                fileResponseDtoList.add(fileResponseDto);
            }
            //6、链接放回文件连接池
            fastDfsConnectionPool.put(storageClient1);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw new CommRunException(ExceptionEnum.UPLOAD_FILE_FAILURE_ERR.getCode(), ExceptionEnum.UPLOAD_FILE_FAILURE_ERR.getMessage());
        } finally {
            try {
                fastDfsConnectionPool.drop(storageClient1);
            } catch (Exception e1) {
                log.error("上传文件-关闭文件连接池失败", e1);
            }
        }
        //保存文件信息到数据库
        this.saveFileInfoList(fileResponseDtoList, fileRequestDtoList);
        return fileResponseDtoList;
    }

    @Override
    public List<FileResponseDto> batchUploadFile(List<MultipartFile> uploadFileList, String token) {
        List<FileRequestDto> fileRequestDtoList = new ArrayList<>();
        uploadFileList.forEach(uploadFile -> {
            FileRequestDto fileRequestDto = new FileRequestDto();
            fileRequestDto.setFileName(uploadFile.getOriginalFilename());
            fileRequestDto.setToken(token);
            try {
                fileRequestDto.setFileBytes(uploadFile.getBytes());
            } catch (IOException e) {
                log.error("上传文件异常", e);
                throw new FileException(ExceptionEnum.UPLOAD_FILE_FAILURE_ERR.getCode(), ExceptionEnum.UPLOAD_FILE_FAILURE_ERR.getMessage());
            }
            fileRequestDtoList.add(fileRequestDto);
        });

        return this.batchUploadFile(fileRequestDtoList);
    }

    @Override
    public void downloadFileById(String id, HttpServletResponse response) {
        FileResponseDto fileResponseDto = fileBusinessService.findById(id);
        if (fileResponseDto == null) {
            throw new FileException(ExceptionEnum.DOWNLOAD_FILE_NOT_FOUND.getCode(), ExceptionEnum.DOWNLOAD_FILE_NOT_FOUND.getMessage());

        }
        fileResponseDto.setUrl(fileResponseDto.getUrl().replace(minIOProperties.getMinIOUrl(), ""));
        this.downloadFileByUrl(fileResponseDto.getUrl(), fileResponseDto.getName(), response);
    }

    @Override
    public void previewFileById(String id, HttpServletResponse response) {
        FileResponseDto fileResponseDto = fileBusinessService.findById(id);
        if (fileResponseDto == null) {
            throw new FileException(ExceptionEnum.DOWNLOAD_FILE_NOT_FOUND.getCode(), ExceptionEnum.DOWNLOAD_FILE_NOT_FOUND.getMessage());

        }
        this.downloadFileByUrl(fileResponseDto.getUrl(), fileResponseDto.getName(), response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteFileById(String id) {
        FileResponseDto fileResponseDto = fileBusinessService.findById(id);
        if (fileResponseDto == null) {
            return false;
        }
        return this.deleteFileByUrl(fileResponseDto.getUrl());
    }

    @Override
    public FileResponseDto previewFile(String id) {
        FileResponseDto responseDto = fileBusinessService.findById(id);
        if (responseDto == null) {
            throw new FileException(ExceptionEnum.PREVIEW_FILE_NOT_FOUND.getCode(), ExceptionEnum.PREVIEW_FILE_NOT_FOUND.getMessage());
        }
        return fileBusinessService.findById(id);
    }

    @Override
    public void batchDownloadToZip(List<Long> fileIds, String zipFileName, HttpServletResponse response) {

    }

    @Override
    public Map<String, byte[]> internalBatchDownload(List<Long> fileIds) {
        return null;
    }

    /**
     * 下载文件，加锁是因为防止并发出错
     *
     * @param url
     * @return
     */
    public synchronized byte[] download(String url) {
        log.info("文件下载，开始：【{}】【{}】", Instant.now().toString(), url);
        StorageClient1 storageClient = null;
        try {
            //从连接池中获取连接
            storageClient = fastDfsConnectionPool.get();
            byte[] bytes = storageClient.download_file1(url);
            log.info("文件下载，结束：【{}】【{}】", Instant.now().toString(), url);
            return bytes;
        } catch (IOException | MyException e) {
            log.error("文件下载，异常：【{}】【{}】", Instant.now().toString(), url);
            log.error("文件下载，异常：", e);
            throw new FileException(ExceptionEnum.DOWNLOAD_FILE_FAILURE_ERR.getCode(), ExceptionEnum.DOWNLOAD_FILE_FAILURE_ERR.getMessage(), e);
        } finally {
            //清除该无效连接
            fastDfsConnectionPool.drop(storageClient);
        }
    }


}
