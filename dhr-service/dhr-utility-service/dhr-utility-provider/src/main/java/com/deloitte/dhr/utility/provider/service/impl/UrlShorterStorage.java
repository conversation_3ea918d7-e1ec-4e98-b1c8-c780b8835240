package com.deloitte.dhr.utility.provider.service.impl;


import com.deloitte.dhr.common.base.utils.LoginUtil;
import com.deloitte.dhr.utility.provider.entity.ShorterString;
import com.deloitte.dhr.utility.provider.entity.UrlShortInfo;
import com.deloitte.dhr.utility.provider.mapper.UrlShortInfoMapper;
import com.deloitte.dhr.utility.provider.service.ShorterGetter;
import com.deloitte.dhr.utility.provider.service.ShorterStorage;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 11/11/2021 19:29
 */
@Service
@AllArgsConstructor
public class UrlShorterStorage<T extends ShorterGetter> implements ShorterStorage<T> {
    private UrlShortInfoMapper urlShortInfoMapper;

    @Override
    public String get(String shorter) {
        UrlShortInfo queryDto = new UrlShortInfo();
        queryDto.setShorter(shorter);
        UrlShortInfo urlShortInfo = urlShortInfoMapper.selectOne(queryDto);
        if(urlShortInfo != null) {
            return urlShortInfo.getUrl();
        }
        return null;
    }

    @Override
    public void clean(String url) {

    }

    @Override
    public void cleanShorter(String shorter) {

    }

    @Override
    public void save(String origin, T shorter) {
       UrlShortInfo urlShortInfo = new UrlShortInfo();
       urlShortInfo.setUrl(origin);
       urlShortInfo.setShorter(shorter.getShorter());
       urlShortInfo.setEnabled(true);
       urlShortInfo.setCreateBy(LoginUtil.getLoginUser().getId() == null ? "-1" :LoginUtil.getLoginUser().getId());
       urlShortInfo.setUpdateBy(LoginUtil.getLoginUser().getId() == null ? "-1" :LoginUtil.getLoginUser().getId());
       urlShortInfoMapper.insertSelective(urlShortInfo);
    }


    @Override
    public ShorterGetter getByOrigin(String origin) {
        UrlShortInfo queryDto = new UrlShortInfo();
        queryDto.setUrl(origin);
        UrlShortInfo urlShortInfo = urlShortInfoMapper.selectOne(queryDto);
        if(urlShortInfo != null) {
            return new ShorterString(urlShortInfo.getShorter());
        }
        return null;
    }
}
