package com.deloitte.dhr.utility.provider.service;

/**
 * 用来存储字符串短地址,针对不同的generator需要有不同的存储器
 * <AUTHOR>
public interface ShorterStorage<T extends ShorterGetter> {
    /**
     * 根据短码获取原始信息
     * @param shorter
     * @return
     */
    String get(String shorter);

    /**
     * 删除短网址信息
     * @param url
     */
    void clean(String url);

    /**
     * 根据短码删除短网址信息
     * @param shorter
     */
    void cleanShorter(String shorter);

    /**
     * 保存短码信息
     * @param origin
     * @param shorter
     */
    void save(String origin, T shorter);

    /**
     * 根据源信息获取短码信息
     * @param origin
     * @return
     */
   ShorterGetter getByOrigin(String origin);
}
