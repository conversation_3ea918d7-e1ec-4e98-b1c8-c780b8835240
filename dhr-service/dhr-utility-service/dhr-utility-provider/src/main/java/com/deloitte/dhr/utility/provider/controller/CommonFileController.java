package com.deloitte.dhr.utility.provider.controller;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.CommonFileDfsInterface;
import com.deloitte.dhr.utility.api.dto.file.FileRequestDto;
import com.deloitte.dhr.utility.api.dto.file.FileResponseDto;
import com.deloitte.dhr.utility.provider.service.CommonFileDfsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 文件服务相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 07/05/2022
 */
@Slf4j
@RestController
@CrossOrigin
@RequestMapping(value = "/utility/file/dfs")
@Api(value = "/utility/file/dfs", tags = "文件服务相关接口，后续开发以此文档为准，其余controller弃用")
public class CommonFileController implements CommonFileDfsInterface {
    private final CommonFileDfsService commonFileDfsService;

    public CommonFileController(CommonFileDfsService commonFileDfsService) {
        this.commonFileDfsService = commonFileDfsService;
    }

    @Override
    @ApiOperation(value = "上传文件（内部调用）")
    @PostMapping(value = "/internalUpload")
    public ResponseVO<FileResponseDto> uploadFile(@RequestBody FileRequestDto fileRequestDto) {
        return ResponseVO.success(commonFileDfsService.uploadFile(fileRequestDto));
    }

    @Override
    @ApiOperation(value = "上传文件（外部调用）")
    @PostMapping(value = "/upload")
    public ResponseVO<FileResponseDto> uploadFile(@RequestParam(value = "file") MultipartFile uploadFile) {
        return ResponseVO.success(commonFileDfsService.uploadFile(uploadFile, ""));
    }


    @Override
    @ApiOperation(value = "通过id删除文件")
    @GetMapping(value = "/delete")
    public ResponseVO<Boolean> deleteFileById(@RequestParam("fileId") String fileId) {
        return ResponseVO.success(commonFileDfsService.deleteFileById(fileId));
    }

    @Override
    @ApiOperation(value = "下载文件(外部调用)-通过id")
    @GetMapping(value = "/download")
    public void downloadFileById(@RequestParam("fileId") String fileId, HttpServletResponse response) {
        commonFileDfsService.downloadFileById(fileId, response);
    }

    @Override
    @ApiOperation(value = "批量下载文件为zip压缩格式")
    @GetMapping(value = "/batchDownloadToZip")
    public void batchDownloadToZip(@RequestParam("fileIds") List<Long> fileIds, @RequestParam("fileName") String fileName, HttpServletResponse response) {
        commonFileDfsService.batchDownloadToZip(fileIds, fileName, response);
    }

    @Override
    @ApiOperation(value = "批量下载文件(内部调用)")
    @GetMapping(value = "/inter/batchDownload")
    public ResponseVO<Map<String, byte[]>> internalBatchDownload(List<Long> fileIds) {
        return ResponseVO.success(commonFileDfsService.internalBatchDownload(fileIds));
    }

    @Override
    @ApiOperation(value = "预览文件(外部调用)-通过id")
    @GetMapping(value = "/preview")
    public void previewFileById(@RequestParam("fileId") String fileId, HttpServletResponse response) {
        commonFileDfsService.previewFileById(fileId, response);
    }

    @Override
    @ApiOperation(value = "下载文件（内部调用）-通过id")
    @GetMapping(value = "/internalDownload")
    public ResponseVO<Map<String, byte[]>> downloadFileById(@RequestParam("fileId") String fileId) {
        return ResponseVO.success(commonFileDfsService.downloadFileById(fileId));
    }

    @Override
    @ApiOperation(value = "批量上传文件（内部调用）")
    @PostMapping(value = "/internalUpload/batch")
    public ResponseVO<List<FileResponseDto>> batchUploadFileInner(@RequestBody List<FileRequestDto> fileRequestDtoList) {
        return ResponseVO.success(commonFileDfsService.batchUploadFile(fileRequestDtoList));
    }

    @Override
    @ApiOperation(value = "批量上传文件（外部调用）")
    @PostMapping(value = "/upload/batch")
    public ResponseVO<List<FileResponseDto>> batchUploadFile(@RequestParam(value = "file") List<MultipartFile> uploadFileList) {
        return ResponseVO.success(commonFileDfsService.batchUploadFile(uploadFileList, ""));
    }

    @Override
    @ApiOperation(value = "获取预览文件信息")
    @GetMapping(value = "/previewFile")
    public ResponseVO<FileResponseDto> previewFile(@RequestParam("fileId") String fileId) {
        return ResponseVO.success(commonFileDfsService.previewFile(fileId));
    }
}
