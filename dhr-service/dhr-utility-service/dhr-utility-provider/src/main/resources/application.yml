# Nacos配置中心连接配置
nacos:
  server-addr: ${config_nacos_serveraddr:172.16.5.16}
  port: ${config_nacos_port:8848}
  namespace: ${config_nacos_namespace:bcabaf9e-e845-4cea-8f2f-381026ee0c56}
  username: ${config_nacos_username:nacos}
  password: ${config_nacos_password:nacos}

# Spring框架配置
spring:
  profiles:
    active: ${config_profile:dev}
  application:
    name: dhr-utility-service
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

  # MVC配置 - Swagger兼容性配置
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  # Nacos服务发现和配置管理
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.server-addr}:${nacos.port}
        namespace: ${nacos.namespace}
        group: ${spring.profiles.active}
        username: ${nacos.username}
        password: ${nacos.password}
      config:
        file-extension: yaml
        server-addr: ${nacos.server-addr}:${nacos.port}
        group: ${spring.profiles.active}
        enabled: ${config.enable:true}
        namespace: ${nacos.namespace}
        username: ${nacos.username}
        password: ${nacos.password}

  # 配置导入
  config:
    import:
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

# 服务端口配置 (与docker-compose.yml保持一致)
server:
  port: ${config_server_port:8080}

# 日志配置
logging:
  file:
    name: logs/${spring.application.name}/info/log_info.log
  level:
    root: info  # 输出日志级别
#  log:
#    appender:
#      elk:
#        enable: true  # 开启ELK日志
#        ip: ************  # logstash地址
#        port: 5045  # logstash端口

# Feign客户端配置
feign:
  hystrix:
    enabled: true
  compression:
    request:
      enabled: true
      mime-types:
        - text/xml
        - application/xml
        - application/json
    response:
      enabled: true
  client:
    config:
      default:
        connectTimeout: 360000
        readTimeout: 360000

# Hystrix熔断器配置
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 50000

# Ribbon负载均衡配置
ribbon:
  eager-load:
    enabled: true

# DHR配置
dhr:
  swagger:
    enabled: true
    docket:
      basic:
        title: 公共服务
        base-package: com.deloitte.dhr.utility.provider  # 需要扫描的包名
