<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.deloitte.dhr.utility.provider.mapper.FileInfoMapper" >
  <resultMap id="BaseResultMap" type="com.deloitte.dhr.utility.provider.entity.FileInfo" >
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="ATTACH_NAME" property="attachName" jdbcType="VARCHAR" />
    <result column="ATTACH_TYPE" property="attachType" jdbcType="VARCHAR" />
    <result column="FILE_GROUP_ID" property="fileGroupId" jdbcType="VARCHAR" />
    <result column="KEY" property="key" jdbcType="VARCHAR" />
    <result column="FILE_SIZE" property="fileSize" jdbcType="VARCHAR" />
    <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List">
    id, attach_name, attach_type, file_group_id, `key`, file_size, created_date, create_user, updated_date, update_user
  </sql>
  <!-- 条件删除 -->
  <update id="deleteByFileGroupId" parameterType="java.lang.String">
    delete from t_file_info where file_group_id = #{fileGroupId, jdbcType=VARCHAR}
  </update>
  <!-- 批量插入 -->
  <insert id="insertBatch" parameterType="list">
    insert into t_file_info
        (id, attach_name, attach_type, file_group_id, `key`, file_size, created_date, create_user, updated_date, update_user)
        values
    <foreach collection="list" item="file" separator=",">
        (#{file.id, jdbcType=DECIMAL}, #{file.attachName, jdbcType=VARCHAR}, #{file.attachType, jdbcType=VARCHAR}, #{file.fileGroupId, jdbcType=VARCHAR},
         #{file.key, jdbcType=VARCHAR}, #{file.fileSize, jdbcType=VARCHAR}, #{file.createdDate, jdbcType=TIMESTAMP}, #{file.createUser, jdbcType=VARCHAR},
         #{file.updatedDate, jdbcType=TIMESTAMP}, #{file.updateUser, jdbcType=VARCHAR})
    </foreach>
  </insert>

  <!-- 单条插入 -->
  <insert id="insertOne" parameterType="com.deloitte.dhr.utility.provider.entity.FileInfo">
    insert into t_file_info
    (id, attach_name, attach_type, file_group_id, `key`, file_size, created_date, create_user, updated_date, update_user)
    values
      (#{id, jdbcType=DECIMAL}, #{attachName, jdbcType=VARCHAR}, #{attachType, jdbcType=VARCHAR}, #{fileGroupId, jdbcType=VARCHAR},
      #{key, jdbcType=VARCHAR}, #{fileSize, jdbcType=VARCHAR}, #{createdDate, jdbcType=TIMESTAMP}, #{createUser, jdbcType=VARCHAR},
      #{updatedDate, jdbcType=TIMESTAMP}, #{updateUser, jdbcType=VARCHAR})
  </insert>

  <!-- 条件查询 -->
  <select id="findByFileGroupId" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
    <include refid="Base_Column_List" />
    from t_file_info
    where file_group_id = #{fileGroupId, jdbcType=VARCHAR}
  </select>

  <select id="findById" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    from t_file_info
    where id = #{uid}
  </select>

  <select id="findByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_file_info
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>

    <update id="updateByid" parameterType="com.deloitte.dhr.utility.provider.entity.FileInfo">

    UPDATE t_file_info SET ATTACH_NAME = #{attachName},FILE_GROUP_ID=#{fileGroupId},CREATE_USER=#{createUser},UPDATE_USER=#{updateUser}
    WHERE ID =#{id}
  </update>


  <delete id="deleteByKey" parameterType="java.lang.String">
    delete
    FROM t_file_info
    where `ID` = #{key}
  </delete>
</mapper>