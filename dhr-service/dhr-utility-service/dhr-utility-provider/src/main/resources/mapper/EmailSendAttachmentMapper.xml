<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.deloitte.dhr.utility.provider.mapper.EmailSendAttachmentMapper" >
  <resultMap id="BaseResultMap" type="com.deloitte.dhr.utility.provider.entity.EmailSendAttachment" >
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR" />
    <result column="FILE_SIZE" property="fileSize" jdbcType="DECIMAL" />
    <result column="STORAGE_ADDRESS" property="storageAddress" jdbcType="VARCHAR" />
    <result column="SUFFIX" property="suffix" jdbcType="VARCHAR" />
    <result column="EMAIL_ID" property="emailId" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 批量插入 -->
  <insert id="insertBatch" parameterType="list">
    insert into t_email_send_attachment
    (id, file_name, file_size, storage_address, suffix, email_id)
    values
    <foreach collection="list" item="attach" separator=",">
      (#{attach.id, jdbcType=DECIMAL}, #{attach.fileName, jdbcType=VARCHAR},
       #{attach.fileSize, jdbcType=DECIMAL}, #{attach.storageAddress, jdbcType=VARCHAR},
      #{attach.suffix, jdbcType=VARCHAR}, #{attach.emailId, jdbcType=DECIMAL})
    </foreach>
  </insert>
</mapper>