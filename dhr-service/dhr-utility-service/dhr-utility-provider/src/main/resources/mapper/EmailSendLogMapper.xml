<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.deloitte.dhr.utility.provider.mapper.EmailSendLogMapper" >
  <resultMap id="BaseResultMap" type="com.deloitte.dhr.utility.provider.entity.EmailSendLog" >
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="COPY_TO" property="copyTo" jdbcType="VARCHAR" />
    <result column="RECEIVER" property="receiver" jdbcType="VARCHAR" />
    <result column="SUBJECT" property="subject" jdbcType="VARCHAR" />
    <result column="CONTENT" property="content" jdbcType="CLOB" />
  </resultMap>
</mapper>