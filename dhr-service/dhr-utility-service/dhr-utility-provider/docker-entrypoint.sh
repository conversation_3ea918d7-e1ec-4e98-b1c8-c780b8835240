#!/bin/bash
set -e

# 设置JVM参数
export JAVA_OPTS="${JAVA_OPTS:-'-Xms256m -Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200'}"

# 设置应用配置
export CONFIG_PROFILE="${CONFIG_PROFILE:-prod}"
export CONFIG_ENABLE="${CONFIG_ENABLE:-true}"

# 设置Nacos配置(可通过环境变量覆盖)
export CONFIG_NACOS_SERVERADDR="${CONFIG_NACOS_SERVERADDR:-172.16.5.16}"
export CONFIG_NACOS_PORT="${CONFIG_NACOS_PORT:-8848}"
export CONFIG_NACOS_NAMESPACE="${CONFIG_NACOS_NAMESPACE:-bcabaf9e-e845-4cea-8f2f-381026ee0c56}"
export CONFIG_SERVER_PORT="${CONFIG_SERVER_PORT:-8080}"

echo "=================================================="
echo "Starting DHR Utility Service"
echo "Profile: $CONFIG_PROFILE"
echo "Nacos Server: $CONFIG_NACOS_SERVERADDR:$CONFIG_NACOS_PORT"
echo "Service Port: $CONFIG_SERVER_PORT"
echo "Java Options: $JAVA_OPTS"
echo "=================================================="

# 等待Nacos服务可用(可选)
if [ "${WAIT_FOR_NACOS:-false}" = "true" ]; then
    echo "Waiting for Nacos to be available..."
    while ! nc -z $CONFIG_NACOS_SERVERADDR $CONFIG_NACOS_PORT; do
        echo "Waiting for Nacos at $CONFIG_NACOS_SERVERADDR:$CONFIG_NACOS_PORT..."
        sleep 5
    done
    echo "Nacos is available!"
fi

# 执行传入的命令
exec "$@"
