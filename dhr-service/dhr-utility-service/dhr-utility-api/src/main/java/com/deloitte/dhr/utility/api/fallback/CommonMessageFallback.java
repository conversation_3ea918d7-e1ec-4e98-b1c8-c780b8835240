package com.deloitte.dhr.utility.api.fallback;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.CommonMessageInterface;
import com.deloitte.dhr.utility.api.dto.sms.AliyunSmsDto;
import com.deloitte.dhr.utility.api.dto.sms.AliyunSmsResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 降级处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 10/05/2022
 */
@Component
@Slf4j
public class CommonMessageFallback implements FallbackFactory<CommonMessageInterface> {

    @Override
    public CommonMessageInterface create(Throwable throwable) {
        log.error("调用短信服务失败，错误信息为:{}", throwable.toString());
        return new CommonMessageInterface() {

            @Override
            public ResponseVO<AliyunSmsResultDto> sendMessage(AliyunSmsDto aliyunSmsDto) {
                return ResponseVO.fail("发送短信失败!");
            }

            @Override
            public ResponseVO<AliyunSmsResultDto> sendMessageGlobe(AliyunSmsDto aliyunSmsDto) {
                return ResponseVO.fail("发送短信失败!");
            }
        };
    }
}
