package com.deloitte.dhr.utility.api.fallback;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.DhrUserFeign;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version: 1.0
 * @date: 24/04/2022
 */
@Component
public class DhrUserFeignFallback implements DhrUserFeign {
    @Override
    public ResponseVO userSearch(List<String> usernames) throws Exception  {
        return ResponseVO.fail("132获取用户异常!");
    }
}
