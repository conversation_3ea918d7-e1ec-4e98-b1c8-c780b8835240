package com.deloitte.dhr.utility.api.dto.sms;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 09/07/2021 10:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AliyunSmsDto implements Serializable {
    private static final long serialVersionUID = -655615586387085799L;
    /**
     * 手机号码，多个以","隔开
     */
    @NotNull(message = "手机号码不能为空")
    private String phoneNumbers;

    /**
     * 短信模板编码
     */
    @NotNull(message = "短信模板编码不能为空")
    private String templateCode;

    /**
     * 短信模板参数，JSON字符串格式
     */
    private String templateParam;
    /**
     * 上行短信扩展码
     */
    @Length(max = 255, message = "上行短信扩展码的长度不能超过255")
    private String smsUpExtendCode;

    /**
     * 外部流水扩展字段
     */
    @Length(max = 100, message = "外部流水号的长度不能超过100")
    private String outId;

    /**
     * 国际短信内容
     */
    private String message;
}
