package com.deloitte.dhr.utility.api.dto.file;

import lombok.Data;

import java.io.Serializable;

@Data
public class FileResponseDto implements Serializable {
    /**
     * 下载地址
     */
    private String url;

    /**
     * 文件唯一id
     */
    private String id;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 文件大小
     */
    private String size;

    /**
     * 图片文件的分辨率（300*500）
     */
    private String formatSize;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 上传文件时间
     */
    private String createDate;

    /**
     * 下载地址
     */
    private String downloadUrl;

    /**
     * 预览地址
     */
    private String previewUrl;

    /**
     * 上传文件返回状态（成功/失败）
     */
    private String status;

    /**
     * 上传文件返回内容（成功/失败）LINK_TYPE
     */
    private String msg;

    private int httpStatusCode;

    /**
     * 文件组id
     */
    private String fileGroupId;
}
