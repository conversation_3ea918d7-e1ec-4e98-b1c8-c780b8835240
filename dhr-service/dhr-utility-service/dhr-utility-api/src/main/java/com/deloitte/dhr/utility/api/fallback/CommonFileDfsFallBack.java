package com.deloitte.dhr.utility.api.fallback;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.CommonFileDfsInterface;
import com.deloitte.dhr.utility.api.dto.file.FileRequestDto;
import com.deloitte.dhr.utility.api.dto.file.FileResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 文件服务Fallback
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CommonFileDfsFallBack implements FallbackFactory<CommonFileDfsInterface> {
    @Override
    public CommonFileDfsInterface create(Throwable cause) {
        log.error("调用文件服务失败! 错误信息为：{}", cause.toString());
        return new CommonFileDfsInterface() {
            @Override
            public ResponseVO<FileResponseDto> uploadFile(FileRequestDto fileRequestDto) {
                return ResponseVO.fail("调用文件服务失败!错误信息为：" + cause);
            }

            @Override
            public ResponseVO<FileResponseDto> uploadFile(MultipartFile uploadFile) {
                return ResponseVO.fail("调用文件服务失败!错误信息为：" + cause);
            }

            @Override
            public ResponseVO<Boolean> deleteFileById(String fileId) {
                return ResponseVO.fail("调用文件服务失败!错误信息为：" + cause);
            }

            @Override
            public void downloadFileById(String fileId, HttpServletResponse response) {
            }

            @Override
            public void batchDownloadToZip(List<Long> fileIds, String fileName, HttpServletResponse response) {
            }

            @Override
            public ResponseVO<Map<String, byte[]>> internalBatchDownload(List<Long> fileIds) {
                return ResponseVO.fail("调用文件服务失败!错误信息为：" + cause);
            }

            @Override
            public void previewFileById(String fileId, HttpServletResponse response) {
            }

            @Override
            public ResponseVO<Map<String, byte[]>> downloadFileById(String fileId) {
                return ResponseVO.fail("调用文件服务失败!错误信息为：" + cause);
            }

            @Override
            public ResponseVO<List<FileResponseDto>> batchUploadFileInner(List<FileRequestDto> fileRequestDtoList) {
                return ResponseVO.fail("调用文件服务失败!错误信息为：" + cause);
            }

            @Override
            public ResponseVO<List<FileResponseDto>> batchUploadFile(List<MultipartFile> uploadFileList) {
                return ResponseVO.fail("调用文件服务失败!错误信息为：" + cause);
            }

            @Override
            public ResponseVO<FileResponseDto> previewFile(String fileId) {
                return ResponseVO.fail("调用文件服务失败!错误信息为：" + cause);
            }
        };
    }
}
