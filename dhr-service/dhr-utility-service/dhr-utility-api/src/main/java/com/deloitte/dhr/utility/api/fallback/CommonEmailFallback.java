package com.deloitte.dhr.utility.api.fallback;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.CommonEmailInterface;
import com.deloitte.dhr.utility.api.dto.email.EmailParamDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * 降级处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 10/05/2022
 */
@Component
@Slf4j
public class CommonEmailFallback implements FallbackFactory<CommonEmailInterface> {

    @Override
    public CommonEmailInterface create(Throwable cause) {
        log.error("调用邮件服务失败，错误信息为:{}", cause.toString());
        return new CommonEmailInterface() {
            @Override
            public ResponseVO<String> sendSimpleEmail(String receivers, String copyTo, String bcc, String subject, String content, MultipartFile[] attachments) {
                return ResponseVO.fail("发送邮件失败!");
            }

            @Override
            public ResponseVO<String> sendEmailInternal(EmailParamDto emailParamDto) {
                return ResponseVO.fail("发送邮件失败!");
            }
        };
    }
}
