package com.deloitte.dhr.utility.api;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.dto.email.EmailParamDto;
import com.deloitte.dhr.utility.api.fallback.CommonEmailFallback;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 发送邮件微服务间接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 10/05/2022
 */
@FeignClient(value = "dhr-utility-service", path = "/utility/email")
public interface CommonEmailInterface {

    Logger logger = LoggerFactory.getLogger(CommonEmailInterface.class);

    default ResponseVO fallBack(Throwable e){
        if (e instanceof CallNotPermittedException){ logger.error("熔断器已经打开，拒绝访问被保护方法",e); }
        return ResponseVO.fail(e.getMessage());
    }

    /**
     * 发送简单邮件
     *
     * @param receivers   邮件接收方
     * @param copyTo      邮件抄送方
     * @param bcc         邮件密送方
     * @param subject     邮件主题
     * @param content     邮件内容
     * @param attachments 附件
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "sendSimpleEmailFallBack")
    @PostMapping(value = "/send", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    ResponseVO<String> sendSimpleEmail(@RequestParam(value = "receivers") String receivers,
                                       @RequestParam(required = false, value = "copyTo") String copyTo,
                                       @RequestParam(required = false, value = "bcc") String bcc,
                                       @RequestParam(value = "subject") String subject,
                                       @RequestParam(value = "content") String content,
                                       @RequestPart(required = false, value = "attachments") MultipartFile[] attachments);
    default ResponseVO sendSimpleEmailFallBack(String receivers, String copyTo, String bcc, String subject, String content, MultipartFile[] attachments, Throwable e){
        return fallBack(e);
    }
    /**
     * 发送简单邮件
     *
     * @param emailParamDto   邮件接收方
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "sendEmailInternalFallBack")
    @PostMapping(value = "/send/internal", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseVO sendEmailInternal(@RequestBody EmailParamDto emailParamDto);
    default ResponseVO sendEmailInternalFallBack(EmailParamDto emailParamDto, Throwable e){
        return fallBack(e);
    }
}
