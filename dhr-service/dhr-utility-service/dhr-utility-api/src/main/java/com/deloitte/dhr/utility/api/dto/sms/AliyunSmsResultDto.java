package com.deloitte.dhr.utility.api.dto.sms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 09/07/2021 10:55
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AliyunSmsResultDto implements Serializable {
    Boolean sendStatus;

    String message;
}
