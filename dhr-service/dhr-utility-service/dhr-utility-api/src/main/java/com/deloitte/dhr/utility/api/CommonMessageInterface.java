package com.deloitte.dhr.utility.api;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.dto.file.FileRequestDto;
import com.deloitte.dhr.utility.api.dto.sms.AliyunSmsDto;
import com.deloitte.dhr.utility.api.dto.sms.AliyunSmsResultDto;
import com.deloitte.dhr.utility.api.fallback.CommonMessageFallback;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 发送短信相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 10/05/2022
 */
@FeignClient(value = "dhr-utility-service", path = "/utility/message")
public interface CommonMessageInterface {

    Logger logger = LoggerFactory.getLogger(CommonMessageInterface.class);

    default ResponseVO fallBack(Throwable e){
        if (e instanceof CallNotPermittedException){ logger.error("熔断器已经打开，拒绝访问被保护方法",e); }
        return ResponseVO.fail(e.getMessage());
    }

    /**
     * 发送短信
     *
     * @param aliyunSmsDto 发送短信
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "sendMessageFallBack")
    @PostMapping(value = "/SendMessage", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseVO<AliyunSmsResultDto> sendMessage(@RequestBody AliyunSmsDto aliyunSmsDto);
    default ResponseVO sendMessageFallBack(AliyunSmsDto aliyunSmsDto, Throwable e){
        return fallBack(e);
    }

    /**
     * 发送国际短信
     *
     * @param aliyunSmsDto 发送国际短信
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "sendMessageGlobeFallBack")
    @PostMapping(value = "/SendMessageGlobe", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseVO<AliyunSmsResultDto> sendMessageGlobe(@RequestBody AliyunSmsDto aliyunSmsDto);
    default ResponseVO sendMessageGlobeFallBack(AliyunSmsDto aliyunSmsDto, Throwable e){
        return fallBack(e);
    }

}
