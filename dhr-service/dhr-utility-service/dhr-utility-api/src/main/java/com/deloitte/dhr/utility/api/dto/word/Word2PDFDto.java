package com.deloitte.dhr.utility.api.dto.word;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Word2PDFDto implements Serializable {
    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件byte数组
     */
    private byte[] fileBytes;
    /**
     * 填充word中的参数，word模板中的参数样式：$xxx
     */
    private Map<String, String> param;



}
