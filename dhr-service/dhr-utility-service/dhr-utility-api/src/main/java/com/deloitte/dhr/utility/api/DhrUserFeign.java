package com.deloitte.dhr.utility.api;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.dto.sms.AliyunSmsDto;
import com.deloitte.dhr.utility.api.fallback.DhrUserFeignFallback;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 判断用户是否在系统中
 *
 * <AUTHOR> LiYong
 * @version: 1.0
 * @date: 24/04/2022
 */
@FeignClient(value = "dhr-basic-service")
public interface DhrUserFeign {

    Logger logger = LoggerFactory.getLogger(DhrUserFeign.class);

    default ResponseVO fallBack(Throwable e){
        if (e instanceof CallNotPermittedException){ logger.error("熔断器已经打开，拒绝访问被保护方法",e); }
        return ResponseVO.fail(e.getMessage());
    }

    /**
     * 判断用户是否在系统中
     *
     * @return
     * @throws Exception
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "userSearchFallBack")
    @PostMapping(value = "/user/userSearch")
    ResponseVO userSearch(@RequestParam(value = "usernames") List<String>  usernames) throws Exception;
    default ResponseVO userSearchFallBack(List<String>  usernames, Throwable e){
        return fallBack(e);
    }
}
