package com.deloitte.dhr.utility.api;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.dto.word.Word2PDFDto;
import com.deloitte.dhr.utility.api.dto.word.Word2PDFResponseDto;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 文档转换
 *
 * <AUTHOR>
 * @version 1.0
 * @date 10/05/2022
 */
@FeignClient(value = "dhr-utility-service", path = "/utility/file/convert")
public interface CommonFileConvertInterface {

    Logger logger = LoggerFactory.getLogger(CommonFileConvertInterface.class);

    default ResponseVO fallBack(Throwable e){
        if (e instanceof CallNotPermittedException){ logger.error("熔断器已经打开，拒绝访问被保护方法",e); }
        return ResponseVO.fail(e.getMessage());
    }

    /**
     * word转pdf
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "word2pdfFallBack")
    @PostMapping(value = "/word2pdf", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseVO<Word2PDFResponseDto> word2pdf(@RequestBody Word2PDFDto dto) throws Exception;
    default ResponseVO word2pdfFallBack(Word2PDFDto dto, Throwable e){
        return fallBack(e);
    }

}
