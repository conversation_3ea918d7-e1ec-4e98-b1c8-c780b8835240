package com.deloitte.dhr.utility.api.exception;

/**
 * 文件异常枚举类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 10/05/2022
 */
public enum ExceptionEnum {
    /**
     * 系统异常，请联系管理员处理
     */
    SystemException("001-00-00001", "系统异常，请联系管理员处理."),
    /**
     * 邮件接收者不能为空
     */
    EMAIL_RECIEVER_NONE("001-00-00000", "邮件接收者不能为空"),
    /**
     * 短信发送失败
     */
    UMS_SEND_FAIL("001-00-00003", "短信发送失败"),
    /**
     * 上传文件失败
     */
    UPLOAD_FILE_FAILURE_ERR("001-00-10000", "上传文件异常,检查图片格式是否正确！"),

    /**
     * 删除文件失败
     */
    DELETE_FILE_FAILURE_ERR("001-00-10001", "删除文件异常"),

    /**
     * 预览文件失败
     */
    PREVIEW_FILE_FAILURE_ERR("001-00-10002", "预览文件异常"),

    /**
     * 下载文件失败
     */
    DOWNLOAD_FILE_FAILURE_ERR("001-00-10003", "下载文件异常"),
    /**
     * 文件组ID不能为空
     */
    FILE_GROUPID_IS_NOT_NULL("001-00-10004", "文件组ID不能为空"),
    /**
     * 文件组查询为空
     */
    NOT_FOUND_FILE_GROUP("001-00-10005", "文件组查询为空"),

    /**
     * 发送邮件失败
     */
    EMAIL_SEND_ERR("001-00-10006", "发送邮件失败"),

    /**
     * 发送邮件异常
     */
    EMAIL_SEND_EX("001-00-10007", "发送邮件异常"),

    /**
     * 添加附件异常
     */
    EMAIL_ATTACH_ADD_EX("001-00-10008", "添加附件异常"),

    /**
     * 保存附件异常
     */
    EMAIL_ATTACH_SAVE_EX("001-00-10009", "保存附件异常"),

    /**
     * 保存附件释放资源异常
     */
    EMAIL_ATTACH_SAVE_RES_CLOSE_EX("001-00-10010", "保存附件释放资源异常"),

    /**
     * 不支持当前文件格式
     */
    UPLOAD_FILE_ILLEGAL("001-00-10011", "不支持当前文件格式"),

    /**
     * 下载文件不存在
     */
    DOWNLOAD_FILE_NOT_FOUND("001-00-10012", "下载文件不存在"),
    /**
     * 预览文件不存在
     */
    PREVIEW_FILE_NOT_FOUND("001-00-10013", "预览文件不存在"),
    /**
     * 类型转换异常，可能是FORM转PO，或者是PO转VO时发生
     */
    TYPE_CAST_ERROE("1000-001-0001", "类型转换异常，可能是FORM转PO，或者是PO转VO时发生"),
    /**
     * 业务提交的FORM转换成PO对象失败
     */
    FORM_TO_PO_ERROR("1000-001-0002", "业务提交的FORM转换成PO对象失败"),
    /**
     * 业务提交的PO转换成O对象失败
     */
    PO_TO_VO_ERROR("1000-001-0003", "业务提交的PO转换成O对象失败"),
    /**
     * object对象转换成MAP失败
     */
    OBJ_TO_MAP_ERROR("1000-001-0004", "object对象转换成MAP失败"),
    /**
     * 文件转换异常
     */
    FILE_CONVERT_ERROR("1000-001-0005", "文件转换异常");
    /**
     * 错误码
     */
    private String code;
    /**
     * 错误信息
     */
    private String message;

    private ExceptionEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }


    public String getMessage() {
        return message;
    }

}
