package com.deloitte.dhr.utility.api.dto.file;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class FileRequestDto implements Serializable {
    private static final long serialVersionUID = -3207208531700343329L;
    /**
     * 文件唯一id
     */
    private String id;

    /**
     * 文件组唯一id
     */
    private String fileGroupId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件byte数组
     */
    private byte[] fileBytes;

    /**
     * token信息
     */
    private String token;

    /**
     * 员工编号
     */
    private String pernr;

    public FileRequestDto(String fileName, byte[] fileBytes) {
        this.fileName = fileName;
        this.fileBytes = fileBytes;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFileGroupId() {
        return fileGroupId;
    }

    public void setFileGroupId(String fileGroupId) {
        this.fileGroupId = fileGroupId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public byte[] getFileBytes() {
        return fileBytes;
    }

    public void setFileBytes(byte[] fileBytes) {
        this.fileBytes = fileBytes;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public FileRequestDto() {
    }

    public FileRequestDto(String id, String fileGroupId, String fileName, byte[] fileBytes, String token) {
        this.id = id;
        this.fileGroupId = fileGroupId;
        this.fileName = fileName;
        this.fileBytes = fileBytes;
        this.token = token;
    }

    @Override
    public String toString() {
        return "FileRequestDto{" +
                "id='" + id + '\'' +
                ", fileGroupId='" + fileGroupId + '\'' +
                ", fileName='" + fileName + '\'' +
                ", fileBytes=" + Arrays.toString(fileBytes) +
                ", token='" + token + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        FileRequestDto that = (FileRequestDto) o;
        return Objects.equals(id, that.id) && Objects.equals(fileGroupId, that.fileGroupId) && Objects.equals(fileName, that.fileName) && Arrays.equals(fileBytes, that.fileBytes) && Objects.equals(token, that.token);
    }

    @Override
    public int hashCode() {
        int result = Objects.hash(id, fileGroupId, fileName, token);
        result = 31 * result + Arrays.hashCode(fileBytes);
        return result;
    }
}
