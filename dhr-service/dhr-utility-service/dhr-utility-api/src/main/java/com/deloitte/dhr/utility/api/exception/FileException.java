package com.deloitte.dhr.utility.api.exception;

/**
 * 文件异常
 *
 * <AUTHOR>
 * @version 1.0
 * @date 10/05/2022
 */
public class FileException extends RuntimeException {
    private static final long serialVersionUID = 1L;
    private final String code;

    public FileException(String code, String message) {
        super(message);
        this.code = code;
    }

    public FileException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public String getCode() {
        return this.code;
    }
}
