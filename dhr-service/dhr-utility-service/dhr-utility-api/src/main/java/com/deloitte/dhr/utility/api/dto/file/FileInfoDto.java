package com.deloitte.dhr.utility.api.dto.file;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @createDate 2020/10/15 12:35
 */
@Data
public class FileInfoDto implements Serializable {

    private static final long serialVersionUID = -6230733367236289880L;
    /**
     * 文件唯一id
     */
    @ApiModelProperty(value = "文件唯一id", name = "id", example = "1")
    private String id;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名", name = "attachName", example = "1")
    private String attachName;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", name = "attachType", example = "1")
    private String attachType;

    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径", name = "key", example = "1")
    private String key;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小", name = "fileSize", example = "1")
    private String fileSize;
}
