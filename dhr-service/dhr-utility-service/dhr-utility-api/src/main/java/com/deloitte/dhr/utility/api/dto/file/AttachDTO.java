package com.deloitte.dhr.utility.api.dto.file;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 附件上传后返回的实体
 * <br/>29/08/2019 10:47
 *
 * <AUTHOR>
 */
@Data
public class AttachDTO {

    public final static String ATTACHTYPE_FILE = "FILE";

    /**
     * 关联的资源主键
     */
    @NotNull
    private String key;
    /**
     * 用于后端解析时识别当前对象处理，FILE-文件
     */
    @NotNull
    private String attachType;

    @NotNull
    private String attachName;
}
