package com.deloitte.dhr.utility.api.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件转换异常类O
 *
 * <AUTHOR>
 * @version 1.0
 * @date 10/05/2022
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TypeConversionException extends RuntimeException{
    /**
     * SERIAL VERSION UID
     */
    private static final long serialVersionUID = -7057555886318005447L;

    private ExceptionEnum code;

    private String message;

    private String data;

    public TypeConversionException(ExceptionEnum error) {
        this.code = error;
        this.message = error.getMessage();
    }
    public TypeConversionException(ExceptionEnum error, String message) {
        this.code = error;
        this.message = message;
    }

    public TypeConversionException(ExceptionEnum error, String message, String data) {
        this.code = error;
        this.message = message;
        this.data = data;
    }
}
