package com.deloitte.dhr.utility.api;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.TypeReference;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.dto.file.FileResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Objects;

/**
 * Author:  duzhou
 * Date:  29/04/2024 10:31
 */
@Slf4j
@Component
public class FileUtilClient {

    private static final String SERVICE_NAME_UPLOAD_URL = "http://dhr-utility-service/utility/file/dfs/upload";
    private static final String SERVICE_NAME_DOWNLOAD_URL = "http://dhr-utility-service/utility/file/dfs/download?fileId={}";
    private static final String RE_FILE_ID = "fileId";
    private static final String RE_FILE_URL = "fileUrl";
    @Autowired
    RestTemplate restTemplate;

    private static HttpHeaders disposeHttpHeaders(File file) {
        return disposeHttpHeaders(file.getName(), file.length());
    }

    private static HttpHeaders disposeHttpHeaders(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            throw new IllegalArgumentException("File name cannot be null");
        }
        return disposeHttpHeaders(fileName, file.getSize());
    }

    private static HttpHeaders disposeHttpHeaders(String fileName) {
        return disposeHttpHeaders(fileName, null);
    }

    private static HttpHeaders disposeHttpHeaders(@NotNull String fileName, Long length) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        if (Objects.nonNull(length)) {
            headers.setContentLength(length);
        }
        headers.setContentDispositionFormData("media", fileName);
        return headers;
    }

    private static InputStreamResource disposeFileData(InputStream inputStream, String fileName, long cententLength) {
        InputStreamResource resource = new InputStreamResource(inputStream) {
            @Override
            public long contentLength() {
                return cententLength;
            }

            @Override
            public String getFilename() {
                return fileName;
            }
        };
        return resource;
    }

    private String disposeHttpBody(HttpHeaders headers, InputStream inputStream, String fileName, long cententLength, String[] urls) {
        try {
            // 2、封装请求体
            MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
            InputStreamResource resource = disposeFileData(inputStream, fileName, cententLength);
            param.add("file", resource);
            // 3、封装整个请求报文
            HttpEntity<MultiValueMap<String, Object>> formEntity = new HttpEntity<>(param, headers);
            // 4、发送请求
            String url = (Objects.nonNull(urls) && urls.length > 0 && StrUtil.isNotBlank(urls[0])) ? urls[0] : SERVICE_NAME_UPLOAD_URL;
            ResponseEntity<String> data = restTemplate.postForEntity(url, formEntity, String.class);
            // 5、返回结果
            if (!HttpStatus.OK.equals(data.getStatusCode())) {
                throw new RuntimeException("上传文件失败");
            }
            ResponseVO<FileResponseDto> responseVO = getFileResponseDtoResponseVO2(data.getBody());

            return responseVO.getData().getId();
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw e;
        }
    }

    private FileResponseDto disposeHttpBody2(HttpHeaders headers, InputStream inputStream, String fileName, long cententLength, String[] urls) {
        try {
            // 2、封装请求体
            MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
            InputStreamResource resource = disposeFileData(inputStream, fileName, cententLength);
            param.add("file", resource);
            // 3、封装整个请求报文
            HttpEntity<MultiValueMap<String, Object>> formEntity = new HttpEntity<>(param, headers);
            // 4、发送请求
            String url = (Objects.nonNull(urls) && urls.length > 0 && StrUtil.isNotBlank(urls[0])) ? urls[0] : SERVICE_NAME_UPLOAD_URL;
            ResponseEntity<String> data = restTemplate.postForEntity(url, formEntity, String.class);
            // 5、返回结果
            if (!HttpStatus.OK.equals(data.getStatusCode())) {
                throw new RuntimeException("上传文件失败");
            }
            ResponseVO<FileResponseDto> responseVO = getFileResponseDtoResponseVO2(data.getBody());

            return responseVO.getData();
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw e;
        }
    }

    private String disposeHttpBody(HttpHeaders headers, InputStream inputStream, String fileName, long cententLength, String type, String[] urls) {
        try {
            // 2、封装请求体
            MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
            InputStreamResource resource = disposeFileData(inputStream, fileName, cententLength);
            param.add("file", resource);
            // 3、封装整个请求报文
            HttpEntity<MultiValueMap<String, Object>> formEntity = new HttpEntity<>(param, headers);
            // 4、发送请求
            String url = (Objects.nonNull(urls) && urls.length > 0 && StrUtil.isNotBlank(urls[0])) ? urls[0] : SERVICE_NAME_UPLOAD_URL;
            ResponseEntity<String> data = restTemplate.postForEntity(url, formEntity, String.class);
            // 5、返回结果
            if (!HttpStatus.OK.equals(data.getStatusCode())) {
                throw new RuntimeException("上传文件失败");
            }
            ResponseVO<FileResponseDto> responseVO = getFileResponseDtoResponseVO2(data.getBody());

            if(RE_FILE_URL.equals(type)){
                return responseVO.getData().getPreviewUrl();
            }
            return responseVO.getData().getId();
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw e;
        }
    }

    /**
     * 上传文件-File
     *
     * @param file
     * @param url  动态URL,没有使用默认值
     * @return
     */
    public FileResponseDto uploadFile(File file, String... url) {
        try (InputStream inputStream = new FileSystemResource(file).getInputStream();) {
            // 1、封装请求头
            HttpHeaders headers = disposeHttpHeaders(file);
            // 2、封装请求体/并发送
            return disposeHttpBody2(headers, inputStream, file.getName(), file.length(), url);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 上传文件-MultipartFile
     *
     * @param file
     * @param url  动态URL,没有使用默认值
     * @return
     */
    public String uploadMultipartFile(MultipartFile file, String... url) {
        try (InputStream inputStream = file.getInputStream();) {
            // 1、封装请求头
            HttpHeaders headers = disposeHttpHeaders(file);
            // 2、封装请求体/并发送
            return disposeHttpBody(headers, inputStream, file.getOriginalFilename(), file.getSize(), url);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 上传文件-InputStream
     *
     * @param inputStream
     * @param fileName
     * @param cententLength
     * @param url           动态URL,没有使用默认值
     * @return
     */
    public String uploadInputStream(InputStream inputStream, String fileName, long cententLength, String... url) {
        // 1、封装请求头
        HttpHeaders headers = disposeHttpHeaders(fileName);
        // 2、封装请求体/并发送
        return disposeHttpBody(headers, inputStream, fileName, cententLength, url);
    }

    /**
     * 上传文件-Byte
     *
     * @param bytes
     * @param fileName
     * @param url      动态URL,没有使用默认值
     * @return
     */
    public String uploadByte(byte[] bytes, String fileName, String... url) {
        // 1、封装请求头
        HttpHeaders headers = disposeHttpHeaders(fileName);
        // 2、封装请求体/并发送
        return disposeHttpBody(headers, new ByteArrayInputStream(bytes), fileName, bytes.length, url);
    }

    private static ResponseVO<FileResponseDto> getFileResponseDtoResponseVO2(String data) {
        ResponseVO<FileResponseDto> responseVO = JSON.parseObject(data, new TypeReference<ResponseVO<FileResponseDto>>() {
        });
        if (!responseVO.isSuccess()) {
            throw new RuntimeException(responseVO.getMessage());
        }
        return responseVO;
    }

    private static <T> ResponseVO<T> getFileResponseDtoResponseVO(String data, Class<T> tClass) {
        ResponseVO<T> responseVO = JSON.parseObject(data, new TypeReference<ResponseVO<T>>() {
        });
        if (!responseVO.isSuccess()) {
            throw new RuntimeException(responseVO.getMessage());
        }
        return responseVO;
    }

    /**
     * 通过文件ID获取字节文件流
     *
     * @param fileId
     * @param urls   动态URL,没有使用默认值
     * @return
     * @throws IOException
     */
    public InputStream downloadToByteArray(String fileId, String... urls) {
        String url = (Objects.nonNull(urls) && urls.length > 0 && StrUtil.isNotBlank(urls[0])) ? urls[0] : SERVICE_NAME_DOWNLOAD_URL;
        ResponseEntity<org.springframework.core.io.Resource> entity = restTemplate.getForEntity(StrUtil.format(url, fileId), org.springframework.core.io.Resource.class);
        if (Objects.isNull(entity) || Objects.isNull(entity.getBody())) {
            throw new RuntimeException("获取文件失败");
        }
        try {
            return entity.getBody().getInputStream();
        } catch (IOException e) {
            log.error("下载文件失败:", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 通过文件ID获取临时文件，用完需要删除
     *
     * @param fileId
     * @param nums   默认1000字节
     * @return
     */
    public File downloadToFile(String fileId, int... nums) {
        //定义请求头的接收类型
        RequestCallback requestCallback = request -> request.getHeaders().setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM, MediaType.ALL));
        //对响应进行流式处理而不是将其全部加载到内存中
        String path = System.getProperty("java.io.tmpdir") + IdUtil.simpleUUID() + ".temp";
        restTemplate.execute(StrUtil.format(SERVICE_NAME_DOWNLOAD_URL, fileId), HttpMethod.GET, requestCallback, clientHttpResponse -> {
            Files.copy(clientHttpResponse.getBody(), Paths.get(path));
            return null;
        });
        File file = new File(path);
        // 本来想用ContentType来判断请求是否成功，但是ContentType在响应头里面不正确
        int num = (Objects.nonNull(nums) && nums.length > 0 && nums[0] > 0) ? nums[0] : 1000;
        // 小于500字节的把数据读出来看看
        if (file.length() < num) {
            try {
                try (InputStream inputStream = new FileSystemResource(file).getInputStream()) {
                    byte[] bytes = inputStream.readAllBytes();
                    String str = new String(bytes);
                    ResponseVO<String> responseVO = getFileResponseDtoResponseVO(str, String.class);
                } catch (JSONException e) {
                    // 解析不出来 应该不是json
                } catch (IOException e) {
                    log.error("下载文件失败:", e.getMessage());
                    throw new RuntimeException(e.getMessage());
                }
            } catch (Exception e) {
                downloadFileDelete(file);
                throw e;
            }
        }
        return file;
    }

    /**
     * 未了方便操作，增加一个删除文件的方法
     *
     * @param deleteFile
     */
    public void downloadFileDelete(File deleteFile) {
        if (Objects.nonNull(deleteFile)) {
            try {
                Files.delete(deleteFile.toPath());
            } catch (IOException e) {
                log.error("delete file error-{} :{}", deleteFile.getPath(), e.getMessage());
            }
        }
    }

}
