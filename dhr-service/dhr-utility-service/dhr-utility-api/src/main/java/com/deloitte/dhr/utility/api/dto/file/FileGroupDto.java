package com.deloitte.dhr.utility.api.dto.file;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2020/10/15 12:35
 */
@Data
public class FileGroupDto implements Serializable {

    /**
     * 文件组ID
     */
    @ApiModelProperty(value = "文件组ID", name = "fileGroupId", example = "1")
    private String fileGroupId;

    /**
     * 文件组
     */
    @ApiModelProperty(value = "文件组集合", name = "fileInfoDtos", example = "1")
    private List<FileInfoDto> fileInfoDtos;
}
