package com.deloitte.dhr.utility.api.dto.sms;

import com.alibaba.fastjson2.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * "阿里云短信服务响应"值对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AliYunSmsResponseVo implements Serializable {

    private static final long serialVersionUID = 3550395866646222660L;

    private static final String SUCCESS_CODE = "OK";

    /**
     * 请求状态码
     */
    @JSONField(name = "Code")
    private String code;

    /**
     * 状态码的描述
     */
    @JSONField(name = "Message")
    private String message;

    /**
     * 请求ID
     */
    @JSONField(name = "RequestId")
    private String requestId;

    /**
     * 判断请求是否成功
     *
     * @return true -> 成功 | false -> 失败
     */
    public boolean requestSuccessful() {
        return Objects.equals(this.code, SUCCESS_CODE);
    }


}
