package com.deloitte.dhr.utility.api;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.dto.file.FileRequestDto;
import com.deloitte.dhr.utility.api.dto.file.FileResponseDto;
import com.deloitte.dhr.utility.api.fallback.CommonFileDfsFallBack;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * dfs文件服务对外接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 10/05/2022
 */
@FeignClient(value = "dhr-utility-service", path = "/utility/file/dfs")
public interface CommonFileDfsInterface {

    Logger logger = LoggerFactory.getLogger(CommonFileDfsInterface.class);

    default ResponseVO fallBack(Throwable e){
        if (e instanceof CallNotPermittedException){ logger.error("熔断器已经打开，拒绝访问被保护方法",e); }
        return ResponseVO.fail(e.getMessage());
    }

    /**
     * 上传文件（内部调用）
     *
     * @param fileRequestDto
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "internalUploadFallBack")
    @PostMapping(value = "/internalUpload", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseVO<FileResponseDto> uploadFile(@RequestBody FileRequestDto fileRequestDto);
    default ResponseVO internalUploadFallBack(FileRequestDto fileRequestDto, Throwable e){
        return fallBack(e);
    }

    /**
     * 上传文件（外部调用）
     *
     * @param uploadFile
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "uploadFallBack")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResponseVO<FileResponseDto> uploadFile(@RequestParam(value = "file") MultipartFile uploadFile);
    default ResponseVO uploadFallBack(MultipartFile uploadFile, Throwable e){
        return fallBack(e);
    }

    /**
     * 通过id删除文件
     *
     * @param fileId
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "deleteFileByIdFallBack")
    @GetMapping(value = "/delete")
    ResponseVO<Boolean> deleteFileById(@RequestParam("fileId") String fileId);
    default ResponseVO deleteFileByIdFallBack(String fileId, Throwable e){
        return fallBack(e);
    }

    /**
     * 下载文件(外部调用)-通过id
     *
     * @param fileId
     * @param response
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "downloadFallBack")
    @GetMapping(value = "/download")
    void downloadFileById(@RequestParam("fileId") String fileId, HttpServletResponse response);
    default void downloadFallBack(String fileId, Throwable e){
        fallBack(e);
    }

    /**
     * 批量下载文件为zip压缩格式
     *
     * @param fileIds
     * @param fileName
     * @param response
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "batchDownloadToZipFallBack")
    @GetMapping(value = "/batchDownloadToZip")
    void batchDownloadToZip(@RequestParam("fileIds") List<Long> fileIds, @RequestParam("fileName") String fileName, HttpServletResponse response);
    default void batchDownloadToZipFallBack(List<Long> fileIds, String fileName, HttpServletResponse response, Throwable e){
        fallBack(e);
    }

    /**
     * 批量下载文件(内部调用)
     *
     * @param fileIds
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "internalBatchDownloadFallBack")
    @GetMapping(value = "/inter/batchDownload", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseVO<Map<String, byte[]>> internalBatchDownload(@RequestParam("fileIds") List<Long> fileIds);
    default ResponseVO internalBatchDownloadFallBack(List<Long> fileIds, Throwable e){
        return fallBack(e);
    }

    /**
     * 预览文件(外部调用)-通过id
     *
     * @param fileId
     * @param response
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "previewFileByIdFallBack")
    @GetMapping(value = "/preview")
    void previewFileById(@RequestParam("fileId") String fileId, HttpServletResponse response);
    default void previewFileByIdFallBack(String fileId, HttpServletResponse response, Throwable e){
        fallBack(e);
    }

    /**
     * 下载文件（内部调用）-通过id
     *
     * @param fileId
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "internalDownloadFallBack")
    @GetMapping(value = "/internalDownload", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseVO<Map<String, byte[]>> downloadFileById(@RequestParam("fileId") String fileId);
    default ResponseVO internalDownloadFallBack(String fileId, Throwable e){
        return fallBack(e);
    }

    /**
     * 批量上传文件（内部调用）
     *
     * @param fileRequestDtoList
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "batchUploadFileInnerFallBack")
    @PostMapping(value = "/internalUpload/batch", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseVO<List<FileResponseDto>> batchUploadFileInner(@RequestBody List<FileRequestDto> fileRequestDtoList);
    default ResponseVO batchUploadFileInnerFallBack(List<FileRequestDto> fileRequestDtoList, Throwable e){
        return fallBack(e);
    }

    /**
     * 批量上传文件（外部调用）
     *
     * @param uploadFileList
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "batchUploadFileBack")
    @PostMapping(value = "/upload/batch", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResponseVO<List<FileResponseDto>> batchUploadFile(@RequestParam(value = "file") List<MultipartFile> uploadFileList);
    default ResponseVO batchUploadFileBack(List<MultipartFile> uploadFileList, Throwable e){
        return fallBack(e);
    }

    /**
     * 预览文件
     *
     * @param fileId
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "previewFileFallBack")
    @GetMapping(value = "/previewFile")
    ResponseVO<FileResponseDto> previewFile(@RequestParam("fileId") String fileId);
    default ResponseVO previewFileFallBack(String fileId, Throwable e){
        return fallBack(e);
    }

}
