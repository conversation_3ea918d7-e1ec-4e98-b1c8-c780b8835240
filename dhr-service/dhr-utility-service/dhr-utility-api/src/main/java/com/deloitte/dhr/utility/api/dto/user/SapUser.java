package com.deloitte.dhr.utility.api.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * Comment：sapUser
 * Author： liyong
 * Date：2022-05-27 17:51:04
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("sapUser")
public class SapUser implements Serializable {


    @ApiModelProperty(value = "邮件", name = "email")
    private String email;

    @ApiModelProperty(value = "公司", name = "compname")
    private String compname;

    @ApiModelProperty(value = "用户编码", name = "pernr")
    private String pernr;

    @ApiModelProperty(value = "", name = "zhrBip")
    private String zhrBip;

    /**用户名*/
    @ApiModelProperty(value = "用户名", name = "ename")
    private String ename;

    /***/
    @ApiModelProperty(value = "部门名称", name = "deptname")
    private String deptname;

    /**职位*/
    @ApiModelProperty(value = "职位", name = "plans")
    private String plans;

    /***/
    @ApiModelProperty(value = "公司id", name = "compid")
    private String compid;

    /**电话*/
    @ApiModelProperty(value = "电话", name = "phone")
    private String phone;

    /***/
    @ApiModelProperty(value = "部门id", name = "deptid")
    private String deptid;

    /***/
    @ApiModelProperty(value = "", name = "zzzxt")
    private String zzzxt;

    /***/
    @ApiModelProperty(value = "职位名称", name = "postname")
    private String postname;

    /***/
    @ApiModelProperty(value = "工作名称", name = "jobname")
    private String jobname;

    /***/
    @ApiModelProperty(value = "相片", name = "photo")
    private String photo;
}