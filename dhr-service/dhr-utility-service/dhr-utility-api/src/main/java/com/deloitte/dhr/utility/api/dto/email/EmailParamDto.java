package com.deloitte.dhr.utility.api.dto.email;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Map;

/**
 * 邮件接受参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 10/05/2022
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailParamDto implements Serializable {
    private static final long serialVersionUID = -6485615786387085799L;

    /**
     * 邮件接收者，多个使用逗号分隔
     */
    @Length(max = 255, message = "邮件接收者长度不能超过255位")
    @NotBlank(message = "邮件接收者不能为空")
    private String receivers;
    /**
     * 邮件抄送者，多个使用逗号分隔
     */
    @Length(max = 255, message = "邮件抄送者长度不能超过255位")
    private String copyTo;
    /**
     * 邮件主题
     */
    @NotBlank(message = "邮件主题不能为空")
    private String subject;

    /**
     * 邮件内容
     */
    private String content;

    /**
     * 密送者
     */
    private String bcc;

    /**
     * 邮件附件
     */
    private Map<String, byte[]> fileMap;
}
