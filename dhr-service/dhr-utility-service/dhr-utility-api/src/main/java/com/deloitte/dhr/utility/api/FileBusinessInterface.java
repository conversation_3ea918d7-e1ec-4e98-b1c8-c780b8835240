package com.deloitte.dhr.utility.api;

import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.utility.api.dto.file.FileInfoDto;
import com.deloitte.dhr.utility.api.dto.file.FileResponseDto;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * dfs文件服务对外接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 10/05/2022
 */
@FeignClient(value = "dhr-utility-service", path = "/utility/file/bus")
public interface FileBusinessInterface {

    Logger logger = LoggerFactory.getLogger(FileBusinessInterface.class);

    default ResponseVO fallBack(Throwable e){
        if (e instanceof CallNotPermittedException){ logger.error("熔断器已经打开，拒绝访问被保护方法",e); }
        return ResponseVO.fail(e.getMessage());
    }

    /**
     * 获取传入文件组的唯一ID
     *
     * @param fileInfoDtoList
     * @param fileGroupId
     * @param empCode
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "getFileGroupIdFallBack")
    @PostMapping(value = "/getGroupId")
    ResponseVO<String> getFileGroupId(@RequestBody List<FileInfoDto> fileInfoDtoList, @RequestParam("fileGroupId") String fileGroupId, @RequestParam("empCode") String empCode);
    default ResponseVO getFileGroupIdFallBack(List<FileInfoDto> fileInfoDtoList, String fileGroupId, String empCode, Throwable e){
        return fallBack(e);
    }
    /**
     * 通过文件组ID获取文件组
     *
     * @param fileGroupId
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "getFileGroupFallBack")
    @PostMapping(value = "/getFileGroup")
    ResponseVO<List<FileResponseDto>> getFileGroup(String fileGroupId);
    default ResponseVO getFileGroupFallBack(String fileGroupId, Throwable e){
        return fallBack(e);
    }
    /**
     * 通过文件ID获取文件
     *
     * @param id
     * @return
     */
    @CircuitBreaker(name = "defaultBreaker",fallbackMethod = "findByIdFallBack")
    @PostMapping(value = "/{id}")
    ResponseVO<FileResponseDto> findById(@PathVariable("id") String id);
    default ResponseVO findByIdFallBack(String id, Throwable e){
        return fallBack(e);
    }
}
