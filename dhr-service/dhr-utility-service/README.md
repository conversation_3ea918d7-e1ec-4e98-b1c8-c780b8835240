dhr-utility 公共服务
===============

当前最新版本： master（发布日期：2022-06-20）


项目介绍：
-----------------------------------

基础服务: 邮件服务，短信服务，文件服务，人脸识别
```
1.文件采用MiniO,各个环境可以用bucketName: "dhr-dev"，进行区分
2.短信集成阿里短信服务，邮件使用java.mail 配置对应公司的邮箱服务器及可以使用
3.阿里人脸识别
```
```
dhr-utility-api：
公共对外接口
dhr-utility-provider：
公共服务提供者
```
### 配置参数
```
minio:
  endpoint: 172.##.##.##
  port: 9000
  accessKey: #######
  secretKey: #######
  secure: false
  bucketName: "dhr-dev"
```   

### 目录模块
```
├─com.deloitte.dhr.collection
│  ├─config : 配置文件
│  ├─controller : controller服务层
│  ├─elasticsearch : es存储实现
│  ├─mapper : mapper文件
│  ├─model : 实体模型
│  ├─service : service服务层
│  ├─util : 工具类


```

#####
备注
----


