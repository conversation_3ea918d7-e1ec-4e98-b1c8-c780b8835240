package com.deloiite.dhr.gateway.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * Nacos连接信息日志打印组件
 * 在应用启动完成后打印Nacos连接信息，包括IP、命名空间、组、端口
 *
 * <AUTHOR> Gateway Service
 * @since 2024
 */
@Component
public class NacosConnectionLogger implements ApplicationRunner {

    private static final Logger logger = LoggerFactory.getLogger(NacosConnectionLogger.class);

    @Value("${nacos.server-addr:localhost}")
    private String nacosServerAddr;

    @Value("${nacos.port:8848}")
    private String nacosPort;

    @Value("${nacos.namespace:default}")
    private String nacosNamespace;

    @Value("${spring.profiles.active:dev}")
    private String profileActive;

    @Value("${nacos.username:}")
    private String nacosUsername;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        printNacosConnectionInfo();
    }

    /**
     * 打印Nacos连接信息
     */
    private void printNacosConnectionInfo() {
        try {
            logger.info("==========================================================");
            logger.info("              Nacos连接配置信息");
            logger.info("==========================================================");
            logger.info("Nacos服务器IP地址    : {}", nacosServerAddr);
            logger.info("Nacos服务器端口      : {}", nacosPort);
            logger.info("Nacos完整连接地址    : {}:{}", nacosServerAddr, nacosPort);
            logger.info("Nacos命名空间        : {}", nacosNamespace);
            logger.info("Nacos服务分组        : {}", profileActive);
            logger.info("Nacos用户名          : {}", nacosUsername.isEmpty() ? "未设置" : nacosUsername);
            logger.info("==========================================================");
            logger.info("Gateway服务已成功连接到Nacos配置中心！");
            logger.info("==========================================================");
        } catch (Exception e) {
            logger.error("打印Nacos连接信息时发生错误: {}", e.getMessage(), e);
        }
    }
}
