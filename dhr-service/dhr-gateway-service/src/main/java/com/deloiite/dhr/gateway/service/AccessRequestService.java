package com.deloiite.dhr.gateway.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.deloiite.dhr.gateway.entity.Token;
import com.deloitte.dhr.common.base.utils.CryptoUtil;
import org.springframework.http.HttpCookie;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.MultiValueMap;

import java.util.Date;
import java.util.List;

/**
 * Token效验服务
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 24/04/2022
 */
public class AccessRequestService {

    /**
     * 是否有效token
     *
     * @author: LiYong
     * @version: 1.0
     * @date: 24/04/2022
     * @param token
     * @return
     */
    public static boolean isValidatedToken(String token) {
        if(!StrUtil.isEmpty(token)){
            Token tokenObject = JSON.parseObject(CryptoUtil.decode64(CryptoUtil.DEFAULT_SECRET_KEY, token), Token.class);
            return !StrUtil.isEmpty(tokenObject.getOaNumber()) && tokenObject.getExpireTime().getTime() > (new Date()).getTime();
        }
        return false;
    }


    /**
     * 获取cookie
     *
     * @author: LiYong
     * @version: 1.0
     * @date: 24/04/2022
     * @param request
     * @param name
     * @return
     */
    public static String getCookieByName(ServerHttpRequest request, String name) {
        MultiValueMap<String, HttpCookie> cookies = request.getCookies();
        if (cookies == null) {
            return null;
        }
        List<HttpCookie> httpCookies = cookies.get(name);
        if (httpCookies == null || httpCookies.isEmpty()) {
            return null;
        }
        return httpCookies.get(0).getValue();
    }

    /**
     * 是否包含当前url
     *
     * @author: LiYong
     * @version: 1.0
     * @date: 24/04/2022
     * @param urlArray
     * @param url
     * @return
     */
    public static boolean isContainsURL(String urlArray [],String url ) {
        for (String stringObj : urlArray) {
            if(url.contains(stringObj)){
                return true;
            }
        }
        return false;
    }
}
