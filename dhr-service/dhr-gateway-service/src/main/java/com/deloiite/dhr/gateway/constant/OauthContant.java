package com.deloiite.dhr.gateway.constant;

/**
 * 授权公共常量
 *
 * @author: <PERSON>Yong
 * @version: 1.0
 * @date: 24/04/2022
 */
public class OauthContant {

    /**鉴权标识Authorization*/
    public static final String AUTHORIZE_TOKEN = "Authorization";
    /**授权信息*/
    public static final String OAUTH_AUTHORITIES = "authorities";
    /**鉴权标识bearer*/
    public static final String AUTHORIZE_BEARER = "bearer";
    /**登陆名*/
    public static final String OAUTH_USER = "user_name";
    /**用户信息*/
    public static final String OAUTH_USER_DETAIL = "USER_DETAIL";
    /**JSON版本TOKEN*/
    public static final String OAUTH_JSON_TOKEN = "JSON_TOKEN";
    /**令牌加密言*/
    public static final String OAUTH_SECRET="123456";
    /**接收加密标识*/
    public static final String ACCEPT_SECRET = "acceptSecret";
    /**是接收加密标识*/
    public static final String ACCEPT_YES = "yes";
    /**日志traceId*/
    public static final String ACCEPT_TRACEID = "traceId";
    /**cookie*/
    public static final String DHR_TOKEN = "DHR_TOKEN";
}
