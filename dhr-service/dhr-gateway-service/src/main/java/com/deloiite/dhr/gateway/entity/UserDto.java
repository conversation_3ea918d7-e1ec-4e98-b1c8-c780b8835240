package com.deloiite.dhr.gateway.entity;

import lombok.Data;

import java.util.List;

/**
 * User Dto
 *
 * @author: <PERSON><PERSON><PERSON>
 * @version: 1.0
 * @date: 24/04/2022
 */
@Data
public class UserDto {
    /**id*/
    private String id;
    /**用户名*/
    private String username;
    /**密码*/
    private String password;
    /**全名*/
    private String fullname;
    /**电话号码*/
    private String mobile;
    /**邮件*/
    private String email;
    /**菜单权限*/
    private List<Object> menuList;
    /**数据权限*/
    private List<Object> dataAuthList;
    /**授权字符串*/
    private String authorizationString;
    /**员工编码*/
    private String employeeNumber;
    /**oa名称*/
    private String oaName;
    /**界面编码*/
    private String pageCode;

}
