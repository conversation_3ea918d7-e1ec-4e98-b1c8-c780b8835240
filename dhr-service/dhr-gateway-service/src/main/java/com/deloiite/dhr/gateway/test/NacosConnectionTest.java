package com.deloiite.dhr.gateway.test;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Nacos连接信息测试类
 * 用于测试Nacos连接信息日志打印功能
 *
 * <AUTHOR> Gateway Service
 * @since 2024
 */
@Component
public class NacosConnectionTest {

    private static final Logger logger = LoggerFactory.getLogger(NacosConnectionTest.class);

    @Value("${nacos.server-addr:localhost}")
    private String nacosServerAddr;

    @Value("${nacos.port:8848}")
    private String nacosPort;

    @Value("${nacos.namespace:default}")
    private String nacosNamespace;

    @Value("${spring.profiles.active:dev}")
    private String profileActive;

    @Value("${nacos.username:}")
    private String nacosUsername;

    @PostConstruct
    public void testNacosConnectionInfo() {
        logger.info("==========================================================");
        logger.info("              Nacos连接配置信息测试");
        logger.info("==========================================================");
        logger.info("Nacos服务器IP地址    : {}", nacosServerAddr);
        logger.info("Nacos服务器端口      : {}", nacosPort);
        logger.info("Nacos完整连接地址    : {}:{}", nacosServerAddr, nacosPort);
        logger.info("Nacos命名空间        : {}", nacosNamespace);
        logger.info("Nacos服务分组        : {}", profileActive);
        logger.info("Nacos用户名          : {}", nacosUsername.isEmpty() ? "未设置" : nacosUsername);
        logger.info("==========================================================");
        logger.info("Nacos连接信息测试完成！");
        logger.info("==========================================================");
    }
}
