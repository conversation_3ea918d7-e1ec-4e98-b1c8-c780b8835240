package com.deloiite.dhr.gateway.test;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 简单的Nacos连接信息测试类
 * 使用ApplicationReadyEvent确保在应用完全启动后执行
 *
 * <AUTHOR> Gateway Service
 * @since 2024
 */
@Component
public class SimpleNacosTest {

    private static final Logger logger = LoggerFactory.getLogger(SimpleNacosTest.class);

    @Value("${nacos.server-addr:localhost}")
    private String nacosServerAddr;

    @Value("${nacos.port:8848}")
    private String nacosPort;

    @Value("${nacos.namespace:default}")
    private String nacosNamespace;

    @Value("${spring.profiles.active:dev}")
    private String profileActive;

    @Value("${nacos.username:}")
    private String nacosUsername;

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        logger.info("==========================================================");
        logger.info("              Nacos连接配置信息测试");
        logger.info("==========================================================");
        logger.info("Nacos服务器IP地址    : {}", nacosServerAddr);
        logger.info("Nacos服务器端口      : {}", nacosPort);
        logger.info("Nacos完整连接地址    : {}:{}", nacosServerAddr, nacosPort);
        logger.info("Nacos命名空间        : {}", nacosNamespace);
        logger.info("Nacos服务分组        : {}", profileActive);
        logger.info("Nacos用户名          : {}", nacosUsername.isEmpty() ? "未设置" : nacosUsername);
        logger.info("==========================================================");
        logger.info("Nacos连接信息测试完成！");
        logger.info("==========================================================");
    }
}
