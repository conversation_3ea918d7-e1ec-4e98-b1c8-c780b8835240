package com.deloiite.dhr.gateway.fliter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.deloiite.dhr.gateway.constant.CommonConstant;
import com.deloiite.dhr.gateway.constant.OauthContant;
import com.deloiite.dhr.gateway.service.AccessRequestService;
import com.deloitte.dhr.common.base.utils.EncryptUtil;
import com.deloitte.dhr.common.base.utils.UrlUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpCookie;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.MalformedURLException;
import java.net.URI;
import java.util.*;

/**
 * 全局过滤器 :用于鉴权(获取令牌 解析 判断)
 */
@Component
@RefreshScope
@Slf4j
public class AuthorizeFilter implements GlobalFilter, Ordered {

    /**
     * 不拦截的服务
     */
    @Value("${excludePathAuth.urls}")
    public String[] excludePathAuthURLs;
    /**
     * 内部服务
     */
    @Value("${internalService.urls}")
    public String[] internalServiceUrl;
    /**
     * 暴露给第三方的服务
     */
    @Value("${exposureServices.urls}")
    public String[] exposureServicesUrl;
    /**
     * 暴露给第三方的服务key
     */
    @Value("${exposureServices.secretKey}")
    public String exposureServicesSecretKey;
    /**
     * 加密key
     */
    @Value("${mobileAuth.secretKey}")
    public String secretKeyM;
    /**
     * 手机端路径
     */
    @Value("${mobileAuth.urls}")
    public String[] mobileAuthUrls;
    /**
     * 基础路径
     */
    @Value("${projectGroup.basicurl}")
    public String[] basicURL;
    /**
     * 人才路径
     */
    @Value("${projectGroup.rcurl}")
    public String[] personURL;
    /**
     * 是否解密配置
     */
    @Value("${SM4.enable}")
    public Boolean SM4Eable;

    /**
     * 访问类型
     */
    private static final String ACCESS_MODE = "Access-Mode";

    /**
     * 签名访问
     */
    private static final String SIGN="sign";

    public static final String sscToken = "CEimLeR5IVmIchfN9Fq1VHvVoFshxxGIkR0euzJKGxdNpt9Ho9nL9";

    public static final String sccAccessToken="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOlsiYWN0aW9uIiwib3JkZXIiXSwiZXhwIjoxODM1Njg3MjI5LCJ1c2VyX25hbWUiOiJ7XCJkZXBhcnRtZW50XCI6XCLkurrlipvotYTmupDpg6hcIixcImVtYWlsXCI6XCJjcWluZ0BkZWxvaXR0ZS5jb20uY25cIixcImVtcGxveWVlTnVtYmVyXCI6XCIwMDAwMDAwMlwiLFwiZnVsbG5hbWVcIjpcIueOi-WImlwiLFwiaWRcIjpcIjVcIixcIm1vYmlsZVwiOlwiMTU3NjgyNTYwODVcIixcIm9hTmFtZVwiOlwiMDAwMDAwMDJcIixcInBhZ2VDb2RlXCI6XCIwMDAwMDAwMlwiLFwicGFzc3dvcmRcIjpcIiQyYSQxMCRJQ0tnOWFLZFp3akVwUTViMkNDVEh1cDFXRVU1UEhISEdGVms5Zld6MHpVVUg2NWlURy9xR1wiLFwicG9zaXRpb25cIjpcIkhS5Li7566hXCIsXCJ1c2VybmFtZVwiOlwiMDAwMDAwMDJcIn0iLCJqdGkiOiI3NWM0ZDdkMS05ZDVkLTRlODUtYTViMy00MDlhMjJjYTY4NTkiLCJjbGllbnRfaWQiOiJyZXNvdXJjZTEiLCJzY29wZSI6WyJST0xFX0FETUlOIiwiUk9MRV9VU0VSIiwiUk9MRV9BUEkiLCJhbGwiXX0.hXRmFxF4nmcmSVVdymr0UK1obB7Vzy4NyuJVXFHDBxc";


    /**
     * 全局过滤器，获取令牌，规则路由，效验权限
     *
     * @param exchange
     * @param chain
     * @return Mono<Void>
     * @author: liyong
     * @version: 1.0
     * @date: 24/04/2022
     */
    @SneakyThrows
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

        ServerHttpRequest newRequest = null;
        //获取请求对象
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        HttpMethod method = request.getMethod();

        //获取请求路径
        String requestPath = request.getURI().getPath();

        //获取响应对象
        String acceptSecret = request.getHeaders().getFirst(OauthContant.ACCEPT_SECRET);

        String uuid = UUID.randomUUID().toString();
        newRequest = exchange.getRequest().mutate().header(OauthContant.ACCEPT_TRACEID, uuid).build();
        exchange = exchange.mutate().request(newRequest).build();

        //解密加密的参数数据
        exchange = getParamExchange(exchange, request, method, acceptSecret);

        //如果为签名访问则放行
        if (SIGN.equals(request.getHeaders().getFirst(ACCESS_MODE))){
            return chain.filter(exchange);
        }

        //白名单放行判断
        if (extracted(request, requestPath)) {
            return chain.filter(exchange);
        }

        //取令牌数据
        String token = getToken(request, requestPath, response);

        String sscWebToken = request.getQueryParams().getFirst("sscToken");

        if (!StrUtil.isEmpty(sscWebToken)&&sscToken.equals(sscWebToken)) {
            token=sccAccessToken;
        }

        if (StrUtil.isEmpty(token)) {
            //如果没有数据 结束 未找到token
            response.setStatusCode(HttpStatus.UNAUTHORIZED);
            JSONObject jsonMessage = new JSONObject();
            jsonMessage.put(CommonConstant.DATA, "");
            jsonMessage.put(CommonConstant.MESSAGE, "token丢失");
            jsonMessage.put(CommonConstant.CODE, HttpStatus.UNAUTHORIZED.value());
            DataBuffer wrap = response.bufferFactory().wrap(jsonMessage.toJSONString().getBytes());
            return response.writeWith(Mono.just(wrap));
        }

        // 解析令牌数据 ( 判断解析是否正确,正确 就放行 ,否则 结束)
        try {
            //解析令牌封装用户信息
            Map<String, Object> jsonToken = getJsonToken(token);
            //把身份信息和权限信息放在json中，加入http的header中,转发给微服务
            newRequest = exchange.getRequest().mutate()
                    .header(OauthContant.OAUTH_JSON_TOKEN, EncryptUtil.encodeUTF8StringBase64(JSON.toJSONString(jsonToken)))
                    .build();

        } catch (Exception e) {
            e.printStackTrace();
            //解析失败
            JSONObject jsonMessage = new JSONObject();
            jsonMessage.put(CommonConstant.DATA, "");
            jsonMessage.put(CommonConstant.MESSAGE, "解析token失败");
            jsonMessage.put(CommonConstant.CODE, HttpStatus.UNAUTHORIZED.value());
            response.setStatusCode(HttpStatus.UNAUTHORIZED);
            DataBuffer wrap = response.bufferFactory().wrap(jsonMessage.toJSONString().getBytes());
            return response.writeWith(Mono.just(wrap));
        }
        return chain.filter(exchange.mutate().request(newRequest).build());
    }

    //解析令牌封装用户信息
    private static Map<String, Object> getJsonToken(String token) {
        Algorithm algorithm = Algorithm.HMAC256(OauthContant.OAUTH_SECRET);
        JWTVerifier verifier = JWT.require(algorithm).build();
        DecodedJWT jwt = verifier.verify(token);
        Map<String, Claim> claims = jwt.getClaims();

        //取出用户身份信息
        String principal = claims.get(OauthContant.OAUTH_USER).asString();

        //取出用户权限
        List<String> authorities = null;
        Claim claim = claims.get(OauthContant.OAUTH_AUTHORITIES);

        if (claim != null) {
            authorities = claim.asList(String.class);
        }

        Map<String, Object> jsonToken = new HashMap<>();
        jsonToken.put(OauthContant.OAUTH_USER_DETAIL, principal);
        jsonToken.put(OauthContant.OAUTH_AUTHORITIES, authorities);
        jsonToken.put(OauthContant.AUTHORIZE_BEARER, token);
        return jsonToken;
    }

    /**
     * 获取token
     *
     * @param request
     * @param requestPath
     * @param response
     * @return
     */
    private String getToken(ServerHttpRequest request, String requestPath, ServerHttpResponse response) {
        String token = request.getHeaders().getFirst(OauthContant.AUTHORIZE_TOKEN);
        if (!StrUtil.isEmpty(token)) {
            token = token.split(" ")[1];
        } else {
            MultiValueMap<String, HttpCookie> mulCookies = request.getCookies();
            for (String key : mulCookies.keySet()) {
                List<HttpCookie> cookies = mulCookies.get(key);
                for (HttpCookie cookie : cookies) {
                    if (StrUtil.equals(OauthContant.DHR_TOKEN, cookie.getName())) {
                        token = cookie.getValue();
                        break;
                    }
                }

            }
        }
        return token;
    }

    /**
     * 白名单放行判断
     *
     * @param request
     * @param requestPath
     * @return
     */
    private boolean extracted(ServerHttpRequest request, String requestPath) {
        String extranetToken = AccessRequestService.getCookieByName(request, CommonConstant.DHR_EXTRANET_TOKEN);
        String secretKey = AccessRequestService.getCookieByName(request, CommonConstant.AUTH_SECRET_KE);
        if (AccessRequestService.isContainsURL(basicURL, requestPath)) {

            if (AccessRequestService.isContainsURL(excludePathAuthURLs, requestPath) || AccessRequestService.isContainsURL(internalServiceUrl, requestPath)) {
                return true;
            } else if ((requestPath.contains(CommonConstant.EXTRANET_REQUEST_URL) || requestPath.contains(CommonConstant.DOWNLOAD_REQUEST_URL)) && AccessRequestService.isValidatedToken(extranetToken)) {//用于验证外网token逻辑
                return true;
            } else if (AccessRequestService.isContainsURL(exposureServicesUrl, requestPath) && !StrUtil.isEmpty(secretKey) && secretKey.equals(exposureServicesSecretKey)) {
                return true;
            } else if (AccessRequestService.isContainsURL(mobileAuthUrls, requestPath)
                    && !StrUtil.isEmpty(secretKeyM) && secretKeyM.equals(secretKey)) {
                return true;
            }
            //判断 是否为 是否放行白名单 是否为登录的URL 如果不是  权限校验
            String requestURL = request.getURI().getPath();
            if (Arrays.stream(excludePathAuthURLs).anyMatch(url -> requestURL.startsWith(url))) {
                return true;
            }
        } else if (AccessRequestService.isContainsURL(personURL, requestPath)) {
            if (AccessRequestService.isContainsURL(excludePathAuthURLs, requestPath) || AccessRequestService.isContainsURL(internalServiceUrl, requestPath)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 参数加解密
     *
     * @param exchange
     * @param request
     * @param method
     * @param acceptSecret
     * @return
     * @throws MalformedURLException
     */
    private ServerWebExchange getParamExchange(ServerWebExchange exchange, ServerHttpRequest request, HttpMethod method, String acceptSecret) throws MalformedURLException {
        ServerHttpRequest newRequest;
        if (HttpMethod.GET.equals(method) && SM4Eable && OauthContant.ACCEPT_YES.equals(acceptSecret) && (request.getURI().toURL().toString().indexOf("?") != -1)) {
            String requestUrlstring = request.getURI().toURL().toString();
            String UrlAgrString = UrlUtils.getUrlAgrString(requestUrlstring);
            String preUrlString = UrlUtils.getUrlString(requestUrlstring);
            String decryptUrlString = EncryptUtil.SM4DecryptStr(UrlAgrString);
            URI newUri = URI.create(preUrlString + decryptUrlString);
            newRequest = exchange.getRequest().mutate().uri(newUri).build();
            exchange = exchange.mutate().request(newRequest).build();
        }
        return exchange;
    }

    @Override
    public int getOrder() {
        return 0;
    }

}
