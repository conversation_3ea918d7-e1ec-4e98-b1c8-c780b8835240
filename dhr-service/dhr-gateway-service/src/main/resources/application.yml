# Nacos配置中心连接配置
nacos:
  server-addr: ${config_nacos_serveraddr:nacos}
  port: ${config_nacos_port:8848}
  namespace: ${config_nacos_namespace:bcabaf9e-e845-4cea-8f2f-381026ee0c56}
  username: ${config_nacos_username:nacos}
  password: ${config_nacos_password:nacos}

# Spring框架配置
spring:
  profiles:
    active: ${config_profile:dev}
  application:
    name: dhr-gateway-service
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 50MB

  # MVC配置 - Swagger兼容性配置
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  # Nacos服务发现和配置管理
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.server-addr}:${nacos.port}
        namespace: ${nacos.namespace}
        group: ${spring.profiles.active}
        username: ${nacos.username}
        password: ${nacos.password}
      config:
        file-extension: yaml
        server-addr: ${nacos.server-addr}:${nacos.port}
        group: ${spring.profiles.active}
        enabled: ${config.enable:true}
        namespace: ${nacos.namespace}
        username: ${nacos.username}
        password: ${nacos.password}
        refresh-enabled: true

  # 配置导入
  config:
    import:
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

# 服务端口配置
server:
  port: ${config_server_port:9110}

# Hystrix熔断器配置
hystrix:
  threadpool:
    default:
      coreSize: 50
      maximumSize: 10000
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
  command:
    default:
      execution:
        timeout:
          enabled: false
        isolation:
          thread:
            timeoutInMilliseconds: 3600000

# Ribbon负载均衡配置
ribbon:
  ConnectTimeout: 900000
  ReadTimeout: 900000

# 排除认证的路径配置（临时配置，应该从Nacos获取）
excludePathAuth:
  urls: /actuator/**,/webjars/**,/swagger-ui/**,/v3/api-docs/**

# 内部服务配置
internalService:
  urls: /internal/**,/health/**

# 暴露给第三方的服务配置
exposureServices:
  urls: /api/external/**
  secretKey: external-secret-key

# 手机端认证配置
mobileAuth:
  secretKey: mobile-secret-key
  urls: /mobile/**

# 项目组配置
projectGroup:
  basicurl: /basic/**
  rcurl: /rc/**

# SM4加密配置
SM4:
  enable: false

# 日志配置
logging:
  file:
    name: logs/${spring.application.name}/info/log_info.log
  level:
    root: info  # 输出日志级别
