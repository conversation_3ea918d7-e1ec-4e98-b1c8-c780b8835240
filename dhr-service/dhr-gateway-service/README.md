dhr-gateway-service 网关服务
===============

当前最新版本： master（发布日期：2022-06-20）


项目介绍：
-----------------------------------
```
网关服务:请求路由，get请求加解密，令牌认证，请求白名单控制

1. 采用国密SM4对称加密方式，前后端参数加解密，工具在com.deloiite.dhr.gateway.util.SM4Utils
2. 微服务路由配置，白名单配置，直接在nacos配置中心配置，发布不用重启服务就能生效
3. 认证令牌会从header，cookie，request尝试获得令牌信息进行效验
```


### 目录模块
```
├─com.deloiite.dhr.gateway
│  ├─constant : 常量
│  ├─entity : 简单实体
│  ├─fliter : 网关全局过滤器
│  ├─service : 效验服务
│  ├─util : 工具类



```

#####
备注
----


