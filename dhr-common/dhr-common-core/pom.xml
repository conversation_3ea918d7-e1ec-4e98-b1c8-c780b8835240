<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/1.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/1.0.0 http://maven.apache.org/xsd/maven-1.0.0.xsd">
    <parent>
        <artifactId>dhr-common</artifactId>
        <groupId>com.deloitte.dhr</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>dhr-common-core</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-common-bean</artifactId>
        </dependency>
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-swagger-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2-extension</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2-extension-spring5</artifactId>
        </dependency>
    </dependencies>

</project>