package com.deloitte.dhr.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;

/**
 * RestTemplate
 *
 * @author: <PERSON><PERSON><PERSON>
 * @version: 1.0
 * @date: 24/05/2022
 */
@Configuration
public class RestemplateConfig {
    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}