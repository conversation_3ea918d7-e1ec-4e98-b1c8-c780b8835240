package com.deloitte.dhr.common.feign;

import com.alibaba.fastjson2.JSON;
import com.deloitte.dhr.common.base.constants.CommonConstant;
import com.deloitte.dhr.common.base.constants.OauthConstant;
import com.deloitte.dhr.common.base.pojo.UserDto;
import com.deloitte.dhr.common.base.utils.EncryptUtil;
import com.deloitte.dhr.common.base.utils.LanguageUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * feign调用之前拦截
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 11/05/2022
 */
@Slf4j
public class FeignInterceptor implements RequestInterceptor {


    @Override
    public void apply(RequestTemplate requestTemplate) {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        if (authentication != null) {
            Map<String, Object> jsonToken = new HashMap<>(16);
            UserDto userDto=(UserDto)authentication.getPrincipal();
            jsonToken.put(OauthConstant.OAUTH_USER_DETAIL, authentication.getPrincipal());
            List<String> authorities=authentication.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList());
            jsonToken.put(OauthConstant.OAUTH_AUTHORITIES, authorities);
            //把身份信息和权限信息放在json中，加入http的header中,转发给微服务
            requestTemplate.header(OauthConstant.JSON_TOKEN, EncryptUtil.encodeUTF8StringBase64(JSON.toJSONString(jsonToken)));
            requestTemplate.header(OauthConstant.AUTHORIZE_TOKEN,OauthConstant.AUTHORIZE_BEARER+userDto.getAuthorizationString());
        }
        requestTemplate.header(CommonConstant.ACCEPT_LANG, LanguageUtil.getLanguage());
        requestTemplate.header(OauthConstant.ACCEPT_TRACEID, requestTemplate.headers().get(OauthConstant.ACCEPT_TRACEID));
        requestTemplate.header(OauthConstant.PAGE_CODE, requestTemplate.headers().get(OauthConstant.PAGE_CODE));
        requestTemplate.header(OauthConstant.AUTH_FLAG, requestTemplate.headers().get(OauthConstant.AUTH_FLAG));
    }
}
