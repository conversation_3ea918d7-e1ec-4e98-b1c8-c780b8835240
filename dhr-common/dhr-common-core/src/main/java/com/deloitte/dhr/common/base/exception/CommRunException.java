package com.deloitte.dhr.common.base.exception;

import com.deloitte.dhr.common.base.utils.MessageUtils;

/**
 * 通用自定义异常自定义异常
 *
 * @author: Liu
 * @date: 2020年9月8日 下午3:26:52
 */
public class CommRunException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    private String code;

    public CommRunException(String message) {
        super(MessageUtils.toLocale(message) == null ? message : MessageUtils.toLocale(message));
    }

    public CommRunException(String message, String... args) {
        super(MessageUtils.toLocale(message, args) == null ? message : MessageUtils.toLocale(message, args));
    }

    public CommRunException(String code, String message) {
        super(MessageUtils.toLocale(message) == null ? message : MessageUtils.toLocale(message));
        this.code = code;
    }

}
