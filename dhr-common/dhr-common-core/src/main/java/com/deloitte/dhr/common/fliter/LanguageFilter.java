package com.deloitte.dhr.common.fliter;

import com.deloitte.dhr.common.base.constants.CommonConstant;
import com.deloitte.dhr.common.base.utils.LanguageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Locale;

/**
 * <AUTHOR>
 * @version 1.0
 **/
@Component
@Slf4j
public class LanguageFilter extends OncePerRequestFilter {


    @Override
    protected void doFilterInternal(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Filter<PERSON>hain filterChain) throws ServletException, IOException {
        //解析出头中的acceptLanguage
        String acceptLanguage = httpServletRequest.getHeader(CommonConstant.ACCEPT_LANG);
        if(StringUtils.isBlank(acceptLanguage)){
            acceptLanguage = httpServletRequest.getHeader(CommonConstant.LANG);
        }
        acceptLanguage = LanguageUtil.getLanguageString(acceptLanguage);
        Locale.setDefault(new Locale(acceptLanguage));
        httpServletResponse.setHeader(CommonConstant.ACCEPT_LANG, acceptLanguage);
        LocaleContextHolder.setLocale(new Locale(acceptLanguage));
        LocaleContextHolder.setDefaultLocale(new Locale(acceptLanguage));
        LanguageUtil.threadLocal.set(acceptLanguage);
        filterChain.doFilter(httpServletRequest, httpServletResponse);
    }


}