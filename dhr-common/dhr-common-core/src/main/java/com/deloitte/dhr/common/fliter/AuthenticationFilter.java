package com.deloitte.dhr.common.fliter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.deloitte.dhr.common.base.constants.OauthConstant;
import com.deloitte.dhr.common.base.pojo.UserDto;
import com.deloitte.dhr.common.base.utils.EncryptUtil;
import com.deloitte.dhr.common.base.utils.TraceIdUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 验权过滤器
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 24/04/2022
 */
@Component
@Slf4j
public class AuthenticationFilter extends OncePerRequestFilter {

    /**
     * 重写过滤Token验权
     *
     * @author: liyong
     * @version: 1.0
     * @date: 24/04/2022
     * @param httpServletRequest
     * @param httpServletResponse
     * @param filterChain
     * @return
     */
    @SneakyThrows
    @Override
    protected void doFilterInternal(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, FilterChain filterChain) throws ServletException, IOException {
        try{
            //解析出头中的token
            String token = httpServletRequest.getHeader(OauthConstant.JSON_TOKEN);
            if (StringUtils.isBlank(token)) {
                String tokenWeb = getToken(httpServletRequest);
                if (StringUtils.isNotBlank(tokenWeb)) {
                    Map<String, Object> jsonToken = getJsonToken(tokenWeb);
                    if (jsonToken != null) {
                        token = EncryptUtil.encodeUTF8StringBase64(JSON.toJSONString(jsonToken));
                    }
                }
            }

            if (StringUtils.isNotBlank(token)) {
                String json = EncryptUtil.decodeUTF8StringBase64(token);
                //将token转成json对象
                JSONObject jsonObject = JSON.parseObject(json);
                //用户身份信息
                UserDto userDTO = JSON.parseObject(jsonObject.getString(OauthConstant.OAUTH_USER_DETAIL), UserDto.class);
                //用户权限
                String bearer = jsonObject.getString(OauthConstant.AUTHORIZE_BEARER);

                if (StringUtils.isEmpty(userDTO.getAuthorizationString())) {
                    userDTO.setAuthorizationString(bearer);
                }

                JSONArray authoritiesArray = jsonObject.getJSONArray(OauthConstant.OAUTH_AUTHORITIES);
                String[] authorities = new String[0];
                if (authoritiesArray != null) {
                    authorities = authoritiesArray.toArray(new String[authoritiesArray.size()]);
                }
                //将用户信息和权限填充 到用户身份token对象中
                UsernamePasswordAuthenticationToken authenticationToken
                        = new UsernamePasswordAuthenticationToken(userDTO, null, AuthorityUtils.createAuthorityList(authorities));
                authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(httpServletRequest));
                //将authenticationToken填充到安全上下文
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            }


            MDC.put(OauthConstant.ACCEPT_TRACEID, TraceIdUtil.getTraceId());
            filterChain.doFilter(httpServletRequest, httpServletResponse);

        } finally {
            // 清理SecurityContext，避免线程复用时的用户信息串了污染
            SecurityContextHolder.clearContext();
            MDC.clear(); // 同时清理MDC
        }
    }

    //解析令牌封装用户信息
    private static Map<String, Object> getJsonToken(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(OauthConstant.OAUTH_SECRET);
            JWTVerifier verifier = JWT.require(algorithm).build();
            DecodedJWT jwt = verifier.verify(token);
            Map<String, Claim> claims = jwt.getClaims();

            //取出用户身份信息
            String principal = claims.get(OauthConstant.OAUTH_USER).asString();

            //取出用户权限
            List<String> authorities = null;
            Claim claim = claims.get(OauthConstant.OAUTH_AUTHORITIES);

            if (claim != null) {
                authorities = claim.asList(String.class);
            }

            Map<String, Object> jsonToken = new HashMap<>();
            jsonToken.put(OauthConstant.OAUTH_USER_DETAIL, principal);
            jsonToken.put(OauthConstant.OAUTH_AUTHORITIES, authorities);
            jsonToken.put(OauthConstant.AUTHORIZE_BEARER, token);
            return jsonToken;
        } catch (Exception e) {
            log.error(e.toString());
        }
        return null;
    }

    private String getToken(HttpServletRequest request) {
        String token = request.getHeader(OauthConstant.AUTHORIZE_TOKEN);
        if (!StringUtils.isEmpty(token)) {
            String[] tokenSplit = token.split(" ");
            if (tokenSplit.length > 1) {
                token = tokenSplit[1];
                return token;
            }
        }
        return null;
    }

}
