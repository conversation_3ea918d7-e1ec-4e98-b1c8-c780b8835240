package com.deloitte.dhr.common.base.utils;

import com.deloitte.dhr.common.base.pojo.UserDto;
import lombok.extern.slf4j.Slf4j;

/**
 * 构建全局使用的RedisKey
 *
 * @author: gpp
 * @version: 1.0
 * @date: 30/03/2023
 */
@Slf4j
public class BuildRedisUtils {


    private static final String IV_MOBILE = "IV_MOBILE";
    private static final String IV_PC = "IV_PC";
    private static final String CONNECTOR_PREFIX = "_";

    /**
     * 构造key
     * @param isMobile
     * @return
     */
    public static String buildKey(Boolean isMobile) {
        UserDto userDto = LoginUtil.getLoginUser();
        String authorization = userDto.getAuthorizationString();
        String redisKey = MD5Util.encrypt(authorization) + CONNECTOR_PREFIX + IV_PC + CONNECTOR_PREFIX + LanguageUtil.getLanguage();
        if (isMobile) {
            redisKey = MD5Util.encrypt(authorization) + CONNECTOR_PREFIX + IV_MOBILE +
                    CONNECTOR_PREFIX + LanguageUtil.getLanguage();
        }
        return redisKey;
    }

}
