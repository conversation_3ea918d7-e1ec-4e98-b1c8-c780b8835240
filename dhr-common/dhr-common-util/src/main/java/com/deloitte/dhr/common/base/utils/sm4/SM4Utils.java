package com.deloitte.dhr.common.base.utils.sm4;

import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SM4 工具
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 24/04/2022
 */
public class SM4Utils
{
    private String secretKey = "72DQ567ABC12E45Q";
    private String iv = "";
    private boolean hexString = false;

    private static final String ECB_PATTERN = "\\s*|\t|\r|\n";
    private static final String CBC_PATTERN = "\\s*|\t|\r|\n";

    public SM4Utils()
    {
    }

    public String encryptData_ECB(String plainText)
    {
        try
        {
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true;
            ctx.mode = SM4.SM4_ENCRYPT;

            byte[] keyBytes;
            keyBytes = secretKey.getBytes();
            SM4 sm4 = new SM4();
            sm4.sm4_setkey_enc(ctx, keyBytes);
            byte[] encrypted = sm4.sm4_crypt_ecb(ctx, plainText.getBytes("UTF-8"));
            String cipherText =Base64.getEncoder().encodeToString(encrypted);

            if (cipherText != null && cipherText.trim().length() > 0)
            {
                Pattern p = Pattern.compile(ECB_PATTERN);
                Matcher m = p.matcher(cipherText);
                cipherText = m.replaceAll("");
            }
            return cipherText;
        }
        catch (Exception e)
        {
            e.printStackTrace();
            return null;
        }
    }

    public String decryptData_ECB(String cipherText)
    {
        try
        {
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true;
            ctx.mode = SM4.SM4_DECRYPT;

            byte[] keyBytes;
            keyBytes = secretKey.getBytes();
            SM4 sm4 = new SM4();
            sm4.sm4_setkey_dec(ctx, keyBytes);
            byte[] decrypted = sm4.sm4_crypt_ecb(ctx, Base64.getDecoder().decode(cipherText));
            return new String(decrypted, "UTF-8");
        }
        catch (Exception e)
        {
            e.printStackTrace();
            return null;
        }
    }

    public String encryptData_CBC(String plainText){
         try{
                SM4_Context ctx = new SM4_Context();
                ctx.isPadding = true;
                ctx.mode = SM4.SM4_ENCRYPT;

                byte[] keyBytes;
                byte[] ivBytes;

                keyBytes = secretKey.getBytes();
                ivBytes = iv.getBytes();

                SM4 sm4 = new SM4();
                sm4.sm4_setkey_enc(ctx, keyBytes);
                byte[] encrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, plainText.getBytes("UTF-8"));
                String cipherText = Base64.getEncoder().encodeToString(encrypted);
                if (cipherText != null && cipherText.trim().length() > 0)
                {
                    Pattern p = Pattern.compile(CBC_PATTERN);
                    Matcher m = p.matcher(cipherText);
                    cipherText = m.replaceAll("");
                }
                return cipherText;
            }
            catch (Exception e)
            {
                e.printStackTrace();
                return null;
            }
        }

        public String decryptData_CBC(String cipherText)
        {
            try
            {
                SM4_Context ctx = new SM4_Context();
                ctx.isPadding = true;
                ctx.mode = SM4.SM4_DECRYPT;

                byte[] keyBytes;
                byte[] ivBytes;
                if (hexString)
                {
                    keyBytes = SM4HandleUtil.hexStringToBytes(secretKey);
                    ivBytes = SM4HandleUtil.hexStringToBytes(iv);
                }
                else
                {
                    keyBytes = secretKey.getBytes();
                    ivBytes = iv.getBytes();
                }

                SM4 sm4 = new SM4();
                sm4.sm4_setkey_dec(ctx, keyBytes);
                byte[] decrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, Base64.getDecoder().decode(cipherText));
                return new String(decrypted, "UTF-8");
            }
            catch (Exception e)
            {
                e.printStackTrace();
                return null;
            }
        }
}
