package com.deloitte.dhr.common.base.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.MessageSource;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Locale;
import java.util.Objects;

/**
 * SpringUtils：用于获得Bean和HttpServletRequest,国际化
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 11/05/2022
 */
public class SpringUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    /**
     * 获取上下文
     * @return
     */
    public ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringUtils.applicationContext = applicationContext;
    }


    /**
	 * 违背了 Spring 依赖注入思想，慎用。。。
	 * @param beanId
	 * @return Object
	 */
	public static Object getBeanByName(String beanId) {
		if (beanId == null) {
			return null;
		}
		return applicationContext.getBean(beanId);
	}

	/**
	 * 慎用此方法，违背spring的ioc解耦思想。
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static Object getBeanByType(Class clazz) {
		if (clazz == null) {
			return null;
		}
		return applicationContext.getBean(clazz);
	}

	public static HttpServletRequest getHttpServletRequest() {
		ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (Objects.isNull(requestAttributes)) {
			return null;
		}
		return requestAttributes.getRequest();
	}

    /**
     * 获取国际化消息
     * @param code 国际化code
     * @return String
     */
    public static String getMessage(String code){
        Locale locale =new Locale(LanguageUtil.getLanguage());
        MessageSource messageSource = (MessageSource) getBeanByType(MessageSource.class);
        if(messageSource != null){
            return messageSource.getMessage(code, null,locale);
        }
        return "";
    }


}

