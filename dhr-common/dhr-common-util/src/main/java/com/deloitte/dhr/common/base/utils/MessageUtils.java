package com.deloitte.dhr.common.base.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * 国际化工具
 *
 * @author: <PERSON><PERSON>ong
 * @version: 1.0
 * @date: 11/05/2022
 */
@Component
@Slf4j
public class MessageUtils {

    private static MessageSource messageSource;

    @Autowired
    public void Translator(MessageSource messageSource) {
        MessageUtils.messageSource = messageSource;
    }

    /**
     * 国际化翻译
     * @param msgCode
     * @param args
     * @return
     */
    public static String toLocale(String msgCode, String... args) {
        //获得本地语言
        Locale locale = new Locale(LanguageUtil.getLanguage());
        String messageString = null;
        try {
            messageString = messageSource.getMessage(msgCode, args, locale);
        } catch (Exception e) {
            log.error("异常信息", e);
        }
        return messageString;
    }

    /**
     * 指定语言国际化翻译
     * @param msgCode
     * @param language
     * @param args
     * @return
     */
    public static String toLocaleSpecifyLanguage(String msgCode,String language, String... args) {
        //获得本地语言
        Locale locale = new Locale(language);
        String messageString = null;
        try {
            messageString = messageSource.getMessage(msgCode, args, locale);
        } catch (Exception e) {
            log.error("异常信息", e);
        }
        return messageString;
    }

}