package com.deloitte.dhr.common.base.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.deloitte.dhr.common.base.constants.OauthConstant;
import com.deloitte.dhr.common.base.exception.UserUnLoginException;
import com.deloitte.dhr.common.base.pojo.UserDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 获取登录用户的工具类
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 11/05/2022
 */
@Slf4j
public class LoginUtil {

    /**
     * 获得授权信息
     *
     * @author: liyong
     * @version: 1.0
     * @date: 29/04/2022
     * @return
     */
    public static Authentication getAuthentication() {
        SecurityContext context = SecurityContextHolder.getContext();
        Authentication authentication = context.getAuthentication();
        return authentication;
    }

    /**
     * 获得登陆用户
     *
     * @author: liyong
     * @version: 1.0
     * @date: 29/04/2022
     * @return
     */
    public static UserDto getLoginUser() {
        UserDto userDto = null;
        Authentication authentication = getAuthentication();
        if (authentication != null) {
            Object principal = authentication.getPrincipal();

            if (principal != null) {
                userDto = (UserDto) principal;
                log.debug("当前登录员工解析信息为：{}", userDto);
            } else {
                log.debug("当前未有登陆信息");
            }
        }
        return userDto;
    }

    /**
     * 获得用户验签
     *
     * @author: liyong
     * @version: 1.0
     * @date: 29/04/2022
     * @return
     */
    public static String getLoginSignature() {
        String signature=null;
        try {
            String token = getLoginUser().getAuthorizationString();
            Algorithm algorithm = Algorithm.HMAC256(OauthConstant.OAUTH_SECRET);
            JWTVerifier verifier = JWT.require(algorithm).build();
            DecodedJWT jwt = verifier.verify(token);
            signature=jwt.getSignature();
        }catch (Exception e){
            throw new UserUnLoginException("用户登录失效");
        }
        return signature;
    }

    /**
     * 获取请求对象
     *
     * @return
     */
    public static HttpServletRequest getHttpServletRequest() {
        try {
            if (null == RequestContextHolder.getRequestAttributes()) {
                return null;
            }
            ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (Objects.isNull(requestAttributes)) {
                return null;
            }
            return requestAttributes.getRequest();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取当前接口的菜单code
     *
     * @return
     */
    public static String getPageCode() {
        HttpServletRequest request = getHttpServletRequest();
        if (null == request) {
            return null;
        }
        return request.getHeader(OauthConstant.PAGE_CODE);
    }

    /**
     * 获取当前接口的权限标识
     *
     * @return
     */
    public static String getAuthFlag() {
        HttpServletRequest request = getHttpServletRequest();
        if (null == request) {
            return null;
        }
        return request.getHeader(OauthConstant.AUTH_FLAG);
    }

    /**
     * 获得用户信息否则报错
     * @return
     */
    public static UserDto getAsbLoginUser() {
        UserDto userDto = getLoginUser();
        if (userDto == null) {
            throw new UserUnLoginException("用户登录失效");
        }
        return userDto;
    }

    /**
     * 获得登录用户的用户编码
     * @return
     */
    public static String  getLoginUserCode() {
        return getAsbLoginUser().getEmployeeNumber();
    }

    /**
     * 获得登录用户的用户名称
     * @return
     */
    public static String  getLoginUserName() {
        return getAsbLoginUser().getFullname();
    }
}
