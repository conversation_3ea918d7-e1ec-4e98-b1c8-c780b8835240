package com.deloitte.dhr.common.base.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * MD5加密类（封装jdk自带的md5加密方法）
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 11/05/2022
 */
public final class MD5Util {

    private MD5Util(){}

    /**
     * Md5 加密32位
     * @param source
     * @return
     */
    public static String encrypt(String source) {
        return encodeMd5(source.getBytes());
    }
    
    /**
     * MD5 加密16位
     * @param source
     * @return
     */
    public static String encrypt16(String source) {
    	String str = encodeMd5(source.getBytes());
        return str.substring(8, 24);
    }

    private static String encodeMd5(byte[] source) {
        try {
            return encodeHex(MessageDigest.getInstance("MD5").digest(source));
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    private static String encodeHex(byte[] bytes) {
        StringBuffer buffer = new StringBuffer(bytes.length * 2);
        for (int i = 0; i < bytes.length; i++) {
            if (((int) bytes[i] & 0xff) < 0x10){
                buffer.append("0");
            }
            buffer.append(Long.toString((int) bytes[i] & 0xff, 16));
        }
        return buffer.toString();
    }

}
