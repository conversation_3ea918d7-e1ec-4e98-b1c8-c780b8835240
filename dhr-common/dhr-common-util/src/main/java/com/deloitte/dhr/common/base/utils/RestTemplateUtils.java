package com.deloitte.dhr.common.base.utils;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.support.BasicAuthenticationInterceptor;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * 请求工具
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 11/05/2022
 */
public final class RestTemplateUtils {

	/**
	 * POST请求 -- 传参请求体
	 * @param <T>
	 * @param url 请求接口地址
	 * @param bodyParams 请求体参数
	 * @param headParams 请求头参数
	 * @param responseType 返回数据类型
	 * @return
	 */
	public static <T> ResponseEntity<T> postForEntity(String url, Object bodyParams, Map<String, String> headParams, Class<T> responseType){
		// 请求数据
		JSONObject bodyMap = new JSONObject();
		// 请求头
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.setAccept(MediaType.parseMediaTypes(MediaType.APPLICATION_JSON_VALUE));
		
		if(null != headParams && !headParams.isEmpty()) {
			for(Map.Entry<String, String> entry:headParams.entrySet()) {
				String key = entry.getKey();
				String value = entry.getValue();
				headers.add(key, value);
			}
		}
		HttpEntity<?> request = null;
		if(null == bodyParams) {
			request = new HttpEntity<>(bodyMap, headers);
		} else {
			request = new HttpEntity<>(bodyParams, headers);
		}
		
		RestTemplate restTemplate = new RestTemplate();
        return restTemplate.postForEntity(url, request, responseType);
	}

	/**
	 * POST请求 -- 传参请求体
	 * @param <T>
	 * @param url 请求接口地址
	 * @param bodyParams 请求体参数
	 * @param headParams 请求头参数
	 * @param responseType 返回数据类型
	 * @return
	 */
	public static <T> ResponseEntity<T> postForEntityFormUrlencoded(String url, Object bodyParams, Map<String, String> headParams, Class<T> responseType){
		// 请求数据
		JSONObject bodyMap = new JSONObject();
		// 请求头
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
		headers.setAccept(MediaType.parseMediaTypes(MediaType.APPLICATION_FORM_URLENCODED_VALUE));
		
		if(null != headParams && !headParams.isEmpty()) {
			for(Map.Entry<String, String> entry:headParams.entrySet()) {
				String key = entry.getKey();
				String value = entry.getValue();
				headers.add(key, value);
			}
		}
		HttpEntity<?> request = null;
		if(null == bodyParams) {
			request = new HttpEntity<>(bodyMap, headers);
		} else {
			request = new HttpEntity<>(bodyParams, headers);
		}
		
		RestTemplate restTemplate = new RestTemplate();
        return restTemplate.postForEntity(url, request, responseType);
	}

	
	/**
	 * GET请求
	 * @param <T>
	 * @param url 请求接口地址
	 * @param responseType 返回数据类型
	 * @return
	 */
	public static <T> ResponseEntity<T> getForEntity(String url, Class<T> responseType){
		RestTemplate restTemplate = new RestTemplate();
        return restTemplate.getForEntity(url, responseType);
	}

	/**
	 * sap调用使用
	 * @param json
	 * @return
	 */
	public static String post(JSONObject json) {
		String url = json.getString("url");
		String userName=json.getString("username");
		String password=json.getString("password");

		RestTemplate restTemplate = new RestTemplate();
		if(null!=userName && !userName.isEmpty()) {
			restTemplate.getInterceptors().add(new BasicAuthenticationInterceptor(userName, password));
		}
        return restTemplate.postForObject(url, json.get("vdata"), String.class);
	}
	
}
