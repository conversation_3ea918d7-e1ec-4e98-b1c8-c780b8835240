package com.deloitte.dhr.common.base.utils;


import com.deloitte.dhr.common.base.utils.sm4.SM4Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * Encrypt加密工具
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 11/05/2022
 */
public class EncryptUtil {
    private static final Logger logger = LoggerFactory.getLogger(EncryptUtil.class);

    public static String encodeBase64(byte[] bytes){
        return Base64.getEncoder().encodeToString(bytes);
    }

    public static byte[]  decodeBase64(String str){
        return Base64.getDecoder().decode(str);
    }

    public static String encodeUTF8StringBase64(String str){
        String encoded = null;
        encoded = Base64.getEncoder().encodeToString(str.getBytes(StandardCharsets.UTF_8));
        return encoded;

    }

    public static String  decodeUTF8StringBase64(String str){
        String decoded;
        byte[] bytes = Base64.getDecoder().decode(str);
        decoded = new String(bytes, StandardCharsets.UTF_8);
        return decoded;
    }

    public static String encodeURL(String url) {
        return URLEncoder.encode(url, StandardCharsets.UTF_8);
	}


	public static String decodeURL(String url) {
        return URLDecoder.decode(url, StandardCharsets.UTF_8);
	}

    /**
     * 直接数据库修改密码用
     * @param password
     * @return
     */
    public static String passwordEncrypt(String password){
        return new BCryptPasswordEncoder().encode(SM4Encrypt(password));
    }

    public static String SM4Encrypt(String content){
        SM4Utils sm4 = new SM4Utils();
        return sm4.encryptData_ECB(content);
    }

    public static String SM4DecryptStr(String encryptHex){
        SM4Utils sm4 = new SM4Utils();
        return sm4.decryptData_ECB(encryptHex);
    }
}
