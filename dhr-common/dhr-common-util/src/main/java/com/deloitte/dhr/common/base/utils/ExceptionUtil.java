package com.deloitte.dhr.common.base.utils;

import com.deloitte.dhr.common.base.constants.CommonConstant;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * @desc
 * <AUTHOR>
 * @Date 2020/08/24
 */
public class ExceptionUtil {

    /**
     * 获取堆栈信息
     * @param throwable
     * @return
     */
    public static String getStackTrace(Throwable throwable) {
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);

        try {
            throwable.printStackTrace(printWriter);
            return stringWriter.toString();
        } finally {
            printWriter.close();
        }

    }

    /**
     * 返回标准的异常格式
     * @param e Exception
     * @return
     */
    public static Object exceptionMessage(Exception e) {
        Map<String,Object> map = new HashMap<>();
        map.put(CommonConstant.CODE, CommonConstant.FAIL);
        map.put(CommonConstant.EXCEPTION,e.getMessage());
        return map;
    }

    /**
     * 返回标准的异常格式
     * @param message 返回的消息内容
     * @return
     */
    public static Object exceptionMessage(String message) {
        Map<String,Object> map = new HashMap<>();
        map.put(CommonConstant.CODE, CommonConstant.FAIL);
        map.put(CommonConstant.MESSAGE, message);
        return map;
    }

    /**
     * 返回标准的异常格式
     * @param e Exception
     * @param message 返回的消息内容
     * @return
     */
    public static Object exceptionMessage(Exception e,String message) {
        Map<String,Object> map = new HashMap<>();
        map.put(CommonConstant.CODE, CommonConstant.FAIL);
        map.put(CommonConstant.EXCEPTION, e.getMessage());
        map.put(CommonConstant.MESSAGE, message);
        return map;
    }
}
