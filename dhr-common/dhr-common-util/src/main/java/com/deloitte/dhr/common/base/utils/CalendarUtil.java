package com.deloitte.dhr.common.base.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;

/**
 * 日期工具
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 11/05/2022
 */
@Slf4j
public class CalendarUtil {

	public static final String DATE_PATTERN = "yyyyMMdd HHmmss";
	private static TimeZone UTC_TIMEZONE = TimeZone.getTimeZone("UTC");
	private static TimeZone GMT_TIMEZONE = TimeZone.getTimeZone("GMT");	


	/**
	 * 计算时间戳
	 *
	 * @param date_String
	 * @return
	 */
	public long dateToMills(String date_String){
		Calendar cal = Calendar.getInstance();
		try {
			cal.setTime(getDateFormat().parse(date_String));
			long mills = cal.getTimeInMillis();
			return mills;
		} catch (ParseException e) {
			log.error(e.toString());
		}
		return 0;
	}


	/**
	 * 时间显示格式
	 *
	 * @return
	 */
	public SimpleDateFormat getDateFormat() {
		SimpleDateFormat formater = new SimpleDateFormat(DATE_PATTERN);
		TimeZone tZone = Calendar.getInstance().getTimeZone();
		formater.setTimeZone(tZone);
		return formater;
	}


	/**
	 * 时间显示格式
	 *
	 * @param timeZone
	 * @return
	 */
	public SimpleDateFormat getDateFormat(TimeZone timeZone) {
		SimpleDateFormat formater = new SimpleDateFormat(DATE_PATTERN);
		formater.setTimeZone(timeZone);
		return formater;
	}


	/**
	 * 以服务器当前时区显示时间 返回String
	 *
	 * @param calendar
	 * @return
	 */
	public String convertToLocalTimeString(Calendar calendar) {
		if (null == calendar) {
			return null;
		}
		return getDateFormat().format(calendar.getTime());
	}


	/**
	 * 以UTC显示时间 返回String
	 *
	 * @param calendar
	 * @return
	 */
	public String convertToUTCTimeString(Calendar calendar) {
		if (null == calendar) {
			return null;
		}
		return getDateFormat(UTC_TIMEZONE).format(calendar.getTime());
	}


	/**
	 * 以GMT显示时间 返回String
	 *
	 * @param calendar
	 * @return
	 */
	public String convertToGMTTimeString(Calendar calendar) {
		if (null == calendar) {
			return null;
		}
		return getDateFormat(GMT_TIMEZONE).format(calendar.getTime());
	}


	/**
	 * 服务器时间Calendar转GMT Calendar String转Calendar
	 *
	 * @param time
	 * @return
	 */
	public Calendar convertLocalDateStringToGMTCalendar(String time){
		Calendar calendar = null;
		try {
			Date date = getDateFormat().parse(time.trim());
			calendar = new GregorianCalendar(GMT_TIMEZONE);
			calendar.setTime(date);
		} catch (ParseException e) {
			log.error(e.toString());
		}
		return calendar;
	}


	/**
	 * 服务器时间Calendar转UTC Calendar String转Calendar
	 *
	 * @param time
	 * @return
	 */
	public Calendar convertLocalDateStringToUTCCalendar(String time){
		Calendar calendar = null;
		try {
			Date date;
			date = getDateFormat().parse(time.trim());
			calendar = new GregorianCalendar(UTC_TIMEZONE);
			calendar.setTime(date);
		} catch (ParseException e) {
			log.error(e.toString());
		}
		return calendar;
	}


	/**
	 * UTC转GMT Calendar String转Calendar
	 *
	 * @param time
	 * @return
	 */
	public Calendar convertUTCDateStringToGMTCalendar(String time) {
		Calendar calendar = null;
		try {
			Date date = getDateFormat(UTC_TIMEZONE).parse(time.trim());
			calendar = new GregorianCalendar(GMT_TIMEZONE);
			calendar.setTime(date);
		} catch (ParseException e) {
			log.error(e.toString());
		}
		return calendar;
	}


	/**
	 * GMT转UTC Calendar转Calendar
	 *
	 * @param time
	 * @return
	 */
	public Calendar convertGMTToUTCTime(Calendar time) {
		time.setTimeZone(GMT_TIMEZONE);
		Calendar cnTime = Calendar.getInstance();
		int begin_year = time.get(Calendar.YEAR);
		int begin_month = time.get(Calendar.MONTH);
		int begin_day = time.get(Calendar.DAY_OF_MONTH);
		int begin_hour = time.get(Calendar.HOUR_OF_DAY);
		int begin_minute = time.get(Calendar.MINUTE);
		int begin_second = time.get(Calendar.SECOND);
		cnTime.set(Calendar.YEAR, begin_year);
		cnTime.set(Calendar.MONTH, begin_month);
		cnTime.set(Calendar.DAY_OF_MONTH, begin_day);
		cnTime.set(Calendar.HOUR_OF_DAY, begin_hour);
		cnTime.set(Calendar.MINUTE, begin_minute);
		cnTime.set(Calendar.SECOND, begin_second);
		return cnTime;
	}


	/**
	 * GMT转UTC Calendar转Calendar
	 *
	 * @param cnTime
	 * @return
	 */
	public Calendar convertUTCToGMTTime(Calendar cnTime) {
		Calendar result = new GregorianCalendar(UTC_TIMEZONE);
		int year = cnTime.get(Calendar.YEAR);
		int month = cnTime.get(Calendar.MONTH);
		int day = cnTime.get(Calendar.DAY_OF_MONTH);
		int hour = cnTime.get(Calendar.HOUR_OF_DAY);
		int minute = cnTime.get(Calendar.MINUTE);
		int second = cnTime.get(Calendar.SECOND);
		result.set(Calendar.YEAR, year);
		result.set(Calendar.MONTH, month);
		result.set(Calendar.DAY_OF_MONTH, day);
		result.set(Calendar.HOUR_OF_DAY, hour);
		result.set(Calendar.MINUTE, minute);
		result.set(Calendar.SECOND, second);
		return result;
	}

}
