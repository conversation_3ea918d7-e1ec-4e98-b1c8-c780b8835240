/**
 * 
 */
package com.deloitte.dhr.common.base.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.common.base.exception.ParamException;
import org.assertj.core.util.Arrays;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Array;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 参数工具类
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 11/05/2022
 */
@SuppressWarnings("rawtypes")
public class ParamUtil {
	protected static final Logger log = LoggerFactory.getLogger(ParamUtil.class);

	private static final String CONTAIN_CHINESE_PATTERN = "[\u4e00-\u9fa5]";
	
	/**
	 * 判断map是否为null或map.size <= 0
	 * <AUTHOR>
	 * @date 2019年6月17日 上午10:00:30
	 * @param map
	 * @return
	 */
	public static boolean isEmpty(Map map) {
		if(map == null || map.isEmpty()) {return true;}
		return false;
	}
	
	/**
	 * 判断list是否为null或 list.size <= 0
	 * <AUTHOR>
	 * @date 2019年6月17日 上午10:00:30
	 * @param list
	 * @return
	 */
	public static boolean isEmpty(List list) {
		if(list == null || list.isEmpty()) {return true;}
		return false;
	}
	
	/***
	 * 判断是否为空对象或null
	 * @date 2019年7月18日 上午10:56:07
	 * <AUTHOR>
	 * @param obj
	 * @return
	 */
	public static boolean isEmpty(Object obj) {
		if(obj instanceof Map) {
			return isEmpty((Map)obj);
		}else if (obj instanceof List) {
			return isEmpty((List)obj);
		}else if (obj instanceof Array){
			return Arrays.isNullOrEmpty((Object[]) obj);
		}else {
			return Objects.isNull(obj);
		}
	}
	
	/***
	 * 判断是否为null
	 * @date 2019年7月18日 上午10:56:07
	 * <AUTHOR>
	 * @param obj
	 * @return
	 */
	public static boolean isNull(Object obj) {
		return Objects.isNull(obj);
	}
	
	/***
	 * 判断参数是否为有为空的，有一个空则返回true
	 * @date 2019年7月18日 上午10:57:49
	 * <AUTHOR>
	 * @param param
	 * @return
	 */
	public static Boolean isEmpty(Object... param){
		if(param == null) { return true; }
		for(Object o : param) {
			if(isEmpty(o)) {
				return true;
			}
		}
		return false;
	}
	
	/***
	 * 判断参数是否都为空
	 * @date 2019年9月26日 上午9:16:41
	 * <AUTHOR>
	 * @param param
	 * @return
	 */
	public static Boolean isAllEmpty(Object... param){
		if(param == null) { return true; }
		for(Object o : param) {
			if(!isEmpty(o)) {
				return false;
			}
		}
		return true;
	}
	
	/***
	 * 判断参数是否都不为空，空怎返回false
	 * @date 2019年7月18日 上午10:57:49
	 * <AUTHOR>
	 * @param param
	 * @return
	 */
	public static Boolean isNotEmpty(Object... param){
		if(param == null) { return false; }
		for(Object o : param) {
			if(isEmpty(o)) {
				return false;
			}
		}
		return true;
	}
	
	/***
	 * 校验参数是否为空, 空 message不为null则抛出异常，否则抛出默认异常信息
	 * @date 2019年7月18日 上午10:58:37
	 * <AUTHOR>
	 * @param errorMessage
	 * @param param
	 * @throws ParamException
	 */
	public static void validParamMessage(String errorMessage, Object... param) throws ParamException {
		for(Object o : param) {
			if(isEmpty(o)) {
				if(isEmpty(errorMessage)) {
					throw new ParamException();
				}else {
					throw new ParamException(errorMessage);
				}
			}
		}
	}
	/***
	 * 校验参数是否为空, 参数校验失败且message不为null则抛出异常message，否则抛出默认异常信息
	 * @date 2019年7月18日 上午10:58:37
	 * <AUTHOR>
	 * @param param
	 * @throws ParamException
	 */
	public static void validParam(Object... param) throws ParamException{
		validParamMessage(null, param);
	}
	
	/***
	 * 遍历数组中的json并以json中的指定key的value值做  结果集map的key，json为value返回对象
	 * @date 2019年7月18日 下午2:32:49
	 * <AUTHOR>
	 * @param array
	 * @param key
	 * @return
	 */
	public static JSONObject getJSONObjectByArray(JSONArray array, String key){
		JSONObject result = new JSONObject();
		if(isEmpty(array, key)) { return result; }
		JSONObject json;
		String keyValue;
		for(int i = 0; i < array.size(); i++) {
			json = array.getJSONObject(i);
			keyValue = json.getString(key);
			if(isEmpty(keyValue)) { continue; }
			
			result.put(keyValue, json);
		}
		return result;
	}
	
	
	/***
	 * 获取request参数Map
	 * @date 2019年8月9日 下午2:41:58
	 * <AUTHOR>
	 * @param request
	 * @return
	 * @throws IOException
	 */
	public static Map getRequestParams(HttpServletRequest request){
		String httpMethod = request.getMethod();
		if(HttpMethod.POST.matches(httpMethod)) {
			try {
				InputStream stream = request.getInputStream();
				String body = StreamUtils.copyToString(stream, Charset.forName("UTF-8"));
	    		JSONObject paramMap = null;
	    		if(body.startsWith("[")) {
	    			paramMap = new JSONObject();
	    			paramMap.put("list", JSON.parseArray(body));
	    			return paramMap;
	    		}else if(body.startsWith("{")){
	    			return JSON.parseObject(body);
	    		}
	    		return new JSONObject();
			} catch (Exception e) {
				log.error(e.getMessage());
				return new HashMap();
			}
		}else {
			return request.getParameterMap();
		}
	}
	
	/**
     * 判断字符串中是否包含中文
     * @param str
     * 待校验字符串
     * @return 是否为中文
     * @warn 不能校验是否为中文标点符号
     */
    public static boolean isContainChinese(String str) {
        Pattern p = Pattern.compile(CONTAIN_CHINESE_PATTERN);
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        }
        return false;
    }
    
    /***
     * 从request中获取参数，数组只取第一个值
     * @date 2019年12月5日 上午11:26:11
     * <AUTHOR>
     * @param request
     * @param prefix
     * @param suffix
     * @return
     */
    public static Map<String, String> getRequestStringMap(HttpServletRequest request, String prefix, String suffix){
    	Map<String, String[]> requestMap = request.getParameterMap();
    	Map<String, String> param = new HashMap<>();
    	boolean isNofix = ParamUtil.isAllEmpty(prefix, suffix),
    			isAllfix = ParamUtil.isNotEmpty(prefix, suffix), 
    			isPrefix = ParamUtil.isNotEmpty(prefix), 
    			isSuffix = ParamUtil.isNotEmpty(suffix);
		for(Entry<String, String[]> e : requestMap.entrySet()) {
			if(isNofix) {
				param.put(e.getKey(), e.getValue()[0]);
			}else {
				if(isAllfix && e.getKey().startsWith(prefix) && e.getKey().endsWith(suffix)) {
					param.put(e.getKey().replace(prefix, "").replace(suffix, ""), e.getValue()[0]);
				}else if(isPrefix && e.getKey().startsWith(prefix)) {
					param.put(e.getKey().replace(prefix, ""), e.getValue()[0]);
				}else if(isSuffix && e.getKey().endsWith(suffix)) {
					param.put(e.getKey().replace(suffix, ""), e.getValue()[0]);
				}
			}
		}
		return param;
    }
}
