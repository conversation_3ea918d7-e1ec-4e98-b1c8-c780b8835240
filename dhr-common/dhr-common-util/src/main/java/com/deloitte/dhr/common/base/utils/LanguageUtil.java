package com.deloitte.dhr.common.base.utils;

import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * Language工具类
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 11/05/2022
 */
public class LanguageUtil {

    public static final ThreadLocal<String> threadLocal = new ThreadLocal<>();

    public static Locale getLocaleCode() {
        Locale locale = LocaleContextHolder.getLocale();
        if (Locale.CHINA.equals(locale) || Locale.CHINESE.equals(locale)) {
            locale = new Locale("zh");
        } else if (Locale.ENGLISH.equals(locale) || Locale.UK.equals(locale) || Locale.US.equals(locale)) {
            locale = new Locale("en");
        } else {
            locale = new Locale("zh");
        }
        return locale;
    }

    public static String getLanguageString(String acceptLanguage) {
        if (acceptLanguage == null) {
            return "zh";
        } else if (acceptLanguage.toLowerCase(Locale.ROOT).contains("zh")) {
            return "zh";
        } else if (acceptLanguage.toLowerCase(Locale.ROOT).contains("en")) {
            return "en";
        } else {
            return "zh";
        }
    }

    public static String getLanguage() {
        String language = LanguageUtil.threadLocal.get();
        if (language == null) {
            language = getLocaleCode().getLanguage();
        }
        return language;
    }
}
