package com.deloitte.dhr.common.base.utils;

import com.deloitte.dhr.common.base.constants.OauthConstant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

/**
 * 获得TraceId: 用于日志和埋点
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 11/05/2022
 */
public class TraceIdUtil {

    /**
     * 获得TraceId
     *
     * @author: liyong
     * @version: 1.0
     * @date: 10/05/2022
     * @return
     */
    public static String getTraceId() {
        String traceId = MDC.get(OauthConstant.ACCEPT_TRACEID);

        HttpServletRequest httpServletRequest = SpringUtils.getHttpServletRequest();
        if (StringUtils.isEmpty(traceId)&&httpServletRequest != null) {
            traceId = httpServletRequest.getHeader(OauthConstant.ACCEPT_TRACEID);
        }

        if (StringUtils.isBlank(traceId)) {
            traceId = UUID.randomUUID().toString();
            MDC.put(OauthConstant.ACCEPT_TRACEID, traceId);
        }
        return traceId;
    }
}
