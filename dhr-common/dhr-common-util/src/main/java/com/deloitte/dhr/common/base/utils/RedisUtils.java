package com.deloitte.dhr.common.base.utils;

import com.alibaba.fastjson2.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.concurrent.TimeUnit;

/**
 * Redis工具类
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 11/05/2022
 */
@Component
public class RedisUtils {
	
	@Autowired
	private StringRedisTemplate stringRedisTemplate;

	/** 默认过期时长，单位：2天 */
	public final static long DEFAULT_EXPIRE = 24 * 60 * 60 * 2L;
	/** 一天的有效期 */
	public final static long ONE_DAY_EXPIRE = 60 * 60 * 24 * 1L;
	/** 一小时的有效期 */
	public final static long ONE_HOURS_EXPIRE = 60 * 60 * 1L;

	/**
	 * 短信验证码 验证结果有效期
	 */
	public final static long PAYMENT_VEFIFY_EXPIRE = 60 * 1L;

	/** 不设置过期时长 */
	public final static long NOT_EXPIRE = -1L;

	public void set(String key, Object value, long expire) {
		stringRedisTemplate.opsForValue().set(key, toJson(value), expire, TimeUnit.SECONDS);
	}

	public void set(String key, Object value) {
		set(key, value, DEFAULT_EXPIRE);
	}

	public <T> T get(String key, Class<T> clazz, long expire) {
		String value = stringRedisTemplate.opsForValue().get(key);
		if (expire != NOT_EXPIRE) {
			stringRedisTemplate.expire(key, expire, TimeUnit.SECONDS);
		}
		return StringUtils.isBlank(value) ? null : fromJson(value.toString(), clazz);
	}

	public <T> T get(String key, Class<T> clazz) {
		return get(key, clazz, NOT_EXPIRE);
	}

	public String get(String key, long expire) {
		String value = stringRedisTemplate.opsForValue().get(key);
		if (expire != NOT_EXPIRE) {
			stringRedisTemplate.expire(key, expire, TimeUnit.SECONDS);
		}
		return value;
	}

	public String get(String key) {
		return get(key, NOT_EXPIRE);
	}

	public void delete(String key) {
		stringRedisTemplate.delete(key);
	}

	public void delteteAll(Collection<String> keys) {
		stringRedisTemplate.delete(keys);
	}

	/**
	 * 查找匹配key
	 * 
	 * @param pattern
	 * @return Collection<String>
	 */
	public Collection<String> keys(String pattern) {
		return stringRedisTemplate.keys(pattern);
	}

	/**
	 * Object转成JSON数据
	 */
	private String toJson(Object object) {
		if (object instanceof Integer || object instanceof Long || object instanceof Float || object instanceof Double
				|| object instanceof Boolean || object instanceof String) {
			return String.valueOf(object);
		}
		return JSON.toJSONString(object);
	}

	/**
	 * JSON数据，转成Object
	 */
	private <T> T fromJson(String json, Class<T> clazz) {
		return JSON.parseObject(json, clazz);
	}
	
	/**
	 * 枷锁
	 * @param key
	 * @param value
	 * @param timeout 过期时间 （秒）
	 * @return
	 */
	public boolean tryLock(String key, String value, Long timeout) {
        Boolean result = stringRedisTemplate.opsForValue().setIfAbsent(key, value);
        if (result){
            // 过期设置
        	stringRedisTemplate.expire(key ,timeout ,TimeUnit.SECONDS);
        }
        return result;
    }
	
}
