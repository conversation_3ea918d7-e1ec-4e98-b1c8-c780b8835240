package com.deloitte.dhr.common.base.utils;
/**
 * 随机数工具类
 *
 * @author: <PERSON><PERSON>ong
 * @version: 1.0
 * @date: 11/05/2022
 */
public class RandomUtil {

    /**
     * 私有化
     */
    private RandomUtil(){}


    /***************随机数***********************/
    /**
     * 给定字符和长度生成随机数
     *
     * @param source
     * @param length
     * @return
     */
    public static String getRandom(String source, int length) {
        char[] sourceChar = source.toCharArray();
        if (sourceChar == null || sourceChar.length == 0 || length < 0) {
            return null;
        }

        StringBuilder str = new StringBuilder(length);
        //str.append("");
        for (int i = 0; i < length; i++) {
            char c =  sourceChar[org.apache.commons.lang3.RandomUtils.nextInt(1,sourceChar.length)];
            str.append(c);
        }
        return str.toString();
    }

    /**
     * 数字随机数
     * @param length
     * @return
     */
    public static String getrandomInt(int length) {
        return getRandom("0123456789", length);
    }

    /**
     * 得到固定长度的随机字符串，字符串由数字和大小写字母混合组成
     * @param length
     * @return
     */
    public static String getrandomString(int length) {
        return getRandom("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", length);
    }


}
