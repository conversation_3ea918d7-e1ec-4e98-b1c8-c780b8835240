package com.deloitte.dhr.common.base.utils;

import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Base32;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * Crypto加密工具
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 11/05/2022
 */

public class CryptoUtil {

	private static final Logger log = LoggerFactory.getLogger(CryptoUtil.class);
	
	private static Key DEFAULT_KEY;

	public static final String DEFAULT_SECRET_KEY1 = "?:P)(OL><KI*&UJMNHY^%TGBVFR$#EDCXSW@!QAZ";
	public static final String DEFAULT_SECRET_KEY = DEFAULT_SECRET_KEY1;

	public static final String DES = "DES";
	
	public static final String DES_TYPE="DES/CBC/PKCS5Padding";

	public static final Base32 base32 = new Base32();

	static {
		try {
			DEFAULT_KEY = obtainKey(DEFAULT_SECRET_KEY);
		} catch (NoSuchAlgorithmException e) {
			//log.error(e.getMessage());
		}
	}

	/**
	 * 获得key
	 * 
	 * @throws NoSuchAlgorithmException
	 **/
	public static Key obtainKey(String key) throws NoSuchAlgorithmException {
		if (key == null) {
			return DEFAULT_KEY;
		}
		KeyGenerator generator = KeyGenerator.getInstance(DES);
		SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
		random.setSeed(key.getBytes());
		generator.init(random);
		Key key1 = generator.generateKey();
		generator = null;
		return key1;
	}

	/**
	 * 加密<br>
	 * String明文输入,String密文输出
	 */
	public static String encode(String str) {
		return encode64(null, str);
	}

	/**
	 * 加密<br>
	 * String明文输入,String密文输出
	 */
	public static String encode64(String key, String str) {
		return Base64.encodeBase64URLSafeString(obtainEncode(key, str.getBytes()));
	}

	/**
	 * 加密<br>
	 * String明文输入,String密文输出
	 */
	public static String encode32(String key, String str) {
		return base32.encodeAsString(obtainEncode(key, str.getBytes())).replaceAll("=", "");
	}

	/**
	 * 加密<br>
	 * String明文输入,String密文输出
	 */
	public static String encode16(String key, String str) {
		return Hex.encodeHexString(obtainEncode(key, str.getBytes()));
	}

	/**
	 * 解密<br>
	 * 以String密文输入,String明文输出
	 */
	public static String decode(String str) {
		return decode64(null, str);
	}

	/**
	 * 解密<br>
	 * 以String密文输入,String明文输出
	 */
	public static String decode64(String key, String str) {
		return new String(obtainDecode(key, Base64.decodeBase64(str)));
	}

	/**
	 * 解密<br>
	 * 以String密文输入,String明文输出
	 */
	public static String decode32(String key, String str) {
		return new String(obtainDecode(key, base32.decode(str)));
	}

	/**
	 * 解密<br>
	 * 以String密文输入,String明文输出
	 */
	public static String decode16(String key, String str) {
		try {
			return new String(obtainDecode(key, Hex.decodeHex(str.toCharArray())));
		} catch (DecoderException e) {
			log.error(e.getMessage());
		}
		return null;
	}

	/**
	 * 加密<br>
	 * 以byte[]明文输入,byte[]密文输出
	 */
	private static byte[] obtainEncode(String key, byte[] str) {
		byte[] byteFina = null;
		Cipher cipher;
		try {
			Key key1 = obtainKey(key);
			cipher = Cipher.getInstance(DES_TYPE);
//			cipher.init(Cipher.ENCRYPT_MODE, key1);
			cipher.init(Cipher.ENCRYPT_MODE, key1, new IvParameterSpec(new byte[cipher.getBlockSize()]));
			byteFina = cipher.doFinal(str);
		} catch (Exception e) {
			//log.error(e.getMessage());
		} finally {
			cipher = null;
		}
		return byteFina;
	}

	/**
	 * 解密<br>
	 * 以byte[]密文输入,以byte[]明文输出
	 */
	private static byte[] obtainDecode(String key, byte[] str) {
		Cipher cipher;
		byte[] byteFina = null;
		try {
			Key key1 = obtainKey(key);
			cipher = Cipher.getInstance(DES_TYPE);
//			cipher.init(Cipher.DECRYPT_MODE, key1);
			cipher.init(Cipher.DECRYPT_MODE, key1, new IvParameterSpec(new byte[cipher.getBlockSize()]));
			byteFina = cipher.doFinal(str);
		} catch (Exception e) {
			//log.error(e.getMessage());
		} finally {
			cipher = null;
		}
		return byteFina;
	}
}
