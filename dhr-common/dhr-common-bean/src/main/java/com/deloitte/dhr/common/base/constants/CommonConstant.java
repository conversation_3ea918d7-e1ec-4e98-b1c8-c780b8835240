package com.deloitte.dhr.common.base.constants;

/**
 * <p> Title: 公共Constant,主要存放一些基础的全局变量</p>
 * <p>Describe: 主要用于日常业务中固定变量的值 <p/>
 *
 * <AUTHOR> BOBO
 * @date : 2019/04/08
 */
public class CommonConstant {

    /**
     * 国际化标志
     */
    public static final String ACCEPT_LANG = "Accept-Lang";

    /**
     * 国际化标志
     */
    public static final String LANG = "lang";

    /**
     * 国际化标志
     */
    public static final String ACCEPT_LANGUAGE = "Accept-Language";

    /**
     * 用户缓存前缀
     */
    public static final String REDIS_KEY_USER_CACHE = "USER-CACHE:";

    /**
     * 成功
     */
    public final static String SUCCESS = "success";

    /**
     * 失败
     */
    public final static String FAIL = "fail";
    /**
     * 异常
     */
    public final static String EXCEPTION = "exception";

    /**
     * 消息
     */
    public final static String MESSAGE = "message";

    /**
     * 编码
     */
    public final static String CODE = "code";

}
