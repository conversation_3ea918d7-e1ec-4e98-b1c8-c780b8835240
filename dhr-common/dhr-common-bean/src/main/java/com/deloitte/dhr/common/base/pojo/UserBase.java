package com.deloitte.dhr.common.base.pojo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 2019年10月8日00:01:19
 */
@ApiModel("用户base")
@Data
public class UserBase {
	/**姓名*/
	private String ename;
	/**一般标记*/
	private String xjflag;
	/**人员编号*/
	private String pernr;
	/**国家分组*/
	private String molga;
	/**城市公司文本*/
	private String ctext;
	/**证件号码*/
	private String icnum;
	/**国家分组*/
	private String molga_1;
	/**语言代码*/
	private String langu_1;
	/**业态*/
	private String barea_1;
	/**共享*/
	private String share;
	/**一般标记*/
	private String jbflag;
	/**部门编码*/
	private String deptcode;
	/**公司*/
	private String butxt;
	/**员工组*/
	private String persg;
	/**部门文本*/
	private String stext;
	/**公司编码*/
	private String compcode;
	/**城市公司编号*/
	private String compcity;
	/**人事范围*/
	private String werks;
	/**性别代码*/
	private String gesch;
	/**域账号*/
	private String adidno;
	/**NC账号*/
	private String ncno;
	/**工资范围*/
	private String abkrs;
	/**电话号码*/
	private String phone;
	/**业务条线*/
	private String zywtx_t;
	/**上级人员编号*/
	private String pernr_l;
	/**上级姓名*/
	private String ename_l;
	/**邮件地址*/
	private String email;
	/**职位描述*/
	private String plans;
	/**系统切换时，停止登录的标志位*/
	private String stop_log;
	/**板块*/
	private String plate;
	/**板块名称*/
	private String plate_t;
	/**人事范围文本*/
	private String name1;
	/**岗位编码*/
	private String postCode;
	/**所属组织编码*/
	private String orgeh;
	/**所属组织文本*/
	private String orgtx;

}
