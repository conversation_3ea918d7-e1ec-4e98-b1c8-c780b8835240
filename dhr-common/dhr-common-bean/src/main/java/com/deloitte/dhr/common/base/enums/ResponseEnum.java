package com.deloitte.dhr.common.base.enums;

/***
 * portal响应状态码
 * @date 2019年8月9日 上午11:36:03
 * <AUTHOR>
 */
public enum ResponseEnum {
    /**
     * 请求成功
     */
    Success("200", "操作成功"),
    /**
     * 全局异常
     */
    GlobleException("500", "全局异常"),
    /**
     * 请求异常
     */
    SystemException("501", "系统异常"),
    /**
     * 业务异常
     */
    BusinessException("503", "业务异常"),

    /**
     * sap调用服务异常
     */
    ParamException("506", "参数异常"),
    /**
     * portal数据解密异常
     */
    PortalDecrypException("507", "数据解密异常"),
    /**
     * 请求token 无效
     */
    TokenExpired("401", "token无效");

    /**
     * 返回类型code
     */
    private String code;

    /**
     * 返回类型默认消息，可覆盖
     */
    private String msg;

    ResponseEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}