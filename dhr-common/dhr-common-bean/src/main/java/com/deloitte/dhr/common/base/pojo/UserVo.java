package com.deloitte.dhr.common.base.pojo;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel("用户vo")
public class UserVo {
	/**
	 * 姓名
	 */
	@JSONField(name = "ENAME")
	private String ename;
	/**
	 * 一般标记
	 */
	@JSONField(name = "XJFLAG")
	private String xjflag;
	/**
	 * 人员编号
	 */
	@JSONField(name = "PERNR")
	private String pernr;
	/**
	 * 国家分组
	 */
	@JSONField(name = "MOLGA")
	private String molga;
	/**
	 * 城市公司文本
	 */
	@JSONField(name = "CTEXT")
	private String ctext;
	/**
	 * 证件号码
	 */
	@JSONField(name = "ICNUM")
	private String icnum;
	/**
	 * 国家分组
	 */
	@JSONField(name = "MOLGA_1")
	private String molga_1;
	/**
	 * 语言代码
	 */
	@JSONField(name = "LANGU_1")
	private String langu_1;
	/**
	 * 业态
	 */
	@JSONField(name = "BAREA_1")
	private String barea_1;
	/**
	 * 共享
	 */
	@JSONField(name = "SHARE")
	private String share;
	/**
	 * 一般标记
	 */
	@JSONField(name = "JBFLAG")
	private String jbflag;
	/**
	 * 部门编码
	 */
	@JSONField(name = "DEPTCODE")
	private String deptcode;
	/**
	 * 公司
	 */
	@JSONField(name = "BUTXT")
	private String butxt;
	/**
	 * 员工组
	 */
	@JSONField(name = "PERSG")
	private String persg;
	/**
	 * 部门文本
	 */
	@JSONField(name = "STEXT")
	private String stext;
	/**
	 * 公司编码
	 */
	@JSONField(name = "COMPCODE")
	private String compcode;
	/**
	 * 城市公司编号
	 */
	@JSONField(name = "COMPCITY")
	private String compcity;
	/**
	 * 人事范围
	 */
	@JSONField(name = "WERKS")
	private String werks;
	/**
	 * 性别代码
	 */
	@JSONField(name = "GESCH")
	private String gesch;
	/**
	 * 域账号
	 */
	@JSONField(name = "ADIDNO")
	private String adidno;
	/**
	 * NC账号
	 */
	@JSONField(name = "NCNO")
	private String ncno;
	/**
	 * 工资范围
	 */
	@JSONField(name = "ABKRS")
	private String abkrs;
	/**
	 * 电话号码
	 */
	@JSONField(name = "PHONE")
	private String phone;
	/**
	 * 业务条线
	 */
	@JSONField(name = "ZYWTX_T")
	private String zywtx_t;
	/**
	 * 上级人员编号
	 */
	@JSONField(name = "PERNR_L")
	private String pernr_l;
	/**
	 * 上级姓名
	 */
	@JSONField(name = "ENAME_L")
	private String ename_l;
	/**
	 * 邮件地址
	 */
	@JSONField(name = "EMAIL")
	private String email;
	/**
	 * 职位描述
	 */
	@JSONField(name = "PLANS")
	private String plans;
	/**
	 * 系统切换时，停止登录的标志位
	 */
	@JSONField(name = "STOP_LOG")
	private String stop_log;
	
}
