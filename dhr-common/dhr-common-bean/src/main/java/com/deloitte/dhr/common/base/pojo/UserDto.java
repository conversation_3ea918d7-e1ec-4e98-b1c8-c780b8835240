package com.deloitte.dhr.common.base.pojo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 **/
@Data
@ApiModel("用户Dto")
public class UserDto {
    /**id*/
    private String id;
    /**用户名*/
    private String username;
    /**密码*/
    private String password;
    /**全名*/
    private String fullname;
    /**电话号码*/
    private String mobile;
    /**邮件*/
    private String email;
    /**菜单权限*/
    private List<Object> menuList;
    /**数据权限*/
    private List<Object> dataAuthList;
    /**授权字符串*/
    private String authorizationString;
    /**员工编码*/
    private String employeeNumber;
    /**oa名称*/
    private String oaName;
    /**界面编码*/
    private String pageCode;
    private String position;
    private String department;
}
