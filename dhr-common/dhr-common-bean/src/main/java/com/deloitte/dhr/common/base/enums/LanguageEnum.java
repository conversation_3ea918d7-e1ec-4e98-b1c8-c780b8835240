package com.deloitte.dhr.common.base.enums;

import java.util.Objects;

/**
 * 国际化语言枚举
 *
 * @author: <PERSON><PERSON><PERSON>
 * @version: 1.0
 * @date: 22/04/2022
 */
public enum LanguageEnum {

    /**中文，简体*/
    ZH_CN,
    /**英文*/
    EN;

    public static LanguageEnum getDefault() {
        return ZH_CN;
    }

    /**
     * 将指定字符串转换为枚举类型
     *
     * @author: LiYong
     * @version: 1.0
     * @date: 22/04/2022
     * @param language 源字符串
     * @return 对应的枚举类型，可能为null
     */
    public static LanguageEnum parseLanguage(String language) {
        String upperCaseLanguage = language.toUpperCase();
        for (LanguageEnum languageEnum : LanguageEnum.values()) {
            if (Objects.equals(upperCaseLanguage, String.valueOf(languageEnum))) {
                return languageEnum;
            }
        }
        return null;
    }

}
