<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/1.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/1.0.0 http://maven.apache.org/xsd/maven-1.0.0.xsd">
    <parent>
        <artifactId>dhr-common</artifactId>
        <groupId>com.deloitte.dhr</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dhr-common-mybtismvc</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-common-core</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-swagger-starter</artifactId>
        </dependency>
        <!--数据库连接-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

    </dependencies>

</project>
