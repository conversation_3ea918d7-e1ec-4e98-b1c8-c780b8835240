package com.deloitte.dhr.common;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 同步父类Mapper
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 2022/8/2
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SuperSyncModel<T extends Model<?>> extends SuperModel<T> {
    /**
     * 同步状态: Y-同步成功，F-同步失败，N-未同步
     */
    @TableField(value = "SYNC_STATUS")
    @ApiModelProperty(value = "同步状态: Y-同步成功，F-同步失败，N-未同步 ")
    private String syncStatus;

    /**
     * 同步备注，失败原因等
     */
    @TableField(value = "SYNC_COMMENT")
    @ApiModelProperty(value = "同步备注，失败原因等")
    private String syncComment;

    /**
     * 同步批次号，可追溯到schedule服务中的同步批次
     */
    @TableField(value = "BATCH_NO")
    @ApiModelProperty(value = "同步批次号，可追溯到schedule服务中的同步批次")
    private Long batchNo;

    /**
     * 同步批次时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "SYNC_BATCH_DATE")
    @ApiModelProperty(value = "同步批次时间")
    private Date syncBatchDate;

    /**
     * 当前页数
     */
    @TableField(value = "CURR_PAGE")
    @ApiModelProperty(value = "当前页数")
    private Integer currPage;

    /**
     * 删除标记，X为代表当前数据需要删除
     */
    @TableField(value = "DEL_FLAG")
    @ApiModelProperty(value = "删除标记，X为代表当前数据需要删除")
    private String delFlag;

}
