package com.deloitte.dhr.common;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deloitte.dhr.common.base.utils.DesensitizationUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Locale;

/**
 * service实现父类
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 2022/8/2
 */
public class SuperServiceImpl<M extends BaseMapper<T>, T extends SuperModel<T>> extends ServiceImpl<M, T> implements SuperService<T> {

    private static final String DESC = "DESC";

    private static final String ASC = "ASC";

    private static final String DEFAULT_ORDER = "CREATE_TIME";

    @Autowired
    protected SuperMapper<T> superMapper;

    @Override
    public T get(Serializable id) {
        return (T) superMapper.selectById(id);
    }

    @Override
    public T get(T entity) {
        return superMapper.get(entity);
    }

    @Override
    public Boolean delete(T entity) {
        return superMapper.delete(entity) == 1;
    }

    @Override
    public Boolean delete(Collection<? extends Serializable> idList) {
        return !CollectionUtils.isEmpty(idList) && superMapper.deleteBatchIds(idList) == idList.size();
    }

    @Override
    public boolean truncate(String tableName) {
        return superMapper.truncate(tableName);
    }

    @Override
    public boolean deleteAll(String tableName) {
        return superMapper.deleteAll(tableName);
    }

    @Override
    public ResponsePage<T> findList(Page<T> pageReq, T entity) {
        List<T> list = superMapper.findList(pageReq, entity);
        return new ResponsePage<T>(pageReq, list);
    }

    @Override
    public ResponsePage<T> findList(Page<T> pageReq, T entity, BaseOrder order) {
        String orderStr = adjustOrder(order);
        List<T> list = superMapper.findList(pageReq, entity, orderStr);
        return new ResponsePage<T>(pageReq, list);
    }

    @Override
    public ResponsePage findListResp(Page pageReq, Object entity, BaseOrder order) {
        String orderStr = adjustOrder(order);
        List list = superMapper.findListResp(pageReq, entity, orderStr);
        return new ResponsePage(pageReq, list);
    }

    @Override
    public ResponsePage findListResp(long current, long size, Object entity, BaseOrder order) {

        return this.findListResp(new Page<>(current, size), entity, order);
    }

    @Override
    public boolean insert(T entity) {
        entity.preInsert();
        superMapper.insert(entity);
        return true;
    }

    @Override
    public boolean update(T entity) {
        entity.preUpdate();
        superMapper.updateById(entity);
        return true;
    }

    protected String adjustOrder(BaseOrder order) {
        String orderStr = DEFAULT_ORDER + " " + DESC;
        if (null != order && StringUtils.isNotBlank(order.getField()) && StringUtils.isNotBlank(order.getSort())) {
            if (DesensitizationUtil.sql_inj(order.getField() + order.getSort())) {
                orderStr = DEFAULT_ORDER + " " + DESC;
            } else {
                if (DESC.equals(order.getSort().toUpperCase(Locale.ROOT))) {
                    orderStr = order.getField() + " " + DESC;
                } else if (ASC.equals(order.getSort().toUpperCase(Locale.ROOT))) {
                    orderStr = order.getField() + " " + ASC;
                }
            }
        }
        return orderStr;
    }

}
