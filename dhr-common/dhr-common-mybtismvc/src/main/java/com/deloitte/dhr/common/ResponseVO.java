package com.deloitte.dhr.common;

import cn.hutool.core.util.StrUtil;
import com.deloitte.dhr.common.base.enums.ResponseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.io.Serializable;

/**
 * 请求返回外层对象
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 2022/8/2
 */
@ApiModel("基础响应")
@Data
public class ResponseVO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String FAIL = ResponseEnum.GlobleException.getCode();

    public static final String SUCCESS = ResponseEnum.Success.getCode();

    /**
     * 状态码
     */
    @ApiModelProperty(value = "状态码")
    private String code;
    /**
     * 提示消息
     */
    @ApiModelProperty(value = "提示消息")
    private String message;

    /**
     * 具体内容
     */
    @ApiModelProperty("返回的数据")
    private T data;

    /**
     * 响应时间
     */
    @ApiModelProperty(value = "响应时间戳")
    private long timestamp = System.currentTimeMillis();

    public ResponseVO() {
    }

    public ResponseVO(String code, String message, T data) {
        this.code = code;
        this.data = data;
        this.message = message;
    }


    /**
     * 失败(无message)
     */
    public static ResponseVO getFailInstance() {
        return fail();
    }

    /**
     * 失败(有message)
     *
     * @param message
     */
    public static ResponseVO getFailInstance(String message) {
        return successMessage(message);
    }

    /**
     * 失败(有message)
     *
     * @param message
     */
    public static ResponseVO getFailInstance(String code, String message) {
        return msg(code, message);
    }


    public static ResponseVO getSuccessInstance() {
        return success();
    }


    /**
     * 成功(数据)
     *
     * @return JSONMessageResponse
     */
    public static ResponseVO getSuccessInstance(Object data) {
        return success(data);
    }


    /**
     * 成功
     *
     * @param code    状态码
     * @param message 消息
     * @param data    对象
     * @return return
     */
    public static <T> ResponseVO<T> result(String code, String message, T data) {
        return new ResponseVO<T>(code, message, data);
    }

    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return ResponseEnum.Success.getCode().equals(code);
    }

    /**
     * 请求成功消息
     */
    public static <T> ResponseVO<T> success() {
        return successMessage(ResponseEnum.Success.getMsg());
    }

    /**
     * 成功
     */
    public static <T> ResponseVO<T> success(T data) {
        return new ResponseVO<T>(ResponseEnum.Success.getCode(), ResponseEnum.Success.getMsg(), data);
    }

    /**
     * 消息返回
     */
    public static ResponseVO msg(String code, String message) {
        if (StrUtil.isEmpty(code)) {
            code = ResponseEnum.Success.getCode();
        }
        return new ResponseVO<>(code, message, null);
    }

    /**
     * 成功
     */
    public static ResponseVO successMessage(String message) {
        return new ResponseVO<>(ResponseEnum.Success.getCode(), message, null);
    }


    /**
     * 请求失败消息
     *
     * @param message 结果
     * @return
     */
    public static <T> ResponseVO fail(String message) {
        return new ResponseVO<>(ResponseEnum.SystemException.getCode(), message, null);
    }

    /**
     * 请求失败消息
     */
    public static <T> ResponseVO<T> fail() {
        return fail(ResponseEnum.GlobleException.getMsg());
    }


}
