package com.deloitte.dhr.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 排序
 *
 * @author: <PERSON><PERSON>ong
 * @version: 1.0
 * @date: 2022/8/2
 */
@Data
@ApiModel("排序")
public class BaseOrder {

    @ApiModelProperty(value = "排序列")
    private String field;

    @ApiModelProperty(value = "排序方式")
    private String sort;
}
