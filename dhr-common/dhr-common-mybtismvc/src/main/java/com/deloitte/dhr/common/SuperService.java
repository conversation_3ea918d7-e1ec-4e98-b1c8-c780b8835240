package com.deloitte.dhr.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;
import java.util.Collection;

/**
 * 父类SuperService
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 2022/8/2
 */
public interface SuperService<T extends SuperModel<T>> extends IService<T> {

    T get(Serializable id);

    T get(T entity);

    ResponsePage<T> findList(Page<T> pageReq, T entity);

    ResponsePage<T> findList(Page<T> pageReq, T entity, BaseOrder order);

    ResponsePage findListResp(Page pageReq, Object entity, BaseOrder order);

    ResponsePage findListResp(long current, long size, Object entity, BaseOrder order);

    boolean insert(T entity);

    boolean update(T entity);

    Boolean delete(T entity);

    Boolean delete(Collection<? extends Serializable> idList);

    boolean truncate(String tableName);

    boolean deleteAll(String tableName);
}
