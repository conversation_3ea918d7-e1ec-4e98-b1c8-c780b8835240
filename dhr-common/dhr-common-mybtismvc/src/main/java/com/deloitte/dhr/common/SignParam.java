package com.deloitte.dhr.common;

import lombok.Data;

import java.util.List;

/***
 * @Author: yinyu
 * @Date: 15/05/2023 16:20
 * @description:签名参数
 */
@Data
public class SignParam {

    /**
     * 调用方身份Id
     */
    private String appId;
    /**
     * 加密随机盐
     */
    private String salt;
    /**
     * 时间戳
     */
    private String timestamp;
    /**
     * 加密参数列表
     */
    private List<String> params;
    /**
     * 签名
     */
    private String sign;

}
