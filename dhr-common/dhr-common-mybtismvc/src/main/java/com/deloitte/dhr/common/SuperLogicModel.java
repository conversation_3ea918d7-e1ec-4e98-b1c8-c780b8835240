package com.deloitte.dhr.common;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SuperLogicModel
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 2022/8/2
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SuperLogicModel<T extends Model<?>> extends SuperModel<T> {

    @TableField(value = "DELETED")
    @ApiModelProperty(value = "是否删除：0-未删除 1-删除")
    @TableLogic()
    private Boolean deleted = false;

}
