package com.deloitte.dhr.common;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 父类Mapper
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 2022/8/2
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SuperSimpleModel<T extends Model<?>> extends Model<T> {

    @TableField(value = "UPDATE_BY",fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "修改者")
    private String updateBy;

    @TableField(value = "CREATE_BY",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建者")
    private String createBy;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    protected Date createTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "UPDATE_TIME" ,fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "修改时间")
    protected Date updateTime;

    @TableField(value = "REMARK")
    @ApiModelProperty(value = "备注")
    private String remark;

    public void preInsert() {
    }

    public void preUpdate() {
    }

    /**
     * 子类用来自定义重写，插入操作
     * @return
     */
    @Override
    public boolean insert() {
        return super.insert();
    }

    /**
     * 子类用来自定义重写，修改操作
     * @return
     */
    @Override
    public boolean updateById() {
        return super.updateById();
    }


}
