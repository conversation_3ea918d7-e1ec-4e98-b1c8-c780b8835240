package com.deloitte.dhr.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 查询结果分页基类
 *
 * @author: <PERSON>Yong
 * @version: 1.0
 * @date: 2022/8/2
 */
@Setter
@Getter
@ApiModel(description = "查询结果分页基类")
public class ResponsePage<T> {

    @ApiModelProperty(value = "总条数")
    private long totalCount = 0;

    @ApiModelProperty(value = "当前页码")
    private long pageNum = 0;

    @ApiModelProperty(value = "分页条数")
    private long pageSize = 0;

    @ApiModelProperty(value = "最大页码")
    private long maxPage = 0;

    @ApiModelProperty(value = "数据列表")
    private List<T> dataList;

    public ResponsePage() {
    }

    @SuppressWarnings("rawtypes")
    public ResponsePage(Page page, List<T> dataList) {
        this.pageNum = page.getCurrent();
        this.pageSize = page.getSize();
        this.totalCount = page.getTotal();
        this.dataList = dataList;
        this.maxPage = (long) Math.ceil((double)page.getTotal()/(double)page.getSize());
    }

    public ResponsePage(Page<T> pageReq, long totalCount, List<T> dataList) {
        this.pageNum = pageReq.getCurrent();
        this.pageSize = pageReq.getSize();
        this.totalCount = totalCount;
        this.dataList = dataList;
        this.maxPage = (long) Math.ceil((double)totalCount/(double)pageReq.getSize());
    }

    public ResponsePage(long pageNum, long pageSize, long totalCount, List<T> dataList) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.totalCount = totalCount;
        this.dataList = dataList;
        this.maxPage = (long) Math.ceil((double)totalCount/(double)pageSize);
    }

}
