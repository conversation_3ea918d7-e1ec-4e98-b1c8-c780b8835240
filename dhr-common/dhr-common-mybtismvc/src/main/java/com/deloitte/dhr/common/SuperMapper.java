package com.deloitte.dhr.common;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 父类Mapper
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 2022/8/2
 */
public interface SuperMapper<T> extends BaseMapper<T> {

    T get(T entity);

    List<T> findList(Page<T> page, @Param("parma") T entity);

    List<T> findList(Page<T> page, @Param("parma") T entity, @Param("order") String orderStr);

    List findListResp(Page page, @Param("parma") Object entity, @Param("order") String orderStr);

    int delete(T entity);

    @Update("truncate ${tableName} ")
    boolean truncate(@Param("tableName") String tableName);

    @Update("delete from ${tableName} ")
    boolean deleteAll(@Param("tableName") String tableName);
}
