package com.deloitte.dhr.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;

/**
 * 请求参数
 *
 * @author: <PERSON><PERSON><PERSON>
 * @version: 1.0
 * @date: 2022/8/2
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("请求参数")
@Valid
public class Request<T> {
    @Valid
    @ApiModelProperty(value = "默认参数")
    private T param;

    @ApiModelProperty(value = "请求Id")
    private String requestId;

    @ApiModelProperty(value = "排序")
    private BaseOrder order;

    @ApiModelProperty(value = "签名参数")
    private SignParam signParam;

    public T get() {
        return param;
    }

    public Request(T parma) {
        this.param = parma;
    }
}
