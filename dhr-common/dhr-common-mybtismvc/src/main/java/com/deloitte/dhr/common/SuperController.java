package com.deloitte.dhr.common;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 请求返回外层对象
 *
 * @author: <PERSON><PERSON>ong
 * @version: 1.0
 * @date: 2022/8/2
 */
public class SuperController {

    @Resource
    protected HttpServletRequest request;
    @Resource
    protected HttpServletResponse response;
    /**
     * 当前页
     */
    protected static final String CURRENT = "current";
    /**
     * 每页显示条数
     */
    protected static final String SIZE = "size";

    /**
     * 默认每页条目20,最大条目数100
     */
    int DEFAULT_LIMIT = 20;
    int MAX_LIMIT = 10000;

    /**
     * 成功返回
     *
     * @param data
     * @return
     */
    public <T> ResponseVO<T> success(T data) {
        return ResponseVO.success(data);
    }

    public ResponseVO<Boolean> success() {
        return ResponseVO.success();
    }

    public ResponseVO<Boolean> successMessage(String message) {
        return ResponseVO.successMessage(message);
    }

    /**
     * 失败返回
     *
     * @param msg
     * @return
     */
    public <T> ResponseVO<T> fail(String msg) {
        return ResponseVO.fail(msg);
    }

    /**
     * 失败返回
     *
     * @param code
     * @param msg
     * @return
     */
    public <T> ResponseVO<T> fail(String code, String msg) {
        return ResponseVO.result(code, msg, null);
    }



}
