package com.deloitte.dhr.common.pojo;

import lombok.Data;

import java.util.List;

/**
 * Created  on 2022/08/07
 */
@Data
public class BaseParam {

    /**
     * 页面数据权限编码，public才能供DateAuthInnerInterceptor获取到
     */
    private String pageCode;

    /**
     * 数据权限策略
     */
    private Class dataAuthStrategy;

    /**
     * 权限策略列表，和注解顺序从上至下一一对应
     */
    private List<Class> dataAuthStrategyList;
}
