package com.deloitte.dhr.common.util;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;

/**
 * @Author: jack liu
 * @Date: Create in 2020/12/25
 * @Description:
 **/
@Slf4j
public class ReflectUtil {


    /**
     * 获取指定对象的指定属性
     * */
    public static Object getFieldValue (Object obj, String fieldName){
        Object result=null;
        Field field= ReflectUtil.getField(obj,fieldName);
        if(field!=null){
            field.setAccessible(true);
            try {
                result=field.get(obj);
            }catch (Exception  e){
                log.error(e.toString());
            }
        }
        return result;
    }

    private static Field getField ( Object obj, String fieldName){
        Field field=null;
        for(Class<?> clazz=obj.getClass(); clazz!=Object.class;clazz=clazz.getSuperclass()){
            try {
                field=clazz.getDeclaredField(fieldName);
            }catch (NoSuchFieldException e){
                //顺着父类一直查找
            }
        }
        return  field;
    }

    /**
     * 为指定对象的指定属性设置指定的值
     *
     * */
    public static void setFieldValue (Object obj, String fieldName,Object fieldValue){
        Field field=ReflectUtil.getField(obj,fieldName);
        if(field!=null){
            try {
                field.setAccessible(true);
                field.set(obj,fieldValue);
            }catch (Exception e){
                log.error(e.toString());
            }
        }
    }
}
