package com.deloitte.dhr.common.config;

import java.lang.annotation.*;

/**
 * <AUTHOR> @Date: Create in 2022/08/06
 * @Description: 权限注解
 **/

@Repeatable(value = DataAuthList.class)
@Documented
@Target(value = {ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DataAuth {


    /**
     * 权限策略
     */
    Class dataAuthStrategy();

    /**
     * 表别名
     */
    String tableAlias() default "";

    /**
     * 权限字段
     */
    String authColumn() default "";


    /**
     * 权限参数，方便后期扩展
     * 第一个PAGE CODE
     */
    String[] authArgs() default {};

}
