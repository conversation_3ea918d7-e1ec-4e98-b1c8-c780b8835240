package com.deloitte.dhr.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: <PERSON>
 * @Date: Create in 2022/08/07
 * @Description: MDA模块的基础配置数据
 **/
@Data
@Configuration
@ConfigurationProperties("dhr.mda")
public class MdaProperties {

    /**权限缓存前缀**/
    private String  authorityCacheKyePrefix="MDA_AUTHORITY_";

    /**菜单缓存前缀**/
    private String menuCacheKyePrefix="MDA_MENU_";

    /**权限缓存的默认时长**/
    private int menuDefaultCacheExpire =60;

    /**字典表缓存前缀**/
    private String dictionaryCacheKyePrefix="MDA_DICTIONARY_";

    /**超级管理员roleCode**/
    private String superAdmin = "admin";


}
