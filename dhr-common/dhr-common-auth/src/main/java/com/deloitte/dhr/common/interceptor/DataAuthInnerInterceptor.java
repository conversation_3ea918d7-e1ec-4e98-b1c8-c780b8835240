package com.deloitte.dhr.common.interceptor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.config.DataAuth;
import com.deloitte.dhr.common.config.DataAuthStrategy;
import com.deloitte.dhr.common.config.MdaProperties;
import com.deloitte.dhr.common.pojo.BaseParam;
import com.deloitte.dhr.common.util.ReflectUtil;
import io.leangen.geantyref.TypeFactory;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.sql.SQLException;
import java.util.*;

/**
 * @Date: Create in 2022/08/06
 * @Description: 权限插件。 拦截查询sql，并注入权限逻辑
 **/

@Data
@NoArgsConstructor
@Component
@Slf4j
public class DataAuthInnerInterceptor implements InnerInterceptor, ApplicationContextAware {

    private static ApplicationContext context;

    private static MdaProperties mdaProperties;

//    private static DhrPermissionService dhrPermissionService;

    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
        try {
            /*判断当前sql是否为查询sql，如果不是则直接返回*/
            if (!SqlCommandType.SELECT.equals(ms.getSqlCommandType())) {
                return;
            }

            /*判断当前用户是否有超级管理员角色，如果有直接返回*/
            //放到对应strategy里面，需要进行权限过滤再判断
//            if (dhrPermissionService.hasRole(mdaProperties.getSuperAdmin())) {
//                return;
//            }

            List<DataAuth> authList = getDataAuthImpl(ms);
            /*判断当前的查询sql是否含有权限注解，如果没有则直接放行，如果有，则准备注入权限逻辑*/
            if (CollectionUtil.isEmpty(authList)) {
                return;
            }
            /*
              根据参数中的pageCode动态设置
             */
            List<DataAuth> newDataAuthList = setCustomParam(parameter, authList);
            String sql = ((Select) CCJSqlParserUtil.parse(boundSql.getSql())).getSelectBody().toString();
            for (DataAuth newDataAuth : newDataAuthList) {
                sql = permissionSql(sql, newDataAuth);
            }
            ReflectUtil.setFieldValue(boundSql, "sql", sql);
        } catch (Exception e) {
            log.error("数据权限插件异常：{}", e.getMessage());
            throw new CommRunException(e.getMessage());
        }

    }

    private String permissionSql(String sql, DataAuth dataAuth) throws Exception{
        DataAuthStrategy dataAuthStrateg = (DataAuthStrategy) context.getBean(dataAuth.dataAuthStrategy());
        Select select = null;
//        try {
            select = (Select) CCJSqlParserUtil.parse(sql);
            PlainSelect selectBody = (PlainSelect) select.getSelectBody();
//            FromItem fromItem = selectBody.getFromItem();
//            String mainTableAlias = selectBody.getFromItem().getAlias().getName();
//            if (!StringUtils.isEmpty(mainTableAlias) && mainTableAlias.equals(dataAuth.tableAlias())) {
                dataAuthStrateg.constructorAuthDataSql(selectBody, dataAuth);
                sql = select.toString();
//                return sql;
//            } else if (fromItem instanceof SubSelect) {
//                sql = permissionSql(((SubSelect) selectBody.getFromItem()).getSelectBody().toString(), dataAuth);
//            }
//            if (!StringUtils.isEmpty(sql)) {
//                sql = sql.replace(((SubSelect) selectBody.getFromItem()).getSelectBody().toString(), sql);
//                return sql;
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new CommRunException("dhr.mdb.db.error.dataAuthInnerInterceptor", "数据权限插件异常");
//        }
        return sql;

    }


    private List<DataAuth> getDataAuthImpl(MappedStatement mappedStatement) {
        List<DataAuth> authList = new ArrayList<>();
        try {
            String mappedStatementId = mappedStatement.getId();
            String className = mappedStatementId.substring(0, mappedStatementId.lastIndexOf("."));
            String methodName = mappedStatementId.substring(mappedStatementId.lastIndexOf(".") + 1, mappedStatementId.length());

            Class<?> classType = Class.forName(className);
            for (Method method : classType.getDeclaredMethods()) {
                if (!methodName.equalsIgnoreCase(method.getName())) {
                    continue;
                }
                DataAuth[] annotations = method.getAnnotationsByType(DataAuth.class);
                if (CollectionUtil.isNotEmpty(Arrays.asList(annotations))) {
                    authList.addAll(Arrays.asList(annotations));
                    break;
                }
            }
        } catch (Exception e) {
            throw new CommRunException("dhr.mdb.db.error.constructPermissionSql", "构造数据权限sql异常");
        }
        return authList;

    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
        mdaProperties = context.getBean(MdaProperties.class);
//        dhrPermissionService = context.getBean("ss", DhrPermissionService.class);
    }

    private List<DataAuth> setCustomParam(Object param, List<DataAuth> dataAuthList) {
        if (Objects.isNull(param)) {
            return dataAuthList;
        }
        //参数列表
        MapperMethod.ParamMap<Object> paramMap = new MapperMethod.ParamMap<>();

        if (param instanceof MapperMethod.ParamMap) {
            paramMap.putAll((MapperMethod.ParamMap<Object>) param);
        } else {
            paramMap.put(param.getClass().toString(), param);
        }
        if (MapUtil.isEmpty(paramMap)) {
            return dataAuthList;
        }
        //获取pageCode和权限策略
        String pageCode = null;
        Class dataAuthStrategy = null;
        List<Class> dataAuthStrategyList = new ArrayList<>();
        for (Object value : paramMap.values()) {
            if (ObjectUtil.isNull(value)) {
                continue;
            }
            if (!BaseParam.class.isAssignableFrom(value.getClass())) {
                continue;
            }
            //pageCode
            Object pageCodeObject = ReflectUtil.getFieldValue(value, "pageCode");
            if (ObjectUtil.isNotNull(pageCodeObject)) {
                pageCode = (String) pageCodeObject;
            }
            //权限策略
            Object dataAuthStrategyObject = ReflectUtil.getFieldValue(value, "dataAuthStrategy");
            if (ObjectUtil.isNotNull(dataAuthStrategyObject)) {
                dataAuthStrategy = (Class) dataAuthStrategyObject;
            }
            //权限策略列表
            Object dataAuthStrategyListObject = ReflectUtil.getFieldValue(value, "dataAuthStrategyList");
            if (ObjectUtil.isNotNull(dataAuthStrategyListObject)) {
                dataAuthStrategyList = (List<Class>) dataAuthStrategyListObject;
            }
        }
        if (StrUtil.isBlank(pageCode)
                && ObjectUtil.isNull(dataAuthStrategy)
                && CollectionUtil.isEmpty(dataAuthStrategyList)) {
            return dataAuthList;
        }
        if (ObjectUtil.isNotNull(dataAuthStrategy) && CollectionUtil.isEmpty(dataAuthStrategyList)) {
            dataAuthStrategyList.add(dataAuthStrategy);
        }
        //
        List<DataAuth> result = new ArrayList<>();
        for (int index = 0; index < dataAuthList.size(); index++) {
            DataAuth dataAuthTemp = dataAuthList.get(index);
            InvocationHandler handler = Proxy.getInvocationHandler(dataAuthTemp);
            Map<String, Object> memberValues = (Map<String, Object>) cn.hutool.core.util.ReflectUtil.getFieldValue(handler, "memberValues");
            Map<String, Object> newMemberValues = new LinkedHashMap<>(memberValues);

            //设置pageCode
            if (StrUtil.isNotBlank(pageCode)) {
                String[] authArgs = dataAuthTemp.authArgs();
                if (ArrayUtil.isNotEmpty(authArgs)) {
                    authArgs[0] = pageCode;
                }
                newMemberValues.put("authAgrg", authArgs);
                log.info("数据权限拦截器动态更改页面pageCode为[{}]", pageCode);
            }

            //设置dataAuthStategy
            if (CollectionUtil.isNotEmpty(dataAuthStrategyList) && dataAuthStrategyList.size() > index) {
                Class strategyTemp = dataAuthStrategyList.get(index);
                newMemberValues.put("dataAuthStrategy", strategyTemp);
                log.info("数据权限拦截器动态更改权限策略为[{}]", strategyTemp);
            }

            try {
                result.add(TypeFactory.annotation(DataAuth.class, newMemberValues));
            } catch (Exception e) {
                log.error(e.toString());
                result.add(dataAuthTemp);
            }
        }
        return result;
    }

}
