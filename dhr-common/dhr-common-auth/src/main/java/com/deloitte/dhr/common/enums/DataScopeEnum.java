package com.deloitte.dhr.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 数据范围枚举
 * <AUTHOR>
 * @since 2022-08-09
 */
public enum DataScopeEnum {

    /**
     * 组织范围
     */
    ORG_SCOPE("ORG_SCOPE","组织范围"),
    EMP_GROUP_SCOPE("EMP_GROUP_SCOPE","员工组范围"),
    EMP_SUB_GROUP_SCOPE("EMP_SUB_GROUP_SCOPE","员工子组范围"),
    PERSONNEL_SCOPE("PERSONNEL_SCOPE","人事范围"),
    PERSONNEL_SUB_SCOPE("PERSONNEL_SUB_SCOPE","人事子范围"),
    SALARY_ACCOUNTING("SALARY_ACCOUNTING","薪酬核算范围"),
    INFO_TYPE_SCOPE("INFO_TYPE_SCOPE","信息类型");


    DataScopeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    @EnumValue
    @JsonValue
    private final String code;

    private final String msg;
}
