# 忽略 IntelliJ IDEA 的项目文件
.idea/
*.iml
*.ipr
*.iws

# 忽略 Maven 的构建输出
target/
**/target/
.flattened-pom.xml

# 忽略 Gradle 的构建输出
.gradle/
build/
**/build/

# 忽略 Eclipse 的项目文件
.settings/
.project
.classpath
.metadata/

# 忽略 macOS 的系统文件
.DS_Store

# 忽略日志文件
*.log
logs/

# 忽略 Spring Boot 的本地配置文件
application-local.properties
application-local.yml

# 忽略依赖管理工具的缓存
.mvn/
mvnw
mvnw.cmd
gradlew
gradlew.bat

# 忽略其他临时文件
*.swp
*.swo
*.bak
*.tmp
*.orig

# 忽略测试输出
**/test-output/
**/test-results/

# 忽略本地开发环境配置文件
.env
.env.local

# 忽略 Docker 相关文件
# docker-compose.yml
# Dockerfile
# **/docker/
# **/docker-compose/

# 忽略 Node.js 相关文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 忽略前端构建输出
dist/
**/dist/

# 忽略文件夹
.git/
.vscode/
.cursor/