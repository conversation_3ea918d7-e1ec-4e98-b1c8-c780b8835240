#!/bin/bash

# 构建所有 DHR 微服务 Docker 镜像
# 智能平台检测和批量构建

set -e

# 镜像仓库配置
REGISTRY="crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com"
NAMESPACE="deloitte-lkk"

# 检测当前系统架构
CURRENT_ARCH=$(uname -m)
DOCKER_ARCH=$(docker version --format '{{.Server.Arch}}')

# 默认参数
FORCE_PLATFORM=""
VERSION="1.0.0"
BUILD_ARGS=""
SERVICES=("gateway" "ai" "oauth" "utility")
PUSH_TO_REGISTRY=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --platform)
            FORCE_PLATFORM="$2"
            shift 2
            ;;
        --version)
            VERSION="$2"
            shift 2
            ;;
        --amd64)
            FORCE_PLATFORM="linux/amd64"
            shift
            ;;
        --arm64)
            FORCE_PLATFORM="linux/arm64"
            shift
            ;;
        --native)
            FORCE_PLATFORM=""
            shift
            ;;
        --services)
            SERVICES=(${2//,/ })
            shift 2
            ;;
        --push)
            PUSH_TO_REGISTRY=true
            shift
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --platform PLATFORM  指定目标平台 (如: linux/amd64, linux/arm64)"
            echo "  --version VERSION    指定镜像版本 (默认: 1.0.0)"
            echo "  --amd64              构建 AMD64 架构镜像"
            echo "  --arm64              构建 ARM64 架构镜像"
            echo "  --native             使用本机架构构建 (推荐)"
            echo "  --services LIST      指定要构建的服务 (默认: gateway,ai,oauth,utility)"
            echo "  --push               构建完成后推送到镜像仓库"
            echo "  --help, -h           显示此帮助信息"
            echo ""
            echo "可用服务:"
            echo "  gateway              网关服务 (端口: 9110)"
            echo "  ai                   AI服务 (端口: 9044)"
            echo "  oauth               认证服务 (端口: 9006)"
            echo "  utility             工具服务 (端口: 8080)"
            echo ""
            echo "示例:"
            echo "  $0                                    # 构建所有服务"
            echo "  $0 --services gateway,ai              # 只构建网关和AI服务"
            echo "  $0 --amd64 --version 3.7.0           # 构建AMD64架构的3.7.0版本"
            echo "  $0 --push --version 1.0.0            # 构建并推送到镜像仓库"
            echo ""
            echo "镜像仓库:"
            echo "  REGISTRY: $REGISTRY"
            echo "  NAMESPACE: $NAMESPACE"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 智能选择构建平台
if [[ -n "$FORCE_PLATFORM" ]]; then
    BUILD_ARGS="--platform $FORCE_PLATFORM"
    TARGET_ARCH="$FORCE_PLATFORM"
    echo "🎯 强制指定目标架构: $FORCE_PLATFORM"
else
    # 使用本机架构构建（推荐）
    if [[ "$CURRENT_ARCH" == "arm64" ]]; then
        TARGET_ARCH="linux/arm64 (本机架构)"
    elif [[ "$CURRENT_ARCH" == "x86_64" ]]; then
        TARGET_ARCH="linux/amd64 (本机架构)"
    else
        TARGET_ARCH="$DOCKER_ARCH (Docker 默认)"
    fi
    echo "🚀 使用本机架构构建: $TARGET_ARCH"
fi

echo "=================================="
echo "🏗️  DHR 微服务批量构建信息"
echo "=================================="
echo "📋 镜像版本: $VERSION"
echo "🎯 目标架构: $TARGET_ARCH"
echo "💻 当前系统: $CURRENT_ARCH"
echo "🐳 Docker 架构: $DOCKER_ARCH"
echo "🔧 构建服务: ${SERVICES[*]}"
if [[ "$PUSH_TO_REGISTRY" == true ]]; then
    echo "📤 推送仓库: ${REGISTRY}/${NAMESPACE}"
fi
echo "=================================="

# 服务配置映射
declare -A SERVICE_CONFIGS
SERVICE_CONFIGS["gateway"]="dhr-service/dhr-gateway-service/Dockerfile:dhr-gateway-service"
SERVICE_CONFIGS["ai"]="dhr-service/dhr-ai-service/dhr-ai-provider/Dockerfile:dhr-ai-service"
SERVICE_CONFIGS["oauth"]="dhr-service/dhr-oauth-service/Dockerfile:dhr-oauth-service"
SERVICE_CONFIGS["utility"]="dhr-service/dhr-utility-service/dhr-utility-provider/Dockerfile:dhr-utility-service"

# 构建统计
BUILD_SUCCESS=0
BUILD_FAILED=0

# 检查Docker登录状态
check_docker_login() {
    if [[ "$PUSH_TO_REGISTRY" == true ]]; then
        echo "🔐 检查镜像仓库登录状态..."
        if ! grep -q "$REGISTRY" ~/.docker/config.json 2>/dev/null; then
            echo "⚠️  未登录到镜像仓库: $REGISTRY"
            echo "请先执行: docker login $REGISTRY"
            exit 1
        fi
        echo "✅ 镜像仓库登录状态正常"
    fi
}

# 推送镜像到仓库
push_image() {
    local service_name=$1
    local image_name=${SERVICE_CONFIGS[$service_name]#*:}
    local local_tag="dhr-${image_name}:${VERSION}"
    local remote_tag="${REGISTRY}/${NAMESPACE}/dhr-${image_name}:${VERSION}"
    local remote_latest="${REGISTRY}/${NAMESPACE}/dhr-${image_name}:latest"

    echo "📤 推送镜像: $local_tag -> $remote_tag"

    # 添加远程标签
    docker tag "$local_tag" "$remote_tag"
    docker tag "$local_tag" "$remote_latest"

    # 推送版本标签
    if docker push "$remote_tag"; then
        echo "✅ 版本标签推送成功: $remote_tag"
    else
        echo "❌ 版本标签推送失败: $remote_tag"
        return 1
    fi

    # 推送latest标签
    if docker push "$remote_latest"; then
        echo "✅ latest标签推送成功: $remote_latest"
    else
        echo "❌ latest标签推送失败: $remote_latest"
        return 1
    fi
}

# 构建单个服务
build_service() {
    local service_name=$1
    local dockerfile_path=${SERVICE_CONFIGS[$service_name]%:*}
    local image_name=${SERVICE_CONFIGS[$service_name]#*:}
    local image_tag="dhr-${image_name}:${VERSION}"

    echo ""
    echo "🔨 构建服务: $service_name"
    echo "📁 Dockerfile: $dockerfile_path"
    echo "🏷️  镜像标签: $image_tag"

    if docker build $BUILD_ARGS -t "$image_tag" . -f "$dockerfile_path"; then
        echo "✅ $service_name 构建成功"
        ((BUILD_SUCCESS++))

        # 显示镜像信息
        echo "📊 镜像信息:"
        docker images | grep "$image_tag" | head -1

        # 如果启用推送，则推送到仓库
        if [[ "$PUSH_TO_REGISTRY" == true ]]; then
            if push_image "$service_name"; then
                echo "✅ $service_name 推送成功"
            else
                echo "❌ $service_name 推送失败"
                ((BUILD_FAILED++))
                return 1
            fi
        fi
    else
        echo "❌ $service_name 构建失败"
        ((BUILD_FAILED++))
        return 1
    fi
}

# 开始批量构建
echo ""
echo "🚀 开始批量构建..."
echo "=================================="

# 检查Docker登录状态
check_docker_login

for service in "${SERVICES[@]}"; do
    if [[ -n "${SERVICE_CONFIGS[$service]}" ]]; then
        if build_service "$service"; then
            echo "✅ $service 服务构建完成"
        else
            echo "❌ $service 服务构建失败"
        fi
    else
        echo "⚠️  未知服务: $service"
        ((BUILD_FAILED++))
    fi
done

# 构建结果汇总
echo ""
echo "=================================="
echo "📊 构建结果汇总"
echo "=================================="
echo "✅ 构建成功: $BUILD_SUCCESS"
echo "❌ 构建失败: $BUILD_FAILED"
echo "🎯 目标架构: $TARGET_ARCH"
echo "📋 镜像版本: $VERSION"

if [[ "$PUSH_TO_REGISTRY" == true ]]; then
    echo "📤 推送状态: 已推送到 ${REGISTRY}/${NAMESPACE}"
fi

if [[ $BUILD_FAILED -eq 0 ]]; then
    echo ""
    echo "🎉 所有服务构建成功！"
    echo ""
    echo "📋 构建的镜像:"
    docker images | grep "dhr-" | grep "$VERSION" | sort

    if [[ "$PUSH_TO_REGISTRY" == true ]]; then
        echo ""
        echo "📤 推送的镜像:"
        echo "  ${REGISTRY}/${NAMESPACE}/dhr-gateway-service:${VERSION}"
        echo "  ${REGISTRY}/${NAMESPACE}/dhr-ai-service:${VERSION}"
        echo "  ${REGISTRY}/${NAMESPACE}/dhr-oauth-service:${VERSION}"
        echo "  ${REGISTRY}/${NAMESPACE}/dhr-utility-service:${VERSION}"
    fi
else
    echo ""
    echo "⚠️  部分服务构建失败，请检查错误信息"
    exit 1
fi
