# DHR 微服务构建脚本使用指南

## 概述

本项目提供了多个Docker镜像构建脚本，支持智能平台检测、灵活的构建选项和**镜像推送功能**。所有脚本都支持AMD64和ARM64架构，并能够自动检测本机架构。

## 🆕 新增功能

### 镜像推送支持
- **自动推送**: 使用 `--push` 参数自动推送到阿里云镜像仓库
- **仓库配置**: 预配置的镜像仓库地址和命名空间
- **双标签推送**: 同时推送版本标签和latest标签
- **登录检查**: 自动检查Docker登录状态

## 镜像仓库配置

```bash
REGISTRY="crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com"
NAMESPACE="deloitte-lkk"
```

**推送前请确保已登录**: `docker login crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com`

## 构建脚本列表

### 1. 单个服务构建脚本

#### `build-gateway-service.sh` - 网关服务构建
- **服务端口**: 9110
- **默认标签**: `dhr-gateway-service:1.0.0`
- **Dockerfile路径**: `dhr-service/dhr-gateway-service/Dockerfile`
- **推送支持**: ✅ 支持 `--push` 参数

#### `build-ai-service.sh` - AI服务构建
- **服务端口**: 9044
- **默认标签**: `dhr-ai-service:1.0.0`
- **Dockerfile路径**: `dhr-service/dhr-ai-service/dhr-ai-provider/Dockerfile`
- **推送支持**: ✅ 支持 `--push` 参数

#### `build-oauth-service.sh` - 认证服务构建
- **服务端口**: 9006
- **默认标签**: `dhr-oauth-service:1.0.0`
- **Dockerfile路径**: `dhr-service/dhr-oauth-service/Dockerfile`
- **推送支持**: ✅ 支持 `--push` 参数

#### `build-utility-service.sh` - 工具服务构建
- **服务端口**: 8080
- **默认标签**: `dhr-utility-service:1.0.0`
- **Dockerfile路径**: `dhr-service/dhr-utility-service/dhr-utility-provider/Dockerfile`
- **推送支持**: ✅ 支持 `--push` 参数

### 2. 批量构建脚本

#### `build-all-services.sh` - 统一构建脚本
- **功能**: 一次性构建所有微服务
- **支持**: 选择性构建、版本控制、平台指定、**批量推送**
- **推送支持**: ✅ 支持 `--push` 参数

## 通用选项

所有构建脚本都支持以下选项：

```bash
--platform PLATFORM     # 指定目标平台 (如: linux/amd64, linux/arm64)
--tag TAG               # 指定镜像标签 (单个服务脚本)
--version VERSION       # 指定镜像版本 (批量构建脚本)
--amd64                 # 构建 AMD64 架构镜像
--arm64                 # 构建 ARM64 架构镜像
--native                # 使用本机架构构建 (推荐)
--push                  # 🆕 构建完成后推送到镜像仓库
--help, -h              # 显示帮助信息
```

## 使用示例

### 单个服务构建

```bash
# 使用本机架构构建网关服务
./build-gateway-service.sh

# 构建AMD64架构的AI服务
./build-ai-service.sh --amd64

# 构建指定版本的认证服务
./build-oauth-service.sh --tag dhr-oauth-service:3.7.0

# 构建ARM64架构的工具服务
./build-utility-service.sh --arm64

# 🆕 构建并推送到镜像仓库
./build-gateway-service.sh --push
./build-ai-service.sh --push --tag dhr-ai-service:1.0.0
```

### 批量构建

```bash
# 构建所有服务
./build-all-services.sh

# 只构建网关和AI服务
./build-all-services.sh --services gateway,ai

# 构建AMD64架构的3.7.0版本
./build-all-services.sh --amd64 --version 3.7.0

# 🆕 构建所有服务并推送到镜像仓库
./build-all-services.sh --push --version 1.0.0

# 🆕 选择性构建并推送
./build-all-services.sh --services gateway,ai --push --version 1.0.0
```

## 🚀 镜像推送功能详解

### 推送流程

1. **构建镜像**: 使用指定的参数构建Docker镜像
2. **登录检查**: 自动检查是否已登录到镜像仓库
3. **添加标签**: 为镜像添加远程仓库标签
4. **推送镜像**: 推送版本标签和latest标签到仓库

### 推送的镜像标签

```bash
# 版本标签
crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-service-name:version

# latest标签
crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-service-name:latest
```

### 推送示例

```bash
# 构建并推送网关服务
./build-gateway-service.sh --push

# 输出示例:
# 📤 开始推送镜像到仓库...
# 🔐 检查镜像仓库登录状态...
# ✅ 镜像仓库登录状态正常
# 📤 推送镜像: dhr-gateway-service:1.0.0 -> crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-gateway-service:1.0.0
# ✅ 版本标签推送成功: crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-gateway-service:1.0.0
# ✅ latest标签推送成功: crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-gateway-service:latest
# 🎉 镜像推送成功！
```

## 架构检测

### 自动检测
- 脚本会自动检测当前系统架构
- 推荐使用 `--native` 选项（默认行为）
- 自动选择最优的构建方式

### 手动指定
- `--amd64`: 强制构建AMD64架构镜像
- `--arm64`: 强制构建ARM64架构镜像
- `--platform`: 指定具体的平台标识符

## 构建上下文

### 多阶段构建
- 所有Dockerfile都使用多阶段构建
- 构建阶段：使用 `maven:3.8.6-openjdk-11` 镜像
- 运行阶段：使用 `openjdk:11-jre-slim` 镜像

### 项目结构
- 构建上下文为项目根目录
- 支持多模块Maven项目结构
- 自动处理依赖关系和构建顺序

## 环境要求

### 必需环境
- Docker 20.10+
- 支持多架构构建的Docker环境
- 足够的磁盘空间（建议10GB+）

### 推送要求
- 已登录到镜像仓库: `docker login crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com`
- 稳定的网络连接
- 足够的推送权限

## 故障排查

### 常见问题

1. **权限问题**
   ```bash
   chmod +x build-*.sh
   ```

2. **架构不匹配**
   ```bash
   # 使用本机架构
   ./build-*.sh --native

   # 或指定特定架构
   ./build-*.sh --amd64
   ```

3. **构建失败**
   ```bash
   # 检查Docker状态
   docker info

   # 清理Docker缓存
   docker system prune -a
   ```

4. **推送失败**
   ```bash
   # 检查登录状态
   docker login crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com

   # 检查网络连接
   ping crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com
   ```

### 调试模式

```bash
# 显示详细构建信息
docker build --progress=plain -t test-image . -f Dockerfile

# 检查镜像架构
docker inspect image-name | grep Architecture

# 检查推送状态
docker images | grep "crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com"
```

## 最佳实践

### 开发环境
- 使用 `--native` 选项（默认）
- 定期清理未使用的镜像
- 使用语义化版本标签

### 生产环境
- 明确指定目标架构
- 使用稳定的版本标签
- 启用镜像扫描和安全检查
- **使用 `--push` 自动推送到仓库**

### CI/CD集成
- 支持环境变量配置
- 自动版本管理
- 构建结果报告
- **自动镜像推送和部署**

## 脚本特性

### 智能检测
- 自动检测系统架构
- 智能选择构建平台
- 错误处理和用户提示

### 灵活配置
- 支持命令行参数
- 配置文件支持
- 环境变量覆盖

### 用户友好
- 彩色输出和表情符号
- 详细的进度信息
- 清晰的帮助文档
- **推送状态实时反馈**

## 更新日志

### v1.0.0 (2024-12-19) 🆕
- **新增镜像推送功能**
- 支持阿里云镜像仓库
- 自动双标签推送（版本+latest）
- 登录状态自动检查
- 支持多阶段构建
- 支持AMD64和ARM64架构
- 智能平台检测
- 批量构建功能

### v3.7.0 (2024-12-01)
- 初始版本发布
- 基础构建功能
- 单服务构建支持

## 技术支持

如有问题，请联系：
- **团队**: Deloitte DHR Team
- **邮箱**: <EMAIL>
- **文档**: [项目Wiki](https://wiki.deloitte.com/dhr)

## 许可证

本项目遵循 Deloitte 内部开发规范，仅供内部使用。
