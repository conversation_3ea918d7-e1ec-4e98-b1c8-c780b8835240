apiVersion: apps/v1
kind: Deployment
metadata:
  name: dhr-gateway-service
  namespace: hr-ai-sammy-dev
  labels:
    app: dhr-gateway-service
    component: gateway
    version: "3.7.0"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dhr-gateway-service
  template:
    metadata:
      labels:
        app: dhr-gateway-service
        component: gateway
        version: "3.7.0"
    spec:
      containers:
      - name: dhr-gateway-service
        image: crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-gateway-service:1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 9110
          protocol: TCP
        env:
        - name: CONFIG_PROFILE
          value: "dev"
        - name: CONFIG_NACOS_SERVERADDR
          value: "nacos"
        - name: CONFIG_NACOS_PORT
          value: "8848"
        - name: CONFIG_NACOS_NAMESPACE
          value: "bcabaf9e-e845-4cea-8f2f-381026ee0c56"
        - name: CONFIG_NACOS_USERNAME
          value: "nacos"
        - name: CONFIG_NACOS_PASSWORD
          value: "nacos"
        - name: CONFIG_SERVER_PORT
          value: "9110"
        - name: JVM_OPTS
          value: "-Xms512m -Xmx1024m -Dfile.encoding=UTF-8 -Duser.timezone=GMT+08"
        # enableServiceLinks 的默认值为 true
        # 作用：控制是否自动为 Pod 注入集群内所有 Service 的环境变量（如 SERVICE_HOST、SERVICE_PORT 等）。
        # 设置为 false 后，Pod 内将不会自动注入这些 Service 相关的环境变量，可以减少环境变量污染，提升安全性和可维护性。
        enableServiceLinks: false
        resources:
          requests:
            memory: "1024Mi"
            cpu: "500m"
          limits:
            memory: "2048Mi"
            cpu: "1000m"
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: dhr-gateway-service
  namespace: hr-ai-sammy-dev
  labels:
    app: dhr-gateway-service
    component: gateway
spec:
  type: ClusterIP
  ports:
  - port: 9110
    targetPort: 9110
    protocol: TCP
    name: http
  selector:
    app: dhr-gateway-service
