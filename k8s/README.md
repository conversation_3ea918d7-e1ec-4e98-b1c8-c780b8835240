# DHR 微服务 Kubernetes 部署文件

本目录包含 DHR (Digital HR) 平台所有微服务的 Kubernetes 部署配置文件。

## 文件结构

```
k8s/
├── README.md                           # 部署说明文档
├── namespace.yaml                      # 命名空间和资源配额配置
├── dhr-all-services.yaml              # 所有服务的简化部署文件
├── dhr-gateway-service-deployment.yaml # 网关服务部署文件
├── dhr-oauth-service-deployment.yaml   # OAuth认证服务部署文件
├── dhr-ai-service-deployment.yaml      # AI服务部署文件
└── dhr-utility-service-deployment.yaml # 工具服务部署文件
```

## 微服务概览

| 服务名称 | 组件 | 端口 | 描述 | 镜像 |
|---------|------|------|------|------|
| dhr-gateway-service | Gateway | 9110 | API网关服务 | dhr-gateway-service:3.7.0-SNAPSHOT |
| dhr-oauth-service | Auth | 9006 | OAuth2认证服务 | dhr-oauth-service:3.7.0-SNAPSHOT |
| dhr-ai-service | AI | 9044 | HR助手、教学、培训AI服务 | dhr-ai-service:3.7.0-SNAPSHOT |
| dhr-utility-service | Utility | 8080 | 工具服务(文件、短信、邮件) | dhr-utility-service:3.7.0-SNAPSHOT |

## 部署前准备

### 1. 环境要求
- Kubernetes 1.18+
- 已配置 kubectl
- 集群中已部署 Nacos 服务发现与配置中心
- 存储类 `nfs-storage` (用于 utility 服务文件存储)

### 2. 镜像准备
确保以下 Docker 镜像已构建并推送到镜像仓库：
```bash
# 构建所有服务镜像
./build-images.sh

# 或手动构建各个服务
docker build -t dhr-gateway-service:3.7.0-SNAPSHOT ./dhr-service/dhr-gateway-service/
docker build -t dhr-oauth-service:3.7.0-SNAPSHOT ./dhr-service/dhr-oauth-service/
docker build -t dhr-ai-service:3.7.0-SNAPSHOT ./dhr-service/dhr-ai-service/dhr-ai-provider/
docker build -t dhr-utility-service:3.7.0-SNAPSHOT ./dhr-service/dhr-utility-service/dhr-utility-provider/
```

### 3. Nacos 配置
确保 Nacos 中已配置对应的服务配置文件：
- `dhr-gateway-service-prod.yaml`
- `dhr-oauth-service-prod.yaml`
- `dhr-ai-service-prod.yaml`
- `dhr-utility-service-prod.yaml`

## 部署方式

### 方式一：快速部署所有服务
```bash
# 创建命名空间和资源配额
kubectl apply -f namespace.yaml

# 部署所有服务（简化版）
kubectl apply -f dhr-all-services.yaml
```

### 方式二：逐个部署服务（推荐生产环境）
```bash
# 1. 创建命名空间
kubectl apply -f namespace.yaml

# 2. 部署网关服务
kubectl apply -f dhr-gateway-service-deployment.yaml

# 3. 部署认证服务
kubectl apply -f dhr-oauth-service-deployment.yaml

# 4. 部署AI服务
kubectl apply -f dhr-ai-service-deployment.yaml

# 5. 部署工具服务
kubectl apply -f dhr-utility-service-deployment.yaml
```

## 服务访问

### 内部访问（集群内）
- Gateway: `http://dhr-gateway-service.dhr-system:9110`
- OAuth: `http://dhr-oauth-service.dhr-system:9006`
- AI: `http://dhr-ai-service.dhr-system:9044`
- Utility: `http://dhr-utility-service.dhr-system:8080`

### 外部访问（NodePort）
- Gateway: `http://<节点IP>:30110`
- OAuth: `http://<节点IP>:30006`
- AI: `http://<节点IP>:30044`
- Utility: `http://<节点IP>:30080`

## 监控与运维

### 查看服务状态
```bash
# 查看所有Pod状态
kubectl get pods -n dhr-system

# 查看服务状态
kubectl get svc -n dhr-system

# 查看部署状态
kubectl get deployment -n dhr-system
```

### 查看日志
```bash
# 查看特定服务日志
kubectl logs -f deployment/dhr-gateway-service -n dhr-system
kubectl logs -f deployment/dhr-oauth-service -n dhr-system
kubectl logs -f deployment/dhr-ai-service -n dhr-system
kubectl logs -f deployment/dhr-utility-service -n dhr-system
```

### 健康检查
每个服务都配置了健康检查端点：
- 就绪性检查：`/actuator/health`
- 存活性检查：`/actuator/health`

## 扩容与更新

### 扩容服务
```bash
# 扩容特定服务到3个副本
kubectl scale deployment dhr-ai-service --replicas=3 -n dhr-system
```

### 更新镜像
```bash
# 更新服务镜像
kubectl set image deployment/dhr-gateway-service dhr-gateway-service=dhr-gateway-service:3.7.0 -n dhr-system
```

### 滚动更新
```bash
# 重新应用配置文件进行滚动更新
kubectl apply -f dhr-gateway-service-deployment.yaml
```

## 故障排查

### 常见问题

1. **Pod启动失败**
   ```bash
   kubectl describe pod <pod-name> -n dhr-system
   kubectl logs <pod-name> -n dhr-system
   ```

2. **无法连接Nacos**
   - 检查环境变量 `CONFIG_NACOS_SERVERADDR`
   - 确认Nacos服务在集群中可访问

3. **健康检查失败**
   - 检查服务启动时间是否足够
   - 调整 `initialDelaySeconds` 参数

4. **资源不足**
   ```bash
   kubectl describe nodes
   kubectl top nodes
   kubectl top pods -n dhr-system
   ```

## 配置自定义

### 环境变量
根据实际部署环境，需要调整以下环境变量：
- `CONFIG_NACOS_SERVERADDR`: Nacos服务地址
- `CONFIG_NACOS_PORT`: Nacos端口
- `CONFIG_NACOS_NAMESPACE`: Nacos命名空间
- `CONFIG_PROFILE`: Spring激活的配置文件

### 资源配置
根据实际负载调整每个服务的资源配置：
- `requests`: 最小资源要求
- `limits`: 最大资源限制

### 存储配置
- `dhr-utility-service` 需要持久化存储，调整 `storageClassName` 为实际的存储类
- 日志目录使用 `hostPath`，生产环境建议使用持久化卷

## 安全配置

1. **镜像安全**
   - 使用私有镜像仓库
   - 定期更新基础镜像

2. **网络安全**
   - 配置 NetworkPolicy
   - 使用 TLS 加密服务间通信

3. **权限控制**
   - 配置 RBAC
   - 使用最小权限原则

## 版本记录

- v3.7.0: 初始K8s部署配置
  - 支持4个核心微服务部署
  - 包含健康检查和资源限制
  - 提供完整的Service配置
