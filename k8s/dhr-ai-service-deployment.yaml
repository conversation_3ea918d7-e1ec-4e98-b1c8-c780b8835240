apiVersion: apps/v1
kind: Deployment
metadata:
  name: dhr-ai-service
  namespace: hr-ai-sammy-dev
  labels:
    app: dhr-ai-service
    component: ai
    version: "3.7.0"
spec:
  replicas: 2
  selector:
    matchLabels:
      app: dhr-ai-service
  template:
    metadata:
      labels:
        app: dhr-ai-service
        component: ai
        version: "3.7.0"
    spec:
      containers:
      - name: dhr-ai-service
        image: crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-ai-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 9044
          protocol: TCP
        env:
        - name: CONFIG_PROFILE
          value: "prod"
        - name: CONFIG_NACOS_SERVERADDR
          value: "nacos-service.default.svc.cluster.local"
        - name: CONFIG_NACOS_PORT
          value: "8848"
        - name: CONFIG_NACOS_NAMESPACE
          value: "bcabaf9e-e845-4cea-8f2f-381026ee0c56"
        - name: CONFIG_NACOS_USERNAME
          value: "nacos"
        - name: CONFIG_NACOS_PASSWORD
          value: "nacos"
        - name: CONFIG_SERVER_PORT
          value: "9044"
        - name: JVM_OPTS
          value: "-Xms1024m -Xmx2048m -Dfile.encoding=UTF-8 -Duser.timezone=GMT+08"
        resources:
          requests:
            memory: "1024Mi"
            cpu: "500m"
          limits:
            memory: "2048Mi"
            cpu: "1000m"
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 9044
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 9044
          initialDelaySeconds: 120
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: sap-lib
          mountPath: /app/lib
          readOnly: true
      volumes:
      - name: logs
        hostPath:
          path: /var/log/dhr/ai
          type: DirectoryOrCreate
      - name: sap-lib
        configMap:
          name: dhr-ai-sap-lib
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: dhr-ai-service
  namespace: hr-ai-sammy-dev
  labels:
    app: dhr-ai-service
    component: ai
spec:
  type: ClusterIP
  ports:
  - port: 9044
    targetPort: 9044
    protocol: TCP
    name: http
  selector:
    app: dhr-ai-service
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: dhr-ai-sap-lib
  labels:
    app: dhr-ai-service
data:
  # SAP JCO 库文件需要单独配置
  # 由于二进制文件无法直接存储在ConfigMap中，建议使用Secret或初始化容器
  README.txt: |
    SAP JCO 库文件 (sapjco3.jar) 需要单独配置
    建议使用以下方式之一：
    1. 构建时将库文件打包到镜像中
    2. 使用Secret存储库文件
    3. 使用初始化容器下载库文件
