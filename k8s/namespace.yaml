apiVersion: v1
kind: Namespace
metadata:
  name: dhr-system
  labels:
    name: dhr-system
    environment: production
    version: "3.7.0"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: dhr-resource-quota
  namespace: dhr-system
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    pods: "20"
    services: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: dhr-limit-range
  namespace: dhr-system
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
