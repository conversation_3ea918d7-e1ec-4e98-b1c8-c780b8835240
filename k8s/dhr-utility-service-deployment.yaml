apiVersion: apps/v1
kind: Deployment
metadata:
  name: dhr-utility-service
  namespace: hr-ai-sammy-dev
  labels:
    app: dhr-utility-service
    component: utility
    version: "3.7.0"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dhr-utility-service
  template:
    metadata:
      labels:
        app: dhr-utility-service
        component: utility
        version: "3.7.0"
    spec:
      containers:
      - name: dhr-utility-service
        image: crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-utility-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
          protocol: TCP
        env:
        - name: CONFIG_PROFILE
          value: "prod"
        - name: CONFIG_NACOS_SERVERADDR
          value: "nacos-service.default.svc.cluster.local"
        - name: CONFIG_NACOS_PORT
          value: "8848"
        - name: CONFIG_NACOS_NAMESPACE
          value: "bcabaf9e-e845-4cea-8f2f-381026ee0c56"
        - name: CONFIG_NACOS_USERNAME
          value: "nacos"
        - name: CONFIG_NACOS_PASSWORD
          value: "nacos"
        - name: CONFIG_SERVER_PORT
          value: "8080"
        - name: JVM_OPTS
          value: "-Xms512m -Xmx1024m -Dfile.encoding=UTF-8 -Duser.timezone=GMT+08"
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1024Mi"
            cpu: "500m"
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: dhr-utility-service
  namespace: hr-ai-sammy-dev
  labels:
    app: dhr-utility-service
    component: utility
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: dhr-utility-service

