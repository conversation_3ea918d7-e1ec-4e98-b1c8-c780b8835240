version: '3.8'

services:
  # Nacos 服务发现和配置中心
  nacos:
    image: nacos/nacos-server:v2.1.1
    container_name: dhr-nacos
    environment:
      - MODE=standalone
      - PREFER_HOST_MODE=hostname
      - SPRING_DATASOURCE_PLATFORM=mysql
      - MYSQL_SERVICE_HOST=mysql
      - MYSQL_SERVICE_PORT=3306
      - MYSQL_SERVICE_DB_NAME=nacos_config
      - MYSQL_SERVICE_USER=nacos
      - MYSQL_SERVICE_PASSWORD=nacos123
    ports:
      - "8848:8848"
      - "9848:9848"
    depends_on:
      - mysql
    volumes:
      - ./nacos/logs:/home/<USER>/logs
    networks:
      - dhr-network
    restart: unless-stopped

  # MySQL 数据库
  mysql:
    image: mysql:8.0.33
    container_name: dhr-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=root123
      - MYSQL_DATABASE=nacos_config
      - MY<PERSON>QL_USER=nacos
      - MYSQL_PASSWORD=nacos123
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - dhr-network
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7.0-alpine
    container_name: dhr-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes --requirepass redis123
    networks:
      - dhr-network
    restart: unless-stopped

  # API 网关服务
  gateway-service:
    build:
      context: ./dhr-service/dhr-gateway-service
      dockerfile: Dockerfile
    container_name: dhr-gateway-service
    environment:
      - CONFIG_PROFILE=docker
      - CONFIG_NACOS_SERVERADDR=nacos
      - CONFIG_NACOS_PORT=8848
      - CONFIG_SERVER_PORT=9110
      - WAIT_FOR_NACOS=true
    ports:
      - "9110:9110"
    depends_on:
      - nacos
      - redis
    volumes:
      - ./logs/gateway:/app/logs
    networks:
      - dhr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9110/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # OAuth 认证服务
  oauth-service:
    build:
      context: ./dhr-service/dhr-oauth-service
      dockerfile: Dockerfile
    container_name: dhr-oauth-service
    environment:
      - CONFIG_PROFILE=docker
      - CONFIG_NACOS_SERVERADDR=nacos
      - CONFIG_NACOS_PORT=8848
      - CONFIG_SERVER_PORT=9006
      - WAIT_FOR_NACOS=true
    ports:
      - "9006:9006"
    depends_on:
      - nacos
      - mysql
      - redis
    volumes:
      - ./logs/oauth:/app/logs
    networks:
      - dhr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9006/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # AI 服务
  ai-service:
    build:
      context: ./dhr-service/dhr-ai-service/dhr-ai-provider
      dockerfile: Dockerfile
    container_name: dhr-ai-service
    environment:
      - CONFIG_PROFILE=docker
      - CONFIG_NACOS_SERVERADDR=nacos
      - CONFIG_NACOS_PORT=8848
      - CONFIG_SERVER_PORT=9044
      - WAIT_FOR_NACOS=true
      - JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC
    ports:
      - "9044:9044"
    depends_on:
      - nacos
      - mysql
      - redis
    volumes:
      - ./logs/ai:/app/logs
    networks:
      - dhr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9044/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s

  # 工具服务
  utility-service:
    build:
      context: ./dhr-service/dhr-utility-service/dhr-utility-provider
      dockerfile: Dockerfile
    container_name: dhr-utility-service
    environment:
      - CONFIG_PROFILE=docker
      - CONFIG_NACOS_SERVERADDR=nacos
      - CONFIG_NACOS_PORT=8848
      - CONFIG_SERVER_PORT=8080
      - WAIT_FOR_NACOS=true
    ports:
      - "8080:8080"
    depends_on:
      - nacos
      - mysql
      - redis
    volumes:
      - ./logs/utility:/app/logs
      - ./files:/app/files
    networks:
      - dhr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  mysql-data:
    driver: local
  redis-data:
    driver: local

networks:
  dhr-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
