# DHR Assets End Docker 部署指南

## 概述

本项目使用Docker容器化部署，支持多阶段构建（Multi-Stage Build），构建过程完全在容器内部进行，无需在宿主机安装Maven或JDK。

## 多阶段构建架构

### 构建阶段 (Builder Stage)
- **基础镜像**: `maven:3.8.8-openjdk-11-slim`
- **架构**: `linux/amd64`
- **功能**: 在容器内部执行Maven构建
- **优势**:
  - 无需本地Maven/JDK环境
  - 构建环境完全隔离
  - 支持CI/CD流水线

### 运行阶段 (Runtime Stage)
- **基础镜像**: `openjdk:11-jre-slim`
- **架构**: `linux/amd64`
- **功能**: 运行编译后的JAR文件
- **优势**:
  - 镜像体积更小
  - 安全性更高
  - 运行时性能更好

## 微服务架构

| 服务名称 | 端口 | 内存配置 | 描述 |
|---------|------|----------|------|
| dhr-gateway-service | 9110 | 256M-512M | API网关，路由和过滤器 |
| dhr-oauth-service | 9006 | 256M-512M | OAuth2认证服务 |
| dhr-ai-service | 9044 | 512M-1024M | AI服务(HR助手、教学、培训) |
| dhr-utility-service | 8080 | 256M-512M | 工具服务(文件、短信、邮件) |

## 构建方式

### 1. 自动构建（推荐）
```bash
# 构建所有服务镜像
./build-images.sh

# 构建并推送到镜像仓库
./build-images.sh --push

# 构建多架构镜像
./build-images.sh --multi-arch --push
```

### 2. 手动构建
```bash
# 网关服务
docker build -t dhr/dhr-gateway-service:latest dhr-service/dhr-gateway-service/

# AI服务
docker build -t dhr/dhr-ai-service:latest dhr-service/dhr-ai-service/dhr-ai-provider/

# OAuth服务
docker build -t dhr/dhr-oauth-service:latest dhr-service/dhr-oauth-service/

# 工具服务
docker build -t dhr/dhr-utility-service:latest dhr-service/dhr-utility-service/dhr-utility-provider/
```

## 环境要求

### 宿主机要求
- **Docker**: 20.10+ (支持Buildx)
- **操作系统**: Linux/macOS/Windows
- **内存**: 至少4GB可用内存
- **磁盘**: 至少10GB可用空间

### 容器内环境
- **JDK**: OpenJDK 11
- **Maven**: 3.8.8
- **架构**: AMD64 (x86_64)
- **时区**: Asia/Shanghai

## 配置说明

### 环境变量
```bash
# JVM配置
JAVA_OPTS="-Xms256m -Xmx512m -XX:+UseG1GC"

# 应用配置
CONFIG_PROFILE=prod
CONFIG_ENABLE=true

# Nacos配置
CONFIG_NACOS_SERVERADDR=***********
CONFIG_NACOS_PORT=8848
CONFIG_NACOS_NAMESPACE=7f5f8a1e-e611-4003-a992-7899fa427dc9
```

### 健康检查
```dockerfile
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:PORT/actuator/health || exit 1
```

## 部署方式

### 1. Docker Compose（开发环境）
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f service-name
```

### 2. Kubernetes（生产环境）
```bash
# 部署到K8s集群
kubectl apply -f k8s/

# 查看部署状态
kubectl get pods -n dhr

# 查看服务
kubectl get svc -n dhr
```

## 镜像仓库

### 阿里云容器镜像服务
- **仓库地址**: `crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com`
- **命名空间**: `deloitte-lkk`
- **镜像标签**: `dhr/service-name:version`

### 推送镜像
```bash
# 登录镜像仓库
docker login crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com

# 构建并推送
./build-images.sh --push

# 手动推送
docker push crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-service-name:latest
```

## 监控和日志

### 日志目录
```
/app/logs/
├── dhr-gateway-service/
│   └── info/
├── dhr-oauth-service/
│   └── info/
├── dhr-ai-service/
│   └── info/
└── dhr-utility-service/
    └── info/
```

### 健康检查端点
- **网关服务**: `http://localhost:9110/actuator/health`
- **认证服务**: `http://localhost:9006/actuator/health`
- **AI服务**: `http://localhost:9044/actuator/health`
- **工具服务**: `http://localhost:8080/actuator/health`

## 故障排查

### 常见问题

1. **构建失败**
   ```bash
   # 检查Docker版本
   docker --version

   # 检查构建日志
   docker build --progress=plain -t test-image .
   ```

2. **启动失败**
   ```bash
   # 查看容器日志
   docker logs container-name

   # 检查端口占用
   netstat -tlnp | grep :PORT
   ```

3. **内存不足**
   ```bash
   # 调整JVM参数
   -Xms256m -Xmx512m

   # 检查系统内存
   free -h
   ```

### 调试模式
```bash
# 进入容器调试
docker exec -it container-name /bin/bash

# 查看进程
ps aux

# 查看网络
netstat -tlnp
```

## 最佳实践

### 安全配置
- 使用非root用户运行应用
- 限制容器资源使用
- 定期更新基础镜像
- 扫描镜像安全漏洞

### 性能优化
- 使用多阶段构建减少镜像大小
- 配置合适的JVM参数
- 启用健康检查
- 使用数据卷持久化数据

### 监控告警
- 配置资源使用监控
- 设置日志轮转
- 监控服务健康状态
- 配置异常告警

## 更新日志

### v3.7.0 (2024-12-19)
- 支持多阶段构建
- 移除宿主机Maven/JDK依赖
- 优化镜像大小和安全性
- 支持AMD64架构

### v3.6.0 (2024-12-01)
- 初始Docker化支持
- 基础微服务架构
- 健康检查和监控

## 技术支持

如有问题，请联系：
- **团队**: Deloitte DHR Team
- **邮箱**: <EMAIL>
- **文档**: [项目Wiki](https://wiki.deloitte.com/dhr)
