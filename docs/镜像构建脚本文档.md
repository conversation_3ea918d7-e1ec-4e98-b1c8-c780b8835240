# DHR 微服务 Docker 镜像构建脚本文档

## 概述

`build-images.sh` 是 DHR Assets End 项目的 Docker 镜像构建脚本，用于自动化构建和推送微服务的 Docker 镜像到阿里云容器镜像仓库。

## 功能特性

- ✅ **自动化构建** - 一键构建所有微服务的 Docker 镜像
- ✅ **JDK 版本检测** - 自动检测 JDK 版本兼容性
- ✅ **镜像推送** - 支持推送镜像到阿里云容器镜像仓库
- ✅ **灵活构建** - 支持跳过 Maven 构建，仅构建 Docker 镜像
- ✅ **清理功能** - 构建后自动清理旧镜像
- ✅ **错误处理** - 完整的错误检查和日志输出

## 技术要求

### 环境依赖
- **Docker** - 用于构建和推送镜像
- **Maven 3.8.8+** - 用于构建 Java 项目（可选）
- **JDK 11** - 推荐版本，其他版本可能导致兼容性问题
- **网络连接** - 用于推送镜像到远程仓库

### 仓库配置
- **镜像仓库**: `crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com`
- **命名空间**: `deloitte-lkk`

## 命令行选项

| 选项 | 简写 | 描述 |
|------|------|------|
| `--help` | `-h` | 显示帮助信息 |
| `--clean` | `-c` | 构建后清理旧的 Docker 镜像 |
| `--push` | `-p` | 构建完成后推送镜像到仓库 |
| `--push-only` | - | 仅推送现有镜像（跳过构建过程） |
| `--skip-maven` | - | 跳过 Maven 构建，使用现有 JAR 文件 |
| `--force-maven` | - | 强制执行 Maven 构建（忽略 JDK 版本警告） |

## 使用示例

### 基础使用

```bash
# 构建所有微服务镜像
./build-images.sh

# 构建镜像并清理旧镜像
./build-images.sh --clean

# 查看帮助信息
./build-images.sh --help
```

### Maven 构建控制

```bash
# 跳过 Maven 构建（推荐用于 JDK 版本不兼容时）
./build-images.sh --skip-maven

# 强制执行 Maven 构建
./build-images.sh --force-maven
```

### 镜像推送

```bash
# 构建并推送所有镜像
./build-images.sh --push

# 跳过 Maven 构建，构建 Docker 镜像并推送
./build-images.sh --skip-maven --push

# 仅推送现有镜像到仓库
./build-images.sh --push-only

# 构建、推送并清理
./build-images.sh --push --clean
```

## 构建流程

### 1. 环境检查阶段
- 检查 Maven 是否安装
- 检查 Docker 是否安装
- 验证 JDK 版本兼容性

### 2. 构建阶段
```
Maven 构建 (可选)
    ↓
Docker 镜像构建
    ↓
镜像标签添加
    ↓
推送到仓库 (可选)
    ↓
清理旧镜像 (可选)
```

### 3. 微服务构建顺序
1. **dhr-gateway-service** - API 网关服务
2. **dhr-oauth-service** - OAuth2 认证服务
3. **dhr-ai-service** - AI 服务（HR助手、教学、培训）
4. **dhr-utility-service** - 工具服务（文件、短信、邮件）

## 镜像标签策略

### 本地标签
- `dhr/{service-name}:{version}`
- `dhr/{service-name}:latest`

### 远程标签（推送到仓库）
- `{REGISTRY}/{NAMESPACE}/{service-name}:{version}`
- `{REGISTRY}/{NAMESPACE}/{service-name}:latest`

### 示例
```
本地: dhr/dhr-gateway-service:3.7.0
远程: crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-gateway-service:3.7.0
```

## 仓库推送配置

### 登录阿里云容器镜像仓库
```bash
docker login crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com
```

### 推送的镜像列表
推送完成后，以下镜像将在阿里云仓库中可用：

- `crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-gateway-service:latest`
- `crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-oauth-service:latest`
- `crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-ai-service:latest`
- `crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-utility-service:latest`

## 错误处理

### 常见问题及解决方案

#### 1. JDK 版本不兼容
**问题**: 当前 JDK 版本不是 11 时的警告
```
[WARNING] Detected JDK 17, but project requires JDK 11
[INFO] Automatically using --skip-maven mode to avoid compatibility issues
```

**解决方案**:
- 脚本会自动跳过 Maven 构建
- 或手动使用 `--skip-maven` 参数
- 或使用 `--force-maven` 强制构建

#### 2. Docker 未登录
**问题**: 推送镜像时认证失败
```
[WARNING] Not logged in to registry: crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com
[INFO] Please run: docker login crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com
```

**解决方案**:
```bash
docker login crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com
```

#### 3. JAR 文件不存在
**问题**: Maven 构建失败或 JAR 文件缺失
```
[ERROR] JAR file not found: dhr-service/dhr-gateway-service/target/dhr-gateway-service.jar
```

**解决方案**:
- 检查 Maven 构建是否成功
- 确保项目已正确编译
- 使用 `--force-maven` 重新构建

#### 4. Dockerfile 不存在
**问题**: 服务目录中缺少 Dockerfile
```
[ERROR] Dockerfile not found in dhr-service/dhr-gateway-service
```

**解决方案**:
- 检查服务目录结构
- 确保每个服务都有 Dockerfile

## 日志输出

脚本使用彩色日志输出，便于识别不同类型的信息：

- 🔵 **[INFO]** - 一般信息（蓝色）
- 🟢 **[SUCCESS]** - 成功操作（绿色）
- 🟡 **[WARNING]** - 警告信息（黄色）
- 🔴 **[ERROR]** - 错误信息（红色）

## 性能优化建议

### 1. 跳过 Maven 构建
如果 JAR 文件已存在且未更改，使用 `--skip-maven` 可显著减少构建时间。

### 2. 并行构建
对于大量微服务，可考虑修改脚本支持并行构建。

### 3. 增量构建
Docker 会自动利用层缓存，确保 Dockerfile 编写合理以最大化缓存效果。

## 安全考虑

### 1. 镜像仓库认证
- 不在脚本中硬编码认证信息
- 使用 `docker login` 进行安全认证
- 定期更新仓库访问凭据

### 2. 镜像内容安全
- 基础镜像使用官方 `openjdk:11-jre-slim`
- 应用以非 root 用户运行
- 定期更新基础镜像以获取安全补丁

## 扩展配置

### 自定义仓库配置
如需更改仓库配置，修改脚本中的变量：

```bash
# 镜像仓库配置
REGISTRY="your-registry.com"
NAMESPACE="your-namespace"
```

### 添加新微服务
在 `main_with_maven()` 和 `docker_only_build()` 函数中添加新的服务构建调用：

```bash
# 新服务
build_service_image "new-service-name" "path/to/new-service"
```

## 版本历史

## 维护说明

### 定期维护任务
1. 更新基础镜像版本
2. 检查依赖工具版本兼容性
3. 验证仓库推送功能
4. 清理无用的镜像标签

### 故障排查
1. 检查 Docker 守护进程状态
2. 验证网络连接
3. 确认仓库权限
4. 查看详细错误日志

---

**注意**: 此文档基于 DHR Assets End 项目的 `build-images.sh` 脚本 v3.7.0 版本编写。如有疑问，请联系 Deloitte DHR Team。
