# `--push-only` 推送失败故障排查指南

## 常见失败原因及解决方案

### 1. 🔍 快速诊断
首先运行诊断命令来检查环境状态：
```bash
./build-images.sh --diagnose
```

这个命令会检查：
- Docker 是否正常运行
- 本地镜像是否存在
- 仓库登录状态

### 2. 📦 本地镜像不存在
**错误信息**:
```
[ERROR] Local image not found: dhr/dhr-gateway-service:3.7.0
[INFO] Please build the image first or run without --push-only
```

**原因**: `--push-only` 模式需要本地已存在相应的镜像，但镜像尚未构建。

**解决方案**:
```bash
# 方案1: 先构建镜像，再推送
./build-images.sh --skip-maven  # 构建镜像
./build-images.sh --push-only   # 推送镜像

# 方案2: 构建并推送一步完成
./build-images.sh --skip-maven --push

# 方案3: 检查现有镜像
docker images | grep dhr/
```

### 3. 🔐 Docker 仓库未登录
**错误信息**:
```
[WARNING] Not logged in to registry: crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com
[INFO] Please run: docker login crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com
```

**解决方案**:
```bash
# 登录阿里云容器镜像仓库
docker login crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com

# 输入用户名和密码后再次尝试推送
./build-images.sh --push-only
```

### 4. 🏷️ 镜像标签问题
**错误信息**:
```
denied: requested access to the resource is denied
```

**原因**: 镜像标签格式不正确或仓库权限问题。

**解决方案**:
```bash
# 检查本地镜像标签
docker images | grep dhr/

# 手动添加正确的标签
docker tag dhr/dhr-gateway-service:3.7.0 crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-gateway-service:3.7.0

# 验证标签是否正确
docker images | grep crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com
```

### 5. 🌐 网络连接问题
**错误信息**:
```
Error response from daemon: Get https://crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/v2/: dial tcp: lookup crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com: no such host
```

**解决方案**:
```bash
# 检查网络连接
ping crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com

# 检查DNS解析
nslookup crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com

# 如果是企业网络，可能需要配置代理
export HTTP_PROXY=http://your-proxy:port
export HTTPS_PROXY=http://your-proxy:port
```

### 6. 💾 磁盘空间不足
**错误信息**:
```
no space left on device
```

**解决方案**:
```bash
# 检查磁盘空间
df -h

# 清理Docker资源
docker system prune -a

# 删除无用的镜像
docker rmi $(docker images -f "dangling=true" -q)
```

## 完整的故障排查流程

### 步骤 1: 运行诊断
```bash
./build-images.sh --diagnose
```

### 步骤 2: 检查本地镜像
```bash
docker images | grep dhr/
```

期望看到以下镜像：
- `dhr/dhr-gateway-service:3.7.0`
- `dhr/dhr-oauth-service:3.7.0`
- `dhr/dhr-ai-service:3.7.0`
- `dhr/dhr-utility-service:3.7.0`

### 步骤 3: 检查登录状态
```bash
cat ~/.docker/config.json | grep crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com
```

### 步骤 4: 手动测试推送
```bash
# 选择一个较小的镜像进行测试
docker tag dhr/dhr-gateway-service:latest crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-gateway-service:test

docker push crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-gateway-service:test
```

### 步骤 5: 查看详细错误信息
如果推送仍然失败，使用详细模式：
```bash
docker push crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/deloitte-lkk/dhr-gateway-service:test --debug
```

## 预防措施

### 1. 构建前检查
```bash
# 在构建前验证环境
./build-images.sh --diagnose
```

### 2. 分步骤执行
```bash
# 步骤1: 构建镜像
./build-images.sh --skip-maven

# 步骤2: 验证镜像
docker images | grep dhr/

# 步骤3: 推送镜像
./build-images.sh --push-only
```

### 3. 定期维护
```bash
# 定期清理Docker资源
docker system prune

# 定期检查登录状态
docker info | grep Registry
```

## 联系支持

如果问题仍然存在，请收集以下信息：

1. **系统信息**:
   ```bash
   docker version
   docker info
   ./build-images.sh --diagnose
   ```

2. **网络信息**:
   ```bash
   curl -I https://crpi-kqrzeej49dmzkvq2.cn-shanghai.personal.cr.aliyuncs.com/v2/
   ```

3. **错误日志**: 完整的错误输出信息

将这些信息提供给技术支持团队以获得进一步帮助。
