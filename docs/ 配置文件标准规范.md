# DHR微服务 application.yml 配置文件标准规范

## 1. 统一配置文件结构

所有微服务的 `application.yml` 配置文件都应遵循以下标准结构：

```yaml
# Nacos配置中心连接配置
nacos:
  server-addr: ${config_nacos_serveraddr:172.16.5.16}
  port: ${config_nacos_port:8848}
  namespace: ${config_nacos_namespace:bcabaf9e-e845-4cea-8f2f-381026ee0c56}
  username: ${config_nacos_username:nacos}
  password: ${config_nacos_password:nacos}

# Spring框架配置
spring:
  profiles:
    active: ${config_profile:dev}
  application:
    name: {service-name}
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

  # 服务特定配置...

  # Nacos服务发现和配置管理
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.server-addr}:${nacos.port}
        namespace: ${nacos.namespace}
        group: ${spring.profiles.active}
        username: ${nacos.username}
        password: ${nacos.password}
      config:
        file-extension: yaml
        server-addr: ${nacos.server-addr}:${nacos.port}
        group: ${spring.profiles.active}
        enabled: ${config.enable:true}
        namespace: ${nacos.namespace}
        username: ${nacos.username}
        password: ${nacos.password}

  # 配置导入
  config:
    import:
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

# 服务端口配置
server:
  port: ${config_server_port:default_port}

# 其他框架配置 (按需添加)
# - Hystrix配置
# - Ribbon配置
# - Feign配置

# 日志配置
logging:
  file:
    name: logs/${spring.application.name}/info/log_info.log
  level:
    root: info  # 输出日志级别

# DHR配置
dhr:
  swagger:
    enabled: true
    docket:
      basic:
        title: 服务标题
        base-package: com.deloitte.dhr.{module}
```

## 2. 配置优化说明

### 2.1 统一的配置顺序
1. **Nacos配置** - 配置中心连接配置
2. **Spring配置** - 核心框架配置
3. **Server配置** - 服务端口配置
4. **框架配置** - Hystrix/Ribbon/Feign等
5. **日志配置** - 日志输出配置
6. **DHR配置** - 项目自定义配置

### 2.2 注释规范
- 每个配置块都有明确的中文注释

### 2.3 Nacos 2.x 认证配置说明
从 Nacos 2.0 版本开始，默认启用了认证机制，所有配置都必须包含用户名和密码：

```yaml
nacos:
  username: ${config_nacos_username:nacos}
  password: ${config_nacos_password:nacos}
```

**重要提示：**
- 默认用户名和密码都是 `nacos`
- 生产环境中应修改默认密码并使用更安全的凭据
- 在 K8s 环境中，认证信息通过环境变量 `CONFIG_NACOS_USERNAME` 和 `CONFIG_NACOS_PASSWORD` 传递
- 使用 curl 访问 Nacos API 时需要添加认证参数：
  ```bash
  curl -u nacos:nacos -X GET "http://nacos:8848/nacos/v1/cs/configs?dataId=service-config.yaml&group=dev"
  ```
- 行内注释使用 `# 注释内容` 格式
- 重要配置项都有解释说明

### 2.3 格式统一
- 缩进使用2个空格
- 配置块之间用空行分隔
- 注释位置保持一致

## 3. 各服务端口配置

| 服务名称 | 默认端口 | 环境变量 |
|---------|---------|---------|
| dhr-gateway-service | 9110 | config_server_port |
| dhr-oauth-service | 9006 | config_server_port |
| dhr-ai-service | 9044 | config_server_port |
| dhr-utility-service | 8080 | config_server_port |

## 4. 服务特定配置

### 4.1 Gateway Service
- 包含 Hystrix 熔断器配置
- 包含 Ribbon 负载均衡配置
- 包含文件上传配置

### 4.2 OAuth Service
- 包含 Thymeleaf 模板引擎配置
- 包含国际化配置
- 包含加密配置
- 包含登出URL配置

### 4.3 AI Service
- 仅包含基础配置
- 包含国际化配置

### 4.4 Utility Service
- 包含 Feign 客户端配置
- 包含 Hystrix 熔断器配置
- 包含 Ribbon 负载均衡配置
- 包含压缩配置

## 5. 修复的问题

1. **端口不一致**: utility-service 端口从 9025 修正为 8080 (与docker-compose.yml一致)
2. **配置顺序混乱**: 统一按标准顺序排列配置项
3. **注释不规范**: 统一注释格式和内容
4. **空行不统一**: 规范配置块之间的空行使用
5. **缺少配置项**: 为部分服务补充必要的配置项

## 6. 最佳实践

1. **环境变量优先**: 所有可变配置都使用环境变量覆盖
2. **默认值设置**: 为所有环境变量提供合理的默认值
3. **配置分离**: 敏感配置和环境相关配置通过Nacos管理
4. **注释完整**: 重要配置项都要有清晰的中文注释
5. **格式一致**: 严格按照标准格式编写配置文件

此规范确保了所有微服务配置文件的一致性和可维护性。
