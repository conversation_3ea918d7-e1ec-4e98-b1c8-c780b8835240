package com.deloitte.dhr.log.util;

import ch.qos.logback.core.PropertyDefinerBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 获取当前服务的ip地址
 *
 * @author: <PERSON><PERSON><PERSON>
 * @version: 1.0
 * @date: 19/05/2022
 */
public class LogIpUtil extends PropertyDefinerBase {
    private static final Logger logger = LoggerFactory.getLogger(LogIpUtil.class);
    private static String webIP;
    static {
        try {
            webIP = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            logger.error("获取日志Ip异常", e);
            webIP = null;
        }
    }

    @Override
    public String getPropertyValue() {
        return webIP;
    }
}
