package com.deloitte.dhr.log.event;

import com.deloitte.dhr.log.entity.OptDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**

 <AUTHOR>
 @version: 1.0
 @create 31/05/2022
 */
@Component
public class EventPublish {
    @Autowired
    private ApplicationContext applicationContext;

    public void publishEvent(OptDTO sysLog) {
        applicationContext.publishEvent(new SysEvent(sysLog));
    }

}
