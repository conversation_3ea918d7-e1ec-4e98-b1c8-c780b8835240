package com.deloitte.dhr.log.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 实体类
 * 系统日志
 * </p>
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode
@Accessors(chain = true)
public class OptDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**主键*/
    private Long id;

    /**事件id，和日志tracedId一致*/
    private String eventId;

    /**员工编号*/
    private String userId;

    /**员工姓名*/
    private String userName;

    /**访问者IP地址*/

    /**访问时间*/
    @JSONField(name="accessTime",format="yyyy-MM-dd HH:mm:ss")
    private Date accessTime;

    /**语言编码*/
    private String localeCode;

    /**日志描述*/
    private String describes;

    /** 请求路由*/
    private String routeURL;

    /** 处理方法*/
    private String dealMethod;

    /** 处理时间*/
    private Double dealTime;



    /**模块编号：功能模块*/
    private String moduleCode;

    /**事件标识字段: 界面code*/
    private String eventView;

    /**事件名称*/
    private String eventName;

    /**设备类型:所处端 PC:pc端   MOBILE:移动端*/
    private String deviceType;

    /**事件触发条件*/
    private String eventCondition;

    /**变量名称（key）*/
    private String eventKey;

    /**变量属性值（value）*/
    private String eventValue;

    /** 变量数据类型*/
    private String eventType;

}
