package com.deloitte.dhr.log.event;

import com.deloitte.dhr.log.entity.OptDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;

import java.util.function.Consumer;

/**
 * 异步监听日志事件
 */
@Slf4j
@AllArgsConstructor
public class SysListener {
    private Consumer<OptDTO> consumer;

    public SysListener() {
    }

    @Async
    @Order
    @EventListener(SysEvent.class)
    public void saveSysLog(SysEvent event) {
        OptDTO opt = (OptDTO) event.getSource();
        consumer.accept(opt);
    }
}
