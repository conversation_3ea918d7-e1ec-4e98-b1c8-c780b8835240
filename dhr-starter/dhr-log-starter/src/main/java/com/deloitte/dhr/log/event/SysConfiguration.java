package com.deloitte.dhr.log.event;

import com.deloitte.dhr.log.entity.OptDTO;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.function.Consumer;

/**
 * 注册监听Bean
 */
@Configuration
@EnableAsync
public class SysConfiguration {
    @Bean
    public SysListener sysListener(OptService optService){
        Consumer<OptDTO> consumer = (optLog) -> optService.save(optLog);
        return new SysListener(consumer);
    }
}