<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">

    <property name="log.default.level" value="info"/>
    <springProperty scope="context" name="log.root.level" source="logging.level.root" defaultValue="info"/>
    <springProperty scope="context" name="log.appender.graylog.enable" source="deloitte.log.appender.graylog.enable"
                    defaultValue="false"/>

    <!-- 日志存放路径 -->
    <!--    <property name="log.path" value="./logs"/>-->
    <springProperty scope="context" name="log.path" source="logging.path" defaultValue="./logs"/>
    <property name="log.pattern"
              value="%d{HH:mm:ss.SSS} [%X{traceId}] [%thread] %-5level %logger{20} - [%method,%line] - %msg%n"/>
    <!-- 日志输出格式 -->
    <define name="local_ip" class="com.deloitte.dhr.log.util.LogIpUtil"/>
    <!-- ELK日志收集支持 -->
    <!-- 是否ELK收集日志  参数： false:不开启(默认)  true:开启     -->
    <springProperty scope="context" name="log.appender.elk.enable" source="logging.log.appender.elk.enable"
                    defaultValue="false"/>
    <springProperty scope="context" name="log.appender.elk.ip" source="logging.log.appender.elk.ip"
                    defaultValue="0.0.0.0"/>
    <springProperty scope="context" name="log.appender.elk.port" source="logging.log.appender.elk.port"
                    defaultValue="3333"/>
    <springProperty scope="context" name="local_port" source="server.port"/>
    <springProperty scope="context" name="local_application_name" source="spring.application.name"/>

    <!-- 彩色日志 -->
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%level){blue} %clr(${PID}){magenta} %clr([%X{traceId}]){yellow} %clr([%thread]){orange} %clr(%-40.40logger{39}){cyan} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
    <property name="CONSOLE_LOG_PATTERN_NO_COLOR"
              value="[${APP_NAME}:${ServerIP}:${ServerPort}] %d{yyyy-MM-dd HH:mm:ss.SSS} %level ${PID} [%X{traceId}] [%thread] %-40.40logger{39} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>


    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${log.root.level}</level>
        </filter>
    </appender>

    <appender name="debug-console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>dhr-logback[%X{traceId}]%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%C:%L] - %m%n aaa</pattern>
        </encoder>
    </appender>

    <!-- 系统日志输出 -->
    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">

        <file>${log.path}/${local_application_name}/info/log_info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/${local_application_name}/info/log_info_file_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
            <maxFileSize>100mb</maxFileSize>
        </rollingPolicy>

        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>

    </appender>

    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${local_application_name}/error/log_error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/${local_application_name}/error/log_error_file_%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
            <maxFileSize>10mb</maxFileSize>
        </rollingPolicy>

        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>

        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <if condition='property("log.appender.elk.enable").equalsIgnoreCase("true")'>
        <then>
            <appender name="logstash-info" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
                <destination>${log.appender.elk.ip}:${log.appender.elk.port}</destination>
                <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
                    <includeMdcKeyName>traceId</includeMdcKeyName>
                    <customFields>
                        {"tag":"dhr-logback","ip":"${local_ip}","port":"${local_port}","appname":"${local_application_name}"}
                    </customFields>
                </encoder>
                <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                    <level>INFO</level>
                </filter>
            </appender>
            <appender name="logstash-error" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
                <destination>${log.appender.elk.ip}:${log.appender.elk.port}</destination>
                <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
                    <includeMdcKeyName>traceId</includeMdcKeyName>
                    <customFields>
                        {"tag":"dhr-logback","ip":"${local_ip}","port":"${local_port}","appname":"${local_application_name}"}
                    </customFields>
                </encoder>
                <filter class="ch.qos.logback.classic.filter.LevelFilter">
                    <level>ERROR</level>
                    <onMatch>ACCEPT</onMatch>
                    <onMismatch>DENY</onMismatch>
                </filter>
            </appender>
        </then>
    </if>

    <!--addtivity=false 终止将该包路径下的log传递给上级，此处的上级就是root -->
    <logger name="com.deloitte.dhr" level="${log.root.level}" additivity="false">
        <appender-ref ref="file_info"/>
        <appender-ref ref="file_error"/>
        <appender-ref ref="console"/>
        <if condition='property("log.appender.elk.enable").equalsIgnoreCase("true")'>
            <then>
                <appender-ref ref="logstash-info"/>
                <appender-ref ref="logstash-error"/>
            </then>
            <else>
            </else>
        </if>
    </logger>

    <!-- Spring日志级别控制  -->
    <logger name="org.springframework" level="error" additivity="false">
        <appender-ref ref="console"/>
    </logger>

    <!--系统操作日志-->
    <root level="${log.root.level}">
        <appender-ref ref="console"/>
        <appender-ref ref="file_info"/>
        <appender-ref ref="file_error"/>
        <if condition='property("log.appender.elk.enable").equalsIgnoreCase("true")'>
            <then>
                <appender-ref ref="logstash-info"/>
                <appender-ref ref="logstash-error"/>
            </then>
            <else>
            </else>
        </if>
    </root>

</configuration>