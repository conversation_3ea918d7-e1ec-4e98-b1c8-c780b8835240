dhr-log-starter 日志启动器
===============

当前最新版本： master（发布日期：2022-06-20）


项目介绍：
-----------------------------------
```
彩色日志，匹配埋点的事件id，引入依赖生效，需要ELK时候按照下面配置一下

1.日志tracedId 和 埋点事件id统一
2.日志等级默认Info，通过配置文件设置
3.日志会生成本地服务器文件，会根据大小拆分，日期拆分，30天后过期
4.日志可配置化对接EKL系统
5.分布式环境下采用feign适配器来传递tracedId
```

### 如何使用 
```
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-log-starter</artifactId>
            <version>${revision}</version>
        </dependency>

```
### 参数配置
```
启动器有默认配置，下面是需要手动开启ELK配置
logging:
  level:
    root: info  #输出日志级别
  log:
    appender:
      elk:
        enable: true         #开启ELK日志
        ip: 172.XX.X.XXX     #logstash 地址
        port: 5045           #logstash 地址

```

#####
备注
----


