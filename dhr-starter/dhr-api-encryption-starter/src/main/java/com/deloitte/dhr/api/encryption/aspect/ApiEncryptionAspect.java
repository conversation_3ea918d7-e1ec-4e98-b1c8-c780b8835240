package com.deloitte.dhr.api.encryption.aspect;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.api.encryption.Utils.RequestBodyUtil;
import com.deloitte.dhr.api.encryption.annotation.ApiEncryption;
import com.deloitte.dhr.common.SignParam;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.utils.MD5Util;
import com.deloitte.dhr.common.base.utils.RedisUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/***
 * @Author: yinyu
 * @Date: 15/05/2023 15:50
 * @description:api接口加密
 */

@Aspect
@Component
public class ApiEncryptionAspect {

    @Autowired
    private RedisUtils redisUtils;

    private static final String SIGN_PARAM = "signParam";

    private static final String PREFIX = "dhr_api_secret:";

    @Around(value = "@annotation(apiEncryption)")
    public Object doAround(ProceedingJoinPoint joinPoint, ApiEncryption apiEncryption) throws Throwable {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            throw new CommRunException("访问失败");
        }
        HttpServletRequest request = requestAttributes.getRequest();
        if (request == null) {
            throw new CommRunException("访问失败");
        }
        String bodyStr = RequestBodyUtil.getBodyString(request);
        JSONObject bodyObj = JSON.parseObject(bodyStr, JSONObject.class);
        String paramStr = bodyObj.getString(SIGN_PARAM);
        SignParam signParam = JSON.parseObject(paramStr, SignParam.class);
        checkSign(signParam);
        return joinPoint.proceed();
    }

    private void checkSign(SignParam signParam){
        if (signParam==null){
            throw new CommRunException("访问失败");
        }
        //获取密钥
        String apiSecret=redisUtils.get(String.format("%s%s",PREFIX,signParam.getAppId()));
        if (StrUtil.isBlank(apiSecret)){
            throw new CommRunException("访问失败");
        }
        String paramStr=StrUtil.join("",signParam.getParams());
        //根据签名规则拼接
        String signStr=String.format("%s%s%s%s%s",signParam.getAppId(),truncate(paramStr)
                ,signParam.getSalt(),signParam.getTimestamp(),apiSecret);
        //用MD5加密后比较
        if (!MD5Util.encrypt(signStr).equals(signParam.getSign())){
            throw new CommRunException("访问失败");
        }
    }

    private String truncate(String paramStr) {
        if (paramStr == null) {
            return null;
        }
        int len = paramStr.length();
        return len <= 20 ? paramStr : (paramStr.substring(0, 10) + len + paramStr.substring(len - 10, len));
    }
}
