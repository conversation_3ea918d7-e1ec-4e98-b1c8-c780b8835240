package com.deloitte.dhr.api.encryption.wrapper;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;

/***
 * @Author: yinyu
 * @Date: 15/05/2023 15:50
 * @description:可反复读取ReqeustBody的请求对象
 */

public class RepeatedlyReadRequestWrapper extends HttpServletRequestWrapper {

    private final byte[] body;

    public RepeatedlyReadRequestWrapper(HttpServletRequest request, byte[] requestBody) {
        super(request);
        body = requestBody;
    }

    @Override
    public BufferedReader getReader() {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    @Override
    public ServletInputStream getInputStream() {
        final ByteArrayInputStream bais = new ByteArrayInputStream(body);
        return new ServletInputStream() {

            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener listener) {

            }

            @Override
            public int read() {
                return bais.read();
            }
        };
    }
}
