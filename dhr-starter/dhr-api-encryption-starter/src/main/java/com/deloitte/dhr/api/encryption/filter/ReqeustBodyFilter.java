package com.deloitte.dhr.api.encryption.filter;

import com.deloitte.dhr.api.encryption.Utils.RequestBodyUtil;
import com.deloitte.dhr.api.encryption.wrapper.RepeatedlyReadRequestWrapper;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/***
 * @Author: yinyu
 * @Date: 15/05/2023 15:50
 * @description:ReqeustBody过滤器用于使ReqeustBody可以反复读取
 */

@Component
@WebFilter(urlPatterns = "/**", filterName = "ReqeustBodyFilter")
public class ReqeustBodyFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, Fi<PERSON><PERSON>hain chain) throws IOException, ServletException {
        String contentType = request.getContentType();
        ServletRequest requestWrapper = null;
        // 文件上传时不可以过滤器包装request，会报错Required request part 'file' is not present
        if (request instanceof HttpServletRequest
                && !MediaType.MULTIPART_FORM_DATA_VALUE.equals(contentType)){
            byte[] requestBody = RequestBodyUtil.readBytes(request.getReader(), "utf-8");
            if (requestBody != null) {
                requestWrapper = new RepeatedlyReadRequestWrapper((HttpServletRequest) request,requestBody);
            }
        }
        if (null == requestWrapper) {
            // 过滤器包装request不需要，将返回原来的request
            chain.doFilter(request, response);
        } else {
            // 过滤器包装request成功
            chain.doFilter(requestWrapper, response);
        }
    }

    @Override
    public void destroy() {
    }
}
