package com.deloitte.dhr.api.encryption.handler;

import com.deloitte.dhr.api.encryption.annotation.ApiEncryption;
import com.deloitte.dhr.common.base.exception.CommRunException;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/***
 * @Author: yinyu
 * @Date: 17/05/2023 14:33
 * @description:接口加密拦截器
 */
@Component
public class SignInterceptor implements AsyncHandlerInterceptor {

    /**
     * 访问类型
     */
    private static final String ACCESS_MODE = "Access-Mode";

    /**
     * 签名访问
     */
    private static final String SIGN="sign";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        String accessMode=request.getHeader(ACCESS_MODE);
        //不是签名访问则放行
        if (!SIGN.equals(accessMode)){
            return true;
        }
        HandlerMethod handlerMethod= (HandlerMethod) handler;
        ApiEncryption apiEncryption=handlerMethod.getMethodAnnotation(ApiEncryption.class);
        //签名访问的方法必须匹配接口加密注解
        if (apiEncryption==null){
            throw new CommRunException("访问失败");
        }
        return true;
    }
}

