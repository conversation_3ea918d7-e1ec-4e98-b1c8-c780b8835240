package com.deloitte.dhr.resubmit.aspect;

import com.deloitte.dhr.resubmit.Strategy.ResubmitStrategyHandler;
import com.deloitte.dhr.resubmit.annotation.Resubmit;
import com.deloitte.dhr.resubmit.util.SpringContextUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/***
 * @Author: yinyu
 * @Date: 22/03/2023 18:14
 * @description:防止接口重复提交切面
 */

@Aspect
@Component
public class ResubmitAspect {

    /**
     * 防止重复提交开关
     */
    @Value("${resubmit.switch:true}")
    private Boolean resubmitSwitch;

    @Around(value = "@annotation(resubmit)")
    public Object doAround(ProceedingJoinPoint joinPoint, Resubmit resubmit) throws Throwable {
        if (!resubmitSwitch){
            return joinPoint.proceed();
        }
        ResubmitStrategyHandler resubmitStrategyHandler = (ResubmitStrategyHandler) SpringContextUtil.getBean(resubmit.resubmitStrategy());
        return resubmitStrategyHandler.handle(joinPoint, resubmit);
    }

}
