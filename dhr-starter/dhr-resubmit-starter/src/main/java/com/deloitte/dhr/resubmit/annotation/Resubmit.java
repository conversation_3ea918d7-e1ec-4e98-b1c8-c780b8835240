package com.deloitte.dhr.resubmit.annotation;

import com.deloitte.dhr.resubmit.Strategy.DefaultResubmitStrategy;

import java.lang.annotation.*;

/***
 * @Author: yinyu
 * @Date: 22/03/2023 18:14
 * @description:防止接口重复提交注解
 */

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Resubmit {

    /**
     * 唯一标识
     */
    String[] keys();

    /**
     * 防止重复提交的持续时间(key过期时间)
     */
    long leaseTime() default 10L;

    /**
     * 实现策略
     */
    Class resubmitStrategy() default DefaultResubmitStrategy.class;
}
