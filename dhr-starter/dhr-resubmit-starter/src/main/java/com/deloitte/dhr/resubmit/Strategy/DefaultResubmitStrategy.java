package com.deloitte.dhr.resubmit.Strategy;

import com.deloitte.dhr.common.base.utils.MD5Util;
import com.deloitte.dhr.resubmit.annotation.Resubmit;
import com.deloitte.dhr.resubmit.exception.RedisLockException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/***
 * @Author: yinyu
 * @Date: 23/03/2023 10:04
 * @description:
 */
@Slf4j
@Component
public class DefaultResubmitStrategy implements ResubmitStrategyHandler {

    @Resource
    private RedissonClient redissonClient;

    /**
     * key前缀, 用于区分不同业务
     */
    @Value("${redisGroup.resubmit:resubmit}")
    private String prefix;

    @Override
    public Object handle(ProceedingJoinPoint joinPoint, Resubmit resubmit) throws Throwable {
        String md5Key=MD5Util.encrypt(this.getRedisKey(joinPoint, resubmit.keys()));
        md5Key =  String.format("%s:%s",prefix,md5Key);
        // 获取锁
        RLock lock = redissonClient.getLock(md5Key);
        try {
            boolean locked = lock.tryLock(0, resubmit.leaseTime(), TimeUnit.SECONDS);
            if (!locked) {
                throw new RedisLockException(LOCK_FAIL_MESSAGE);
            }
            return joinPoint.proceed();
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            if (lock.isHeldByCurrentThread()){
                lock.unlock();
            }
            throw e;
        }finally {
            if (lock.isHeldByCurrentThread()&&resubmit.leaseTime()==-1L){
                lock.unlock();
            }
        }
    }

    private String getRedisKey(ProceedingJoinPoint joinPoint,String[] keys){
        //得到被切面修饰的方法的参数列表
        Object[] args = joinPoint.getArgs();
        //得到被代理的方法
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        StringBuilder stringBuilder=new StringBuilder();
        //获取被拦截方法参数名列表(使用Spring支持类库)
        LocalVariableTableParameterNameDiscoverer parameterNameDiscoverer = new LocalVariableTableParameterNameDiscoverer();
        String[] paraNameArr = parameterNameDiscoverer.getParameterNames(method);
        //使用SPEL进行key的解析
        ExpressionParser parser = new SpelExpressionParser();
        //SPEL上下文
        StandardEvaluationContext context = new StandardEvaluationContext();
        //把方法参数放入SPEL上下文中
        for(int i=0;i<paraNameArr.length;i++){
            context.setVariable(paraNameArr[i], args[i]);
        }
        for (String key:keys){
            stringBuilder.append(parser.parseExpression(key).getValue(context,String.class));
        }
        return stringBuilder.toString();
    }

}
