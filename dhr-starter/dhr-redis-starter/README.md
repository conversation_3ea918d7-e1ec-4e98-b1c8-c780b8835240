dhr-redis-starter 日志启动器
===============

当前最新版本： master（发布日期：2022-06-20）


项目介绍：
-----------------------------------

Redis快捷使用启动器，封装功能强大工具类，注解策略锁功能



### 如何使用 
```
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-redis-starter</artifactId>
            <version>${revision}</version>
        </dependency>
```
```

RedisUtil：直接使用工具类
RedisLock：直接在方法上加上 @RedisLock 注解实现redis注解锁

@RedisLock（LockStrategy=LockStrategy.FAIL_FAST,waitTime=30,name="oauthUser"）

name: 锁名称
waitTime:尝试获取锁的超时时间(秒)，默认30s
LockStrategy:锁策略
    FAIL_FAST:尝试获取锁，未获取到锁抛出异常
    KEEP_ACQUIRE：等待获取锁，直到获取锁成功
    KEEP_ACQUIRE_TIMEOUT:等待获取锁，超过一定时间内仍未获取到锁抛出异常

```
### 参数配置
```
需要添加redis连接信息
  redis:
    host: XXX.16.4.XXX
    port: 6379
    password: XXXX



```

#####
备注
----


