package com.deloitte.dhr.redis.enums;

import com.deloitte.dhr.redis.annotation.RedisLock;
import com.deloitte.dhr.redis.exception.RedisLockAcquireTimeoutException;
import com.deloitte.dhr.redis.exception.RedisLockFailFastException;
import com.deloitte.dhr.redis.handler.LockStrategyHandler;
import org.springframework.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;

import java.util.concurrent.TimeUnit;

/**
 * 加锁策略
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public enum LockStrategy implements LockStrategyHandler {

    /**
     * 尝试获取锁，未获取到锁抛出异常
     *
     * @see RedisLockFailFastException
     */
    FAIL_FAST() {
        @Override
        public void lock(RLock lock, RedisLock redisLock) throws InterruptedException {
            try {
                boolean locked = lock.tryLock(0, redisLock.leaseTime(), TimeUnit.SECONDS);
                if (!locked) {
                    throw new RedisLockFailFastException(StringUtils.hasText(redisLock.exceptionMessage()) ? redisLock.exceptionMessage() : FAST_FAIL_MESSAGE);
                }
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
                throw e;
            }
        }
    },
    /**
     * 等待获取锁，直到获取锁成功
     */
    KEEP_ACQUIRE() {
        @Override
        public void lock(RLock lock, RedisLock redisLock) {
            lock.lock(redisLock.leaseTime(), TimeUnit.SECONDS);
        }
    },
    /**
     * 等待获取锁，超过一定时间内仍未获取到锁抛出异常
     *
     * @see RedisLockAcquireTimeoutException
     */
    KEEP_ACQUIRE_TIMEOUT() {
        @Override
        public void lock(RLock lock, RedisLock redisLock) throws InterruptedException {
            try {
                boolean locked = lock.tryLock(redisLock.waitTime(), redisLock.leaseTime(), TimeUnit.SECONDS);
                if (!locked) {
                    throw new RedisLockAcquireTimeoutException(StringUtils.hasText(redisLock.exceptionMessage()) ? redisLock.exceptionMessage() : KEEP_ACQUIRE_TIMEOUT_MESSAGE);
                }
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
                throw e;
            }
        }
    };
}
