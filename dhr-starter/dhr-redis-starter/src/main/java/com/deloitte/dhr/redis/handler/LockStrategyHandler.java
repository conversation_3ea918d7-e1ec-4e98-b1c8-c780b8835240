package com.deloitte.dhr.redis.handler;

import com.deloitte.dhr.redis.annotation.RedisLock;
import org.redisson.api.RLock;

/**
 * 获取锁策略处理器器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface LockStrategyHandler {
    String FAST_FAIL_MESSAGE = "获取锁失败";
    String KEEP_ACQUIRE_TIMEOUT_MESSAGE = "获取锁超时";

    /**
     * 获取锁
     * @param lock {@link RLock}
     * @param redisLock {@link RedisLock}
     * @throws InterruptedException 中断异常
     */
    void lock(RLock lock, RedisLock redisLock) throws InterruptedException;
}
