package com.deloitte.dhr.redis.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Order
@ConditionalOnClass(HttpServletRequest.class)
@RestControllerAdvice
public class RedisExceptionHandler {

    @ExceptionHandler(RedisLockException.class)
    public ResponseEntity<Map<String, Object>> handlerException(RedisLockException ex) {
        Map<String, Object> error = new HashMap<>(2, 1);
        error.put("code", HttpStatus.BAD_REQUEST.value());
        error.put("msg", ex.getMessage());
        log.warn("[全局业务异常] 业务编码：{} 异常记录：{}", error.get("code"), error.get("msg"), ex);
        return new ResponseEntity<>(error, HttpStatus.OK);
    }
}