package com.deloitte.dhr.redis.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * redis 配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@ConfigurationProperties(prefix = "universal.redis")
public class UniversalRedisProperties {

    /**
     * key前缀, 用于区分不同业务
     */
    private String prefix;

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }
}
