package com.deloitte.dhr.redis.lock;

import com.deloitte.dhr.redis.enums.LockType;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

/**
 * 工厂类，根据类型创建锁
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class LockFactory {

    private LockFactory() {
        throw new IllegalStateException("Utility class");
    }

    public static RLock createLock(RedissonClient redissonClient, LockType lockType, String key){
        switch (lockType){
            case READ_LOCK:
                return redissonClient.getReadWriteLock(key).readLock();
            case WRITE_LOCK:
                return redissonClient.getReadWriteLock(key).writeLock();
            default:
                return redissonClient.getLock(key);
        }
    }
}
