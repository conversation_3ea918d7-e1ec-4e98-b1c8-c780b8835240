
/* --------------- 创建表 --------------- */
DROP TABLE IF EXISTS dhr_excel_parser_log;
CREATE TABLE dhr_excel_parser_log(
                                     id bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键ID' ,
                                     bs_code VARCHAR(32)    COMMENT '业务标识' ,
                                     file_name VARCHAR(255)    COMMENT '文件名' ,
                                     start_time DATETIME    COMMENT '开始时间' ,
                                     end_time DATETIME    COMMENT '结束时间' ,
                                     parser_status INT    COMMENT '解析状态;1：解析中，2：解析完成，3：解析异常' ,
                                     create_by VARCHAR(20)    COMMENT '创建人' ,
                                     create_time DATETIME    COMMENT '创建时间' ,
                                     update_by VARCHAR(20)    COMMENT '更新人' ,
                                     update_time DATETIME    COMMENT '更新时间' ,
                                     remark VARCHAR(255)    COMMENT '备注' ,
                                     PRIMARY KEY (id)
)  COMMENT = 'excel解析日志表';
DROP TABLE IF EXISTS dhr_excel_parser_error;
CREATE TABLE dhr_excel_parser_error(
                                       id bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键ID' ,
                                       parser_log_id bigint(20)    COMMENT '解析日志ID' ,
                                       error_index INT    COMMENT '错误行号' ,
                                       error_message text    COMMENT '错误信息' ,
                                       error_data text    COMMENT '错误数据内容' ,
                                       create_by VARCHAR(20)    COMMENT '创建人' ,
                                       create_time DATETIME    COMMENT '创建时间' ,
                                       update_by VARCHAR(20)    COMMENT '更新人' ,
                                       update_time DATETIME    COMMENT '更新时间' ,
                                       remark VARCHAR(255)    COMMENT '备注' ,
                                       PRIMARY KEY (id)
)  COMMENT = 'excel解析错误表';