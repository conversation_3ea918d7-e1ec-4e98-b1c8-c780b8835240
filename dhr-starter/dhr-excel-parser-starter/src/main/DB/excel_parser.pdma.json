{"name": "excel_parser", "describe": "excel解析", "avatar": "", "version": "4.2.0", "createdTime": "2022-9-7 17:18:19", "updatedTime": "2022-12-1 16:49:10", "dbConns": [], "profile": {"default": {"db": "29D1CE08-4C35-4D2D-AAA9-23D93305B52E", "dbConn": "B7C4C720-C258-41EF-87EF-2BD0388258A0", "entityInitFields": [{"defKey": "TENANT_ID", "defName": "租户号", "comment": "", "type": "", "len": 32, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "refDict": "", "uiHint": "", "id": "ADB3AD14-6603-43E2-8261-114E32442B5B"}, {"defKey": "REVISION", "defName": "乐观锁", "comment": "", "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "type": "", "len": 32, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "92BF430E-01FA-4AEF-944F-25A142632654"}, {"defKey": "CREATED_BY", "defName": "创建人", "comment": "", "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "type": "", "len": 32, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "C8BE2C7A-8251-4ADD-BB4F-411C5754DA62"}, {"defKey": "CREATED_TIME", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "4E471FD6-3E73-4A90-B660-51598A482409"}, {"defKey": "UPDATED_BY", "defName": "更新人", "comment": "", "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "type": "", "len": 32, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "0DC24AA9-4CD0-45D8-95CF-FA546BE343AB"}, {"defKey": "UPDATED_TIME", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "09F64AC4-4DEE-428F-AF64-4C103884E1AC"}], "entityInitProperties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}}, "javaHome": "", "sql": {"delimiter": ""}, "dataTypeSupports": [{"defKey": "MYSQL", "id": "29D1CE08-4C35-4D2D-AAA9-23D93305B52E"}, {"defKey": "ORACLE", "id": "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542"}, {"defKey": "SQLServer", "id": "BFC87171-C74F-494A-B7C2-76B9C55FACC9"}, {"defKey": "PostgreSQL", "id": "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022"}, {"defKey": "DB2", "id": "89504F5D-94BF-4C9E-8B2E-44F37305FED5"}, {"defKey": "DM", "id": "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307"}, {"defKey": "GaussDB", "id": "592C7013-143D-4E7B-AF64-0D7BF1E28230"}, {"defKey": "Kingbase", "id": "77BD85E5-9D0D-4096-8427-CBA306FC9C6A"}, {"defKey": "MaxCompute", "id": "11D1FB71-A587-4217-89BA-611B8A1F83E0"}, {"defKey": "SQLite", "id": "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1"}, {"defKey": "JAVA", "id": "797A1496-D649-4261-89B4-544132EC3F36"}, {"defKey": "JavaMybatis", "id": "895CFD1D-4273-4D32-A2C4-CAC70200AB5B"}, {"defKey": "JavaMybatisPlus", "id": "A2EE7B4A-CE62-4290-B00C-B26C1BF18073"}, {"defKey": "C#", "id": "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30"}, {"defKey": "Hive", "id": "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2"}, {"defKey": "Golang", "id": "B91D99E0-9B7C-416C-8737-B760957DAF09"}], "codeTemplates": [{"type": "appCode", "applyFor": "797A1496-D649-4261-89B4-544132EC3F36", " JpaBean": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.entity;\n$blankline\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\nimport javax.persistence.*;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n\n /**\n * {{=it.entity.defName}};{{=it.entity.comment}}\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@ApiModel(value = \"{{=it.entity.defName}}\",description = \"{{=it.entity.comment}}\")\n@Table(name=\"{{=it.entity.defKey}}\")\npublic class {{=beanClass}} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    {{? field.primaryKey }}\n    @Id\n    @GeneratedValue\n    {{?}}\n    @ApiModelProperty(name = \"{{=field.defName}}\",notes = \"{{=field.comment}}\")\n    private {{=field.type}} {{=it.func.camel(field.defKey,false)}} ;\n{{~}}\n$blankline\n\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.defKey,true)}}(){\n        return this.{{=it.func.camel(field.defKey,false)}};\n    }\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public void set{{=it.func.camel(field.defKey,true)}}({{=field.type}} {{= it.func.camel(field.defKey,false) }}){\n        this.{{=it.func.camel(field.defKey,false)}}={{=it.func.camel(field.defKey,false)}};\n    }\n{{~}}\n}"}, {"type": "appCode", "applyFor": "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30", "Default": "using System;\nusing System.Collections.Generic;\n\n$blankline\n{{\n    var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n}}\n/*\n * <AUTHOR> http://www.chiner.com.cn\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n * @desc : {{=it.func.join(it.entity.defName,it.entity.comment,'-')}}\n */\nnamespace PDManer.Application\n{\n    public partial class {{=it.func.camel(it.entity.defKey,true) }}\n    {\n    \n        {{~it.entity.fields:field:index}}\n        /// <summary>\n        /// {{=it.func.join(field.defName,field.comment,';')}}\n        /// </summary>\n        public {{=field.type}} {{=it.func.camel(field.defKey,true)}} { get; set; }\n        $blankline\n        {{~}}\n        \n    }\n}", "SqlSugar": "using System;\nusing System.Collections.Generic;\nusing SqlSugar;\n\n$blankline\n{{\n    var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    var sqlSugartable='[SugarTable(\"{{=it.entity.defKey}}\", TableDescription = \"{{=it.func.join(it.entity.defName,it.entity.comment,';')}}\")]';\n}}\n/*\n * <AUTHOR> <EMAIL>\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n * @desc : {{=it.func.join(it.entity.defName,it.entity.comment,'-')}}\n */\nnamespace Model.DBModel\n{\n    /// <summary>\n    /// {{=it.func.join(it.entity.defName,it.entity.comment,';')}}\n    /// </summary>\n    {{=sqlSugartable}}\n    public class {{=it.entity.defKey}}\n    {\n        {{~it.entity.fields:field:index}}\n        /// <summary>\n        /// {{=it.func.join(field.defName,field.comment,';')}}\n        /// </summary>\n        {{? field.primaryKey }}\n        [SugarColumn(IsIdentity = true, IsPrimaryKey = true)]\n        {{?}}\n        public {{=field.type}} {{=it.func.camel(field.defKey,true)}}{ get; set; }\n        $blankline\n        {{~}}\n    }\n}"}, {"applyFor": "895CFD1D-4273-4D32-A2C4-CAC70200AB5B", "type": "appCode", "Controller": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.controller;\n$blankline\nimport io.swagger.annotations.Api;\nimport io.swagger.annotations.ApiOperation;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageRequest;\nimport org.springframework.http.ResponseEntity;\nimport org.springframework.web.bind.annotation.*;\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.service.{{=serviceClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表控制层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Api(tags = \"{{=it.entity.defName}}对象功能接口\")\n@RestController\n@RequestMapping(\"/{{=it.func.camel(it.entity.defKey,false)}}\")\npublic class {{=beanClass}}Controller{\n    @Autowired\n    private {{=serviceClass}} {{=serviceVarName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    @ApiOperation(\"通过ID查询单条数据\")\n    @GetMapping(\"{{{=it.func.camel(pkVarName,false)}}}\")\n    public ResponseEntity<{{=beanClass}}> queryById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.queryById({{=pkVarName}}));\n    }\n    $blankline\n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    @ApiOperation(\"分页查询\")\n    @GetMapping\n    public ResponseEntity<Page<{{=beanClass}}>> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest){\n        return ResponseEntity.ok({{=serviceVarName}}.paginQuery({{=beanVarName}}, pageRequest));\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"新增数据\")\n    @PostMapping\n    public ResponseEntity<{{=beanClass}}> add({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.insert({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"更新数据\")\n    @PutMapping\n    public ResponseEntity<{{=beanClass}}> edit({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.update({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    @ApiOperation(\"通过主键删除数据\")\n    @DeleteMapping\n    public ResponseEntity<Boolean> deleteById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.deleteById({{=pkVarName}}));\n    }\n}", "Service": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.service;\n$blankline\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageRequest;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务接口\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\npublic interface {{=serviceClass}}{\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}});\n    \n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest);\n\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} insert({{=beanClass}} {{=beanVarName}});\n\n    \n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} update({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    boolean deleteById({{=pkDataType}} {{=pkVarName}});\n}", "ServiceImpl": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkVarNameU = \"UndefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkVarNameU = it.func.camel(field.defKey,true);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    var mapperName = beanVarName+'Mapper';\n    \n}}package {{=pkgName}}.service.impl;\n$blankline\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.stereotype.Service;\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageImpl;\nimport org.springframework.data.domain.PageRequest;\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.mapper.{{=beanClass}}Mapper;\nimport {{=pkgName}}.service.{{=serviceClass}};\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务实现类\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Service\npublic class {{=serviceClass}}Impl implements {{=serviceClass}}{\n    @Autowired\n    private {{=beanClass}}Mapper {{=mapperName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    public {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}}){\n        return {{=mapperName}}.queryById({{=pkVarName}});\n    }\n    $blankline\n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    public Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest){\n        long total = {{=mapperName}}.count({{=beanVarName}});\n        return new PageImpl<>({{=mapperName}}.queryAllByLimit({{=beanVarName}}, pageRequest), pageRequest, total);\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} insert({{=beanClass}} {{=beanVarName}}){\n        {{=mapperName}}.insert({{=beanVarName}});\n        return {{=beanVarName}};\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} update({{=beanClass}} {{=beanVarName}}){\n        {{=mapperName}}.update({{=beanVarName}});\n        return queryById({{=beanVarName}}.get{{=pkVarNameU}}());\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    public boolean deleteById({{=pkDataType}} {{=pkVarName}}){\n        int total = {{=mapperName}}.deleteById({{=pkVarName}});\n        return total > 0;\n    }\n}", "Mapper": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.mapper;\n$blankline\nimport java.util.List;\nimport org.apache.ibatis.annotations.Mapper;\nimport org.apache.ibatis.annotations.Param;\nimport org.springframework.data.domain.Pageable;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表数据库访问层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Mapper\npublic interface {{=beanClass}}Mapper{\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}});\n    \n    /** \n     * 分页查询指定行数据\n     *\n     * @param {{=beanVarName}} 查询条件\n     * @param pageable 分页对象\n     * @return 对象列表\n     */\n    List<{{=beanClass}}> queryAllByLimit({{=beanClass}} {{=beanVarName}}, @Param(\"pageable\") Pageable pageable);\n\n    /** \n     * 统计总行数\n     *\n     * @param {{=beanVarName}} 查询条件\n     * @return 总行数\n     */\n    long count({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 影响行数\n     */\n    int insert({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 批量新增数据\n     *\n     * @param entities List<{{=beanClass}}> 实例对象列表\n     * @return 影响行数\n     */\n    int insertBatch(@Param(\"entities\") List<{{=beanClass}}> entities);\n    \n    /** \n     * 批量新增或按主键更新数据\n     *\n     * @param entities List<{{=beanClass}}> 实例对象列表\n     * @return 影响行数\n     */\n    int insertOrUpdateBatch(@Param(\"entities\") List<{{=beanClass}}> entities);\n    \n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 影响行数\n     */\n    int update({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 影响行数\n     */\n    int deleteById({{=pkDataType}} {{=pkVarName}});\n}", "Mapper.xml": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    var pkField = \"UNDEFINED_ID\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkField = field.defKey;\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n<mapper namespace=\"{{=pkgName}}.mapper.{{=beanClass}}Mapper\">\n    <resultMap type=\"{{=pkgName}}.entity.{{=beanClass}}\" id=\"{{=beanClass}}Map\">\n    {{~it.entity.fields:field:index}}\n        <result property=\"{{=it.func.camel(field.defKey,false)}}\" column=\"{{=field.defKey}}\" jdbcType=\"{{=field.dbType}}\"/>\n    {{~}}\n    </resultMap>\n    $blankline\n    <!-- 通过ID查询单条数据 -->\n    <select id=\"queryById\" resultMap=\"{{=beanClass}}Map\">\n        select\n            {{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}}\n        from {{=it.entity.defKey}}\n        where {{=pkField}} = #{{{=pkVarName}}}\n    </select>\n    $blankline\n    <!--分页查询指定行数据-->\n    <select id=\"queryAllByLimit\" resultMap=\"{{=beanClass}}Map\">\n        select\n            {{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}}\n        from {{=it.entity.defKey}}\n        <where>\n        {{~it.entity.fields:field:index}}\n            <if test=\"{{=it.func.camel(field.defKey,false)}} != null and {{=it.func.camel(field.defKey,false)}} != ''\">\n                and {{=field.defKey}} = #{{{=it.func.camel(field.defKey,false)}}}\n            </if>\n        {{~}}\n        </where>\n        limit #{pageable.offset}, #{pageable.pageSize}\n    </select>\n    $blankline\n    <!--统计总行数-->\n    <select id=\"count\" resultType=\"java.lang.Long\">\n        select count(1)\n        from {{=it.entity.defKey}}\n        <where>\n        {{~it.entity.fields:field:index}}\n            <if test=\"{{=it.func.camel(field.defKey,false)}} != null and {{=it.func.camel(field.defKey,false)}} != ''\">\n                and {{=field.defKey}} = #{{{=it.func.camel(field.defKey,false)}}}\n            </if>\n        {{~}}\n        </where>\n    </select>\n    $blankline\n    <!--新增数据-->\n    <insert id=\"insert\" keyProperty=\"{{=pkField}}\" useGeneratedKeys=\"true\">\n        insert into {{=it.entity.defKey}}({{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}})\n        values ({{=it.entity.fields.map(function(e,i){return '#{'+it.func.camel(e.defKey,false)+'}'}).join(',')}})\n    </insert>\n    $blankline\n    <!-- 批量新增数据 -->\n    <insert id=\"insertBatch\" keyProperty=\"{{=pkField}}\" useGeneratedKeys=\"true\">\n        insert into {{=it.entity.defKey}}({{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}})\n        values\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n            ({{=it.entity.fields.map(function(e,i){return '#{entity.'+it.func.camel(e.defKey,false)+'}'}).join(',')}})\n        </foreach>\n    </insert>\n    $blankline\n    <!-- 批量新增或按主键更新数据 -->\n    <insert id=\"insertOrUpdateBatch\" keyProperty=\"{{=pkField}}\" useGeneratedKeys=\"true\">\n        insert into {{=it.entity.defKey}}({{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}})\n        values\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n            ({{=it.entity.fields.map(function(e,i){return '#{entity.'+it.func.camel(e.defKey,false)+'}'}).join(',')}})\n        </foreach>\n        on duplicate key update\n        {{=it.entity.fields.map(function(e,i){return e.defKey + '=values('+e.defKey+')'}).join(',\\n\\t\\t')}}\n    </insert>\n    $blankline\n    <!-- 更新数据 -->\n    <update id=\"update\">\n        update {{=it.entity.defKey}}\n        <set>\n        {{~it.entity.fields:field:index}}\n            <if test=\"{{=it.func.camel(field.defKey,false)}} != null and {{=it.func.camel(field.defKey,false)}} != ''\">\n                {{=field.defKey}} = #{{{=it.func.camel(field.defKey,false)}}},\n            </if>\n        {{~}}\n        </set>\n        where {{=pkField}} = #{{{=pkVarName}}}\n    </update>\n    $blankline\n    <!--通过主键删除-->\n    <delete id=\"deleteById\">\n        delete from {{=it.entity.defKey}} where {{=pkField}} = #{{{=pkVarName}}}\n    </delete>\n</mapper>\n\n", "Entity": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.entity;\n$blankline\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n\n /**\n * {{=it.entity.defName}};{{=it.entity.comment}}\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@ApiModel(value = \"{{=it.entity.defName}}\",description = \"{{=it.entity.comment}}\")\npublic class {{=beanClass}} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    @ApiModelProperty(name = \"{{=field.defName}}\",notes = \"{{=field.comment}}\")\n    private {{=field.type}} {{=it.func.camel(field.defKey,false)}} ;\n{{~}}\n$blankline\n\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.defKey,true)}}(){\n        return this.{{=it.func.camel(field.defKey,false)}};\n    }\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public void set{{=it.func.camel(field.defKey,true)}}({{=field.type}} {{= it.func.camel(field.defKey,false) }}){\n        this.{{=it.func.camel(field.defKey,false)}}={{=it.func.camel(field.defKey,false)}};\n    }\n{{~}}\n}"}, {"applyFor": "A2EE7B4A-CE62-4290-B00C-B26C1BF18073", "type": "appCode", "Controller": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.controller;\n$blankline\nimport java.util.List;\nimport io.swagger.annotations.Api;\nimport io.swagger.annotations.ApiOperation;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.data.domain.PageImpl;\nimport org.springframework.data.domain.PageRequest;\nimport org.springframework.http.ResponseEntity;\nimport org.springframework.web.bind.annotation.*;\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.service.{{=serviceClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表控制层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Api(tags = \"{{=it.entity.defName}}对象功能接口\")\n@RestController\n@RequestMapping(\"/{{=it.func.camel(it.entity.defKey,false)}}\")\npublic class {{=beanClass}}Controller{\n    @Autowired\n    private {{=serviceClass}} {{=serviceVarName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    @ApiOperation(\"通过ID查询单条数据\")\n    @GetMapping(\"{{{=it.func.camel(pkVarName,false)}}}\")\n    public ResponseEntity<{{=beanClass}}> queryById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.queryById({{=pkVarName}}));\n    }\n    $blankline\n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    @ApiOperation(\"分页查询\")\n    @GetMapping\n    public ResponseEntity<PageImpl<{{=beanClass}}>> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest){\n        //1.分页参数\n        long current = pageRequest.getPageNumber();\n        long size = pageRequest.getPageSize();\n\n        //2.分页查询\n        /*把Mybatis的分页对象做封装转换，MP的分页对象上有一些SQL敏感信息，还是通过spring的分页模型来封装数据吧*/\n        com.baomidou.mybatisplus.extension.plugins.pagination.Page<{{=beanClass}}> pageResult = {{=serviceVarName}}.paginQuery({{=beanVarName}}, current,size);\n\n        //3. 分页结果组装\n        List<{{=beanClass}}> dataList = pageResult.getRecords();\n        long total = pageResult.getTotal();\n        PageImpl<{{=beanClass}}> retPage = new PageImpl<{{=beanClass}}>(dataList,pageRequest,total);\n        return ResponseEntity.ok(retPage);\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"新增数据\")\n    @PostMapping\n    public ResponseEntity<{{=beanClass}}> add({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.insert({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"更新数据\")\n    @PutMapping\n    public ResponseEntity<{{=beanClass}}> edit({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.update({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    @ApiOperation(\"通过主键删除数据\")\n    @DeleteMapping\n    public ResponseEntity<Boolean> deleteById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.deleteById({{=pkVarName}}));\n    }\n}", "Service": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.service;\n$blankline\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务接口\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\npublic interface {{=serviceClass}}{\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}});\n    $blankline\n    /**\n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param current 当前页码\n     * @param size  每页大小\n     * @return\n     */\n    Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, long current, long size);\n\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} insert({{=beanClass}} {{=beanVarName}});\n\n    \n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} update({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    boolean deleteById({{=pkDataType}} {{=pkVarName}});\n}", "ServiceImpl": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkVarNameU = \"UndefinedId\";\n    var pkFieldKey = \"UNDEFINED\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkFieldKey = field.defKey;\n            pkVarName = it.func.camel(field.defKey,false);\n            pkVarNameU = it.func.camel(field.defKey,true);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    var mapperName = beanVarName+'Mapper';\n    \n}}package {{=pkgName}}.service.impl;\n$blankline\nimport cn.hutool.core.util.StrUtil;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.stereotype.Service;\nimport com.baomidou.mybatisplus.core.metadata.IPage;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;\nimport com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;\n\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.mapper.{{=beanClass}}Mapper;\nimport {{=pkgName}}.service.{{=serviceClass}};\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务实现类\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Service\npublic class {{=serviceClass}}Impl implements {{=serviceClass}}{\n    @Autowired\n    private {{=beanClass}}Mapper {{=mapperName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    public {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}}){\n        return {{=mapperName}}.selectById({{=pkVarName}});\n    }\n    $blankline\n    /**\n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param current 当前页码\n     * @param size  每页大小\n     * @return\n     */\n    public Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, long current, long size){\n        //1. 构建动态查询条件\n        LambdaQueryWrapper<{{=beanClass}}> queryWrapper = new LambdaQueryWrapper<>();\n        {{~it.entity.fields.filter(function(e){return e[\"type\"]===\"String\"&&e.defKey !== pkFieldKey}):field:index}}\n        if(StrUtil.isNotBlank({{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}())){\n            queryWrapper.eq({{=beanClass}}::get{{=it.func.camel(field.defKey,true)}}, {{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}());\n        }\n        {{~}}\n\n        //2. 执行分页查询\n        Page<{{=beanClass}}> pagin = new Page<>(current , size , true);\n        IPage<{{=beanClass}}> selectResult = {{=mapperName}}.selectByPage(pagin , queryWrapper);\n        pagin.setPages(selectResult.getPages());\n        pagin.setTotal(selectResult.getTotal());\n        pagin.setRecords(selectResult.getRecords());\n\n        //3. 返回结果\n        return pagin;\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} insert({{=beanClass}} {{=beanVarName}}){\n        {{=mapperName}}.insert({{=beanVarName}});\n        return {{=beanVarName}};\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} update({{=beanClass}} {{=beanVarName}}){\n        //1. 根据条件动态更新\n        LambdaUpdateChainWrapper<{{=beanClass}}> chainWrapper = new LambdaUpdateChainWrapper<{{=beanClass}}>({{=mapperName}});\n        {{~it.entity.fields.filter(function(e){return e[\"type\"]===\"String\"&&e.defKey !== pkFieldKey}):field:index}}\n        if(StrUtil.isNotBlank({{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}())){\n            chainWrapper.eq({{=beanClass}}::get{{=it.func.camel(field.defKey,true)}}, {{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}());\n        }\n        {{~}}\n        //2. 设置主键，并更新\n        chainWrapper.set({{=beanClass}}::get{{=pkVarNameU}}, {{=beanVarName}}.get{{=pkVarNameU}}());\n        boolean ret = chainWrapper.update();\n        //3. 更新成功了，查询最最对象返回\n        if(ret){\n            return queryById({{=beanVarName}}.get{{=pkVarNameU}}());\n        }else{\n            return {{=beanVarName}};\n        }\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    public boolean deleteById({{=pkDataType}} {{=pkVarName}}){\n        int total = {{=mapperName}}.deleteById({{=pkVarName}});\n        return total > 0;\n    }\n}", "Mapper": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.mapper;\n$blankline\n\nimport com.baomidou.mybatisplus.core.conditions.Wrapper;\nimport com.baomidou.mybatisplus.core.mapper.BaseMapper;\nimport com.baomidou.mybatisplus.core.metadata.IPage;\nimport com.baomidou.mybatisplus.core.toolkit.Constants;\nimport org.apache.ibatis.annotations.Mapper;\nimport org.apache.ibatis.annotations.Param;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表数据库访问层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Mapper\npublic interface {{=beanClass}}Mapper  extends BaseMapper<{{=beanClass}}>{\n    /** \n     * 分页查询指定行数据\n     *\n     * @param page 分页参数\n     * @param wrapper 动态查询条件\n     * @return 分页对象列表\n     */\n    IPage<{{=beanClass}}> selectByPage(IPage<{{=beanClass}}> page , @Param(Constants.WRAPPER) Wrapper<{{=beanClass}}> wrapper);\n}", "Mapper.xml": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    var pkField = \"UNDEFINED_ID\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkField = field.defKey;\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n$blankline\n\n<mapper namespace=\"{{=pkgName}}.mapper.{{=beanClass}}Mapper\">\n     <select id=\"selectByPage\" resultType=\"{{=pkgName}}.entity.{{=beanClass}}\">\n        select * from user ${ew.customSqlSegment}\n    </select>\n</mapper>\n\n", "Entity": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.entity;\n$blankline\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\nimport com.baomidou.mybatisplus.annotation.TableName;\nimport com.baomidou.mybatisplus.annotation.TableId;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n\n /**\n * {{=it.entity.defName}};{{=it.entity.comment}}\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@ApiModel(value = \"{{=it.entity.defName}}\",description = \"{{=it.entity.comment}}\")\n@TableName(\"{{=it.entity.defKey}}\")\npublic class {{=beanClass}} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    @ApiModelProperty(name = \"{{=field.defName}}\",notes = \"{{=field.comment}}\")\n    {{? field.primaryKey }}\n    @TableId\n    {{?}}\n    private {{=field.type}} {{=it.func.camel(field.defKey,false)}} ;\n{{~}}\n$blankline\n\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.defKey,true)}}(){\n        return this.{{=it.func.camel(field.defKey,false)}};\n    }\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public void set{{=it.func.camel(field.defKey,true)}}({{=field.type}} {{= it.func.camel(field.defKey,false) }}){\n        this.{{=it.func.camel(field.defKey,false)}}={{=it.func.camel(field.defKey,false)}};\n    }\n{{~}}\n}"}, {"applyFor": "29D1CE08-4C35-4D2D-AAA9-23D93305B52E", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.dbType}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.autoIncrement ? 'AUTO_INCREMENT' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }} COMMENT '{{=it.func.join(field.defName,field.comment,';')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n)  COMMENT = '{{=it.func.join(it.entity.defName,it.entity.comment,';') }}';\n$blankline\n", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "deleteIndex": "{{~ it.entity.indexes:index}}\nDROP INDEX IF EXISTS {{=index.defKey}} ;\n{{~}}", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n\n\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n{{? modifyEntities && modifyEntities.length > 0}}\n/* --------------- 修改表 --------------- */\n{{~ modifyEntities:entity}}\n{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}{{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}{{=indexChanged?'\\n\\t更改了索引':''}}{{=changed?'\\n\\t更改了属性':''}}{{=relaArray.length>0?('\\n\\t'+relaArray.join('\\n\\t')):''}}\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n{{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n{{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n{{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n{{?}}\n{{~}}\n{{?}}\n", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('ALTER TABLE '+before.defKey+' RENAME TO '+after.defKey);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            ret.push('ALTER TABLE '+after.defKey+' COMMENT \\''+commentText+'\\'');\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldAdded) { \n            let ddlItem = 'ADD COLUMN '+field.defKey+' '+field.dbType;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }\n            if(field.autoIncrement){\n                ddlItem += ' AUTO_INCREMENT';\n            }\n            if(field.defaultValue){\n                ddlItem += (' DEFAULT ' + field.defaultValue);\n            }\n            ddlItem += (' COMMENT \\''+field.defName+';'+field.comment+'\\'');\n            \n            if(field.index>0 && field.afterFieldKey){\n                ddlItem += (' AFTER '+field.afterFieldKey);\n            }\n            ret.push(ddlItem);\n        }\n        return firstDDL+'\\n'+ret.join(',\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldRemoved) { \n            ret.push('DROP '+field.defKey);\n        }\n        return firstDDL+'\\n'+ret.join(',\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldModified) { \n            let changeDDL = '';\n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey === after.defKey){\n                changeDDL += (' MODIFY COLUMN '+after.defKey);\n            }else{\n                changeDDL += (' CHANGE COLUMN '+before.defKey+' '+after.defKey);\n            }\n            changeDDL += (' '+after.dbType);\n            if(after.len>0){\n                changeDDL += ('('+after.len);\n                if(parseInt(after.scale)>0){\n                    changeDDL += (','+after.scale);\n                }\n                changeDDL += ')';\n            }\n            if(after.notNull){\n                changeDDL += ' NOT NULL';\n            }\n            let defaultValue = '';\n            if(after.defaultValue != null && after.defaultValue.length>0){\n                defaultValue = (after.defaultValue);\n            }else{\n                defaultValue = 'NULL';\n            }\n            if(defaultValue != 'NULL'){\n                changeDDL += (' DEFAULT ' + defaultValue);\n            }\n\n            let comment = after.comment||'';\n            if(comment){\n                changeDDL += (' COMMENT \\''+comment+'\\';');\n            }\n            \n            ret.push(firstDDL+' '+changeDDL);\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"applyFor": "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542", "type": "dbDDL", "createTable": "CREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? '' : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "DROP TABLE {{=it.entity.defKey}};", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "deleteIndex": "{{~ it.entity.indexes:index}}\nDROP INDEX {{=index.defKey}} ;\n{{~}}", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* -------------------------------------------------- */\n创建表：\n{{~ createEntities:entity}}\n    {{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* -------------------------------------------------- */\n删除表：\n{{~ dropEntities:entity}}\n    {{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* -------------------------------------------------- */\n修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n    {{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n    {{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n    {{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n    {{?}}{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}\n    {{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('\\n\\t建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('\\n\\t解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}\n{{=indexChanged?'\\n\\t更改了索引':''}}\n{{=changed?'\\n\\t更改了属性':''}}\n{{=relaArray.length>0?relaArray.join(''):''}}\n{{~}}\n{{?}}", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push(`ALTER TABLE ${before.defKey} RENAME TO ${after.defKey}`);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            let myText = `COMMENT ON TABLE ${after.defKey} IS '${commentText}'`;\n            ret.push(myText);\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldAdded) { \n            let ddlItem = `ADD (${field.defKey} ${field.dbType}`;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            let defaultValue = field.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                ddlItem += (' DEFAULT ' + defaultValue);\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }\n            ddlItem += ')';\n            ret.push(`${firstDDL} ${ddlItem}`);\n            \n            /*处理字段注释*/\n            let fieldComments = [];\n            if(field.defName != null &&field.defName.length>0){\n                fieldComments.push(field.defName);\n            }\n            if(field.comment != null &&field.comment.length>0){\n                fieldComments.push(field.comment);\n            }\n            let commentText = fieldComments.join(';');\n            if(commentText != null && commentText.length > 0){\n                let commentDDL = `COMMENT ON COLUMN ${entity.data.baseInfo.defKey}.${field.defKey} IS '${commentText}'`;\n                 ret.push(commentDDL);\n            }\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldRemoved) { \n            ret.push(`${firstDDL} DROP COLUMN ${field.defKey}`);\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldModified) { \n            let changeDDL = '';\n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey !== after.defKey){\n                let renameText = `ALTER TABLE ${entity.data.baseInfo.defKey} RENAME COLUMN ${before.defKey} TO ${after.defKey};`;\n                ret.push(renameText);\n            }\n            /*如果没有变化，则不生成变更语句*/\n            if(before.dbType === after.dbType \n            && before['len'] === after['len'] \n            && before.scale === after.scale\n            && before.primaryKey === after.primaryKey\n            && before.notNull === after.notNull\n            && before.autoIncrement === after.autoIncrement\n            && before.defaultValue === after.defaultValue){\n                continue;\n            }\n            changeDDL += ('MODIFY ('+after.defKey+'');\n            changeDDL += (' '+after.dbType);\n            if(after.len>0){\n                changeDDL += ('('+after.len);\n                if(parseInt(after.scale)>0){\n                    changeDDL += (','+after.scale);\n                }\n                changeDDL += ')';\n            }\n            let defaultValue = after.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                changeDDL += (' DEFAULT ' + defaultValue);\n            }\n            \n            if(after.notNull){\n                changeDDL += ' NOT NULL';\n            }\n            changeDDL += ')';\n            ret.push(`${firstDDL} ${changeDDL};`);\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"applyFor": "BFC87171-C74F-494A-B7C2-76B9C55FACC9", "type": "dbDDL", "createTable": "IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[{{=it.entity.defKey}}]') AND type in (N'U')) DROP TABLE [dbo].[{{=it.entity.defKey}}];\n\nCREATE TABLE [dbo].[{{=it.entity.defKey}}](\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? ' IDENTITY(1,1)' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}EXEC sp_addextendedproperty 'MS_Description', '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}', 'SCHEMA', dbo, 'table', {{=it.entity.defKey}}, null, null;{{?}}\n{{~it.entity.fields:field:index}}\nEXEC sp_addextendedproperty 'MS_Description', '{{=it.func.join(field.defName,field.comment,';')}}', 'SCHEMA', dbo, 'table', {{=it.entity.defKey}}, 'column', {{=field.defKey}};\n{{~}}\n", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[{{=it.entity.defKey}}]') AND type in (N'U')) DROP TABLE [dbo].[{{=it.entity.defKey}}];", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "deleteIndex": "", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n\n\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n{{? modifyEntities && modifyEntities.length > 0}}\n/* --------------- 修改表 --------------- */\n{{~ modifyEntities:entity}}\n{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}{{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}{{=indexChanged?'\\n\\t更改了索引':''}}{{=changed?'\\n\\t更改了属性':''}}{{=relaArray.length>0?('\\n\\t'+relaArray.join('\\n\\t')):''}}\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n{{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n{{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n{{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n{{?}}\n{{~}}\n{{?}}", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push(`EXEC sp_rename '${before.defKey}','${after.defKey}'`);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            let myText = `IF ((SELECT COUNT(*) FROM ::fn_listextendedproperty('MS_Description','SCHEMA', 'dbo','TABLE', '${after.defKey}', NULL, NULL)) > 0)\n            \\n\\tEXEC sp_updateextendedproperty 'MS_Description', '${commentText}','SCHEMA', 'dbo','TABLE', '${after.defKey}'\n            \\nELSE\n            \\n\\tEXEC sp_addextendedproperty 'MS_Description', '${commentText}', 'SCHEMA', 'dbo','TABLE', '${after.defKey}'\n            `;\n            ret.push(myText);\n            /*ret.push('ALTER TABLE '+after.defKey+' COMMENT \\''+commentText+'\\'');*/\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE [dbo].[${entity.data.baseInfo.defKey}]`;\n        for (let field of fieldAdded) { \n            let ddlItem = `ADD [${field.defKey}] ${field.dbType}`;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            let defaultValue = field.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                ddlItem += (' DEFAULT ' + defaultValue);\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }\n            ret.push(`${firstDDL} ${ddlItem}`);\n            \n            /*处理字段注释*/\n            let fieldComments = [];\n            if(field.defName != null &&field.defName.length>0){\n                fieldComments.push(field.defName);\n            }\n            if(field.comment != null &&field.comment.length>0){\n                fieldComments.push(field.comment);\n            }\n            let commentText = fieldComments.join(';');\n            if(commentText != null && commentText.length > 0){\n                let commentDDL = `EXEC sp_addextendedproperty 'MS_Description', N'${commentText}','SCHEMA', N'dbo','TABLE', N'${entity.data.baseInfo.defKey}','COLUMN', N'${field.defKey}'`;\n                ret.push(commentDDL);\n            }\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE [dbo].[${entity.data.baseInfo.defKey}]`;\n        for (let field of fieldRemoved) { \n            ret.push(`${firstDDL} DROP COLUMN [${field.defKey}]`);\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = `ALTER TABLE [dbo].[${entity.data.baseInfo.defKey}]`;\n        for (let field of fieldModified) { \n            let changeDDL = '';\n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey === after.defKey){\n                changeDDL += (' ALTER COLUMN ['+after.defKey+']');\n            }else{\n                let renameText = `EXEC sp_rename '[dbo].[${entity.data.baseInfo.defKey}].[${before.defKey}]','${after.defKey}','COLUMN';`;\n                ret.push(renameText);\n                continue;\n            }\n            changeDDL += (' '+after.dbType);\n            if(after.len>0){\n                changeDDL += ('('+after.len);\n                if(parseInt(after.scale)>0){\n                    changeDDL += (','+after.scale);\n                }\n                changeDDL += ')';\n            }\n            let defaultValue = after.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                changeDDL += (' DEFAULT ' + defaultValue);\n            }\n            \n            if(after.notNull){\n                changeDDL += ' NOT NULL';\n            }\n            ret.push(`${firstDDL} ${changeDDL};`);\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"applyFor": "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{? field.autoIncrement}}SERIAL{{??}}{{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "deleteIndex": "{{~ it.entity.indexes:index}}\nDROP INDEX {{=index.defKey}} ;\n{{~}}", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n\n\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n{{? modifyEntities && modifyEntities.length > 0}}\n/* --------------- 修改表 --------------- */\n{{~ modifyEntities:entity}}\n{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}{{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}{{=indexChanged?'\\n\\t更改了索引':''}}{{=changed?'\\n\\t更改了属性':''}}{{=relaArray.length>0?('\\n\\t'+relaArray.join('\\n\\t')):''}}\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n{{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n{{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n{{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n{{?}}\n{{~}}\n{{?}}", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push(`ALTER TABLE ${before.defKey} RENAME TO ${after.defKey}`);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            let myText = `COMMENT ON TABLE ${after.defKey} IS '${commentText}'`;\n            ret.push(myText);\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldAdded) { \n            let ddlItem = `ADD COLUMN ${field.defKey} ${field.dbType}`;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }            \n            let defaultValue = field.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                ddlItem += (' DEFAULT ' + defaultValue);\n            }\n\n            ret.push(`${firstDDL} ${ddlItem}`);\n            \n            /*处理字段注释*/\n            let fieldComments = [];\n            if(field.defName != null &&field.defName.length>0){\n                fieldComments.push(field.defName);\n            }\n            if(field.comment != null &&field.comment.length>0){\n                fieldComments.push(field.comment);\n            }\n            let commentText = fieldComments.join(';');\n            if(commentText != null && commentText.length > 0){\n                let commentDDL = `COMMENT ON COLUMN ${entity.data.baseInfo.defKey}.${field.defKey} IS '${commentText}'`;\n                 ret.push(commentDDL);\n            }\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldRemoved) { \n            ret.push(`${firstDDL} DROP COLUMN ${field.defKey}`);\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldModified) { \n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey !== after.defKey){\n                let renameText = `ALTER TABLE ${entity.data.baseInfo.defKey} RENAME COLUMN ${before.defKey} TO ${after.defKey};`;\n                ret.push(renameText);\n            }\n            /*如果没有变化，则不生成变更语句*/\n            if(before.dbType === after.dbType \n            && before['len'] === after['len'] \n            && before.scale === after.scale\n            && before.primaryKey === after.primaryKey\n            && before.notNull === after.notNull\n            && before.autoIncrement === after.autoIncrement\n            && before.defaultValue === after.defaultValue){\n                continue;\n            }\n            if(before.dbType !== after.dbType || before.len !== after.len || before.scale !== after.scale){\n                let dbTypeDDL = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${after.defKey} TYPE ${before.dbType}`;\n                if(after.len>0){\n                    dbTypeDDL += ('('+after.len);\n                    if(parseInt(after.scale)>0){\n                        dbTypeDDL += (','+after.scale);\n                    }\n                    dbTypeDDL += ')';\n                }\n                ret.push(dbTypeDDL+';');\n            }\n            \n            if(before.defaultValue !== after.defaultValue){\n                let defaultDDL = '';\n                let defaultValue = after.defaultValue;\n                defaultValue = (defaultValue==null)?\"NULL\":(\"\"+defaultValue);\n                if(defaultValue.length>0){\n                    defaultDDL += ('SET DEFAULT ' + defaultValue);\n                }\n                let defaultTpl = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${defaultDDL};`;\n                ret.push(defaultTpl);\n            }\n            \n            if(before.notNull !== after.notNull){\n                let notNullDDL= 'SET NULL';\n                if(after.notNull){\n                    let notNullDDL= 'SET NOT NULL';\n                }\n                let notNullTpl = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${notNullDDL};`;\n                ret.push(notNullTpl);\n            }\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n-- 索引重建\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"type": "dbDDL", "applyFor": "89504F5D-94BF-4C9E-8B2E-44F37305FED5", "createTable": "CREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? '' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n"}, {"type": "dbDDL", "applyFor": "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307", "createTable": "CREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? '' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ','('+field.defaultValue+')',' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n"}, {"type": "dbDDL", "applyFor": "592C7013-143D-4E7B-AF64-0D7BF1E28230", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? ' AUTO_INCREMENT' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n"}, {"type": "dbDDL", "applyFor": "77BD85E5-9D0D-4096-8427-CBA306FC9C6A", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n"}, {"applyFor": "11D1FB71-A587-4217-89BA-611B8A1F83E0", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.autoIncrement ? 'AUTO_INCREMENT' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }} COMMENT '{{=it.func.join(field.defName,field.comment,';')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n)  COMMENT '{{=it.func.join(it.entity.defName,it.entity.comment,';') }}';\n$blankline\n", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "", "createIndex": "", "deleteIndex": "", "message": "", "update": ""}, {"applyFor": "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.autoIncrement ? 'AUTOINCREMENT' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }} --{{=it.func.join(field.defName,field.comment,';')}}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n)  ; --{{=it.func.join(it.entity.defName,it.entity.comment,';') }}\n$blankline\n", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n"}, {"type": "dbDDL", "applyFor": "dictSQLTemplate", "content": "/* 插入字典总表[{{=it.dict.defKey}}-{{=it.dict.defName}}] */\nINSERT INTO SYS_DICT(KEY_,LABEL,INTRO,REVISION) VALUES('{{=it.dict.defKey}}','{{=it.dict.defName}}','{{=it.dict.intro}}',1);\n/* 插入字典明细表 */\n{{~it.dict.items:item:index}}\nINSERT INTO SYS_DICT_ITEM(DICT_KEY,KEY_,LABEL,SORT_,INTRO,REVISION) VALUES('{{=it.dict.defKey}}','{{=item.defKey}}','{{=item.defName}}','{{=item.sort}}','{{=item.intro}}',1);\n{{~}}"}, {"applyFor": "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2", "type": "dbDDL", "createTable": "/**字段名,关键字等全部用的小写*/\ndrop table if exists {{=it.entity.defKey}};\n/**补充上库名,external关键字根据建表规范看是否添加*/\ncreate [external] table if not exists {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n/**这里把varchar,char,text,date,datetime字段全部映射为string类型.tinyint unsigned,bit,Integer,tinyint,smallint,mediumint映射为int类型,int unsigned映射为bigint.其它自定义映射规则根据自己情况修改*/\n/**当长度>0只有为decimal类型或double类型时才保留长度和小数的位数*/\n{{~it.entity.fields:field:index}}\n    {{=it.func.lowerCase(field.defKey)}} {{=it.func.lowerCase(field.dbType)=='varchar'||it.func.lowerCase(field.dbType)=='char'||it.func.lowerCase(field.dbType)=='text'||it.func.lowerCase(field.dbType)=='date'||it.func.lowerCase(field.dbType)=='datetime' ? 'string':it.func.lowerCase(field.dbType)=='tinyint unsigned'||it.func.lowerCase(field.dbType)=='bit'||it.func.lowerCase(field.dbType)=='integer'||it.func.lowerCase(field.dbType)=='tinyint'||it.func.lowerCase(field.dbType)=='smallint'||it.func.lowerCase(field.dbType)=='mediumint' ? 'int':it.func.lowerCase(field.dbType)=='int unsigned' ? 'bigint':it.func.lowerCase(field.dbType)}}{{?field.len>0&&(it.func.lowerCase(field.dbType)=='decimal'||it.func.lowerCase(field.dbType)=='double')}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{=')'}}{{?}}{{?}} comment '{{=it.func.join(field.defName,field.comment,'')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n{{?}}\n)\n{{\n    let partitionedBy = it.entity.properties['partitioned by'];\n    partitionedBy = partitionedBy?partitionedBy:'请在扩展属性中配置[partitioned by]属性';\n}}\ncomment '{{=it.func.join(it.entity.defName,';') }}'\n/**是否分区表,分区字段名和字段注释自定义*/\n[partitioned by {{=partitionedBy}}]\n/**文件存储格式自定义*/\n[stored as orc]\n/**hdfs上的地址自定义*/\n[location xxx]\n;", "createView": "", "deleteTable": "", "createIndex": "", "deleteIndex": "", "message": "", "update": ""}, {"applyFor": "B91D99E0-9B7C-416C-8737-B760957DAF09", "type": "appCode", "content": "{{\n    var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1<10?\"0\"+today.getMonth():today.getMonth();\n    var days=today.getDate()<10?\"0\"+today.getDate():today.getDate();\n    var hours = today.getHours()<10?\"0\"+today.getHours():today.getHours();         \n\tvar minutes = today.getMinutes()<10?\"0\"+today.getMinutes():today.getMinutes();      \n\tvar seconds = today.getSeconds()<10?\"0\"+today.getSeconds():today.getSeconds();    \n}}\n// Package models  {{=it.func.join(it.entity.defName,it.entity.comment,'，')}}\n// author : http://www.liyang.love\n// date : {{=fullYear}}-{{=month}}-{{=days}} {{=hours}}:{{=minutes}}\n// desc : {{=it.func.join(it.entity.defName,it.entity.comment,'，')}}\npackage models\n\n$blankline\n\n// {{=it.func.camel(it.entity.defKey,true) }}  {{=it.func.join(it.entity.defName,it.entity.comment,'，')}}。\n// 说明:{{=it.entity.comment}}\n// 表名:{{=it.entity.defKey}}\n// group: {{=it.func.camel(it.entity.defKey,true) }}\n// obsolete:\n// appliesto:go 1.8+;\n// namespace:hongmouer.his.models.{{=it.func.camel(it.entity.defKey,true) }}\n// assembly: hongmouer.his.models.go\n// class:HongMouer.HIS.Models.{{=it.func.camel(it.entity.defKey,true) }}\n// version:{{=fullYear}}-{{=month}}-{{=days}} {{=hours}}:{{=minutes}}\ntype {{=it.func.camel(it.entity.defKey,true) }} struct {\n    {{~it.entity.fields:field:index}}\n    {{=formatGoLang(it.func.camel(field.defKey,true),null,field,it.entity.fields,null,1)}} {{=formatGoLang(field.type,\"type\",field,it.entity.fields,10,3)}}  `gorm:\"column:{{=field.primaryKey?\"primaryKey;\":\"\"}}{{=field.defKey}}\" json:\"{{=it.func.camel(field.defKey,true)}}\"` {{=formatGoLang(\"gorm:column:\"+field.defKey+\" json:\"+it.func.camel(field.defKey,true),null,field,it.entity.fields,null,2)}}  //type:{{=formatGoLang(field.type,\"type\",field,it.entity.fields,null,3)}}  comment:{{=formatGoLang(it.func.join(field.defName,field.comment,';'),\"defName\",field,it.entity.fields,null,4)}}  version:{{=fullYear}}-{{=month}}-{{=days}} {{=hours}}:{{=minutes}}\n    {{~}}\n}\n\n\n$blankline\n// TableName 表名:{{=it.entity.defKey}}，{{=it.entity.defName}}。\n// 说明:{{=it.entity.comment}}\nfunc (ZentaoUserInfo) TableName() string {\n\treturn \"{{=it.entity.defKey}}\"\n}\n\n{{\n\nfunction formatGoLang(str, fieldName, field, fileds, emptLength, isFiled) {\n    var maxLength = 0;\n\n    if (isFiled == 1) {\n        for (var i = 0; i < fileds.length; i++) {\n            if (getBlength(it.func.camel(fileds[i].defKey, true)) > maxLength) {\n                maxLength = getBlength(it.func.camel(fileds[i].defKey, true)) + 2;\n            }\n        }\n    } else if (isFiled == 2) {\n        for (var i = 0; i < fileds.length; i++) {\n            var newStr = \"gorm:column:\" + fileds[i].defKey + \" json:\" + it.func.camel(fileds[i].defKey, true);\n            if (getBlength(newStr) > maxLength) {\n                maxLength = getBlength(newStr) + 2;\n            }\n        }\n        var empt = \"\";\n        var strLength = getBlength(str);\n        if (field.primaryKey) {\n            strLength += getBlength(\"primaryKey;\");\n        }\n        for (var j = 0; j < maxLength - strLength; j++) {\n            empt += ' ';\n        }\n        return empt;\n    } else if (isFiled == 3) {\n        /*获取某个字段的最大长度*/\n        for (var i = 0; i < fileds.length; i++) {\n            var newStr = eval(\"fileds[\" + i + \"].\" + fieldName);\n            if (getBlength(newStr) > maxLength) {\n                maxLength = getBlength(newStr) + 1;\n            }\n        }\n    } else if (isFiled == 4) {\n        /*获取某个字段的最大长度*/\n        for (var i = 0; i < fileds.length; i++) {\n            var newStr = fileds[i].comment + \";\" + fileds[i].defName;\n            if (getBlength(newStr) > maxLength) {\n                maxLength = getBlength(newStr) + 1;\n            }\n        }\n    }\n    else {\n        maxLength = emptLength;\n    }\n\n    var strLength = getBlength(str);\n    for (var j = 0; j < maxLength - strLength; j++) {\n        str += ' ';\n    }\n    return str;\n}\n\nfunction getBlength(str) {\n    var n = 0;\n    for (var i = str.length; i--;) {\n        n += str.charCodeAt(i) > 255 ? 2 : 1;\n    }\n    return n;\n} \n\n}}"}], "generatorDoc": {"docTemplate": ""}, "relationFieldSize": "15", "uiHint": [{"defKey": "Input", "defName": "普通输入框", "id": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "Select", "defName": "下拉输入框", "id": "FB111359-2B73-4443-926C-08A98E446448"}, {"defKey": "CheckBox", "defName": "复选框", "id": "0CB8A6C9-1115-4FC0-B51E-5C028065082F"}, {"defKey": "RadioBox", "defName": "单选框", "id": "5C04987A-260F-4B7C-A5D5-22A181AAE9CA"}, {"defKey": "Double", "defName": "小数输入", "id": "8D5BAFE4-E15C-4707-A047-8EE59C58E70F"}, {"defKey": "Integer", "defName": "整数输入", "id": "9999AF2A-A44E-415C-A2DC-D7C613BD0073"}, {"defKey": "Money", "defName": "金额输入", "id": "2B0C3D0C-7BAF-4B36-81AD-9362B5E5DC2E"}, {"defKey": "Date", "defName": "日期输入", "id": "E4D94E14-F695-487F-AFC2-4D888009B7DA"}, {"defKey": "DataYearMonth", "defName": "年月输入", "id": "936927E3-DD2D-4096-87FD-074CDE278D59"}, {"defKey": "Text", "defName": "长文本输入", "id": "D89DD4F1-ADAC-4469-BF8D-B3FF41AE7963"}, {"defKey": "RichText", "defName": "富文本输入", "id": "C134EB1F-4CFF-49E0-882F-2C6FB275CB20"}], "headers": [{"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": false}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": false}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "menuWidth": "298px"}, "entities": [{"id": "42333470-8C51-41F6-B9BD-08A2D6492ADD", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "dhr_excel_parser_log", "defName": "excel解析日志表", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": false}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": false}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "主键ID", "comment": "", "type": "bigint", "len": 20, "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "domain": "", "refDict": "", "uiHint": "", "id": "2C5E8AE9-25FE-4D1D-8338-E9F9A10B0259"}, {"defKey": "bs_code", "defName": "业务标识", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "id": "54D0A7C4-589F-48D5-B1BF-933A7B49B247"}, {"defKey": "file_name", "defName": "文件名", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "2B493AD8-643F-4291-8F9E-4CA8B404C179"}, {"defKey": "start_time", "defName": "开始时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "02035795-F7D6-466B-8879-9BD7D1357D73"}, {"defKey": "end_time", "defName": "结束时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "45FB2734-9625-4ECB-98C1-28587A9242A5"}, {"defKey": "parser_status", "defName": "解析状态", "comment": "1：解析中，2：解析完成，3：解析异常", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "D0795503-C860-4D3E-8F46-C5F844AE54C3"}, {"defKey": "create_by", "defName": "创建人", "comment": "", "domain": "", "type": "VARCHAR", "len": 20, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "69CE2DD9-AE46-4484-8B6E-5643EB6C02F4"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "94E86872-85F5-4FAE-A145-3E1D00A0A075"}, {"defKey": "update_by", "defName": "更新人", "comment": "", "domain": "", "type": "VARCHAR", "len": 20, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "D4A81FAD-5D04-42DA-854B-B37571C13079"}, {"defKey": "update_time", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "0B7D70A9-B7A5-489A-9248-458A437E7DE9"}, {"defKey": "remark", "defName": "备注", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "BB0D5750-37D8-43E4-85A0-9EB2C54D297C"}], "correlations": [], "indexes": []}, {"id": "AB59B98B-CD62-4F98-9151-A9B0AF7ABAA3", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "dhr_excel_parser_error", "defName": "excel解析错误表", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": false}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": false}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "主键ID", "comment": "", "type": "bigint", "len": 20, "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "domain": "", "refDict": "", "uiHint": "", "id": "2C5E8AE9-25FE-4D1D-8338-E9F9A10B0259"}, {"defKey": "parser_log_id", "defName": "解析日志ID", "comment": "", "type": "bigint", "len": 20, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "", "id": "78F64E9C-9AB7-49AF-9FA1-A05A423B377B"}, {"defKey": "error_index", "defName": "错误行号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "1B52EE3D-2C4B-42F9-9D3A-8819BF1AB400"}, {"defKey": "error_message", "defName": "错误信息", "comment": "", "type": "text", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "", "id": "04ACBDF5-E99A-43DC-AC28-9266BE1C4D9F"}, {"defKey": "error_data", "defName": "错误数据内容", "comment": "", "type": "text", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "", "id": "D3ED59F0-787D-46D6-BEF3-A21BD4BFE894"}, {"defKey": "create_by", "defName": "创建人", "comment": "", "domain": "", "type": "VARCHAR", "len": 20, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "69CE2DD9-AE46-4484-8B6E-5643EB6C02F4"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "94E86872-85F5-4FAE-A145-3E1D00A0A075"}, {"defKey": "update_by", "defName": "更新人", "comment": "", "domain": "", "type": "VARCHAR", "len": 20, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "D4A81FAD-5D04-42DA-854B-B37571C13079"}, {"defKey": "update_time", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "0B7D70A9-B7A5-489A-9248-458A437E7DE9"}, {"defKey": "remark", "defName": "备注", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "BB0D5750-37D8-43E4-85A0-9EB2C54D297C"}], "correlations": [], "indexes": [{"defKey": "", "defName": null, "unique": false, "comment": "", "fields": [{"fieldDefKey": "78F64E9C-9AB7-49AF-9FA1-A05A423B377B", "ascOrDesc": "A", "id": "00E9011A-6ED0-4FF5-8376-9BD0959BBA91"}], "id": "E45EE7A4-9EF3-46CD-91AB-C20613F262FA"}]}, {"id": "6DA01BC5-6293-485E-98E4-BD5DBAE6DCD3", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_test1", "defName": "数据测试表", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": false}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": false}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "主键ID", "comment": "", "type": "bigint", "len": 20, "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "domain": "", "refDict": "", "uiHint": "", "id": "2C5E8AE9-25FE-4D1D-8338-E9F9A10B0259"}, {"defKey": "bs_code", "defName": "业务标识", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "id": "54D0A7C4-589F-48D5-B1BF-933A7B49B247"}, {"defKey": "file_name", "defName": "文件名", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "2B493AD8-643F-4291-8F9E-4CA8B404C179"}, {"defKey": "start_time", "defName": "开始时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "02035795-F7D6-466B-8879-9BD7D1357D73"}, {"defKey": "end_time", "defName": "结束时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "45FB2734-9625-4ECB-98C1-28587A9242A5"}, {"defKey": "parser_status", "defName": "解析状态", "comment": "1：解析中，2：解析完成，3：解析异常", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "id": "D0795503-C860-4D3E-8F46-C5F844AE54C3"}, {"defKey": "create_by", "defName": "创建人", "comment": "", "domain": "", "type": "VARCHAR", "len": 20, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "69CE2DD9-AE46-4484-8B6E-5643EB6C02F4"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "94E86872-85F5-4FAE-A145-3E1D00A0A075"}, {"defKey": "update_by", "defName": "更新人", "comment": "", "domain": "", "type": "VARCHAR", "len": 20, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "D4A81FAD-5D04-42DA-854B-B37571C13079"}, {"defKey": "update_time", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "0B7D70A9-B7A5-489A-9248-458A437E7DE9"}, {"defKey": "remark", "defName": "备注", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "id": "BB0D5750-37D8-43E4-85A0-9EB2C54D297C"}], "correlations": [], "indexes": []}, {"id": "6F72FDB3-3FB3-4720-B8B6-97F39D030D17", "defKey": "dhr_excel_parser_address", "defName": "excel异步下载地址表", "comment": "", "properties": {}, "fields": [{"defKey": "id", "defName": "主键ID", "comment": "", "domain": "", "type": "BIGINT", "len": 20, "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "refDict": "", "id": "F5F47331-CCE5-4C12-A3EB-27A11B279BAB", "extProps": {}}, {"defKey": "bs_code", "defName": "业务标识", "comment": "", "domain": "16120F75-6AA7-4483-868D-F07F511BB081", "type": "VARCHAR", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "'NULL'", "hideInGraph": false, "refDict": "", "id": "1E46C171-1744-4052-92D3-5D4C048B1567", "extProps": {}}, {"defKey": "file_name", "defName": "文件名", "comment": "", "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "type": "VARCHAR", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "'NULL'", "hideInGraph": false, "refDict": "", "id": "D3A4E037-A4EC-4364-B59E-152F5AD0F472", "extProps": {}}, {"defKey": "address", "defName": "地址", "comment": "", "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "type": "VARCHAR", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "'NULL'", "hideInGraph": false, "refDict": "", "id": "E6A9A411-767C-4856-8AAD-04D7A9BD85CE", "extProps": {}}, {"defKey": "start_time", "defName": "开始时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "'NULL'", "hideInGraph": false, "refDict": "", "id": "03C2D9C8-5C66-44B9-98E3-C6DBB06E2A11", "extProps": {}}, {"defKey": "end_time", "defName": "结束时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "'NULL'", "hideInGraph": false, "refDict": "", "id": "E5581AFA-EF55-4D2C-ABAD-6934FCDBEB54", "extProps": {}}, {"defKey": "parser_status", "defName": "解析状态;1：导出中，2：导出完成，3：导出异常", "comment": "", "domain": "", "type": "INT", "len": 11, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "NULL", "hideInGraph": false, "refDict": "", "id": "BFAC4E2B-C962-4A53-BD1F-83C44E818A88", "extProps": {}}, {"defKey": "create_by", "defName": "创建人", "comment": "", "domain": "", "type": "VARCHAR", "len": 20, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "'NULL'", "hideInGraph": false, "refDict": "", "id": "5257712F-9640-496C-BE9C-797D880CB2AA", "extProps": {}}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "'NULL'", "hideInGraph": false, "refDict": "", "id": "30D858F7-2ABC-4A26-A416-75369D0CB99B", "extProps": {}}, {"defKey": "update_by", "defName": "更新人", "comment": "", "domain": "", "type": "VARCHAR", "len": 20, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "'NULL'", "hideInGraph": false, "refDict": "", "id": "B40BDEDE-159B-4434-BEF2-531DB93CA7EC", "extProps": {}}, {"defKey": "update_time", "defName": "更新时间", "comment": "", "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "'NULL'", "hideInGraph": false, "refDict": "", "id": "6E47ECD5-7405-4A8E-A677-516C00917108", "extProps": {}}, {"defKey": "remark", "defName": "备注", "comment": "", "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "type": "VARCHAR", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "'NULL'", "hideInGraph": false, "refDict": "", "id": "AE963800-3249-473E-8B4C-AC47A8A498F1", "extProps": {}}], "indexes": [], "nameTemplate": "{defKey}[{defName}]", "headers": [{"freeze": false, "refKey": "hideInGraph", "hideInGraph": true}, {"freeze": true, "refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"freeze": true, "refKey": "defName", "hideInGraph": false}, {"freeze": false, "refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"freeze": false, "refKey": "notNull", "hideInGraph": true}, {"freeze": false, "refKey": "autoIncrement", "hideInGraph": true}, {"freeze": false, "refKey": "domain", "hideInGraph": true}, {"freeze": false, "refKey": "type", "hideInGraph": false}, {"freeze": false, "refKey": "len", "hideInGraph": false}, {"freeze": false, "refKey": "scale", "hideInGraph": false}, {"freeze": false, "refKey": "comment", "hideInGraph": true}, {"freeze": false, "refKey": "refDict", "hideInGraph": true}, {"freeze": false, "refKey": "defaultValue", "hideInGraph": true}, {"freeze": false, "refKey": "isStandard", "hideInGraph": false}, {"freeze": false, "refKey": "uiHint", "hideInGraph": true}, {"freeze": false, "refKey": "extProps", "hideInGraph": true}], "correlations": []}], "views": [], "dicts": [{"defKey": "Gender", "defName": "性别", "intro": "", "items": [{"defKey": "M", "defName": "男", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "sort": "1", "id": "3622D417-DA1A-408F-BEE1-11D328D534A0"}, {"defKey": "F", "defName": "女", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "sort": "2", "id": "380A0790-64A7-481E-831C-32F7BEE1502B"}, {"defKey": "U", "defName": "未知", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "sort": "3", "id": "FA239F4D-1276-40D9-B230-F66BD35C3C27"}], "id": "BF9E20E0-80D3-486D-BD58-5FADCF3E4A1D"}, {"defKey": "Political", "defName": "政治面貌", "intro": "", "items": [{"defKey": "10", "defName": "共青团员", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "61F3145A-7599-4CCB-B298-D5EE788107BE"}, {"defKey": "20", "defName": "中共党员", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "ED16D25A-AB2F-4FA0-BB48-2B9031FA28C4"}, {"defKey": "30", "defName": "民主党派", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "0FB7974A-AE11-438F-86E0-B163316F9272"}, {"defKey": "40", "defName": "群众", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "7D247234-7E97-45B1-8C56-4A17A370854A"}, {"defKey": "90", "defName": "未知", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "DA65D752-AF04-4A11-81D8-14A38692A64A"}], "id": "06EED564-BBA9-4747-8D73-AF809A330CB8"}, {"defKey": "<PERSON><PERSON>", "defName": "婚姻状况", "intro": "婚姻状况的码表", "items": [{"defKey": "UNMARRIED", "defName": "未婚", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "sort": "1", "id": "20EE81BC-74EE-47DA-A56F-9663B23F44BD"}, {"defKey": "MARRIED", "defName": "已婚", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "sort": "2", "id": "4DCA10A8-417E-4A8D-BDF6-0A278C060ADC"}, {"defKey": "WIDOWED", "defName": "丧偶", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "sort": "3", "id": "826062A7-057C-4892-B338-06459F5B808D"}, {"defKey": "DIVORCE", "defName": "离婚", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "sort": "4", "id": "B23200B4-5E59-4F5E-A779-D981A040FA32"}, {"defKey": "UNSPECIFIED", "defName": "未说明", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "sort": "5", "id": "A7928FE2-349A-4702-9682-2EF7205E077B"}], "id": "EA1587B7-3954-437A-BFE0-FCB0453BCABA"}, {"defKey": "StudentStatus", "defName": "学生状态", "intro": "", "items": [{"defKey": "Normal", "defName": "正常", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "sort": "1", "id": "E9CA1CC9-8851-4F6B-86BA-B9CF0E44EB73"}, {"defKey": "Graduated", "defName": "毕业", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "sort": "2", "id": "DEC51D7C-99DF-430C-817D-0499862D3CCC"}, {"defKey": "Studied", "defName": "肄业", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "sort": "3", "id": "8853D6B6-75D3-4479-9006-FC731CD85B20"}, {"defKey": "Stop", "defName": "休学", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "sort": "4", "id": "C74BA8CF-1DC6-4C79-BAAC-F11EB9C6AF01"}], "id": "4642BC5F-02EE-4E17-A60C-CF22F86A0282"}, {"defKey": "GBNation", "defName": "民族", "intro": "", "items": [{"defKey": "01", "defName": "汉族", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "9224DF53-F7C0-447D-B8ED-0A39F799EE19"}, {"defKey": "02", "defName": "蒙古族", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "D57818E3-9206-45BB-AE79-27C64A4AB80F"}, {"defKey": "03", "defName": "回族", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "0A1A3CA9-6D68-4E15-8BD0-9A2FF428D804"}, {"defKey": "04", "defName": "藏族", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "7CC6B6BE-47EA-460E-ACFA-C377468DEA11"}, {"defKey": "05", "defName": "维吾尔族", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "A666D51F-D249-4FAC-B1F3-78C371836CB3"}, {"defKey": "06", "defName": "苗族", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "A0C9E1BA-D87B-4695-ADFA-287FDA32BB5A"}, {"defKey": "07", "defName": "彝族", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "149B5B9E-C1D1-4790-8CCF-0ED5F4B25172"}, {"defKey": "08", "defName": "壮族", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "F9A3E65C-BF4D-4C6B-ADB7-8C9CF0487360"}, {"defKey": "09", "defName": "布依族", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "14F17DE4-E96A-460B-98A6-F84EC8CF9885"}, {"defKey": "10", "defName": "朝鲜族", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "8A81AB18-B1BF-4797-A6E5-DEDB2C6566B0"}, {"defKey": "11", "defName": "满族", "intro": "", "parentKey": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "7D460947-FBD2-4E4D-8366-3B38DCAF09D1"}], "id": "115EDEFC-0323-410E-81AB-CCAB8879837A"}, {"defKey": "GradeLevel", "defName": "受教育程度", "sort": "", "intro": "", "items": [], "id": "9E7C9788-B805-4C7D-8531-FD1D9DC79B05"}], "viewGroups": [], "dataTypeMapping": {"referURL": "", "mappings": [{"defKey": "string", "id": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "defName": "字串", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "VARCHAR", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "VARCHAR2", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "VARCHAR", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "VARCHAR", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "VARCHAR", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "VARCHAR2", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "VARCHAR", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "VARCHAR", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "STRING", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "TEXT", "797A1496-D649-4261-89B4-544132EC3F36": "String", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "String", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "String", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "string", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "string", "B91D99E0-9B7C-416C-8737-B760957DAF09": "string"}, {"defKey": "double", "id": "1A0BDC09-0792-4174-9E8E-80BE8DF44B8E", "defName": "小数", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "DECIMAL", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "DECIMAL", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "DECIMAL", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "NUMERIC", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "DECIMAL", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "DECIMAL", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "NUMERIC", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "NUMERIC", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "DOUBLE", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "REAL", "797A1496-D649-4261-89B4-544132EC3F36": "Double", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "Double", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "Double", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "decimal", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "double", "B91D99E0-9B7C-416C-8737-B760957DAF09": "*float64"}, {"defKey": "int", "id": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "defName": "整数", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "INT", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "INT", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "INT", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "INTEGER", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "INT", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "INTEGER", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "INTEGER", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "INT4", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "INT", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "INTEGER", "797A1496-D649-4261-89B4-544132EC3F36": "Integer", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "Integer", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "Integer", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "float", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "int", "B91D99E0-9B7C-416C-8737-B760957DAF09": "*int"}, {"defKey": "date", "id": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "defName": "日期", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "DATETIME", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "DATE", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "DATETIME", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "TIMESTAMP", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "DATE", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "DATE", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "DATE", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "DATE", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "DATETIME", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "NUMERIC", "797A1496-D649-4261-89B4-544132EC3F36": "Date", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "Date", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "Date", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "DateTime", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "timestamp", "B91D99E0-9B7C-416C-8737-B760957DAF09": "*time.Time"}, {"defKey": "bytes", "id": "D516E75B-90F5-4741-B9B3-A186A263F04C", "defName": "二进制", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "BLOB", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "BLOB", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "VARBINARY", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "BYTEA", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "BLOB", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "BLOB", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "BYTEA", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "BYTEA", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "BINARY", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "NONE", "797A1496-D649-4261-89B4-544132EC3F36": "byte[]", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "byte[]", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "byte[]", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "binary", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "binary", "B91D99E0-9B7C-416C-8737-B760957DAF09": "[]byte"}, {"defKey": "largeText", "id": "B17BDED3-085F-40E1-9019-3B79CF2BF075", "defName": "大文本", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "TEXT", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "CLOB", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "TEXT", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "TEXT", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "CLOB", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "CLOB", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "TEXT", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "TEXT", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "STRING", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "TEXT", "797A1496-D649-4261-89B4-544132EC3F36": "String", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "String", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "String", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "string", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "string", "B91D99E0-9B7C-416C-8737-B760957DAF09": "string"}]}, "domains": [{"defKey": "DefaultString", "defName": "默认字串", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": 255, "scale": "", "uiHint": "", "id": "9092C4E0-1A54-4859-ABBB-5B62DBC27573"}, {"defKey": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "defName": "主键标识", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": 32, "scale": "", "uiHint": "", "id": "16120F75-6AA7-4483-868D-F07F511BB081"}, {"defKey": "Name", "defName": "名称", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": 90, "scale": "", "uiHint": "", "id": "54611CCC-CA4B-42E1-9F32-4944C85B85A6"}, {"defKey": "Int", "defName": "整数", "applyFor": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "len": "", "scale": "", "uiHint": "", "id": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E"}, {"defKey": "Double", "defName": "小数", "applyFor": "1A0BDC09-0792-4174-9E8E-80BE8DF44B8E", "len": 24, "scale": 6, "uiHint": "", "id": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4"}, {"defKey": "Money", "defName": "金额", "applyFor": "1A0BDC09-0792-4174-9E8E-80BE8DF44B8E", "len": 24, "scale": 6, "uiHint": "", "id": "C3B1681B-99F9-4818-9E80-DE1652A51D85"}, {"defKey": "DateTime", "defName": "日期时间", "applyFor": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "len": "", "scale": "", "uiHint": "", "id": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC"}, {"defKey": "YesNo", "defName": "是否", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": "1", "scale": "", "uiHint": "", "id": "6F7C1C5C-D159-41E6-BF9D-54DEEFA79AFF"}, {"defKey": "Dict", "defName": "数据字典", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": "32", "scale": "", "uiHint": "", "id": "73FD2BAD-2358-4336-B96D-45DC897BD792"}, {"defKey": "DescText", "defName": "描述文本", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": "900", "scale": "", "uiHint": "", "id": "3E948CEC-3070-472C-AF92-F3CA11EC9D15"}], "diagrams": [{"defKey": "excel_parser", "defName": "", "relationType": "field", "canvasData": {"cells": [{"id": "83f7d0ef-6a5a-40b6-ab26-d0750385ddd9", "shape": "erdRelation", "source": {"cell": "bafb71d2-bea8-4618-9174-17c5472e2f60", "port": "2C5E8AE9-25FE-4D1D-8338-E9F9A10B0259%out"}, "target": {"cell": "9643d03c-1fb8-4e81-ac08-ec38b0132893", "port": "78F64E9C-9AB7-49AF-9FA1-A05A423B377B%in"}, "relation": "1:n", "fillColor": "#ACDAFC", "router": {"name": "manhattan"}}, {"id": "9643d03c-1fb8-4e81-ac08-ec38b0132893", "shape": "table", "position": {"x": 640, "y": 150}, "count": 0, "originKey": "AB59B98B-CD62-4F98-9151-A9B0AF7ABAA3"}, {"id": "bafb71d2-bea8-4618-9174-17c5472e2f60", "shape": "table", "position": {"x": 220, "y": 190}, "count": 0, "originKey": "42333470-8C51-41F6-B9BD-08A2D6492ADD"}]}, "id": "281B895B-1C73-4C3A-A585-AFE61F1FE140"}], "standardFields": [{"defKey": "personInfo", "defName": "个人基本信息要素", "fields": [{"defKey": "ID_CARD_NO", "defName": "身份证号", "comment": "", "type": "VARCHAR", "len": "60", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "refDict": "", "uiHint": "", "id": "A64A91C8-A41F-4113-92FB-7563D7EF054D"}, {"defKey": "MOBILE_PHONE", "defName": "手机号", "comment": "", "type": "VARCHAR", "len": "60", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "refDict": "", "uiHint": "", "id": "479DA2AB-1974-411A-A81E-92FB939E75EB"}, {"defKey": "GENDER", "defName": "性别", "comment": "", "type": "VARCHAR", "len": "32", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "73FD2BAD-2358-4336-B96D-45DC897BD792", "refDict": "BF9E20E0-80D3-486D-BD58-5FADCF3E4A1D", "uiHint": "", "id": "48473E29-6594-4912-AADE-C8AB44FCA3E9"}, {"defKey": "BIRTH", "defName": "出生日期", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "uiHint": "", "id": "2BD3D2EE-2411-49A6-983D-84B81057312F"}, {"defKey": "AVATAR", "defName": "头像", "comment": "", "type": "VARCHAR", "len": "60", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "refDict": "", "uiHint": "", "id": "FDD67CEE-4B52-4BD1-A1A3-9C5EBC6037E6"}, {"defKey": "HEIGHT", "defName": "身高", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "refDict": "", "uiHint": "", "id": "CAAA0E79-41A1-4758-B481-D171168C4D68"}, {"defKey": "WEIGHT", "defName": "体重", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "refDict": "", "uiHint": "", "id": "575482CE-64A6-4CB9-99DC-8E126D190AAA"}, {"defKey": "NATION", "defName": "名族", "comment": "", "type": "VARCHAR", "len": "32", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "73FD2BAD-2358-4336-B96D-45DC897BD792", "refDict": "115EDEFC-0323-410E-81AB-CCAB8879837A", "uiHint": "", "id": "15B0D75D-0B97-4985-A816-D0EAFA90446B"}, {"defKey": "POLITICAL", "defName": "政治面貌", "comment": "", "type": "VARCHAR", "len": "32", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "73FD2BAD-2358-4336-B96D-45DC897BD792", "refDict": "06EED564-BBA9-4747-8D73-AF809A330CB8", "uiHint": "", "id": "F458E86D-84D6-45A1-9DD3-51E6C8170D7F"}, {"defKey": "MARITAL", "defName": "婚姻状况", "comment": "", "type": "VARCHAR", "len": "32", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "73FD2BAD-2358-4336-B96D-45DC897BD792", "refDict": "EA1587B7-3954-437A-BFE0-FCB0453BCABA", "uiHint": "", "id": "7275E578-6893-4922-AC69-95B261BFBD61"}, {"defKey": "DOMICILE_PLACE_PROVINCE", "defName": "籍贯（省）", "comment": "", "type": "VARCHAR", "len": "60", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "refDict": "", "uiHint": "", "id": "F04BF130-3EC1-4E02-9DED-3214CA88E352"}, {"defKey": "DOMICILE_PLACE_CITY", "defName": "籍贯（市）", "comment": "", "type": "VARCHAR", "len": "32", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "73FD2BAD-2358-4336-B96D-45DC897BD792", "refDict": "", "uiHint": "", "id": "B97F5BC2-33DE-4857-9DB8-ECFD02C9040C"}, {"defKey": "DOMICILE_PLACE_ADDRESS", "defName": "户籍地址", "comment": "", "type": "VARCHAR", "len": "60", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "refDict": "", "uiHint": "", "id": "812ADF1D-8C03-40CA-B030-E539838FB889"}], "id": "F30202B9-4B5D-4CE7-87CE-B3890C84D3F2"}], "dbConn": [{"defKey": "B7C4C720-C258-41EF-87EF-2BD0388258A0", "defName": "dhr-dev", "type": "29D1CE08-4C35-4D2D-AAA9-23D93305B52E", "properties": {"driver_class_name": "com.mysql.cj.jdbc.Driver", "url": "*********************************************************************************************************************", "password": "dhr_talent_dev321", "username": "dhr_talent_dev"}}]}