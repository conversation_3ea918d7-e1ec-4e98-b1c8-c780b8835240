package com.deloitte.dhr.excel.parser.util;

import cn.hutool.extra.spring.SpringUtil;
import com.deloitte.dhr.common.base.utils.LanguageUtil;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

import java.util.Locale;

/***
 * @Author: yinyu
 * @Date: 19/09/2022 17:06
 * @description:
 */
@Component
public class MessageUtil {

    public static String getText(String code,Object... args){
        MessageSource messageSource=SpringUtil.getBean(MessageSource.class);
        return messageSource.getMessage(code,args,"",new Locale(LanguageUtil.getLanguage()));
    }

    public static String getText(String code,Locale locale,Object... args){
        MessageSource messageSource=SpringUtil.getBean(MessageSource.class);
        return messageSource.getMessage(code,args,"",locale);
    }
}
