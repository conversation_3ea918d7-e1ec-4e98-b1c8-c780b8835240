package com.deloitte.dhr.excel.parser.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

import com.deloitte.dhr.common.SuperLogicModel;

/**
 * dhr_excel_parser_addressexcel异步下载地址表
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
@Data
@TableName("dhr_excel_parser_address")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("dhr_excel_parser_addressexcel异步下载地址表")
public class ExcelParserAddress extends SuperLogicModel<ExcelParserAddress> {

	/**业务标识*/
	@TableField(value = "bs_code")
	private String bsCode; 

	/**文件名*/
	@TableField(value = "file_name")
	private String fileName; 

	/**地址*/
	@TableField(value = "address")
	private String address; 

	/**开始时间*/
	@TableField(value = "start_time")
	private Date startTime; 

	/**结束时间*/
	@TableField(value = "end_time")
	private Date endTime; 

	/**解析状态;1：导出中，2：导出完成，3：导出异常*/
	@TableField(value = "parser_status")
	private Integer parserStatus; 

}