package com.deloitte.dhr.excel.parser.template;

import com.alibaba.excel.write.handler.WriteHandler;
import com.deloitte.dhr.excel.parser.listener.ParserReadListener;
import com.deloitte.dhr.excel.parser.pojo.entity.ExcelSheet;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * Excel 解析 Template
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
public interface ExcelParserTemplate<T> {
    /**
     * 默认处理sheet页号
     */
    Integer DEFAULT_SHEET_NO = 0;


    /**
     * 导出excel
     * @param fileName 导出文件名（带文件后缀）
     * @param sheetList ExcelSheet对象包含：sheetName sheet页名称， ExcelSheetData sheet页数据对象
     * @param writeHandlerList 写excel处理器
     * @param headerList 动态表头，传了就使用动态表头，没传就使用实体上的表头
     */
    void export(String fileName, List<ExcelSheet> sheetList, List<WriteHandler> writeHandlerList,List<List<String>> headerList);


    /**
     * 同步导入excel
     * @param bsCode 业务编码
     * @param inputStream 输入流
     * @param fileName 文件名
     * @param sheetName sheet页名称
     * @param clazz sheet页映射实体对象
     * @param readListener 解析监听器
     * @return
     */
    Boolean importExcel(String bsCode,InputStream inputStream, String fileName, String sheetName, Class<T> clazz, ParserReadListener readListener);

    /**
     * 同步导入excel
     * @param bsCode 业务编码
     * @param inputStream 输入流
     * @param fileName 文件名
     * @param sheetName sheet页名称
     * @param clazz sheet页映射实体对象
     * @param readListener 解析监听器
     * @return
     */
    Boolean importExcelSync(String bsCode,InputStream inputStream, String fileName, String sheetName, Class<T> clazz, ParserReadListener readListener);


    /**
     * 异步导入excel
     * @param bsCode 业务编码
     * @param inputStream 输入流
     * @param fileName 文件名
     * @param sheetName sheet页名称
     * @param clazz sheet页映射实体对象
     * @param readListener 解析监听器
     * @return
     */
    void importExcelAsync(String bsCode, InputStream inputStream, String fileName, String sheetName, Class<T> clazz, ParserReadListener readListener);

    /**
     * 导入excel
     * @param bsCode 业务编码
     * @param file 导入文件 MultipartFile
     * @param sheetName sheet页名称
     * @param clazz sheet页映射实体对象
     * @param readListener 解析监听器
     * @return
     * @throws IOException
     */
    default Boolean importExcel(String bsCode, MultipartFile file, String sheetName, Class<T> clazz, ParserReadListener readListener) throws IOException {
        return importExcel(bsCode,file.getInputStream(),file.getOriginalFilename(),sheetName,clazz,readListener);
    }

    /**
     * 异步导入excel
     * @param bsCode 业务编码
     * @param file 文件
     * @param sheetName sheet页名称
     * @param clazz sheet页映射实体对象
     * @param readListener 解析监听器
     * @return
     * @throws IOException
     */
    default void importExcelAsync(String bsCode, MultipartFile file, String sheetName, Class<T> clazz, ParserReadListener readListener) throws IOException {
        importExcelAsync(bsCode,file.getInputStream(),file.getOriginalFilename(),sheetName,clazz,readListener);
    }

}