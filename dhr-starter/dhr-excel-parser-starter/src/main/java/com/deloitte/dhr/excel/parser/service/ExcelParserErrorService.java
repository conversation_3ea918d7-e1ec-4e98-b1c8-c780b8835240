package com.deloitte.dhr.excel.parser.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperService;
import com.deloitte.dhr.excel.parser.dao.domain.ExcelParserError;

/**
 * ExcelParserErrorService
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
public interface ExcelParserErrorService extends SuperService<ExcelParserError> {
    /**
     * 保存行错误
     * @param index 行号
     * @param message 错误信息
     * @param data 错误数据
     * @return
     */
    boolean saveRowError(Integer index, String message, String data);

    /**
     * 分页查询
     * @param pageReq
     * @param entity
     * @param order
     * @return
     */
    ResponsePage findPageList(Page pageReq, Object entity, BaseOrder order);
}
