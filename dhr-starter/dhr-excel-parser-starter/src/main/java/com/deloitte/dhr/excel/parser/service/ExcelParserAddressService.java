package com.deloitte.dhr.excel.parser.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.excel.parser.dao.domain.ExcelParserAddress;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperService;
import com.deloitte.dhr.excel.parser.pojo.ExcelParserAddressRequest;
import com.deloitte.dhr.excel.parser.pojo.ExcelParserAddressResponse;

/**
 * dhr_excel_parser_addressexcel异步下载地址表
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
public interface ExcelParserAddressService extends SuperService<ExcelParserAddress> {

    /**
     * 分页查询数据
     * @param current
     * @param size
     * @param bsCdoe
     * @return
     */
    IPage<ExcelParserAddressResponse> findListPage(Long current, Long size, String bsCdoe);
    
    /**
     * 开始导出
     *
     * @param bsCode
     * @return
     */
    Boolean startExport(String bsCode);

    /**
     * 结束导出
     * @param fileName
     * @param adress
     * @param status
     * @return
     */
    Boolean endExport(String fileName,String adress,Integer status);
}
