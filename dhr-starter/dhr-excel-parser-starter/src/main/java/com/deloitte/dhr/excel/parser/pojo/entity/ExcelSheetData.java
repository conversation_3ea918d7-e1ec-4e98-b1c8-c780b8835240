package com.deloitte.dhr.excel.parser.pojo.entity;

import com.alibaba.excel.write.handler.WriteHandler;
import lombok.Data;

import java.util.List;

/**
 * Excel Sheet页数据
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
@Data
public class ExcelSheetData<T> {

    /**
     * 表头
     */
    private Class<T> clazz;

    /**
     * 数据
     */
    private List<T> data;

    /**
     * 处理器
     */
    private List<WriteHandler> writeHandlerList;

    public ExcelSheetData(Class<T> clazz, List<T> data) {
        this.clazz = clazz;
        this.data = data;
    }

    public ExcelSheetData(Class<T> clazz, List<T> data, List<WriteHandler> writeHandlerList) {
        this.clazz = clazz;
        this.data = data;
        this.writeHandlerList = writeHandlerList;
    }
}
