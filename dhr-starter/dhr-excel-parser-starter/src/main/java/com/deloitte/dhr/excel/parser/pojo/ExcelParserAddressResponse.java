package com.deloitte.dhr.excel.parser.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import java.util.Date;

/**
 * dhr_excel_parser_addressexcel异步下载地址表
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
@Data
@ApiModel("ExcelParserAddressResponse")
public class ExcelParserAddressResponse {

	/**主键ID*/
    @ApiModelProperty(value = "主键ID", name = "id")
	private Long id; 
  	
	/**业务标识*/
    @ApiModelProperty(value = "业务标识", name = "bs_code")
	private String bsCode; 
  	
	/**文件名*/
    @ApiModelProperty(value = "文件名", name = "file_name")
	private String fileName; 
  	
	/**地址*/
    @ApiModelProperty(value = "地址", name = "address")
	private String address; 
  	
	/**开始时间*/
    @ApiModelProperty(value = "开始时间", name = "start_time")
	private Date startTime; 
  	
	/**结束时间*/
    @ApiModelProperty(value = "结束时间", name = "end_time")
	private Date endTime; 
  	
	/**解析状态;1：导出中，2：导出完成，3：导出异常*/
    @ApiModelProperty(value = "解析状态;1：导出中，2：导出完成，3：导出异常", name = "parser_status")
	private Integer parserStatus; 
  	
}