package com.deloitte.dhr.excel.parser.pojo;

import com.deloitte.dhr.common.validation.Insert;
import com.deloitte.dhr.common.validation.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotNull;

/**
 * Comment：dhr_excel_parser_errorexcel解析错误表
 * <AUTHOR>
 * @date 2022-09-21
 */
@Data
@ApiModel("DhrExcelParserErrorRequest")
public class DhrExcelParserErrorRequest  {


	/**主键ID*/
    
         @NotNull(groups = {Update.class},message = "id不能为空")
                @ApiModelProperty( value = "主键ID", name = "id") 
	private Long id;
    	

	/**解析日志ID*/
    
            @NotNull(groups = {Insert.class, Update.class},message = "parserLogId不能为空")
            @ApiModelProperty( value = "解析日志ID", name = "parser_log_id") 
	private Long parserLogId;
    	

	/**错误行号*/
    
            @NotNull(groups = {Insert.class, Update.class},message = "errorIndex不能为空")
            @ApiModelProperty( value = "错误行号", name = "error_index") 
	private Integer errorIndex;
    	

	/**错误信息*/
    
            @NotNull(groups = {Insert.class, Update.class},message = "errorMessage不能为空")
            @Length(max = 5,message = "errorMessage不能超过 0 个字符")
        @ApiModelProperty( value = "错误信息", name = "error_message") 
	private String errorMessage;
    	

	/**错误数据内容*/
    
            @NotNull(groups = {Insert.class, Update.class},message = "errorData不能为空")
            @Length(max = 5,message = "errorData不能超过 0 个字符")
        @ApiModelProperty( value = "错误数据内容", name = "error_data") 
	private String errorData;
    	





}