package com.deloitte.dhr.excel.parser.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperService;
import com.deloitte.dhr.excel.parser.dao.domain.ExcelParserLog;

/**
 * ExcelParserLogService
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
public interface ExcelParserLogService extends SuperService<ExcelParserLog> {

    /**
     * 开始导入日志
     * @param bsCode 业务编码
     * @param fileName 文件名
     * @return
     */
    boolean startParser(String bsCode, String fileName);

    /**
     * 解析结束日志
     * @param status 状态
     * @return
     */
    boolean endParser(Integer status);

    /**
     * 分页查询
     * @param pageReq
     * @param entity
     * @param order
     * @return
     */
    ResponsePage findPageList(Page pageReq, Object entity, BaseOrder order);
}
