package com.deloitte.dhr.excel.parser.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * Comment：dhr_excel_parser_errorexcel解析错误表
 * <AUTHOR>
 * @date 2022-09-21
 */

@Data
@ApiModel("DhrExcelParserErrorResponse")
public class DhrExcelParserErrorResponse {

	/**主键ID*/
    @ApiModelProperty( value = "主键ID", name = "id") 
	private Long id;
    	
	/**解析日志ID*/
    @ApiModelProperty( value = "解析日志ID", name = "parser_log_id") 
	private Long parserLogId;
    	
	/**错误行号*/
    @ApiModelProperty( value = "错误行号", name = "error_index") 
	private Integer errorIndex;
    	
	/**错误信息*/
    @ApiModelProperty( value = "错误信息", name = "error_message") 
	private String errorMessage;
    	
	/**错误数据内容*/
    @ApiModelProperty( value = "错误数据内容", name = "error_data") 
	private String errorData;
    	
}