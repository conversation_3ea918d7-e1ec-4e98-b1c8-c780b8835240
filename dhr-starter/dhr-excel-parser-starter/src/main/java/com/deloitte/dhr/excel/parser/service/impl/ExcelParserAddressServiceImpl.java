package com.deloitte.dhr.excel.parser.service.impl;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.excel.parser.constant.ParserStatusConstant;
import com.deloitte.dhr.excel.parser.dao.domain.ExcelParserAddress;
import com.deloitte.dhr.excel.parser.dao.mapper.ExcelParserAddressMapper;
import com.deloitte.dhr.excel.parser.pojo.ExcelParserAddressResponse;
import com.deloitte.dhr.excel.parser.service.ExcelParserAddressService;
import com.deloitte.dhr.excel.parser.util.CurrentAdressUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import com.deloitte.dhr.common.SuperServiceImpl;

/**
 * dhr_excel_parser_addressexcel异步下载地址表
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
@Service
public class ExcelParserAddressServiceImpl extends SuperServiceImpl<ExcelParserAddressMapper, ExcelParserAddress> implements ExcelParserAddressService {

    @Autowired
    ExcelParserAddressMapper excelParserAddressMapper;

    @Override
    public IPage<ExcelParserAddressResponse> findListPage(Long current, Long size, String bsCdoe) {
        LambdaQueryWrapper<ExcelParserAddress> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(ExcelParserAddress::getBsCode,bsCdoe);
        queryWrapper.orderByDesc(ExcelParserAddress::getStartTime);
        IPage responseIPage=excelParserAddressMapper.selectPage(new Page(current,size),queryWrapper);
        List<ExcelParserAddressResponse> resps= BeanUtil.copyToList(responseIPage.getRecords(),ExcelParserAddressResponse.class);
        responseIPage.setRecords(resps);
        return responseIPage;
    }

    @Override
    public Boolean startExport(String bsCode) {
        ExcelParserAddress excelParserAddress = new ExcelParserAddress();
        excelParserAddress.setBsCode(bsCode);
        excelParserAddress.setParserStatus(ParserStatusConstant.PARSING);
        excelParserAddress.setStartTime(new Date());
        int rows = excelParserAddressMapper.insert(excelParserAddress);
        CurrentAdressUtil.set(excelParserAddress);
        return rows>0;
    }

    @Override
    public Boolean endExport(String fileName,String adress,Integer status) {
        ExcelParserAddress excelParserAddress = CurrentAdressUtil.get();
        excelParserAddress.setFileName(fileName);
        excelParserAddress.setAddress(adress);
        excelParserAddress.setParserStatus(status);
        excelParserAddress.setEndTime(new Date());
        int rows = excelParserAddressMapper.updateById(excelParserAddress);
        CurrentAdressUtil.remove();
        return rows>0;
    }

}

