package com.deloitte.dhr.excel.parser.pojo;

import com.deloitte.dhr.common.validation.Insert;
import com.deloitte.dhr.common.validation.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * dhr_excel_parser_addressexcel异步下载地址表
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
@Data
@ApiModel("ExcelParserAddressRequest")
public class ExcelParserAddressRequest {

	/**主键ID*/
    @NotNull(groups = {Update.class}, message = "id不能为空")
    @ApiModelProperty(value = "主键ID", name = "id") 
	private Long id; 

	/**业务标识*/
    @NotNull(groups = {Insert.class, Update.class}, message = "bsCode不能为空")
    @Length(max = 32, message = "bsCode不能超过 32 个字符")
    @ApiModelProperty(value = "业务标识", name = "bs_code") 
	private String bsCode; 

	/**文件名*/
    @NotNull(groups = {Insert.class, Update.class}, message = "fileName不能为空")
    @Length(max = 255, message = "fileName不能超过 255 个字符")
    @ApiModelProperty(value = "文件名", name = "file_name") 
	private String fileName; 

	/**地址*/
    @NotNull(groups = {Insert.class, Update.class}, message = "address不能为空")
    @Length(max = 255, message = "address不能超过 255 个字符")
    @ApiModelProperty(value = "地址", name = "address") 
	private String address; 

	/**开始时间*/
    @NotNull(groups = {Insert.class, Update.class}, message = "startTime不能为空")
    @ApiModelProperty(value = "开始时间", name = "start_time") 
	private Date startTime; 

	/**结束时间*/
    @NotNull(groups = {Insert.class, Update.class}, message = "endTime不能为空")
    @ApiModelProperty(value = "结束时间", name = "end_time") 
	private Date endTime; 

	/**解析状态;1：导出中，2：导出完成，3：导出异常*/
    @NotNull(groups = {Insert.class, Update.class}, message = "parserStatus不能为空")
    @ApiModelProperty(value = "解析状态;1：导出中，2：导出完成，3：导出异常", name = "parser_status") 
	private Integer parserStatus; 

}