package com.deloitte.dhr.excel.parser.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.excel.parser.dao.domain.ExcelParserError;
import com.deloitte.dhr.excel.parser.dao.domain.ExcelParserLog;
import com.deloitte.dhr.excel.parser.dao.mapper.ExcelParserErrorMapper;
import com.deloitte.dhr.excel.parser.dao.mapper.ExcelParserLogMapper;
import com.deloitte.dhr.excel.parser.service.ExcelParserErrorService;
import com.deloitte.dhr.excel.parser.service.ExcelParserLogService;
import com.deloitte.dhr.excel.parser.util.CurrentLogUtil;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * ExcelParserErrorServiceImpl
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
@Service
public class ExcelParserErrorServiceImpl extends SuperServiceImpl<ExcelParserErrorMapper, ExcelParserError> implements ExcelParserErrorService {

    @Resource
    private ExcelParserErrorMapper excelParserErrorMapper;


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public boolean saveRowError(Integer index, String message, String data) {
        ExcelParserLog excelParserLog = CurrentLogUtil.get();
        ExcelParserError excelParserError = new ExcelParserError();
        excelParserError.setParserLogId(excelParserLog.getId());
        excelParserError.setErrorData(data);
        excelParserError.setErrorMessage(message);
        excelParserError.setErrorIndex(index);
        int rows = excelParserErrorMapper.insert(excelParserError);
        return rows > 0;
    }

    @Override
    public ResponsePage findPageList(Page pageReq, Object entity, BaseOrder order) {
        return findListResp(pageReq,entity,order);
    }
}
