package com.deloitte.dhr.excel.parser.util;

import com.deloitte.dhr.excel.parser.dao.domain.ExcelParserLog;

/**
 * 解析当前文件名信息
 *
 * <AUTHOR>
 * 2022/9/08
 */
public class CurrentFileNameUtil {

     private static final ThreadLocal<String> logThreadLocal = new ThreadLocal<>();

     /**
      * 获取当前解析文件名
      * @return
      */
     public static String get(){
          return logThreadLocal.get();
     }

     /**
      * 设置当前解析文件名
      * @param fileName
      */
     public static void set(String fileName){
          logThreadLocal.set(fileName);
     }

     /**
      * 移除当前解析文件名
      */
     public static void remove(){
          logThreadLocal.remove();
     }

}
