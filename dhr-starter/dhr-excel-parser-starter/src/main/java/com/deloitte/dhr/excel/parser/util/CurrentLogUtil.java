package com.deloitte.dhr.excel.parser.util;

import com.deloitte.dhr.excel.parser.dao.domain.ExcelParserLog;

/**
 * 解析日志当前上下文
 *
 * <AUTHOR>
 * 2022/9/08
 */
public class CurrentLogUtil {

     private static final ThreadLocal<ExcelParserLog> logThreadLocal = new ThreadLocal<>();

     /**
      * 获取当前日志解析对象
      * @return
      */
     public static ExcelParserLog get(){
          return logThreadLocal.get();
     }

     /**
      * 设置当前日志解析对象
      * @param excelParserLog
      */
     public static void set(ExcelParserLog excelParserLog){
          logThreadLocal.set(excelParserLog);
     }

     /**
      * 移除当前日志解析对象
      */
     public static void remove(){
          logThreadLocal.remove();
     }

}
