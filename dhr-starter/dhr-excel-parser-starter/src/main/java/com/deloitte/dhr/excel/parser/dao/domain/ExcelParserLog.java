package com.deloitte.dhr.excel.parser.dao.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * ExcelParserLog excel解析日志表
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
@Data
@TableName("dhr_excel_parser_log")
@AllArgsConstructor
@NoArgsConstructor
public class ExcelParserLog extends SuperModel<ExcelParserLog> {

	/**
	 * 业务标识
	 */
	@TableField(value = "bs_code")
	private String bsCode;


	/**
	 * 文件名
	 */
	@TableField(value = "file_name")
	private String fileName;

	/**
	 * 开始时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@TableField(value = "start_time")
	protected Date startTime;

	/**
	 * 结束时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@TableField(value = "end_time")
	protected Date endTime;

	/**
	 * 解析状态（1：解析中，2：解析完成，3：解析异常）
	 */
	@TableField(value = "parser_status")
	private Integer parserStatus;

}