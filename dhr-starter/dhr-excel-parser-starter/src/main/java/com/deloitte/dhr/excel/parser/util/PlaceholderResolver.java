package com.deloitte.dhr.excel.parser.util;

import java.util.function.Function;

/***
 * @Author: yinyu
 * @Date: 26/09/2022 17:01
 * @description:
 */
public class PlaceholderResolver {

    /**
     * 默认前缀占位符
     */
    public static final String DEFAULT_PLACEHOLDER_PREFIX = "${";

    /**
     * 默认后缀占位符
     */
    public static final String DEFAULT_PLACEHOLDER_SUFFIX = "}";


    /**
     * 根据替换规则来替换指定模板中的占位符值
     *
     * @param content 要解析的字符串
     * @param rule    解析规则回调
     * @return
     */
    public static String resolveByRule(String content, Function<String, String> rule) {
        int start = content.indexOf(DEFAULT_PLACEHOLDER_PREFIX);
        if (start == -1) {
            return content;
        }
        StringBuilder result = new StringBuilder(content);
        while (start != -1) {
            int end = result.indexOf(DEFAULT_PLACEHOLDER_SUFFIX, start);
            //获取占位符属性值，如${id}, 即获取id
            String placeholder = result.substring(start + DEFAULT_PLACEHOLDER_PREFIX.length(), end);
            //替换整个占位符内容，即将${id}值替换为替换规则回调中的内容
            String replaceContent = placeholder.trim().isEmpty() ? "" : rule.apply(placeholder);
            result.replace(start, end + DEFAULT_PLACEHOLDER_SUFFIX.length(), replaceContent);
            start = result.indexOf(DEFAULT_PLACEHOLDER_PREFIX, start + replaceContent.length());
        }
        return result.toString();
    }

}
