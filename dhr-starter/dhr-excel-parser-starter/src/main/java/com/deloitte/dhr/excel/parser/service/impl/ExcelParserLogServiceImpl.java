package com.deloitte.dhr.excel.parser.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.excel.parser.constant.ParserStatusConstant;
import com.deloitte.dhr.excel.parser.dao.domain.ExcelParserLog;
import com.deloitte.dhr.excel.parser.dao.mapper.ExcelParserLogMapper;
import com.deloitte.dhr.excel.parser.service.ExcelParserLogService;
import com.deloitte.dhr.excel.parser.util.CurrentLogUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ExcelParserLogServiceImpl
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
@Service
public class ExcelParserLogServiceImpl extends SuperServiceImpl<ExcelParserLogMapper, ExcelParserLog> implements ExcelParserLogService {

    @Resource
    private ExcelParserLogMapper excelParserLogMapper;


    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean startParser(String bsCode, String fileName) {
        ExcelParserLog excelParserLog = new ExcelParserLog();
        excelParserLog.setFileName(fileName);
        excelParserLog.setParserStatus(ParserStatusConstant.PARSING);
        excelParserLog.setStartTime(new Date());
        excelParserLog.setBsCode(bsCode);
        int rows = excelParserLogMapper.insert(excelParserLog);
        CurrentLogUtil.set(excelParserLog);
        return rows>0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean endParser(Integer status) {
        ExcelParserLog excelParserLog = CurrentLogUtil.get();
        excelParserLog.setParserStatus(status);
        excelParserLog.setEndTime(new Date());
        int rows = excelParserLogMapper.updateById(excelParserLog);
        CurrentLogUtil.remove();
        return rows>0;
    }

    @Override
    public ResponsePage findPageList(Page pageReq, Object entity, BaseOrder order) {
        return findListResp(pageReq,entity,order);
    }
}
