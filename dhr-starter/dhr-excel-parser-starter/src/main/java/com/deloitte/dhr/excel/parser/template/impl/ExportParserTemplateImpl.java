package com.deloitte.dhr.excel.parser.template.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.common.base.utils.LanguageUtil;
import com.deloitte.dhr.excel.parser.constant.ParserStatusConstant;
import com.deloitte.dhr.excel.parser.listener.ParserReadListener;
import com.deloitte.dhr.excel.parser.pojo.entity.ExcelSheet;
import com.deloitte.dhr.excel.parser.pojo.entity.ExcelSheetData;
import com.deloitte.dhr.excel.parser.service.ExcelParserErrorService;
import com.deloitte.dhr.excel.parser.service.ExcelParserLogService;
import com.deloitte.dhr.excel.parser.template.ExcelParserTemplate;
import com.deloitte.dhr.excel.parser.util.CurrentFileNameUtil;
import com.deloitte.dhr.excel.parser.util.CurrentLogUtil;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.concurrent.*;

/**
 * 系统导出模板表Service接口实现
 *
 * <AUTHOR>
 * @date 2020-04-20
 */
@Component
public class ExportParserTemplateImpl implements ExcelParserTemplate<T> {
    
    private static final Logger logger = LoggerFactory.getLogger(ExportParserTemplateImpl.class);

    @Resource
    private ExcelParserLogService excelParserLogService;

    @Resource
    private ExcelParserErrorService excelParserErrorService;

    private final ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat("excel-parser-pool-%d").build();

    /**
     * 导入异步执行线程池
     */
    private ExecutorService cachedThreadPool = new ThreadPoolExecutor(10, 50, 300, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), namedThreadFactory);

    /**
     * 自定义线程池
     * @param cachedThreadPool
     */
    public void setCachedThreadPool(ExecutorService cachedThreadPool) {
        this.cachedThreadPool = cachedThreadPool;
    }

    @Override
    public void export(String fileName, List<ExcelSheet> sheetList, List<WriteHandler> writeHandlerList,List<List<String>> headerList) {
        OutputStream responseOutPutSteam = null;
        try {
            // 获取请求响应输出流
            responseOutPutSteam = this.getResponseOutPutSteam(fileName);
            // 指定写入到Web
            ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(responseOutPutSteam);
            // 注册写入处理器
            for (WriteHandler writeHandler : writeHandlerList) {
                excelWriterBuilder.registerWriteHandler(writeHandler);
            }

            excelWriterBuilder.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy());
            ExcelWriter excelWriter = excelWriterBuilder.build();
            // 填充表头、数据
            for (ExcelSheet excelSheet : sheetList) {
                ExcelSheetData excelSheetData = excelSheet.getExcelSheetData();
                WriteSheet writeSheet = CollUtil.isEmpty(headerList)?
                        EasyExcel.writerSheet(excelSheet.getSheetName()).head(excelSheetData.getClazz()).build():
                        EasyExcel.writerSheet(excelSheet.getSheetName()).head(headerList).build();
                writeSheet.setCustomWriteHandlerList(excelSheetData.getWriteHandlerList());
                excelWriter.write(excelSheetData.getData(), writeSheet);
            }
            excelWriter.finish();
        } catch (Exception e) {
            logger.error("{}导出异常,原因:{}",fileName,e.getMessage());
            throw new RuntimeException("模板导出失败，<br/>原因:<br/>" + e.getMessage(), e);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean importExcel(String bsCode, InputStream inputStream, String fileName, String sheetName, Class<T> clazz,
                               ParserReadListener readListener) {
        // 记录开始导入日志
        excelParserLogService.startParser(bsCode,fileName);
        // 解析excel
        ExcelReader excelReader = null;
        try {
            CurrentFileNameUtil.set(fileName);
            excelReader = EasyExcel.read(inputStream,clazz, readListener).build();
            ReadSheet readSheet = EasyExcel.readSheet(sheetName).build();
            excelReader.read(readSheet);
        } catch (Exception e) {
            // 记录异常日志
            if (e instanceof ExcelDataConvertException) {
                excelParserErrorService.saveRowError(((ExcelDataConvertException) e).getRowIndex(), "数据类型转换异常", JSONObject.toJSONString(((ExcelDataConvertException) e).getCellData()));
            } else {
                excelParserErrorService.saveRowError(0, e.getMessage(), null);
            }
            excelParserLogService.endParser(ParserStatusConstant.PARSE_EXCEPTION);
            throw e;
        } finally {
            //状态还是解析中的话，判断为没读取到指定sheet名称的数据
            if (CurrentLogUtil.get()!=null&&CurrentLogUtil.get().getParserStatus()==1){
                if ("en".equals(LanguageUtil.getLanguage())){
                    excelParserErrorService.saveRowError(0,"No sheet read",null);
                }else {
                    excelParserErrorService.saveRowError(0,"未读取到sheet",null);
                }
                excelParserLogService.endParser(ParserStatusConstant.PARSE_EXCEPTION);
            }
            excelReader.finish();
            CurrentFileNameUtil.remove();
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean importExcelSync(String bsCode, InputStream inputStream, String fileName, String sheetName, Class<T> clazz, ParserReadListener readListener) {
        // 解析excel
        ExcelReader excelReader = null;
        try {
            CurrentFileNameUtil.set(fileName);
            excelReader = EasyExcel.read(inputStream,clazz, readListener).build();
            ReadSheet readSheet = EasyExcel.readSheet(sheetName).build();
            excelReader.read(readSheet);
        }catch (Exception e){
            throw e;
        }finally {
            excelReader.finish();
            CurrentFileNameUtil.remove();
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcelAsync(String bsCode, InputStream inputStream, String fileName, String sheetName, Class<T> clazz,
                               ParserReadListener readListener) {
        cachedThreadPool.execute(new Runnable() {
            @Override
            public void run() {
                getExcelParserTemplate().importExcel(bsCode,inputStream,fileName,sheetName,clazz,readListener);
            }
        });
    }

    /**
     * 从spring ioc容器获取本bean
     * 解决事物失效问题
     * @return
     */
    private ExcelParserTemplate getExcelParserTemplate() {
        return SpringUtil.getBean(this.getClass());
    }


    /**
     * 获取请求响应输出流
     * @param fileName 文件名
     * @return
     */
    private OutputStream getResponseOutPutSteam(String fileName) {
        try {
            ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletResponse response = requestAttributes.getResponse();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=" + fileName);
            return response.getOutputStream();
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return null;
    }

}