package com.deloitte.dhr.excel.parser.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.deloitte.dhr.excel.parser.constant.ParserStatusConstant;
import com.deloitte.dhr.excel.parser.dao.domain.ExcelParserError;
import com.deloitte.dhr.excel.parser.dao.domain.ExcelParserLog;
import com.deloitte.dhr.excel.parser.dao.mapper.ExcelParserErrorMapper;
import com.deloitte.dhr.excel.parser.dao.mapper.ExcelParserLogMapper;
import com.deloitte.dhr.excel.parser.service.ExcelParserErrorService;
import com.deloitte.dhr.excel.parser.service.ExcelParserLogService;
import com.deloitte.dhr.excel.parser.util.CurrentLogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * ExcelParser默认解析读监听器
 *
 * <AUTHOR>
 * 2022/9/08
 */
public abstract class ParserReadListener<T> implements ReadListener<T> {
    /**
     * Validator 校验器
     */
    protected static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    private static final Logger logger = LoggerFactory.getLogger(ParserReadListener.class);

    private ExcelParserLogService excelParserLogService = SpringUtil.getBean("excelParserLogServiceImpl");

    private ExcelParserErrorService excelParserErrorService = SpringUtil.getBean("excelParserErrorServiceImpl");

    /**
     * 默认：每隔100条存储数据库
     */
    protected static final int BATCH_COUNT = 100;

    /**
     * 数据缓存列表
     */
    private List<T> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    /**
     * 是否解析异常
     */
    protected boolean IS_ERROR = false;

    private boolean IS_START = false;

    /**
     * 日志ID
     */
    private Long logId;

    @Override
    public void invoke(T data, AnalysisContext context) {
        if (!IS_START) {
            IS_START = true;
        }

        // 数据基础校验
        baseValidate(data,context);
        // 处理每行数据
        handleRowData(data, context);
        if (data != null) {
            cachedDataList.add(data);
            if (cachedDataList.size() >= BATCH_COUNT) {
                // 按批次保存数据
                saveData(cachedDataList);
                // 保存后清空数据缓存列表
                cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        }
    }

    /**
     * 获取行号
     * @param context
     * @return
     */
    public Integer getRowIndex(AnalysisContext context) {
        return context.readRowHolder().getRowIndex()+1;
    }

    /**
     * 解析结束
     * @param status
     */
    protected void endParser(Integer status) {
        excelParserLogService.endParser(status);
    }

    /**
     * 数据基础校验
     * @param data
     */
    protected void baseValidate(T data, AnalysisContext context) {
        Set<ConstraintViolation<T>> validateSet = validator.validate(data);
        if (validateSet.size() > 0) {
            // 设置异常标识
            setErrorFlag();
            StringBuffer messageBuffer = new StringBuffer();
            validateSet.forEach(item->{
                messageBuffer.append(item.getMessage()+"；");
            });
            // 保存基础校验
            Integer rowIndex = getRowIndex(context);
            saveRowError(rowIndex,messageBuffer.toString(),data);
        }

    }

    /**
     * 保存数据行错误日志
     *
     * @param index 行号
     * @param message 错误信息
     * @param data 错误具体数据
     * @return
     */
    protected boolean saveRowError(Integer index, String message, T data) {
        // 设置异常标识
        setErrorFlag();
        return excelParserErrorService.saveRowError(index,message,JSONUtil.toJsonStr(data));
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (IS_START){
            // 结束后保存最后一批数据
            if (CollUtil.isNotEmpty(cachedDataList)){
                saveData(cachedDataList);
            }
        }
        // 解析结束
        if (IS_ERROR) {
            endParser(ParserStatusConstant.PARSE_EXCEPTION);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        } else {
            endParser(ParserStatusConstant.PARSE_COMPLETE);
        }

    }

    /**
     * 处理每行数据
     * @param data 数据
     * @param context 解析上下文对象
     * @return
     */
    protected abstract void handleRowData(T data, AnalysisContext context);

    /**
     * 持久化数据
     * @param cachedDataList 数据缓存列表
     */
    @Transactional(rollbackFor = Exception.class)
    protected abstract void saveData(List<T> cachedDataList);

    /**
     * 设置异常标识
     * @return
     */
    protected boolean setErrorFlag() {
        // 设置异常标识
        if (!IS_ERROR) {
            IS_ERROR = true;
        }
        return true;
    }


}

