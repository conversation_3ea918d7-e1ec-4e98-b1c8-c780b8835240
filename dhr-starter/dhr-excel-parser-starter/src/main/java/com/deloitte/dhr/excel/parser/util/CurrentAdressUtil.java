package com.deloitte.dhr.excel.parser.util;

import com.deloitte.dhr.excel.parser.dao.domain.ExcelParserAddress;

/***
 * @Author: yinyu
 * @Date: 30/11/2022 17:45
 * @description:
 */
public class CurrentAdressUtil {

    private static final ThreadLocal<ExcelParserAddress> logThreadLocal = new ThreadLocal<>();

    /**
     * 获取当前地址解析对象
     * @return
     */
    public static ExcelParserAddress get(){
        return logThreadLocal.get();
    }

    /**
     * 设置当前地址解析对象
     * @param excelParserLog
     */
    public static void set(ExcelParserAddress excelParserLog){
        logThreadLocal.set(excelParserLog);
    }

    /**
     * 移除当前地址解析对象
     */
    public static void remove(){
        logThreadLocal.remove();
    }
}
