package com.deloitte.dhr.excel.parser.listener;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.deloitte.dhr.common.base.exception.CommRunException;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.List;
import java.util.Set;

/***
 * @Author: yinyu
 * @Date: 30/11/2022 15:43
 * @description:ExcelParser默认解析读监听器(同步导入)
 */
public abstract class SyncParserReadListener<T> implements ReadListener<T> {

    /**
     * Validator 校验器
     */
    protected static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 默认：每隔100条存储数据库
     */
    protected static final int BATCH_COUNT = 100;

    /**
     * 数据缓存列表
     */
    private List<T> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private boolean IS_START = false;

    @Override
    public void invoke(T data, AnalysisContext context) {
        if (!IS_START) {
            IS_START = true;
        }

        // 数据基础校验
        baseValidate(data,context);
        // 处理每行数据
        handleRowData(data, context);
        if (data != null) {
            cachedDataList.add(data);
            if (cachedDataList.size() >= BATCH_COUNT) {
                // 按批次保存数据
                saveData(cachedDataList);
                // 保存后清空数据缓存列表
                cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        }
    }

    /**
     * 数据基础校验
     * @param data
     */
    protected void baseValidate(T data, AnalysisContext context) {
        Set<ConstraintViolation<T>> validateSet = validator.validate(data);
        if (validateSet.size() > 0) {
            StringBuffer messageBuffer = new StringBuffer();
            validateSet.forEach(item->{
                messageBuffer.append(item.getMessage()+"；");
            });
            throw new CommRunException(messageBuffer.toString());
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (IS_START){
            // 结束后保存最后一批数据
            if (CollUtil.isNotEmpty(cachedDataList)){
                saveData(cachedDataList);
            }
        }else {
            throw new CommRunException("文件内容为空!");
        }
    }

    /**
     * 处理每行数据
     * @param data 数据
     * @param context 解析上下文对象
     * @return
     */
    protected abstract void handleRowData(T data, AnalysisContext context);

    /**
     * 持久化数据
     * @param cachedDataList 数据缓存列表
     */
    @Transactional(rollbackFor = Exception.class)
    protected abstract void saveData(List<T> cachedDataList);

}
