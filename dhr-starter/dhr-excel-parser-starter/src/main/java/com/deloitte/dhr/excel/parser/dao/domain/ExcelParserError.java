package com.deloitte.dhr.excel.parser.dao.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deloitte.dhr.common.SuperModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ExcelParserError excel解析错误表
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
@Data
@TableName("dhr_excel_parser_error")
@AllArgsConstructor
@NoArgsConstructor
public class ExcelParserError extends SuperModel<ExcelParserError> {

	/**
	 * 解析日志ID
	 */
	@TableField(value = "parser_log_id")
	private Long parserLogId;


	/**
	 * 错误行号
	 */
	@TableField(value = "error_index")
	private Integer errorIndex;

	/**
	 * 错误信息
	 */
	@TableField(value = "error_message")
	private String errorMessage;

	/**
	 * 错误数据内容
	 */
	@TableField(value = "error_data")
	private String errorData;

}