package com.deloitte.dhr.excel.parser.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.deloitte.dhr.excel.parser.util.MessageUtil;
import com.deloitte.dhr.excel.parser.util.PlaceholderResolver;
import org.apache.poi.ss.usermodel.Row;

import java.util.List;
import java.util.stream.Collectors;

/***
 * @Author: yinyu
 * @Date: 26/09/2022 17:11
 * @description:
 */
public class I18nCellWriteHandler implements CellWriteHandler {

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        if (isHead) {
            List<String> originHeadNames = head.getHeadNameList();
            if (CollUtil.isNotEmpty(originHeadNames)) {
                List<String> newHeadNames = originHeadNames.stream().
                        map(headName ->
                                PlaceholderResolver.resolveByRule(headName,
                                        (name) -> MessageUtil.getText(name, null))).
                        collect(Collectors.toList());
                head.setHeadNameList(newHeadNames);
            }
        }
    }
}
