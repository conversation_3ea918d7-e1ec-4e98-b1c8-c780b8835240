package com.deloitte.dhr.excel.parser.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.metadata.holder.ReadHolder;
import com.alibaba.excel.read.metadata.property.ExcelReadHeadProperty;
import com.alibaba.excel.util.ConverterUtils;
import com.deloitte.dhr.excel.parser.util.MessageUtil;
import com.deloitte.dhr.excel.parser.util.PlaceholderResolver;
import com.deloitte.dhr.excel.parser.util.RequestContextUtil;
import com.deloitte.dhr.excel.parser.util.RequestHeaderHandler;
import lombok.NoArgsConstructor;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.*;

/***
 * @Author: yinyu
 * @Date: 26/09/2022 17:48
 * @description:
 */
@NoArgsConstructor
public abstract class I18nReadListener<T> extends ParserReadListener<T> {

    protected Locale locale;

    private SecurityContext securityContext;

    private Map<String, String> headerMap;

    public I18nReadListener(Locale locale, SecurityContext securityContext) {
        this.locale = locale;
        this.securityContext = securityContext;
        headerMap = RequestContextUtil.getHeaderMap();
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        SecurityContextHolder.setContext(securityContext);
        //将旧请求的header设置进新请求的threadlocal中
        RequestHeaderHandler.setHeaderMap(headerMap);
        buildHead(headMap,context);
    }

    private void buildHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        ExcelReadHeadProperty excelHeadPropertyData = context.readSheetHolder().excelReadHeadProperty();
        Map<Integer, Head> nowHeadMapData = excelHeadPropertyData.getHeadMap();
        // 如果 nowHeadMapData 不为空，说明头的顺序已经确定 ，不需要重新构建头
        if (CollUtil.isNotEmpty(nowHeadMapData)) {
            return;
        }
        ReadHolder readHolder = context.currentReadHolder();
        ExcelReadHeadProperty headProperty = readHolder.excelReadHeadProperty();
        Class clazz = headProperty.getHeadClazz();
        // 框架层面把HeadMapData替换掉了，这里要重新解析拿到原始的 HeadMapData
        ExcelReadHeadProperty originExcelHeadPropertyData = new ExcelReadHeadProperty(context.currentReadHolder(), clazz, null);
        Map<Integer, Head> originHeadMapData = originExcelHeadPropertyData.getHeadMap();
        // 下面代码就是 copy的 com.alibaba.excel.read.processor.DefaultAnalysisEventProcessor#buildHead
        Map<Integer, String> dataMap = ConverterUtils.convertToStringMap(headMap, context);
        Map<Integer, Head> tmpHeadMap = new HashMap<>(originHeadMapData.size() * 4 / 3 + 1);
        Map<Integer, String> tmpHeadNameMap = new HashMap<>(originHeadMapData.size() * 4 / 3 + 1);
        for (Map.Entry<Integer, Head> entry : originHeadMapData.entrySet()) {
            Head headData = entry.getValue();
            List<String> headNameList = headData.getHeadNameList();
            String headName = PlaceholderResolver.resolveByRule(headNameList.get(headNameList.size() - 1),
                    (name) -> MessageUtil.getText(name, locale,null));
            tmpHeadNameMap.put(entry.getKey(),headName);
        }
        for (Map.Entry<Integer, String> stringEntry : dataMap.entrySet()) {
            if (stringEntry == null) {
                continue;
            }
            String headString = stringEntry.getValue();
            Integer stringKey = stringEntry.getKey();
            if (StrUtil.isEmpty(headString)) {
                continue;
            }
            if (context.currentReadHolder().globalConfiguration().getAutoTrim()) {
                headString = headString.trim();
            }
            if (!headString.equals(tmpHeadNameMap.get(stringKey))){
                this.saveRowError(-1, "请使用规定的模板上传", null);
                continue;
            }else {
                Head head=originHeadMapData.get(stringKey);
                head.setColumnIndex(stringKey);
                tmpHeadMap.put(stringKey,head);
            }
        }
        excelHeadPropertyData.setHeadMap(tmpHeadMap);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        super.doAfterAllAnalysed(context);
        //业务逻辑结束后remove threadlocal
        if (RequestHeaderHandler.getHeaderMap()!=null){
            RequestHeaderHandler.remove();
        }
    }
}
