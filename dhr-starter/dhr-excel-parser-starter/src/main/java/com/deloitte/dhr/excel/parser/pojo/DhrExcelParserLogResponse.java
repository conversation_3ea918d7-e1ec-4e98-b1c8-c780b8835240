package com.deloitte.dhr.excel.parser.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * Comment：dhr_excel_parser_logexcel解析日志表
 * <AUTHOR>
 * @date 2022-09-21
 */

@Data
@ApiModel("DhrExcelParserLogResponse")
public class DhrExcelParserLogResponse {

	/**主键ID*/
    @ApiModelProperty( value = "主键ID", name = "id") 
	private Long id;
    	
	/**业务标识*/
    @ApiModelProperty( value = "业务标识", name = "bs_code") 
	private String bsCode;
    	
	/**文件名*/
    @ApiModelProperty( value = "文件名", name = "file_name") 
	private String fileName;
    	
	/**开始时间*/
    @ApiModelProperty( value = "开始时间", name = "start_time") 
	private Date startTime;
    	
	/**结束时间*/
    @ApiModelProperty( value = "结束时间", name = "end_time") 
	private Date endTime;
    	
	/**解析状态;1：解析中，2：解析完成，3：解析异常*/
    @ApiModelProperty( value = "解析状态;1：解析中，2：解析完成，3：解析异常", name = "parser_status") 
	private Integer parserStatus;
    	
}