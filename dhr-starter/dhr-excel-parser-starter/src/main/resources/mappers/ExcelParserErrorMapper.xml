<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.excel.parser.dao.mapper.ExcelParserErrorMapper">

    <sql id="ExcelParserErrorColumns">
        a.id AS "id",
        a.parser_log_id AS "parserLogId",
        a.error_index AS "errorindex",
        a.error_message AS "errorMessage",
        a.error_data AS "errorData",
        a.create_by AS "createBy",
        a.create_time AS "createTime",
        a.update_by AS "updateBy",
        a.update_time AS "updateTime",
        a.remark AS "remark"
    </sql>

    <select id="findListResp" resultType="com.deloitte.dhr.excel.parser.dao.domain.ExcelParserError">
        SELECT
        <include refid="ExcelParserErrorColumns"/>
        from dhr_excel_parser_error a
        <where>
            1=1
            <if test="parma.id != null and parma.id != ''">
                AND a.id = #{parma.id}
            </if>
            <if test="parma.parserLogId != null and parma.parserLogId != ''">
                AND a.parser_log_id = #{parma.parserLogId}
            </if>
            <if test="parma.errorindex != null and parma.errorindex != ''">
                AND a.error_index = #{parma.errorindex}
            </if>
            <if test="parma.errorMessage != null and parma.errorMessage != ''">
                AND a.error_message = #{parma.errorMessage}
            </if>
            <if test="parma.errorData != null and parma.errorData != ''">
                AND a.error_data = #{parma.errorData}
            </if>
        </where>
        order by ${order}
    </select>

</mapper>