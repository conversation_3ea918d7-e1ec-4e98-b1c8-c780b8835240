<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.excel.parser.dao.mapper.ExcelParserLogMapper">

    <sql id="ExcelParserLogColumns">
        a.id AS "id",
        a.bs_code AS "bsCode",
        a.file_name AS "fileName",
        a.start_time AS "startTime",
        a.end_time AS "endTime",
        a.parser_status AS "parserStatus",
        a.create_by AS "createBy",
        a.create_time AS "createTime",
        a.update_by AS "updateBy",
        a.update_time AS "updateTime",
        a.remark AS "remark"
    </sql>

    <select id="findListResp" resultType="com.deloitte.dhr.excel.parser.dao.domain.ExcelParserLog">
        SELECT
        <include refid="ExcelParserLogColumns"/>
        from dhr_excel_parser_log a
        <where>
            1=1
            <if test="parma.id != null and parma.id != ''">
                AND a.id = #{parma.id}
            </if>
            <if test="parma.fileName != null and parma.fileName != ''">
                AND a.file_name = #{parma.fileName}
            </if>
            <if test="parma.parserStatus != null and parma.parserStatus != ''">
                AND a.parser_status = #{parma.parserStatus}
            </if>
        </where>
        order by ${order}
    </select>

</mapper>