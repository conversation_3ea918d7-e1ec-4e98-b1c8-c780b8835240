<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deloitte.dhr.excel.parser.dao.mapper.ExcelParserAddressMapper">

	<sql id="ExcelParserAddressColumns">
		a.id AS "id",
		a.bs_code AS "bsCode",
		a.file_name AS "fileName",
		a.address AS "address",
		a.start_time AS "startTime",
		a.end_time AS "endTime",
		a.parser_status AS "parserStatus",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark"
    </sql>

</mapper>