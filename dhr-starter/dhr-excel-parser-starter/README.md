### 介绍
本starter使用场景是针对excel导入、导出场景，可根据业务定义的实体进行快速导入导出处理。
#### 导入：
- 基础校验：支持业务实体类中字段进行使用spring-boot-starter-validation提供的验证注解对字段判空、长度、类型检验等，基础校验自动记录日志
- 业务校验：通过接口方式记录业务校验异常
- 导入方式支持同步、异步处理

#### 导出：
- 支持导出到多个sheet页，可指定sheet页名称

###使用
####1.引入starter依赖
```xml
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-excel-parser-starter</artifactId>
            <version>${revision}</version>
        </dependency>
```
####2.使用

定义业务实体：

```java
package com.deloitte.dhr.talent.module.talentdevelopment.excel.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.deloitte.dhr.talent.module.talentdevelopment.excel.converter.TdPersonWarehouseEmpStatusConverter;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * TdPersonWarehouseEmpDTO
 * @Author： JInit
 * Date： 2022-09-02
 */
@Data
public class TdPersonWarehouseEmpDTO {
    @NotBlank("员工编号不能为空")
    @ExcelProperty(value = "员工编号")
    @ColumnWidth(value = 20)
    private String empCode;

    @ExcelProperty(value = "员工姓名")
    @ColumnWidth(value = 20)
    private String empName;

    @ExcelProperty(value = "组织全路径")
    @ColumnWidth(value = 30)
    private String orgPathName;

    @ExcelProperty(value = "岗位")
    @ColumnWidth(value = 20)
    private String positionName;


    @ExcelProperty(value = "状态", converter = TdPersonWarehouseEmpStatusConverter.class)
    @ColumnWidth(value = 20)
    private String status;

    @ExcelProperty(value = "备注")
    @ColumnWidth(value = 20)
    private String remark;

}

```
定义枚举转换类：
```java
package com.deloitte.dhr.talent.module.talentdevelopment.excel.converter;


import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.HashMap;
import java.util.Map;


/**
 *  人才库人员状态转换器
 * @Author： JInit
 * Date： 2022-09-08
 */
public class TdPersonWarehouseEmpStatusConverter implements Converter<String> {
    /**
     * 导出转换
     */
    public static final Map<String,String> CONVERTER_EXPORT_CACHE = new HashMap(16);

    /**
     * 导入转换
     */
    public static final Map<String,String> CONVERTER_IMPORT_CACHE = new HashMap(16);

    public TdPersonWarehouseEmpStatusConverter() {
        CONVERTER_EXPORT_CACHE.put("INWAREHOUSE","在库");
        CONVERTER_EXPORT_CACHE.put("OUTWAREHOUSE","不在库");
        for (String key : CONVERTER_EXPORT_CACHE.keySet()) {
            CONVERTER_IMPORT_CACHE.put(CONVERTER_EXPORT_CACHE.get(key),key);
        }
    }

    /**
     * Java 类型
     * @return
     */
    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    /**
     * EXCEL 类型
     * @return
     */
    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }


    /**
     * 导入时转换成 名称->枚举值 to java default Type
     * @param cellData
     * @param excelContentProperty
     * @param globalConfiguration
     * @return
     * @throws Exception
     */
    @Override
    public String convertToJavaData(ReadCellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String strVal = cellData.getStringValue();
        if (StrUtil.isNotBlank(strVal) && CONVERTER_IMPORT_CACHE.containsKey(strVal.toUpperCase())) {
            return CONVERTER_IMPORT_CACHE.get(strVal);
        }
        return "";
    }


    /**
     *  导出时枚举值->名称to excel type
     * @param value
     * @param excelContentProperty
     * @param globalConfiguration
     * @return
     * @throws Exception
     */
    @Override
    public WriteCellData convertToExcelData(String value, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) {
        if (StrUtil.isNotBlank(value) && CONVERTER_EXPORT_CACHE.containsKey(value.toUpperCase())) {
            String convertValue = CONVERTER_EXPORT_CACHE.get(value);
            return new WriteCellData(convertValue);
        }
        return new WriteCellData("");
    }
}

```

导入数据监听器：
```java
package com.deloitte.dhr.talent.module.talentdevelopment.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.deloitte.dhr.excel.parser.listener.ParserReadListener;
import com.deloitte.dhr.talent.module.talentdevelopment.excel.dto.TdPersonWarehouseEmpTplDTO;
import com.deloitte.dhr.talent.module.talentdevelopment.pojo.DhrTdPersonWarehouseEmpRequest;
import com.deloitte.dhr.talent.module.talentdevelopment.service.DhrTdPersonWarehouseEmpService;

import java.util.List;
import java.util.stream.Collectors;

/**
 * TdPersonWarehouseEmpListener
 * @Author： JInit
 * Date： 2022-09-02
 */
public class TdPersonWarehouseEmpListener extends ParserReadListener<TdPersonWarehouseEmpTplDTO> {

    private DhrTdPersonWarehouseEmpService dhrTdPersonWarehouseEmpService;

    private String personnelCode;

    public TdPersonWarehouseEmpListener(DhrTdPersonWarehouseEmpService dhrTdPersonWarehouseEmpService,String personnelCode) {
        this.dhrTdPersonWarehouseEmpService = dhrTdPersonWarehouseEmpService;
        this.personnelCode = personnelCode;
    }

    @Override
    protected void handleRowData(TdPersonWarehouseEmpTplDTO data, AnalysisContext context) {

    }

    @Override
    protected void saveData(List<TdPersonWarehouseEmpTplDTO> cachedDataList) {
        List<String> codeList = cachedDataList.stream().map(TdPersonWarehouseEmpTplDTO::getEmpCode).collect(Collectors.toList());
        DhrTdPersonWarehouseEmpRequest request = new DhrTdPersonWarehouseEmpRequest();
        request.setPersonnelCode(personnelCode);
        request.setEmpCodes(codeList);
        dhrTdPersonWarehouseEmpService.insertDhrTdPersonWarehouseEmp(request);
    }
}
```

注入ExcelParserTemplate，调用导入接口
```java
    @Resource
    private ExcelParserTemplate excelParserTemplate;

    @Override
    public boolean importData(MultipartFile file, String bsCode, String personnelCode) {
        try {
        excelParserTemplate.importExcelAsync(bsCode,file,"sheet",TdPersonWarehouseEmpTplDTO.class,new TdPersonWarehouseEmpListener(this,personnelCode));
        } catch (IOException e) {
        throw new RuntimeException(e);
        }
        return true;
    }
```

导出示例：
```java
// 获取数据 
// List<User> dataList = getDataList();
// 导入excel
List<UserDTO> userDTOS = BeanUtil.copyToList(dataList, UserDTO.class);
ExcelSheetData excelSheetData = new ExcelSheetData<>(UserDTO.class,userDTOS);
String fileName = "测试-"+DateUtil.now()+".xlsx";
ExcelSheet excelSheet = new ExcelSheet("sheet",excelSheetData);
excelParserTemplate.export(fileName, CollUtil.toList(excelSheet),new ArrayList<>());
```

####3.日志存储
参考src/main/DB/init-mysql.sql