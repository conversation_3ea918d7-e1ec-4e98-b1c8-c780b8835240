package com.deloitte.dhr.report.configure;

import com.deloitte.dhr.report.util.SeleniumUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * ReportConfigure 自动装配
 *
 * <AUTHOR>
 * 2022/6/10
 */
@Configuration
@ComponentScan("com.deloitte.dhr.report")
public class ReportConfigure {

    @Value("${dhr.report.selenium-server}")
    private String serverUrl;

    @Bean
    public void configSelenium() {
        SeleniumUtil.setServerUrl(this.serverUrl);
    }
}
