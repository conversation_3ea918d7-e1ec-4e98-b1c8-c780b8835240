package com.deloitte.dhr.report.template;

import com.deloitte.dhr.report.pojo.entity.ReportHtmlTpl;
import org.openqa.selenium.print.PrintOptions;

/**
 * 报表处理 Template
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
public interface ReportTemplate {

    /**
     * 根据模板生成html文件
     *
     * @param reportHtmlTpl html生成模板实体
     * @return
     */
    byte[] generateHtml(ReportHtmlTpl reportHtmlTpl);

    /**
     * html文件转换pdf文件
     *
     * @param url 待转换成pdf的html地址
     * @return
     */
    byte[] htmlConvertPdf(String url);

    /**
     * html文件转换pdf文件
     *
     * @param url 待转换成pdf的html地址
     * @param printOptions 打印pdf的参数配置
     * @return
     */
    byte[] htmlConvertPdf(String url, PrintOptions printOptions);

}