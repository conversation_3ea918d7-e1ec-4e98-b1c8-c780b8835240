package com.deloitte.dhr.report.util;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.openqa.selenium.PageLoadStrategy;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.remote.DesiredCapabilities;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.concurrent.*;

/**
 * Selenium 工具类
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
@Component
public class SeleniumUtil {
    /**
     * 驱动driver
     */
    private static volatile RemoteWebDriver driver;

    /**
     * 服务地址
     */
    public static String serverUrl;

    /**
     * 是否心跳检查
     */
    private static Boolean isHeartbeatKeep;

    /**
     * 心跳检查周期
     */
    private static Integer heartbeatTime = 240000;

    private final ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat("selenium-pool-%d").build();

    /**
     * 线程池
     */
    private static ExecutorService cachedThreadPool = new ThreadPoolExecutor(1,5,300, TimeUnit.SECONDS,new ArrayBlockingQueue<>(10));

    public static void setServerUrl(String serverUrl) {
        SeleniumUtil.serverUrl = serverUrl;
    }

    /**
     * 获取 单例 RemoteWebDriver
     * @return
     */
    public static RemoteWebDriver getSingletonRemoteWebDriver() {
        if (driver == null) {
            synchronized (RemoteWebDriver.class) {
                if (driver == null) {
                    ChromeOptions browserOptions = new ChromeOptions();
                    browserOptions.setHeadless(true);
                    browserOptions.setPageLoadStrategy(PageLoadStrategy.NORMAL);
                    // Keeping browser open
                    browserOptions.setExperimentalOption("detach",true);
                    DesiredCapabilities dc = new DesiredCapabilities(browserOptions);
                    try {
                        driver = new RemoteWebDriver(new URL(serverUrl), dc);
                        // keep the heartbeat
                        heartbeatKeep();
                    } catch (MalformedURLException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
        return driver;
    }

    /**
     * 保持心跳检查
     */
    private static void heartbeatKeep() {
        isHeartbeatKeep = true;
        cachedThreadPool.execute(new Runnable() {
            @Override
            public void run() {
                while (isHeartbeatKeep) {
                    try {
                        driver.getCurrentUrl();
                        Thread.sleep(heartbeatTime);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }

            }
        });
    }

    /**
     * 获取新的 RemoteWebDriver
     *
     * @return
     */
    public static RemoteWebDriver getNewRemoteWebDriver() {
        ChromeOptions browserOptions = new ChromeOptions();
        browserOptions.setHeadless(true);
        browserOptions.setPageLoadStrategy(PageLoadStrategy.NORMAL);
        // Keeping browser open
        browserOptions.setExperimentalOption("detach",true);
        DesiredCapabilities dc = new DesiredCapabilities(browserOptions);
        try {
            return new RemoteWebDriver(new URL(serverUrl), dc);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
    }

    public static void setDriver(RemoteWebDriver driver) {
        SeleniumUtil.driver = driver;
    }

    /**
     * 手动退出
     *
     * @return
     */
    public static Boolean quit() {
        if (SeleniumUtil.driver != null) {
            SeleniumUtil.driver.quit();
        }
        SeleniumUtil.driver = null;
        isHeartbeatKeep = false;
        return true;
    }

    public static void setIsHeartbeatKeep(Boolean isHeartbeatKeep) {
        SeleniumUtil.isHeartbeatKeep = isHeartbeatKeep;
    }

    public static void setHeartbeatTime(Integer heartbeatTime) {
        SeleniumUtil.heartbeatTime = heartbeatTime;
    }
}
