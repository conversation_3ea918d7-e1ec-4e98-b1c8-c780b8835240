package com.deloitte.dhr.report.template.impl;

import com.deloitte.dhr.report.pojo.entity.ReportHtmlTpl;
import com.deloitte.dhr.report.template.ReportTemplate;
import com.deloitte.dhr.report.util.FreemarkerUtil;
import com.deloitte.dhr.report.util.SeleniumUtil;
import org.openqa.selenium.Pdf;
import org.openqa.selenium.WebDriverException;
import org.openqa.selenium.print.PageMargin;
import org.openqa.selenium.print.PageSize;
import org.openqa.selenium.print.PrintOptions;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Base64;

/**
 * 报表处理 Template Impl
 *
 * <AUTHOR>
 * @date 2020-04-20
 */
@Component
public class ReportTemplateImpl implements ReportTemplate {
    
    private static final Logger logger = LoggerFactory.getLogger(ReportTemplateImpl.class);


    @Override
    public byte[] generateHtml(ReportHtmlTpl reportHtmlTpl) {
        return FreemarkerUtil.renderTemplate(reportHtmlTpl.getTemplateName(),reportHtmlTpl.getDataMap());
    }

    @Override
    public byte[] htmlConvertPdf(String url) {
        PrintOptions printOptions = new PrintOptions();
        printOptions.setBackground(true);
        printOptions.setPageMargin(new PageMargin(0, 0, 0, 0));
        printOptions.setPageSize(new PageSize(29.7D, 21.0D));
        return htmlConvertPdf(url,printOptions);
    }

    @Override
    public byte[] htmlConvertPdf(String url, PrintOptions printOptions) {
        try {
            return printPdf(url,printOptions);
        } catch (WebDriverException webDriverException) {
            webDriverException.printStackTrace();
            // reset singleton driver
            SeleniumUtil.setDriver(null);
            SeleniumUtil.setIsHeartbeatKeep(false);
            // retry
            return printPdf(url,printOptions);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 打印PDF
     *
     * @param url url地址
     * @param printOptions 打印参数
     * @return
     */
    private byte[] printPdf(String url, PrintOptions printOptions) {
        RemoteWebDriver driver  = SeleniumUtil.getSingletonRemoteWebDriver();
        driver.get(url);
        Pdf pdf = driver.print(printOptions);
        String content = pdf.getContent();
        return Base64.getDecoder().decode(content);
    }

}