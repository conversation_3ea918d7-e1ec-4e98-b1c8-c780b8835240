package com.deloitte.dhr.report.pojo.entity;

import lombok.Data;

import java.util.Map;

/**
 * ReportHtmlTpl HTML报表模板实体
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
@Data
public class ReportHtmlTpl {

    /**
     * 生成html模板名称
     */
    private String templateName;

    /**
     * 填充freemarker模板的数据
     */
    private Map<String, Object> dataMap;

    public ReportHtmlTpl() {
    }

    public ReportHtmlTpl(String templateName, Map<String, Object> dataMap) {
        this.templateName = templateName;
        this.dataMap = dataMap;
    }
}
