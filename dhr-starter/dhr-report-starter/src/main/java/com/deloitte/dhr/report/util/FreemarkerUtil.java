package com.deloitte.dhr.report.util;


import cn.hutool.extra.spring.SpringUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.util.Map;

/**
 * 报表处理 Template
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
@Slf4j
public class FreemarkerUtil {
    /**
     * 渲染模板内容
     *
     * @param templateName
     * @param dataMap
     * @return
     */
    public static byte[] renderTemplate(String templateName, Map<String, Object> dataMap) {
        Configuration configuration = SpringUtil.getBean(Configuration.class);
        Template template;
        try {
            template = configuration.getTemplate(templateName);
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("Failed to get html generated template："+templateName);
        }
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            template.process(dataMap, new OutputStreamWriter(byteArrayOutputStream));
            return byteArrayOutputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("Reading html template exception:"+templateName);
        } catch (TemplateException e) {
            e.printStackTrace();
            throw new RuntimeException("Rendering the html template is abnormal, please check the parameter integrity:"+templateName);
        }

    }


}
