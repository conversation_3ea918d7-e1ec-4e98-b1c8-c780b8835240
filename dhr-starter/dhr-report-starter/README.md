### 介绍
本starter使用场景是针对报表导出快速处理。

#### html导出pdf：
- 支持通过模板渲染成html，html转换pdf文件场景的报表

### 使用
#### 1.selenium环境准备

```
## 拉取镜像
docker pull selenium/standalone-chrome:4.4.0-20220831
## 启动
docker run -d -p 4445:4444 -p 7900:7900 -e SE_NODE_MAX_SESSIONS=8 --shm-size=2g selenium/standalone-chrome:4.4.0-20220831
```
启动完成后可访问进行查看
http://ip:4445

#### 2.引入starter依赖
```xml
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-report-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>31.0.1-jre</version>
        </dependency>
```

添加配置：

```yaml

## html转换pdf场景
spring:
  freemarker:
    template-loader-path: classpath:/template
    suffix: .html
    prefer-file-system-access: false
    
## selenium服务
dhr: 
  report:
    selenium-server: http://***********:4445

```

#### 3.后端使用

通过模板生成html：

```java
@Resource
private ReportTemplate reportTemplate;

byte[] fileBytes = reportTemplate.generateHtml(tpl);

```
html转换pdf：

```java
@Resource
private ReportTemplate reportTemplate;

byte[] fileBytes = reportTemplate.htmlConvertPdf("http://**********:9025/utility/file/dfs/preview?fileId=1574307253090861058");

// 该手动退出方法仅在本地测试时使用，存在性能问题
// SeleniumUtil.quit()


```


#### 4.其他说明

- 为减少无头浏览器selenium启动开销，新增了心跳保持机制（仅在第一次使用时进行启动浏览器）
- 因为selenium并发连接数有限，虽然有超时释放机制，本地连接测试时，请使用完成后手动调用SeleniumUtil.quit()退出浏览器释放并发连接

 

