### 介绍
本starter使用场景是针对预设计算公式计算提供通用接口。
支持if/else条件判断，以及java计算等

#### 功能：
- 公式构建
- 公式验证
- 公式解析

### 使用
#### 公式构建
公式构建器分为3部分内容：公式包命名、公式依赖类、公式主体
配合前端构建简单公式主体：简单if/else结构
```
公式内容：
    when $f:FormulaDemo(actualCurrent/targetCurrent>=k1&&actualCurrent/targetCurrent<k2)
    then $f.setResult(30.0000);,
    when $f:FormulaDemo(actualCurrent/targetCurrent>=k2&&actualCurrent/targetCurrent<k3) 
    then $f.setResult(50.0000);,
    
含义：
    如果 [指标][当期][实际值] / [指标][当期][目标值] >= k1 && [指标][当期][实际值] / [指标][当期][目标值] < k2
    结果 = 30

    如果 [指标][当期][实际值] / [指标][当期][目标值] >= k2 && [指标][当期][实际值] / [指标][当期][目标值] < k3
    结果 = 50

```
#### 后端公式验证、解析示例

#### 1.引入starter依赖
```xml
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-rule-starter</artifactId>
            <version>${revision}</version>
        </dependency>
```


#### 2.后端使用

包含公式构建、校验、解析

```java
@Resource
private RuleTemplate template;

// test build
String body = "when $f:FormulaDemo(actualCurrent/targetCurrent>=k1&&actualCurrent/targetCurrent<k2) then $f.setResult(30.0000);,when $f:FormulaDemo(actualCurrent/targetCurrent>=k2&&actualCurrent/targetCurrent<k3) then $f.setResult(50.0000);,";
FormulaBuilder formulaBuilder = new FormulaBuilder();
formulaBuilder.setBody(body);
formulaBuilder.setPackageName("test1");
List<String> list = new ArrayList<>();
list.add("com.deloitte.dhr.rule.test.FormulaDemo");
formulaBuilder.setImportClassList(list);
String formulaStr = template.build(formulaBuilder);
logger.info("build result:{}",formulaStr);
// test verify
RuleVerifyResult verify = template.verify(formulaStr);
logger.info("verify result:{}",verify);
// test parse
FormulaDemo formulaDemo = new FormulaDemo();
formulaDemo.setActualCurrent(25.00);
formulaDemo.setTargetCurrent(100.00);
formulaDemo.setK1(0.2);
formulaDemo.setK2(0.4);
formulaDemo.setK3(0.9);
FormulaDemo parse = template.parse(formulaStr, formulaDemo);
logger.info("parse result:{}",parse);


```


#### 3.其他说明
暂无

 

