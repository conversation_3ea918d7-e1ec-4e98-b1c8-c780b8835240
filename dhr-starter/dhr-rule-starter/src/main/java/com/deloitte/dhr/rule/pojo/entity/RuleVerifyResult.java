package com.deloitte.dhr.rule.pojo.entity;

import lombok.Data;
import org.kie.api.builder.Message;

import java.util.List;
import java.util.Map;

/**
 * RuleVerifyResult 验证结果
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
@Data
public class RuleVerifyResult {

    /**
     * 是否成功
     */
    private Boolean isSuccess;

    /**
     * 错误信息
     */
    private List<Message> errorList;

    public RuleVerifyResult() {
        this.isSuccess = true;
    }
}
