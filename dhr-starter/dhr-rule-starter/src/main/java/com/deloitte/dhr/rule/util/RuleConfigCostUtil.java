package com.deloitte.dhr.rule.util;

import cn.hutool.core.util.StrUtil;
import com.deloitte.dhr.rule.pojo.dto.FormulaConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Config 规则配置转换工具类
 *
 * <AUTHOR>
 * @since 2022-09-02
 */
public class RuleConfigCostUtil {

    /**
     * 配置转换为规则字符串
     * @param list 配置列表
     * @param conditionMap 条件表达式替换规则映射
     * @param resultMap 结果表达式替换规则映射
     * @return
     */
    public static String costRuleStr(List<FormulaConfig> list, Map<String,String> conditionMap, Map<String,String> resultMap) {
        StringBuilder formulaStrBuilder = new StringBuilder();
        for (FormulaConfig formulaConfig : list) {
            // 解析条件表达式
            String conditionExpression = formulaConfig.getConditionExpression();
            for (String key : conditionMap.keySet()) {
                conditionExpression = StrUtil.replace(conditionExpression,key,conditionMap.get(key));
            }
            formulaStrBuilder.append(conditionExpression);

            // 解析结果表达式
            String resultExpression = formulaConfig.getResultExpression();
            for (String key : resultMap.keySet()) {
                resultExpression = StrUtil.replace(resultExpression,key,resultMap.get(key));
            }
            formulaStrBuilder.append(resultExpression);
            formulaStrBuilder.append(";,");
        }
        return formulaStrBuilder.toString();
    }

    /**
     * 规则字符串转换为规则列表
     * @param ruleStr 规则字符串
     * @param conditionMap 条件表达式替换规则映射
     * @param resultMap 结果表达式替换规则映射
     * @return
     */
    public static List<FormulaConfig> costRuleConfig(String ruleStr, Map<String,String> conditionMap, Map<String,String> resultMap) {
        List<FormulaConfig> list = new ArrayList<>();
        String[] ruleArray = ruleStr.split(";,");
        for(String rule : ruleArray) {
            FormulaConfig formulaConfig = new FormulaConfig();
            String[] ruleOneArray = rule.split("then");
            String conditionExpression = ruleOneArray[0];
            for (String key : conditionMap.keySet()) {
                conditionExpression = StrUtil.replace(conditionExpression,conditionMap.get(key),key);
            }
            formulaConfig.setConditionExpression(conditionExpression);
            String resultExpression = " then" + ruleOneArray[1];
            for (String key : resultMap.keySet()) {
                resultExpression = StrUtil.replace(resultExpression,resultMap.get(key),key);
            }
            formulaConfig.setResultExpression(resultExpression);
            list.add(formulaConfig);
        }
        return list;
    }

}
