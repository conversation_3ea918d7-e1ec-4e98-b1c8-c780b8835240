package com.deloitte.dhr.rule.template;

import com.deloitte.dhr.rule.pojo.entity.FormulaBuilder;
import com.deloitte.dhr.rule.pojo.entity.RuleVerifyResult;
import org.apache.poi.ss.formula.functions.T;

/**
 * 规则引擎 Template
 *
 * <AUTHOR>
 * @date 2020-11-29
 */
public interface RuleTemplate {
    /**
     * 构建公式串
     *
     * @param formulaBuilder 公式构建器
     * @return
     */
    String build(FormulaBuilder formulaBuilder);
    /**
     * 公式校验
     *
     * @param formulaStr 公式串
     * @return
     */
    RuleVerifyResult verify(String formulaStr);

    /**
     * 公式解析
     *
     * @param formulaStr 公式串
     * @param workEntry 工作实体对象
     * @return
     */
    <T> T  parse(String formulaStr, T workEntry);


}