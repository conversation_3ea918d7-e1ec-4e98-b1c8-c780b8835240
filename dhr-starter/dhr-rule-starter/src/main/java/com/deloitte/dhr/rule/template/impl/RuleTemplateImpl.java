package com.deloitte.dhr.rule.template.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.deloitte.dhr.rule.pojo.dto.FormulaConfig;
import com.deloitte.dhr.rule.pojo.entity.FormulaBuilder;
import com.deloitte.dhr.rule.pojo.entity.RuleVerifyResult;
import com.deloitte.dhr.rule.template.RuleTemplate;
import com.deloitte.dhr.rule.test.FormulaDemo;
import com.deloitte.dhr.rule.util.RuleConfigCostUtil;
import org.kie.api.builder.Message;
import org.kie.api.builder.Results;
import org.kie.api.io.ResourceType;
import org.kie.api.runtime.KieSession;
import org.kie.internal.utils.KieHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 规则引擎 Template Impl
 *
 * <AUTHOR>
 * @date 2020-11-29
 */
@Component
public class RuleTemplateImpl implements RuleTemplate {
    
    private static final Logger logger = LoggerFactory.getLogger(RuleTemplateImpl.class);

    /**
     * package key
     */
    private static String PACKAGE_KEY = "package";

    /**
     * import key
     */
    private static String IMPORT_KEY = "import";

    /**
     * 换行符号
     */
    private static String NEWLINE = "\n";

    @Override
    public String build(FormulaBuilder formulaBuilder) {
        StringBuffer strBuffer = new StringBuffer();
        // 构建 package
        strBuffer.append(PACKAGE_KEY+" "+ formulaBuilder.getPackageName()).append(NEWLINE);
        // 构建 import
        List<String> importClassList = formulaBuilder.getImportClassList();
        if (CollUtil.isNotEmpty(importClassList)) {
            importClassList.forEach(importClass -> {
                strBuffer.append(IMPORT_KEY+" "+importClass).append(NEWLINE);
            });
        }
        // 构建body
        String body = formulaBuilder.getBody();
        String[] split = body.split(",");
        for(int i=0; i < split.length; i++) {
            strBuffer.append("rule 'rule calculate" + i + "' ").append(NEWLINE);
            strBuffer.append(" no-loop true ").append(NEWLINE);
            strBuffer.append(split[i]);
            strBuffer.append(NEWLINE).append(" drools.halt(); ");
            strBuffer.append(NEWLINE).append(" end ").append(NEWLINE);
        }
        return strBuffer.toString();
    }

    @Override
    public RuleVerifyResult verify(String formulaStr) {
        RuleVerifyResult result = new RuleVerifyResult();
        KieHelper kieHelper = new KieHelper();
        kieHelper.addContent(formulaStr, ResourceType.DRL);
        Results results = kieHelper.verify();
        if (results.hasMessages(Message.Level.WARNING, Message.Level.ERROR)) {
            result.setIsSuccess(false);
            List<Message> messages = results.getMessages(Message.Level.WARNING, Message.Level.ERROR);
            result.setErrorList(messages);
        }
        return result;
    }

    @Override
    public <T> T  parse(String formulaStr, T workEntry) {
        KieHelper kieHelper = new KieHelper();
        kieHelper.addContent(formulaStr, ResourceType.DRL);
        KieSession kieSession = kieHelper.build().newKieSession();
        kieSession.insert(workEntry);
        kieSession.fireAllRules();
        kieSession.dispose();
        return workEntry;
    }



    public static void main(String[] args) {
        testFormulaConfig();
        //test();
        // test build
        String body = "when $f:FormulaDemo(actualCurrent / targetCurrent >= k1&&actualCurrent/targetCurrent<k2) then $f.setResult(30.0000);,when $f:FormulaDemo(actualCurrent/targetCurrent>=k2&&actualCurrent/targetCurrent<k3) then $f.setResult(50.0000);,";
        FormulaBuilder formulaBuilder = new FormulaBuilder();
        formulaBuilder.setBody(body);
        formulaBuilder.setPackageName("test1");
        List<String> list = new ArrayList<>();
        list.add("com.deloitte.dhr.rule.test.FormulaDemo");
        formulaBuilder.setImportClassList(list);
        RuleTemplate template = new RuleTemplateImpl();
        String formulaStr = template.build(formulaBuilder);
        logger.info("build result:{}",formulaStr);
        // test verify
        RuleVerifyResult verify = template.verify(formulaStr);
        logger.info("verify result:{}",verify);
        // test parse
        FormulaDemo formulaDemo = new FormulaDemo();
        formulaDemo.setActualCurrent(25.00);
        formulaDemo.setTargetCurrent(100.00);
        formulaDemo.setK1(0.2);
        formulaDemo.setK2(0.4);
        formulaDemo.setK3(0.9);
        FormulaDemo parse = template.parse(formulaStr, formulaDemo);
        logger.info("parse result:{}",parse);
    }


    public static void testFormulaConfig() {
        List<FormulaConfig> formulaConfigList = new ArrayList<>();
        formulaConfigList.add(new FormulaConfig("如果(目标值 < 完成值)","结果(完成值*0.8)"));
        formulaConfigList.add(new FormulaConfig("如果(目标值 >= 完成值)","结果(完成值*1.2)"));
        formulaConfigList.add(new FormulaConfig("如果()","结果(111.312)"));

        Map<String,String> conditionMap = new HashMap<>(16);
        conditionMap.put("如果"," when $f:FormulaDemo");
        conditionMap.put("目标值"," targetCurrent");
        conditionMap.put("完成值"," actualCurrent");
        conditionMap.put("或者"," ||");
        conditionMap.put("并且"," &&");
        Map<String,String> resultMap = new HashMap<>(16);
        resultMap.put("结果"," then $f.setResult");
        resultMap.put("目标值"," $f.getTargetCurrent()");
        resultMap.put("完成值"," $f.getActualCurrent()");
        resultMap.put("或者"," ||");
        resultMap.put("并且"," &&");
        String ruleStr = RuleConfigCostUtil.costRuleStr(formulaConfigList, conditionMap, resultMap);
        System.out.println("规则串："+ruleStr);

        String body = ruleStr;
        FormulaBuilder formulaBuilder = new FormulaBuilder();
        formulaBuilder.setBody(body);
        formulaBuilder.setPackageName("test1");
        List<String> list = new ArrayList<>();
        list.add("com.deloitte.dhr.rule.test.FormulaDemo");
        formulaBuilder.setImportClassList(list);
        RuleTemplate template = new RuleTemplateImpl();
        String formulaStr = template.build(formulaBuilder);
        System.out.println("build result:"+formulaStr);
        // test verify
        RuleVerifyResult verify = template.verify(formulaStr);
        System.out.println("verify result:"+verify);
        // test parse
        FormulaDemo formulaDemo = new FormulaDemo();
        formulaDemo.setTargetCurrent(70.00);
        formulaDemo.setActualCurrent(80.00);
        FormulaDemo parse = template.parse(formulaStr, formulaDemo);
        System.out.println("parse result:"+parse);


        List<FormulaConfig> listFormulaConfig = RuleConfigCostUtil.costRuleConfig(ruleStr, conditionMap, resultMap);
        System.out.println(listFormulaConfig);
    }
}