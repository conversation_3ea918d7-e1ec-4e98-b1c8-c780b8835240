## 项目说明

### 一、环境说明

```
JDK 版本 11
Maven 版本 3.6 +
Spring Boot 2.7.8
Spring Cloud 2021.0.5
Spring Cloud Alibaba 2021.0.5.0
```

#### 二、模块划分

```
├── dhr-common
│──────────├── dhr-common-auth                 数据权限相关组件
│──────────├── dhr-common-bean                 项目开发基础公共模块基础：异常定义，枚举，基础实体，工具类等
│──────────├── dhr-common-core                 项目开发基础公共模块核心：异常配置，基础配置，feign适配，filter过滤器等
│──────────├── dhr-common-mybtismvc            mybatis以及mvc公用的基础类
│──────────├── dhr-common-util                 常用工具
├── dhr-service
│──────────├── dhr-ai-service                  AI 服务：集成AI能力的HR助手、教学课程、AI陪练、培训任务等
│──────────├── dhr-basic-service               核心服务：与sap 交互，提供组织、人事、考勤等
│──────────├── dhr-bpm-service                 流程引擎服务：流程引擎，如审批、发起流程等
│──────────├── dhr-collection-service          收集服务：热点操作收集
│──────────├── dhr-gateway-service             网关服务：提供服务路由，统一过滤器拦截处理
│──────────├── dhr-mda-service                 主数据服务：用于同步sap的数据到此服务
│──────────├── dhr-oauth-service               认证服务：基于OAuth2/JWT提供统一认证服务
│──────────├── dhr-performance-new-service     绩效服务：目标管理、绩效管理等
│──────────├── dhr-performance-service         绩效服务：目标管理、绩效管理等
│──────────├── dhr-ssc-service                 门户服务：门户服务，提供门户首页、站内信、待办等
│──────────├── dhr-talent-service              人才服务：360测评、人才库、人才画像等
│──────────├── dhr-utility-service             工具服务：文件、短信、邮件相关等
│──────────├── dhr-xxl-job-admin-service       定时任务服务：配置定时任务调用
│──────────├── dhr-work-order-service          工单服务：工单管理、工单审批、工单发起、工单池
│──────────├── dhr-questionnaire-service       问卷服务：问卷管理、问卷分析、问卷统计等
│──────────└── sql
├── dhr-starter
│──────────├── dhr-api-encryption-starter       API接口加密启动器：提供接口加密解密功能
│──────────├── dhr-excel-parser-starter         excel导入/导出启动器：快速excel导入、导出以及解析日志记录组件
│──────────├── dhr-log-starter                  日志启动器：日志快速接入ELK组件
│──────────├── dhr-quartz-starter               定时任务启动器：定时任务配置、定时任务调度管理组件
│──────────├── dhr-redis-starter                redis工具启动器：封装常用Redis工具类，注解策略锁功能组件
│──────────├── dhr-report-starter               报表导出启动器：html导出pdf、html导出word、html导出excel等
│──────────├── dhr-resubmit-starter             接口防重复提交启动器：接口防重复提交组件
│──────────├── dhr-rule-starter                 规则引擎启动器：规则引擎，如规则、规则引擎等
│──────────├── dhr-swagger-starter              swagger启动器：swagger快速集成
```

#### 三、项目结构

代码主体分为3层，分别是controller层、service层、dao层

- **controller层：** 服务接口层，用于对外提供服务接口，不包含业务逻辑，只提供请求的获取、参数以及响应数据处理等功能；
- **service层：** 业务逻辑层，主要包含两方面的内容：业务数据处理和业务流程编制；这两方面的功能会涉及到多方面的协作，比如：与数据库的数据交互，则通过dao层；与第三方接口的交互，则通过manager层等；
- **dao层：** 数据访问层，提供了与各种数据源进行数据交互的能力，比如与关系型数据库交互（使用mybatis/mybatis-plus)
  、与非结构化数据库交互，比如mongodb等

#### 四、启动说明

```shell
export JAVA_HOME=/home/<USER>/java/jdk1.8.0_201
export CLASSPATH=.:${JAVA_HOME}/jre/lib/rt.jar:${JAVA_HOME}/lib/dt.jar:${JAVA_HOME}/lib/tools.jar:${JAVA_HOME}/lib/sapjco3.jar
export PATH=$PATH:${JAVA_HOME}/bin
export LD_LIBRARY_PATH=/home/<USER>/java/jdk1.8.0_201/lib/amd64/server
```

启动提供shell脚本进行快速启动、停止：

##### 启动脚本

启动脚本：

```shell
source /etc/profile
nohup java  -jar  -Xms500m -Xmx700m -Xdebug -Xrunjdwp:server=y,transport=dt_socket,address=***********:19809,suspend=n   -Dconfig.profile=dev -Dconfig.enable=true ./dhr-talent-service.jar  > console.log 2>error.log & echo $! > pid.log
```

-Dconfig.profile 参数指定激活的具体配置文件是dev/test/prod
-Dconfig.enable 参数指定是否从nacos配置中心拉取配置文件

停止脚本：

```shell
kill -9 `cat pid.log`
```

重启脚本：

```shell
#!/bin/bash 
echo "start";
sudo ./stop.sh;
sudo ./startup.sh;
echo "end";
```

#### 五、分支版本说明

2.3.1: 绩效1.0、insights1.0  
2.3.2: 绩效1.0、insights2.0  
2.3.3: 绩效1.0、insights2.0、mda主数据改造  
2.3.4: 绩效1.0、insights2.0、mda主数据改造、工单回归  
2.3.5: 绩效1.0、insights2.0、mda主数据改造、工单回归、AI陪练  
2.3.6: 绩效1.0、insights2.0、mda主数据改造、工单回归、AI陪练、门户改造  
2.3.7: 绩效1.0、insights2.0、mda主数据改造、工单回归、AI陪练、门户改造、AI伙伴  
2.3.8: 绩效1.0、insights2.0、mda主数据改造、工单回归、AI陪练、门户改造、AI伙伴、AI助手、人才发展  
2.3.9: 绩效1.0、insights2.0、mda主数据改造、工单回归、AI陪练、门户改造、AI伙伴、AI助手、人才发展、AI助手1.1  