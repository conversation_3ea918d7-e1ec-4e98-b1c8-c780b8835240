<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.deloitte.dhr</groupId>
    <artifactId>dhr-business-service</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <modules>
        <module>dhr-starter</module>
        <module>dhr-common</module>
        <module>dhr-service</module>
    </modules>

    <properties>
        <!-- DHR 版本 -->
        <revision>3.7.0-SNAPSHOT</revision>
        <!-- 基础环境 -->
        <java.version>11</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <bcprov-jdk15to18.version>1.69</bcprov-jdk15to18.version>
        <!-- Spring Projects -->
        <spring-boot.version>2.7.8</spring-boot.version>
        <spring-cloud.version>2021.0.5</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
        <bootstrap.version>3.1.3</bootstrap.version>
        <openfeign.version>3.1.5</openfeign.version>
        <!-- Skywalking -->
        <skywalking.version>8.4.0</skywalking.version>
        <!-- POI -->
        <poi.version>4.1.2</poi.version>
        <easyexcel.version>3.1.1</easyexcel.version>
        <!-- db相关 -->
        <mybatis-plus.version>3.4.1</mybatis-plus.version>
        <druid.version>1.2.8</druid.version>
        <tk.mybatis.version>2.0.3</tk.mybatis.version>
        <mybatis-spring-boot.version>2.2.2</mybatis-spring-boot.version>
        <mysql-connector-java.version>8.0.33</mysql-connector-java.version>
        <!-- Tools -->
        <hutool.version>5.8.37</hutool.version>
        <fastjson.version>1.2.75</fastjson.version>
        <fastjson2.version>2.0.55</fastjson2.version>
        <commons-io.version>2.6</commons-io.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <!-- redis -->
        <redisson.version>3.14.1</redisson.version>
        <!-- elasticsearch -->
        <elasticsearch.version>7.1.1</elasticsearch.version>
        <!-- 文档api -->
        <knife4j.version>3.0.3</knife4j.version>
        <springfox.swagger2.version>2.9.2</springfox.swagger2.version>
        <swagger2.version>2.9.2</swagger2.version>
        <swagger3.version>3.0.0</swagger3.version>
        <swagger.version>1.5.21</swagger.version>
        <!-- 阿里服务 -->
        <alipay-sdk.version>4.13.0.ALL</alipay-sdk.version>
        <aliyun-java-sdk-core.version>4.5.16</aliyun-java-sdk-core.version>
        <aliyun-java-sdk-sms.version>3.0.0-rc1</aliyun-java-sdk-sms.version>
        <aliyun-sdk-oss.version>3.10.2</aliyun-sdk-oss.version>
        <!-- 服务工具 -->
        <xxl-job.version>2.3.1</xxl-job.version>
        <!-- 测试 -->
        <powermock.version>2.0.2</powermock.version>
        <!-- 认证相关 -->
        <java-jwt.version>3.10.2</java-jwt.version>
        <spring-security-oauth2.version>2.3.4.RELEASE</spring-security-oauth2.version>
        <spring-security-jwt.version>1.0.10.RELEASE</spring-security-jwt.version>
        <spring-security-core.version>5.3.5.RELEASE</spring-security-core.version>
        <!-- webService -->
        <cxf-rt.version>3.1.12</cxf-rt.version>

        <!-- pdf -->
        <freemarker.version>2.3.31</freemarker.version>
        <selenium.version>4.4.0</selenium.version>
        <!--activiti 6-->
        <activiti.version>6.0.0</activiti.version>
        <activiti-bpmn.version>6.0.0</activiti-bpmn.version>
        <!-- 其他 -->
        <velocity.version>2.3</velocity.version>
        <javax.interceptor-api.version>1.2</javax.interceptor-api.version>
        <javax.servlet-api.version>4.0.1</javax.servlet-api.version>
        <logstash-logback-encoder.version>5.2</logstash-logback-encoder.version>
        <jettison.version>1.1</jettison.version>
        <UserAgentUtils.version>1.21</UserAgentUtils.version>
        <minio.version>7.0.2</minio.version>
        <admin.version>2.7.8</admin.version>
        <httpcore.version>4.4.14</httpcore.version>
        <httpclient.version>4.5.13</httpclient.version>
        <commons-httpclient.version>3.1</commons-httpclient.version>
        <activation.version>1.1.1</activation.version>
        <axis.version>1.4</axis.version>
        <jaxrpc.version>1.1</jaxrpc.version>
        <mail.version>1.4.7</mail.version>
        <nacos-common.version>1.4.1</nacos-common.version>
        <pagehelper-starter.version>1.4.1</pagehelper-starter.version>
        <pagehelper.version>5.3.1</pagehelper.version>
        <jsqlparser.version>4.2</jsqlparser.version>
        <geantyref.version>1.3.11</geantyref.version>
        <commons-pool2.version>2.6.2</commons-pool2.version>
        <jjwt.version>0.9.1</jjwt.version>
        <servlet-api.version>2.5</servlet-api.version>
        <xdocreport.version>1.0.6</xdocreport.version>
        <joda-time.version>2.9.9</joda-time.version>
        <fastdfs-client-java.version>1.27.0.0</fastdfs-client-java.version>
        <aspose-words.version>16.8.0</aspose-words.version>
        <springfox-version>3.0.0</springfox-version>
        <springfox-spi-version>3.0.0</springfox-spi-version>
        <guava.version>31.0.1-jre</guava.version>
        <shedlock.version>4.39.0</shedlock.version>
        <velocity.version>2.3</velocity.version>
        <disruptor.version>3.3.6</disruptor.version>
        <jasypt.version>3.0.2</jasypt.version>
        <alibaba-nls-sdk.version>2.2.11</alibaba-nls-sdk.version>
    </properties>

    <dependencyManagement>

        <dependencies>
            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-bootstrap</artifactId>
                <version>${bootstrap.version}</version>
            </dependency>

            <dependency>
                <artifactId>swagger-annotations</artifactId>
                <groupId>io.swagger</groupId>
                <version>${swagger.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${openfeign.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <!-- 其他 -->
            <dependency>
                <groupId>io.leangen.geantyref</groupId>
                <artifactId>geantyref</artifactId>
                <version>${geantyref.version}</version>
            </dependency>

            <!-- 定时任务分布数锁-->
            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-spring</artifactId>
                <version>${shedlock.version}</version>
            </dependency>

            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-provider-redis-spring</artifactId>
                <version>${shedlock.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>1.2.5.RELEASE</version>
            </dependency>

            <!--sentinel相关-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-actuator</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${java-jwt.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!-- 国密算法Bouncy Castle依赖 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15to18</artifactId>
                <version>${bcprov-jdk15to18.version}</version>
            </dependency>

            <!-- POI -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-core</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- HuTool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!--Swagger-UI API文档生产工具-->
            <!-- https://mvnrepository.com/artifact/io.springfox/springfox-boot-starter -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${springfox-version}</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-spi</artifactId>
                <version>${springfox-spi-version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-bean-validators</artifactId>
                <version>${swagger3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun-java-sdk-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security.oauth</groupId>
                <artifactId>spring-security-oauth2</artifactId>
                <version>${spring-security-oauth2.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-jwt</artifactId>
                <version>${spring-security-jwt.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring-security-core.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.interceptor</groupId>
                <artifactId>javax.interceptor-api</artifactId>
                <version>${javax.interceptor-api.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${javax.servlet-api.version}</version>
            </dependency>

            <!-- Redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback-encoder.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2-extension</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2-extension-spring5</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.jettison</groupId>
                <artifactId>jettison</artifactId>
                <version>${jettison.version}</version>
            </dependency>

            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${UserAgentUtils.version}</version>
            </dependency>

            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-spring-boot-starter</artifactId>
                <version>${tk.mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${httpcore.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-httpclient</groupId>
                <artifactId>commons-httpclient</artifactId>
                <version>${commons-httpclient.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
            </dependency>

            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>${jsqlparser.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.axis</groupId>
                <artifactId>axis</artifactId>
                <version>${axis.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>activation</artifactId>
                <version>${activation.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.xml</groupId>
                <artifactId>jaxrpc</artifactId>
                <version>${jaxrpc.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>mail</artifactId>
                <version>${mail.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.axis</groupId>
                <artifactId>axis-saaj</artifactId>
                <version>${axis.version}</version>
            </dependency>

            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${admin.version}</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>${servlet-api.version}</version>
            </dependency>

            <dependency>
                <groupId>net.oschina.zcx7878</groupId>
                <artifactId>fastdfs-client-java</artifactId>
                <version>${fastdfs-client-java.version}</version>
            </dependency>

            <dependency>
                <groupId>fr.opensagres.xdocreport</groupId>
                <artifactId>xdocreport</artifactId>
                <version>${xdocreport.version}</version>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.version}</version>
            </dependency>

            <!-- pdf -->
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>

            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-api</artifactId>
                <version>${selenium.version}</version>
            </dependency>

            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-remote-driver</artifactId>
                <version>${selenium.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-common</artifactId>
                <version>${nacos-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- velocity模板引擎，用于mybatis-plus代码生成器 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <dependency>
                <groupId>org.activiti</groupId>
                <artifactId>activiti-spring-boot-starter-jpa</artifactId>
                <version>${activiti.version}</version>
            </dependency>

            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt.version}</version>
            </dependency>

            <!-- dhr starter -->
            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-api-encryption-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-excel-parser-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-log-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-quartz-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-redis-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-report-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-resubmit-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-rule-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-swagger-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- dhr common -->
            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-common-bean</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-common-util</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-common-mybtismvc</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-common-auth</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- xxl-job -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.nls</groupId>
                <artifactId>nls-sdk-recognizer</artifactId>
                <version>${alibaba-nls-sdk.version}</version>
            </dependency>

            <!--activiti-->
            <dependency>
                <groupId>org.activiti</groupId>
                <artifactId>activiti-spring-boot-starter-basic</artifactId>
                <version>${activiti.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>activation</artifactId>
                        <groupId>javax.activation</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mybatis</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!--统一版本管理插件，会在install的时候替换${revision}为具体版本号，不加会导致maven识别不了install的包-->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <configuration>
                    <!--true：更新pom文件，不然无法更新module里的pom版本号，此处还有更高级的用法，具体参靠官方文档-->
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>
